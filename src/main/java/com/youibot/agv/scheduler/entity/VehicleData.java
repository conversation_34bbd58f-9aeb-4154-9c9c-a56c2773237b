package com.youibot.agv.scheduler.entity;

import com.youibot.agv.scheduler.param.ErrorParam;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.Colors;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.entity.ShapeInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-09-19 17:50
 */
@Data
@ApiModel(value = "VehicleData", description = "机器人数据")
public class VehicleData implements Serializable {

    public VehicleData(Vehicle vehicle) {
        if (vehicle != null) {
            this.id = vehicle.getId();
            this.name = vehicle.getName();
            this.agvType = vehicle.getAgvType();
            this.agvMapId = vehicle.getAgvMapId();
            this.connectedTime = vehicle.getConnectedTime();
            this.mapStatus = vehicle.getMapStatus();
            this.appointStatus = vehicle.getAppointStatus();
            this.workStatus = vehicle.getWorkStatus();
            this.abnormalStatus = vehicle.getAbnormalStatus();
            this.controlMode = vehicle.getControlMode();
            this.navigationType = vehicle.getNavigationType();
            this.shapeInfo = vehicle.getShapeInfo();
            this.defaultVehicleStatus = vehicle.getDefaultVehicleStatus();
            this.errorMessage = vehicle.getErrorMessage();
            this.executedPaths = vehicle.getExecutedPaths();
            this.runningPaths = vehicle.getRunningPaths();
            this.planedPaths = vehicle.getPlanedPaths();
            this.colors = vehicle.getColors();
            this.onlineStatus = vehicle.getOnlineStatus();
            this.missionName = vehicle.getMissionName();
            this.missionWorkId = vehicle.getMissionWorkId();
            this.autoCharge = vehicle.isAutoCharge();
            this.autoPark = vehicle.isAutoPark();
            this.agvColor = vehicle.getAgvColor();
            this.errorData = vehicle.getErrorData();
            this.missionErrorData = vehicle.getMissionErrorData();
        }
    }

    @ApiModelProperty(value = "Agv ID")
    private String id;

    @ApiModelProperty(value = "名称", position = 1)
    private String name;

    @ApiModelProperty(value = "机器人类型", position = 2)
    private String agvType;

    @ApiModelProperty(value = "AGV地图ID", position = 3)
    private String agvMapId;

    @ApiModelProperty(value = "连接时间", position = 4)
    private Date connectedTime;

    @ApiModelProperty(value = "地图状态  0、未同步  1、已同步", position = 5)
    protected Integer mapStatus;

    @ApiModelProperty(value = "地图指定状态  0、未指定  1、已指定", position = 6)
    protected Integer appointStatus;

    @ApiModelProperty(value = "任务状态 1、空闲 2、任务 3、充电 4、归位", position = 7)
    protected Integer workStatus;

    @ApiModelProperty(value = "异常状态 1、无异常 2、任务异常 3、充电异常 4、归位异常", position = 8)
    protected Integer abnormalStatus;

    @ApiModelProperty(value = "控制模式 1、自动模式 2、手动模式 3、录制模式", position = 9)
    private Integer controlMode;

    @ApiModelProperty(value = "导航类型 LASER:激光导航 QR_CODE:二维码导航 BLEND:混合导航", position = 10)
    private String navigationType;

    @ApiModelProperty(value = "外型信息", position = 11)
    private ShapeInfo shapeInfo;

    @ApiModelProperty(value = "状态信息", position = 12)
    private DefaultVehicleStatus defaultVehicleStatus;

    @ApiModelProperty(value = "错误信息(目前只提示智能充电/归位的报错信息)", position = 13)
    private String errorMessage;

    @ApiModelProperty(value = "AGV已规划的路径", position = 15)
    private List<Path> planedPaths = new LinkedList<>();

    @ApiModelProperty(value = "AGV已执行的路径", position = 16)
    private List<Path> executedPaths = new LinkedList<>();

    @ApiModelProperty(value = "AGV正在运行的路径", position = 17)
    private List<Path> runningPaths = new LinkedList<>();

    @ApiModelProperty(value = "AG", position = 18)
    private Colors colors;

    @ApiModelProperty(value = "在线状态：0:离线 1:在线", position = 19)
    private Integer onlineStatus;

    @ApiModelProperty(value = "执行的任务名称", position = 20)
    private String missionName;

    @ApiModelProperty(value = "执行的作业ID", position = 21)
    private String missionWorkId;

    @ApiModelProperty(value = "是否自动充电")
    private boolean autoCharge;

    @ApiModelProperty(value = "是否自动泊车")
    private boolean autoPark;

    @ApiModelProperty(value = "机器人颜色", position = 23)
    private String agvColor;

    @ApiModelProperty(value = "异常对象集合", position = 24)
    private List<ErrorParam> errorData;

    @ApiModelProperty(value = "任务异常集合", position = 25)
    private List<ErrorParam> missionErrorData;
}
