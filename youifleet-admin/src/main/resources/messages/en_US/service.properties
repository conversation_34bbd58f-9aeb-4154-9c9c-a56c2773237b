system.missing.parameter=Missing Parameter
system.data.type.error=Data Type Error
system.code.format.error=Code [%s] Format Error
system.name.format.error=Name [%s] Format Error
system.code.duplicate.error=Code [%s] Duplicate Error
system.code.is.empty.error=Code [%s] Cannot Be Empty
system.operate.timeout=Operate Timeout
system.directory.is.empty=The file of current directory [%s] is empty
system.directory.is.not.exists=The current directory [%s] is not exists
system.account.is.not.exists=Account does not exist
system.account.is.already.exists=Account already exists
system.account.passwd.is.error=Password Error
system.account.old.passwd.is.error=The original password is incorrect
system.account.is.disable=The account has been deactivated
system.account.has.no.permission=This account does not have any page permissions, please contact the administrator
system.menu.config.is.error=The superior menu cannot serve itself
system.menu.delete.error=Delete the submenu or button first
system.account.permission.deny=Insufficient User Permissions
system.account.token.invalid=Not logged in or logged in has expired
system.db.record.duplicate.error=The record already exists in the database
system.no.avaliable.marker=The set of incoming points is not available
system.no.avaliable.vehicle=The incoming robot collection is not available
system.version.is.dismatch=Current system version mismatch
license.certificate.failure=Certificate Unknown Exception
license.certificate.not.uploaded=Certificate Not Uploaded!
license.certificate.validate.failed=The uploaded certificate data is incorrect
license.certificate.expired=Certificate has expired
excel.export.error=Exporting excel file exception
excel.import.error=Parsing excel file exception
excel.import.code.empty=Import failed, data validation failed on line [{0}], reason: Code is empty
excel.import.name.empty=Import failed, data validation failed on line [{0}], reason: Name is empty
excel.import.type.empty=Import failed, data validation failed on line [{0}], reason: Type is empty
excel.import.row.empty=Import failed, data validation failed on line [{0}], reason: Row is empty
excel.import.colum.empty=Import failed, data validation failed on line [{0}], reason: Colum is empty
excel.import.layer.empty=Import failed, data validation failed on line [{0}], reason: Layers is empty
excel.import.workHeight.empty=Import failed, data validation failed on line [{0}], reason: Job height is empty
excel.import.code.exists=Import failed, data validation failed on line [{0}], reason: Code [{1}] already exists
excel.import.barcode.exists=Import failed, data validation failed on line [{0}], reason:Barcode [{1}] already exists
excel.import.usage.status.error=Import failed, data validation failed on line [{0}], reason: Enable status is error or it may not match the current system language
excel.import.occupy.status.error=Import failed, data validation failed on line [{0}], reason: Occupy status is error or it may not match the current system language
excel.import.notice.level.error=Import failed, data validation failed on line [{0}], reason: Notice level is error or it may not match the current system language
excel.import.event.type.error=Import failed, data validation failed on line [{0}], reason: Event type is error or it may not match the current system language
excel.import.code.repeat=Import failed, Duplicate code [{0}] exists in Excel table
excel.import.barcode.repeat=Import failed, There are duplicate container codes [{0}] in the Excel table
excel.import.data.convert.fail=Import failed, Data validation failed on line [{0}], reason: Incorrect data format in column [{1}]
excel.import.format.error=The table format is incorrect.Please export the template and import it again
excel.import.name.format.error=The imported file format is incorrect
language.empty=Language does not exist.
language.inuse=The language is in use.
language.upload.file.error=The uploaded file format is incorrect.
language.upload.missing.info=The language pack is missing the info file.
language.upload.missing.service=The language pack is missing the service file.
language.upload.missing.web=The language pack is missing the web file.
language.code.duplicate=Code [{0}] already exists.
language.code.nonstandard=Code [{0}] does not meet internationalization requirements.
vehicle.batchOperation.result={0} robots were successfully operated, and {1} robots failed.
vehicle.operation.fail=Operation robot [%s] failed, [%s]
vehicle.connect.fail=Channel not connected or disconnected.
vehicle.request.timeout=Request Timeout,%s
vehicle.is.not.login.error=Robot not logged in, please log in first!
vehicle.network.error=Robot operation failed, robot [%s] network not connected.
vehicle.code.duplicate=Code [{0}] already exists, please enter again.
vehicle.name.duplicate=Name [{0}] already exists, please enter again.
vehicle.name.pattern=The current value [{0}] needs to consist of a letter or number, an underscore.
vehicle.network.anomaly=Communication failed, please check whether the robot is connected.
vehicle.navigation.cancel=Robot path navigation was cancelled.
vehicle.locationMapCode.empty=The robot has no location map in use.
vehicle.out.of.trace.error=Vehicle out of trace！
vehicle.aim.marker.unreachable.error=The target point is unreachable!
vehicle.controlStatus.repair.error=The robot is in repair mode, and control mode switching is not allowed.
vehicle.empty=The robot does not exist.
vehicle.type.empty=The robot type [%s] does not exist.
vehicle.type.bind.duplicate=The robot type [%s] cannot be bounded duplicate.
vehicle.type.excel.head.code=Code
vehicle.type.excel.head.name=Name
vehicle.type.excel.head.rotatable=Allow Rotation.
vehicle.group.excel.head.code=Code
vehicle.group.excel.head.name=Name
vehicle.wait.reason.marker.occupied=Forward point [{0}] is occupied by another robot.
vehicle.wait.reason.marker.inControlArea=Forward point [{0}] is in control area.
vehicle.wait.reason.noParkArea.occupied=Forward No-Parking area is occupied by another robot.
vehicle.wait.reason.auto.door.closed=Forward auto door not opening
vehicle.wait.reason.airshower.door.closed=Forward air shower door not opening
vehicle.wait.reason.elevator.door.closed=Forword elevator not opening
vehicle.wait.reason.marker.inForbiddenArea=Forword point [{0}] is in forbidden area
warehouse.code.empty=Location code is empty
warehouse.material.type.absent=Material code [{0}] does not exist
warehouse.code.duplicate=Location code [{0}] already exists, please enter agagin
warehouse.barcode.duplicate=Barcode [{0}] already exists, please enter again
warehouse.start.need.less.than.end=Start row/column/layer must be smaller than End row/column/layer
warehouse.height.number.consistent=The number of floors should be equal to the number of job height data
warehouse.material.type.error=Error in obtaining material type data
warehouse.empty.or.disable=The storage location does not exist or has been disabled
warehouse.status.lock=This storage location has been locked
warehouse.status.store=This location is in storage status
warehouse.barcode.inuse=This container barcode is already in use by another storage location
warehouse.area.code.duplicate=Storage area code [{0}] already exists, please enter again
warehouse.type.code.duplicate=Type code [{0}] already exists, please enter agagin
warehouse.area.excel.head.code=Encoding
warehouse.area.excel.head.name=Name
warehouse.type.excel.head.code=Encoding
warehouse.type.excel.head.name=Name
warehouse.excel.head.code=Encoding
warehouse.excel.head.type.code=Type Encoding
warehouse.excel.head.area.code=Storage Aea Code
warehouse.excel.head.row=Number of Rows
warehouse.excel.head.colum=Number of Columns
warehouse.excel.head.layer=Number of Layers
warehouse.excel.head.work.height=Homework Height
warehouse.excel.head.work.marker=Job Marker
warehouse.excel.head.occupy.status=Occupancy Status
warehouse.excel.head.barcode=Container Barcode
warehouse.excel.head.usage.code=Enabled Status
warehouse.excel.head.param1=Extended Attribute 1
warehouse.excel.head.param2=Extended Attribute 2
warehouse.excel.head.param3=Extended Attribute 3
warehouse.excel.head.param4=Extended Attribute 4
warehouse.excel.head.param5=Extended Attribute 5
warehouse.excel.head.status.updatedate=Storage occupy status update date
warehouse.usage.status.enable=Enable
warehouse.usage.status.disable=Disable
warehouse.occupy.status.lock=Locked
warehouse.occupy.status.store=Storage
warehouse.occupy.status.free=Idle
statistics.cannot.gt.today=The selected date cannot exceed today
statistics.cannot.lt.one.year.ago=The selected date cannot be earlier than one year, as stated in statistics.canot.lt.one.ear.ago
statistics.start.cannot.gt.end=The start time cannot be greater than the end time
statistics.name.avgHandleTime=Average Processing Time
statistics.name.number=Quantity
statistics.name.other=Other
statistics.name.no.map=No Map Available
statistics.name.low=Low
statistics.name.lower=Lower
statistics.name.medium=Medium
statistics.name.higher=Higher
statistics.name.high=High
statistics.name.busy=Busy
statistics.name.free=Idle
statistics.name.abnormal=Exception
statistics.name.charge=Charging
statistics.name.park=Parking
statistics.name.work=Working
statistics.name.disconnect=Not Connected
statistics.name.wait=Waiting
statistics.name.running=Execution
statistics.name.total.task=Total Task
statistics.name.create=New
statistics.name.finished=Completed
statistics.name.cancel=Cancel
statistics.name.avgExecuteTime=Average Execution Time
statistics.name.avgAllocateTime=Average Execution Time
statistics.name.actual.rate=Actual Utilization Rate
statistics.name.theory.rate=Theoretical Utilization Rate
statistics.name.cpu.rate.total=Total Usage Rate
statistics.name.cpu.rate.java=JAVA
statistics.name.cpu.rate.mysql=MySQL
statistics.name.cpu.rate.other=Other
statistics.name.memo.rate.total=Total Memory Rate
statistics.name.memo.rate.java=JAVA
statistics.name.memo.rate.mysql=MySQL
statistics.name.memo.rate.other=Other
statistics.name.disk.total=Total Disk Capacity
statistics.name.disk.used=Disk Used Capacity
statistics.name.disk.free=Disk Free Capacity
statistics.name.mysql.select=Select
statistics.name.mysql.delete=Delete
statistics.name.mysql.update=Update
statistics.name.mysql.insert=Insert
statistics.name.mysql.times.select=Select
statistics.name.mysql.times.delete=Delete
statistics.name.mysql.times.update=Update
statistics.name.mysql.times.insert=Insert
statistics.unit.number=Number
statistics.unit.tai=Unit
statistics.unit.time=Times
statistics.unit.second=Seconds
statistics.unit.minute=Minutes
statistics.unit.hour=Hour
statistics.unit.day=Days
charge.station.not.exist.error=Charging station does not exist
charge.station.connect.fail=Charging station connection failed
charge.station.request.timeout=Charging station request timeout, please check network connection
charge.station.reset.error=Abnormal reset of charging station
charge.station.break_discharge.error=Abnormal discharge termination of charging station
charge.station.bind.marker.delete.error=该充电桩与充电点有绑定，请先解除绑定关系(待翻译)
charge.station.duplicate.bind.marker.error=充电桩[%s]已经绑定了点位[%s]，请重新选择(待翻译)
config.unit.day=Days
config.unit.meter=Meters
config.unit.second=Seconds
config.value.range=Configured Data Range
config.company.name=Shenzhen YOUIBOT Robot Technology Co., Ltd
config.title.systemVersion=Version Number
config.remark.systemVersion=Version Number
config.title.ownCompany=All Rights Reserved
config.remark.ownCompany=All Rights Reserved
config.title.licenseCompanyName=Authorization Information - Company Name
config.remark.licenseCompanyName=Authorization Information - Company Name
config.title.licenseValidTimeRange=Authorization Information - Validity Period
config.remark.licenseValidTimeRange=Authorization Information - Validity Period
config.title.userOptLogExpireTime=Operation Log
config.remark.userOptLogExpireTime=User action log retention time
config.title.interfaceLogExpireTime=Interface Log
config.remark.interfaceLogExpireTime=System interface log retention time
config.title.runningLogExpireTime=Run Log
config.remark.runningLogExpireTime=System run log retention time
config.title.notificationExpireTime=Notification
config.remark.notificationExpireTime=System alarm notification retention time
config.title.businessDataExpireTime=Business Data
config.remark.businessDataExpireTime=The retention time of operational data for the business, including task lists and other business data
config.title.reportDataExpireTime=Report Data
config.remark.reportDataExpireTime=Record the retention time of archived report data
config.title.markerSpacingCheck=Point Spacing
config.remark.markerSpacingCheck=Point Spacing Judgment Enabled
config.title.markerSpacing=Point Spacing
config.remark.markerSpacing=Point Spacing (mm)
config.title.markerAndPathSpacingCheck=Point to Path Spacing
config.remark.markerAndPathSpacingCheck=Point to Path Spacing Judgment Enabled
config.title.markerAndPathSpacing=Point to Path Spacing
config.remark.markerAndPathSpacing=Point to Path Spacing (mm)
config.title.blockCheckEnable=Obstacle Avoidance and Replanning
config.remark.blockCheckEnable=When the robot encounters an obstacle, the scheduling system re plans the path to make the robot detour
config.title.blockCheckInterval=Obstacle Avoidance and Replanning
config.remark.blockCheckInterval=Obstacle Avoidance Trigger Duration
config.title.removeBlockInterval=Obstacle Avoidance and Replanning
config.remark.removeBlockInterval=Duration of Obstacle Reset
config.title.abnormalVehicleRunPolicy=Abnormal Robot
config.remark.abnormalVehicleRunPolicy=Set the robot's execution strategy when encountering a fault or offline robot ahead
config.title.freeVehicleRunPolicy=Idle Robot
config.remark.freeVehicleRunPolicy=When encountering an idle robot ahead, set the execution strategy for the robot
config.title.workVehicleRunPolicy=Busy Robot
config.remark.workVehicleRunPolicy=When encountering a busy robot ahead, set the robot's execution strategy
config.title.avoidMarkerTypes=Conflict Avoidance Point Types
config.remark.avoidMarkerTypes=The types of points that robots are allowed to avoid when multiple robot paths conflict
config.title.pathApplyLength=Issuing Path Distance
config.remark.pathApplyLength=The path length issued by the scheduling system to the robot, which can be increased when the network environment is poor
config.title.autoReleaseResource=Disconnected Release Resource
config.remark.autoReleaseResource=After the robot disconnects the network for a certain time, the scheduling system releases the position and area occupied by the robot
config.title.disconnectionTime=Disconnect to Release Resources
config.remark.disconnectionTime=Duration of Disconnection
config.title.occupyResourceRange=Virtual Radius of Robot
config.remark.occupyResourceRange=When the robot is not at a point or path, use the center of the robot as the center and this value as the radius to occupy all points contained in the circle
config.title.trackRadius=Track Radius
config.remark.trackRadius=When the distance from the robot to the nearest point or path exceeds this value, the system determines that the robot has derailed
config.title.channelAvoidance=Channel Avoidance
config.remark.channelAvoidance=After enabling channel avoidance, the opposing robot can actively avoid outside the channel
config.title.autoDoorAdvanceLength=Call the automatic door in advance
config.remark.autoDoorAdvanceLength=When the distance between the robot and the point in front of the automatic door is less than this value, it will call to open the door.When this value is 0, it will not call in advance
config.title.showerDoorAdvanceLength=Call the air shower door in advance
config.remark.showerDoorAdvanceLength=When the distance between the robot and the point in front of the air shower door is less than this value, it will call to open the door.When this value is 0, it will not call in advance
config.title.elevatorAdvanceLength=Call the elevator in advance
config.remark.elevatorAdvanceLength=When the distance from the robot to the point in front of the elevator is less than this value, it will call to open the door.When this value is 0, it will not call in advance
config.title.highPerformanceMode=High performance mode
config.remark.highPerformanceMode=When scheduling large-scale robots, the high performance mode can be enabled to improve scheduling efficiency.
config.title.highBattery=High Battery (%)
config.remark.highBattery=High Battery (%)
config.title.lowBattery=Low Battery (%)
config.remark.lowBattery=Low Battery (%)
config.title.chargeTaskTypeId=Create Task
config.remark.chargeTaskTypeId=Create Task
config.title.autoCharge=Status
config.remark.autoCharge=Status
config.title.parkTaskTypeId=Create Task
config.remark.parkTaskTypeId=Create Task
config.title.autoPark=Status
config.remark.autoPark=Status
config.title.pushCycle=Push Cycle
config.remark.pushCycle=Push only once within this time range
config.title.vehicleStatusPushUrl=Robot Status Interface Address
config.remark.vehicleStatusPushUrl=When the robot status changes, push the robot message to the interface address.
config.title.noticePushUrl=Message Push Address
config.remark.noticePushUrl=When a message of the wrong type appears or disappears, push abnormal data to the interface address.
config.title.pdaVersion=Latest Version of PDA
config.remark.pdaVersion=Latest Version of PDA
config.title.vehicleStatusPushInterval=Monitoring console robot status push interval (ms)
config.remark.vehicleStatusPushInterval=Monitoring console robot status push interval (ms)
config.title.noticePushInterval=Monitoring console small bell push interval (ms)
config.remark.noticePushInterval=Monitoring console small bell push interval (ms)
config.title.mapElementPushInterval=Monitoring Console Map Element Status Change Push Interval (ms)
config.remark.mapElementPushInterval=Monitoring Console Map Element Status Change Push Interval (ms)
config.title.thirdSystemTrafficAreaReqUrl=Address for Traffic Management Regional Resource Requests
config.remark.thirdSystemTrafficAreaReqUrl=Fleet, as a client, applies to the server for traffic control area resources and sets the application interface address.
config.title.driveFreeVehicleFreeTime=Free Time
config.remark.driveFreeVehicleFreeTime=When repelling an idle robot, the idle robot needs to reach the specified idle time.
config.property.is.exist.error=The system attribute already exists!
config.property.type.is.duplicate.error=Duplicate system type classification!
config.title.globalPauseExecutingArmScriptIsStop=Global Pause
config.remark.globalPauseExecutingArmScriptIsStop=Robot performing robotic arm movements during a full-court timeout.
config.title.noticePushLanguageType=消息语言配置（待翻译）
config.remark.noticePushLanguageType=定义错误类型的消息推送时的语言（待翻译）
config.title.exceptionNotifyTime=通知时间(待翻译)
config.remark.exceptionNotifyTime=机器人触发急停，分配/申请不到点位、区城资源达到设定时间时发起更高等级通知(待翻译)
notice.missing.notice.config=The system is missing the configuration information for this exception code.
notice.level.common=Normal
notice.level.warning=Warning
notice.level.error=Error
notice.record.status.not.close=Not Closed
notice.record.status.closed=Closed
notice.config.excel.head.code=Encoding
notice.config.excel.head.level=Level
notice.config.excel.head.source=Source
notice.config.excel.head.invalidTime=Invalid Time
notice.config.excel.head.desc=Description
notice.config.excel.head.solution=Solution
notice.record.excel.head.code=Encoding
notice.record.excel.head.level=Level
notice.record.excel.head.source=Source
notice.record.excel.head.desc=Description
notice.record.excel.head.solution=Solution
notice.record.excel.head.status=Status
notice.record.excel.head.vehicle=Robot
notice.record.excel.head.task=Task
notice.record.excel.head.device=Device ID
notice.record.excel.head.map=Map
notice.record.excel.head.create.date=Creation Time
notice.record.excel.head.update.date=Update Time
notice.record.excel.head.close.date=Close Time
notice.record.http.request.param=Abnormal notification status change HTTP push, request parameters
notice.record.http.response.param=Abnormal notification status change HTTP push, response parameters
notice.description.100001=Base Heartbeat Timeout.
notice.solution.100001=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.100002=Core board startup timeout.
notice.solution.100002=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.100020=Communication Failure of Status Light.
notice.solution.100020=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100021=Signal Lamp Communication Failure.
notice.solution.100021=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100030=Single Point Array Lidar 1 Communication Timeout.
notice.solution.100030=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100031=Single Point Array Lidar 1 Failure.
notice.solution.100031=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100032=Single Point Array Lidar 2 Communication Timeout.
notice.solution.100032=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100033=Single Point Array Lidar 2 Failure.
notice.solution.100033=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100034=Single Point Array Lidar 3 Communication Timeout.
notice.solution.100034=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100035=Single Point Array Lidar 3 Failure.
notice.solution.100035=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100036=Single Point Array Lidar 4 Communication Timeout.
notice.solution.100036=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100037=Single Point Array Lidar 4 Failure.
notice.solution.100037=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100050=Safety Lidar 1 Communication Timeout.
notice.solution.100050=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100051=Safety Lidar 1 Failure.
notice.solution.100051=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100052=Safety Lidar 2 Communication Timeout.
notice.solution.100052=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100053=Safety Lidar 2 Failure.
notice.solution.100053=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100054=Safety Lidar 3 Communication Timeout.
notice.solution.100054=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100055=Safety Lidar 3 Failure.
notice.solution.100055=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100056=Safety Lidar 4 Communication Timeout.
notice.solution.100056=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100057=Safety Lidar 4 Failure.
notice.solution.100057=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100100=RE Expansion Board 0 Communication Timeout
notice.solution.100100=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100101=RE Expansion Board 1 Communication Timeout
notice.solution.100101=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100102=RE Expansion Board 2 Communication Timeout
notice.solution.100102=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100103=RE Expansion Board 3 Communication Timeout
notice.solution.100103=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100104=RE Expansion Board 4 Communication Timeout
notice.solution.100104=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100105=RE Expansion Board 5 Communication Timeout
notice.solution.100105=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100106=RE Expansion Board 6 Communication Timeout
notice.solution.100106=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100107=RE Expansion Board 7 Communication Timeout
notice.solution.100107=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100108=RE Expansion Board 8 Communication Timeout
notice.solution.100108=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100109=RE Expansion Board 9 Communication Timeout
notice.solution.100109=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100110=RE Expansion Board 10 Communication Timeout
notice.solution.100110=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100111=RE Expansion Board 11 Communication Timeout
notice.solution.100111=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100112=RE Expansion Board 12 Communication Timeout
notice.solution.100112=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100113=RE Expansion Board 13 Communication Timeout
notice.solution.100113=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100114=RE Expansion Board 14 Communication Timeout
notice.solution.100114=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100115=RE Expansion Board 15 Communication Timeout
notice.solution.100115=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.105000=CAN Communication Failure of The Left Driver.
notice.solution.105000=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105001=Start of CAN Node of Left Driver Timeout.
notice.solution.105001=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105002=Power-on of Left Drive Timeout.
notice.solution.105002=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105003=PDO configuration file of left drive is missing.
notice.solution.105003=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105009=Error in undefined left drive.
notice.solution.105009=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105010=Left-hand drive encoder failure.
notice.solution.105010=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105011=The voltage of the left driver is too high.
notice.solution.105011=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105012=The voltage of the left driver is too low.
notice.solution.105012=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105013=Left-hand drive overcurrent
notice.solution.105013=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105014=Left drive temperature is too high.
notice.solution.105014=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105015=The operation error of the left driver is too large.
notice.solution.105015=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105016=Left driver logic voltage is abnormal.
notice.solution.105016=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105017=Left-hand drive motor failure.
notice.solution.105017=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105018=Left drive failure.
notice.solution.105018=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105019=System data error on the left drive.
notice.solution.105019=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105020=Error in software operation of left driver.
notice.solution.105020=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105021=The motor configuration of the left drive is incorrect.
notice.solution.105021=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105022=Error in positive limit of left drive.
notice.solution.105022=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105023=Negative limit error of the left driver.
notice.solution.105023=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105024=overspeed alarm of the left drive.
notice.solution.105024=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105025=The left drive is overloaded.
notice.solution.105025=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105026=CAN BUS fault of left driver.
notice.solution.105026=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105027=Wrong OpenCan parameter of the left driver.
notice.solution.105027=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105028=The communication of OpenCan on the left driver is abnormal.
notice.solution.105028=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105029=Abnormal locking of the left drive.
notice.solution.105029=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105030=Left drive stopped abnormally.
notice.solution.105030=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105031=Abnormal phase voltage of left driver.
notice.solution.105031=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105100=CAN communication failure of right driver.
notice.solution.105100=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105101=Start of Can node of right driver timed out.
notice.solution.105101=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105102=Power-on startup of right drive timed out.
notice.solution.105102=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105103=PDO configuration file of right drive is missing.
notice.solution.105103=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105109=Error in undefined right drive.
notice.solution.105109=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105110=Encoder failure of right drive.
notice.solution.105110=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105111=The voltage of the right driver is too high.
notice.solution.105111=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105112=The voltage of the right driver is too low.
notice.solution.105112=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105113=Right-hand drive overcurrent.
notice.solution.105113=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105114=Right drive temperature is too high.
notice.solution.105114=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105115=The operation error of the right drive is too large.
notice.solution.105115=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105116=Abnormal logic voltage of right driver.
notice.solution.105116=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105117=Motor fault of right drive.
notice.solution.105117=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105118=Right drive failure.
notice.solution.105118=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105119=System data error in the right drive.
notice.solution.105119=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105120=Error in software operation of right driver.
notice.solution.105120=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105121=The motor configuration of the right drive is incorrect.
notice.solution.105121=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105122=Error in positive limit of right drive.
notice.solution.105122=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105123=Negative limit error of the right driver.
notice.solution.105123=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105124=Overspeed alarm of the right drive.
notice.solution.105124=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105125=The right drive is overloaded.
notice.solution.105125=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105126=CAN BUS fault of right driver.
notice.solution.105126=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105127=Wrong OpenCan parameter of the right driver.
notice.solution.105127=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105128=Abnormal OpenCan communication of the right driver.
notice.solution.105128=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105129=Abnormal locking of the right drive.
notice.solution.105129=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105130=Right drive stopped abnormally.
notice.solution.105130=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105131=Abnormal phase voltage of right driver.
notice.solution.105131=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105200=CAN communication failure of lifting driver.
notice.solution.105200=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105201=The startup of Can node of lifting driver timed out.
notice.solution.105201=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105202=The lifting drive found the origin timeout.
notice.solution.105202=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105203=Power-on start-up of lifting drive timed out.
notice.solution.105203=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105204=PDO configuration parameter of lifting driver is missing.
notice.solution.105204=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105209=Error in undefined lifting drive.
notice.solution.105209=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105210=encoder failure of lifting drive.
notice.solution.105210=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105211=The voltage of the lifting drive is too high.
notice.solution.105211=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105212=The voltage of the lifting drive is too low.
notice.solution.105212=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105213=Overcurrent of lifting driver.
notice.solution.105213=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105214=Elevating drive temperature is too high.
notice.solution.105214=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105215=The operation error of the lifting driver is too large.
notice.solution.105215=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105216=Abnormal logic voltage of lifting driver.
notice.solution.105216=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105217=Motor failure of lifting drive.
notice.solution.105217=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105218=Elevator drive failure.
notice.solution.105218=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105219=System data error of lifting drive.
notice.solution.105219=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105220=Error occurred when the software of lifting driver runs.
notice.solution.105220=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105221=Motor configuration of lifting drive is wrong.
notice.solution.105221=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105222=Error in positive limit of lifting drive.
notice.solution.105222=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105223=Negative limit error of lifting driver.
notice.solution.105223=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105224=overspeed alarm of lifting drive.
notice.solution.105224=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105225=Elevator drive is overloaded.
notice.solution.105225=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105226=CAN BUS fault of lifting driver.
notice.solution.105226=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105227=Error in OpenCan parameter of lifting driver.
notice.solution.105227=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105228=OpenCan communication of lifting driver is abnormal.
notice.solution.105228=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105229=The lifting drive stopped abnormally.
notice.solution.105229=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105230=Abnormal phase voltage of lifting driver.
notice.solution.105230=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105300=CAN communication failure of rotary drive.
notice.solution.105300=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105301=The startup of the rotary drive Can node timed out.
notice.solution.105301=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105302=Rotating the drive to find the origin timed out.
notice.solution.105302=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105303=Power-on startup of rotary drive timed out.
notice.solution.105303=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105304=PDO configuration parameter of rotary drive is missing.
notice.solution.105304=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105309=Error in undefined rotary drive.
notice.solution.105309=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105310=Rotary drive encoder failure.
notice.solution.105310=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105311=The voltage of the rotary drive is too high.
notice.solution.105311=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105312=The voltage of the rotary drive is too low.
notice.solution.105312=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105313=Rotating drive overcurrent.
notice.solution.105313=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105314=Rotating drive temperature is too high.
notice.solution.105314=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105315=The running error of the rotary drive is too large.
notice.solution.105315=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105316=Abnormal logic voltage of rotary drive.
notice.solution.105316=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105317=Rotating drive motor failure.
notice.solution.105317=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105318=Rotating drive failure.
notice.solution.105318=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105319=System data error of rotating drive.
notice.solution.105319=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105320=Error in running the rotary drive software.
notice.solution.105320=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105321=Wrong motor configuration of rotary drive.
notice.solution.105321=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105322=Error in positive limit of rotary drive.
notice.solution.105322=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105323=Negative limit error of rotary drive.
notice.solution.105323=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105324=Overspeed alarm of rotating drive.
notice.solution.105324=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105325=Rotating drive is overloaded.
notice.solution.105325=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105326=CAN BUS fault of rotary drive.
notice.solution.105326=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105327=Wrong OpenCan parameter of rotary drive.
notice.solution.105327=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105328=OpenCan communication of rotary drive is abnormal.
notice.solution.105328=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105329=Rotating drive stopped abnormally.
notice.solution.105329=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105330=Abnormal phase voltage of rotary driver.
notice.solution.105330=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105500=BMS communication is abnormal.
notice.solution.105500=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105501=BMS undefined error.
notice.solution.105501=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105502=Abnormal charging overcurrent.
notice.solution.105502=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105503=Abnormal discharge and overcurrent.
notice.solution.105503=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105504=Battery undervoltage
notice.solution.105504=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105505=Battery overvoltage
notice.solution.105505=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105506=Overall undervoltage.
notice.solution.105506=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105507=Overall overpressure
notice.solution.105507=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105508=The differential pressure is higher than the upper limit of allowable differential pressure for charging.
notice.solution.105508=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105509=Cell pressure difference exceeds the upper limit.
notice.solution.105509=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105510=Battery temperature exceeds the upper limit of charging temperature.
notice.solution.105510=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105511=Battery temperature exceeds the upper limit of discharge temperature.
notice.solution.105511=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105512=The battery cell temperature difference exceeds the upper limit of charging temperature.
notice.solution.105512=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105513=The battery cell temperature difference exceeds the upper limit of discharge temperature.
notice.solution.105513=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105514=Battery temperature is lower than the lower limit of charge and discharge.
notice.solution.105514=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105515=MOS tube temperature exceeds the upper limit of charging temperature.
notice.solution.105515=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105516=The temperature difference of MOS tube exceeds the upper limit of temperature difference.
notice.solution.105516=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105551=bms exception (including protection and sampling failure ...)
notice.solution.105551=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105552=BMS package is abnormal (including overvoltage, overcurrent, high and low temperature faults, etc.)
notice.solution.105552=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105610=PDO1 overcurrent
notice.solution.105610=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105611=PDO2 overcurrent
notice.solution.105611=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105612=PDO3 overcurrent
notice.solution.105612=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105613=PDO4 overcurrent
notice.solution.105613=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105614=PDO5 overcurrent
notice.solution.105614=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105615=PDO6 overcurrent
notice.solution.105615=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105616=PDO7 overcurrent
notice.solution.105616=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105617=Total PDO current is too large.
notice.solution.105617=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105700=Four wheels and four turns, and the left steering motor is undefined.
notice.solution.105700=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105701=Four wheels and four turns, software error of left steering motor.
notice.solution.105701=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105702=Four wheels and four turns, and the left steering motor is overvoltage.
notice.solution.105702=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105703=Four wheels and four turns, and the left steering motor has low voltage.
notice.solution.105703=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105704=Four wheels and four turns, the left steering motor started incorrectly.
notice.solution.105704=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105705=Four wheels and four turns, and the left steering motor has overcurrent.
notice.solution.105705=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105706=Four wheels and four turns, left steering motor encoder error.
notice.solution.105706=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105707=Four wheels and four turns, the temperature of the left steering motor is too high.
notice.solution.105707=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105708=Four wheels and four turns, the left steering motor circuit board is too high.
notice.solution.105708=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105709=Four wheels and four turns, communication of the left steering motor timed out.
notice.solution.105709=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105800=Four wheels and four turns, and the right steering motor is undefined.
notice.solution.105800=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105801=Four wheels and four turns, software error of right steering motor.
notice.solution.105801=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105802=Four wheels and four turns, and the right steering motor is overvoltage.
notice.solution.105802=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105803=Four wheels and four turns, and the right steering motor has low voltage.
notice.solution.105803=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105804=Four wheels and four turns, the right steering motor started incorrectly.
notice.solution.105804=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105805=Four wheels and four turns, and the right steering motor has overcurrent.
notice.solution.105805=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105806=Four wheels and four turns, encoder error of right steering motor.
notice.solution.105806=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105807=Four wheels and four turns, and the temperature of the right steering motor is too high.
notice.solution.105807=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105808=Four wheels and four turns, and the circuit board of the right steering motor is too high.
notice.solution.105808=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105809=Four wheels and four turns, communication of the right steering motor timed out.
notice.solution.105809=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105900=CAN communication failure of steering wheel and front wheel travel driver.
notice.solution.105900=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105901=The Can node of steering wheel and front wheel traveling driver started overtime.
notice.solution.105901=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105902=Power-on start-up of steering wheel and front wheel driving device timed out.
notice.solution.105902=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105903=PDO configuration parameter of steering wheel and front wheel walking driver is missing.
notice.solution.105903=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105909=Steering wheel, front wheel travel driver is not defined correctly.
notice.solution.105909=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105910=Steering wheel, front wheel travel driver encoder failure.
notice.solution.105910=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105911=The driver voltage of steering wheel and front wheels is too high.
notice.solution.105911=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105912=The driver voltage of steering wheel and front wheels is too low.
notice.solution.105912=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105913=The steering wheel, the front-wheel running driver has an overcurrent.
notice.solution.105913=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105914=Steering wheel, front wheel running motor temperature is too high.
notice.solution.105914=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105915=The running error of steering wheel and front wheel walking motor is too large.
notice.solution.105915=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105916=Abnormal logic voltage of steering wheel and front wheel traveling motor.
notice.solution.105916=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105917=Malfunction of steering wheel and front wheel traveling motor.
notice.solution.105917=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105918=Malfunction of steering wheel and front wheel traveling drive.
notice.solution.105918=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105919=Data error in steering wheel and front wheel drive system.
notice.solution.105919=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105920=Error in running the software of steering wheel and front wheel walking driver.
notice.solution.105920=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105921=Wrong configuration of steering wheel and front wheel motor.
notice.solution.105921=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105922=Overspeed alarm of steering wheel and front wheel traveling motor.
notice.solution.105922=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105923=Steering wheel, front wheel running motor is overloaded.
notice.solution.105923=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105924=CAN BUS fault of steering wheel and front wheel traveling driver.
notice.solution.105924=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105925=The OpenCan parameter of steering wheel and front wheel traveling driver is wrong.
notice.solution.105925=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105926=OpenCan communication of steering wheel and front wheel driving device is abnormal.
notice.solution.105926=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105927=The steering wheel and the front wheel are locked abnormally.
notice.solution.105927=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105928=Steering wheel, front wheel travel driver stopped abnormally.
notice.solution.105928=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105929=Abnormal phase voltage of steering wheel and front wheel traveling motor.
notice.solution.105929=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106000=CAN communication failure of steering wheel and front wheel steering driver.
notice.solution.106000=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106001=The Can node of steering wheel and front wheel driver started overtime.
notice.solution.106001=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106002=Power-on start-up of steering wheel and front wheel steering driver timed out.
notice.solution.106002=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106003=PDO configuration parameter of steering wheel and front wheel driver is missing.
notice.solution.106003=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106004=Steering wheel, front wheel steering driver found the origin timeout.
notice.solution.106004=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106009=Steering wheel, front wheel steering drive undefined error.
notice.solution.106009=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106010=Steering wheel, front wheel steering driver encoder failure.
notice.solution.106010=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106011=Steering wheel, front wheel steering driver voltage is too high.
notice.solution.106011=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106012=Steering wheel, front wheel steering driver voltage is too low.
notice.solution.106012=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106013=Steering wheel, front wheel steering driver overcurrent.
notice.solution.106013=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106014=Steering wheel, front wheel steering motor temperature is too high.
notice.solution.106014=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106015=The running error of steering motor of steering wheel and front wheel is too large.
notice.solution.106015=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106016=Abnormal logic voltage of steering motor of steering wheel and front wheel.
notice.solution.106016=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106017=Steering wheel, front wheel steering motor failure.
notice.solution.106017=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106018=Steering wheel, front wheel steering drive failure.
notice.solution.106018=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106019=Data error in steering drive system of steering wheel and front wheel.
notice.solution.106019=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106020=Steering wheel, front wheel steering driver software running error.
notice.solution.106020=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106021=The steering motor of steering wheel and front wheel is configured incorrectly.
notice.solution.106021=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106022=Positive limit error of steering driver of steering wheel and front wheel.
notice.solution.106022=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106023=Negative limit error of steering driver of steering wheel and front wheel.
notice.solution.106023=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106024=Overspeed alarm for steering motor of steering wheel and front wheel.
notice.solution.106024=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106025=Steering wheel, front wheel steering motor is overloaded.
notice.solution.106025=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106026=CAN BUS fault of steering wheel and front wheel driver.
notice.solution.106026=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106027=The OpenCan parameter of steering wheel and front wheel driver is wrong.
notice.solution.106027=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106028=OpenCan communication of steering wheel and front wheel steering driver is abnormal.
notice.solution.106028=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106029=Steering wheel and front wheel steering driver stopped abnormally.
notice.solution.106029=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106030=Abnormal phase voltage of steering motor of steering wheel and front wheel.
notice.solution.106030=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106100=CAN communication failure of steering wheel and rear wheel travel driver.
notice.solution.106100=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106101=The Can node of steering wheel and rear wheel traveling driver started overtime.
notice.solution.106101=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106102=Power-on start-up of steering wheel and rear wheel travel driver timed out.
notice.solution.106102=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106103=PDO configuration parameter of steering wheel and rear wheel walking driver is missing.
notice.solution.106103=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106109=The steering wheel and rear wheel travel driver are not defined correctly.
notice.solution.106109=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106110=Steering wheel, rear wheel travel driver encoder failure.
notice.solution.106110=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106111=The driver voltage of steering wheel and rear wheel is too high.
notice.solution.106111=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106112=The driver voltage of steering wheel and rear wheel is too low.
notice.solution.106112=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106113=Overcurrent of steering wheel and rear wheel traveling driver.
notice.solution.106113=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106114=Steering wheel, rear wheel traveling motor temperature is too high.
notice.solution.106114=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106115=The running error of steering wheel and rear wheel walking motor is too large.
notice.solution.106115=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106116=Abnormal logic voltage of steering wheel and rear wheel traveling motor.
notice.solution.106116=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106117=Malfunction of steering wheel and rear wheel traveling motor.
notice.solution.106117=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106118=Malfunction of steering wheel and rear wheel traveling drive.
notice.solution.106118=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106119=Data error in steering wheel and rear wheel driving system.
notice.solution.106119=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106120=Error in running the software of steering wheel and rear wheel walking driver.
notice.solution.106120=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106121=The motor of steering wheel and rear wheel is misconfigured.
notice.solution.106121=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106122=Overspeed alarm of steering wheel and rear wheel traveling motor.
notice.solution.106122=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106123=Steering wheel, rear wheel traveling motor is overloaded.
notice.solution.106123=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106124=CAN BUS fault of steering wheel and rear wheel traveling driver.
notice.solution.106124=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales department.
notice.description.106125=The OpenCan parameter of steering wheel and rear wheel traveling driver is wrong.
notice.solution.106125=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106126=Abnormal OpenCan communication of steering wheel and rear wheel traveling drive.
notice.solution.106126=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106127=The steering wheel and the rear wheel are locked abnormally.
notice.solution.106127=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106128=Steering wheel, the rear wheel stops running abnormally.
notice.solution.106128=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106129=Abnormal phase voltage of steering wheel and rear wheel traveling motor.
notice.solution.106129=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106200=CAN communication failure of steering wheel and rear wheel steering driver.
notice.solution.106200=Restart recovery is required. Please contact the after-sales department if it cannot be recovered or the abnormality appears many times. Restart recovery is required. If it cannot be recovered or the abnormality appears many times, please contact the after-sales department.
notice.description.106201=The Can node of steering wheel and rear wheel driver started overtime.
notice.solution.106201=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106202=Power-on start-up of steering wheel and rear wheel driver timed out.
notice.solution.106202=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106203=PDO configuration parameters of steering wheel and rear wheel driver are missing.
notice.solution.106203=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106204=Steering wheel, rear wheel steering driver found the origin timeout.
notice.solution.106204=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales department.
notice.description.106209=The steering wheel and rear wheel steering drive are undefined.
notice.solution.106209=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106210=Steering wheel, rear wheel steering driver encoder failure.
notice.solution.106210=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106211=Steering wheel, rear wheel steering driver voltage is too high.
notice.solution.106211=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106212=Steering wheel, rear wheel steering driver voltage is too low.
notice.solution.106212=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106213=Steering wheel, rear wheel steering driver overcurrent.
notice.solution.106213=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106214=The steering motor of steering wheel and rear wheel is overheated.
notice.solution.106214=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106215=The running error of steering motor of steering wheel and rear wheel is too large.
notice.solution.106215=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106216=Abnormal logic voltage of steering motor of steering wheel and rear wheel.
notice.solution.106216=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106217=Steering motor failure of steering wheel and rear wheel.
notice.solution.106217=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106218=Steering wheel, rear wheel steering drive failure.
notice.solution.106218=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106219=Data error in steering drive system of steering wheel and rear wheel.
notice.solution.106219=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106220=Error in software operation of steering driver for steering wheel and rear wheel.
notice.solution.106220=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106221=The steering motor of steering wheel and rear wheel is configured incorrectly.
notice.solution.106221=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106222=Positive limit error of steering driver of steering wheel and rear wheel.
notice.solution.106222=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106223=Negative limit error of steering driver of steering wheel and rear wheel.
notice.solution.106223=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106224=Overspeed alarm for steering motor of steering wheel and rear wheel.
notice.solution.106224=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales department.
notice.description.106225=Steering wheel and rear wheel steering motor are overloaded.
notice.solution.106225=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106226=CAN BUS fault of steering wheel and rear wheel driver.
notice.solution.106226=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106227=The OpenCan parameter of steering wheel and rear wheel driver is wrong.
notice.solution.106227=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106228=OpenCan communication of steering wheel and rear wheel driver is abnormal.
notice.solution.106228=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106229=Steering wheel and rear wheel steering drive stopped abnormally.
notice.solution.106229=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106230=Abnormal phase voltage of steering motor of steering wheel and rear wheel.
notice.solution.106230=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.110001=Left Drive-Exception.
notice.solution.110001=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110002=Left Drive-Exception.
notice.solution.110002=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110003=Left Drive-Exception.
notice.solution.110003=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110004=Left Drive-Exception.
notice.solution.110004=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110005=Left Drive-Exception.
notice.solution.110005=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110006=Left Drive-Exception.
notice.solution.110006=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110007=Left Drive-Exception.
notice.solution.110007=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110008=Left Drive-Exception.
notice.solution.110008=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110009=Left Drive-Exception.
notice.solution.110009=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110010=Left Drive-Exception.
notice.solution.110010=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110011=Left Drive-Exception.
notice.solution.110011=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110012=Left Drive-Exception.
notice.solution.110012=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110013=Left Drive-Exception.
notice.solution.110013=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110014=Left Drive-Exception.
notice.solution.110014=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110015=Left Drive-Exception.
notice.solution.110015=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110016=Left Drive-Exception.
notice.solution.110016=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110017=Left Drive-Exception.
notice.solution.110017=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110018=Left Drive-Exception.
notice.solution.110018=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110019=Left Drive-Exception.
notice.solution.110019=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110020=Left Drive-Exception.
notice.solution.110020=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110021=Left Drive-Exception.
notice.solution.110021=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110022=Left Drive-Exception.
notice.solution.110022=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110023=Left Drive-Exception.
notice.solution.110023=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110024=Left Drive-Exception.
notice.solution.110024=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110025=Left Drive-Exception.
notice.solution.110025=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110026=Left Drive-Exception.
notice.solution.110026=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110027=Left Drive-Exception.
notice.solution.110027=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110028=Left Drive-Exception.
notice.solution.110028=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110029=Left Drive-Exception.
notice.solution.110029=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110030=Left Drive-Exception.
notice.solution.110030=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110031=Left Drive-Exception.
notice.solution.110031=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110032=Right Drive-Exception.
notice.solution.110032=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110033=Right Drive-Exception.
notice.solution.110033=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110034=Right Drive-Exception.
notice.solution.110034=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110035=Right Drive-Exception.
notice.solution.110035=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110036=Right Drive-Exception.
notice.solution.110036=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110037=Right Drive-Exception.
notice.solution.110037=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110038=Right Drive-Exception.
notice.solution.110038=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110039=Right Drive-Exception.
notice.solution.110039=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110040=Right Drive-Exception.
notice.solution.110040=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110041=Right Drive-Exception.
notice.solution.110041=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110042=Right Drive-Exception.
notice.solution.110042=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110043=Right Drive-Exception.
notice.solution.110043=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110044=Right Drive-Exception.
notice.solution.110044=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110045=Right Drive-Exception.
notice.solution.110045=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110046=Right Drive-Exception.
notice.solution.110046=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110047=Right Drive-Exception.
notice.solution.110047=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110048=Right Drive-Exception.
notice.solution.110048=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110049=Right Drive-Exception.
notice.solution.110049=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110050=Right Drive-Exception.
notice.solution.110050=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110051=Right Drive-Exception.
notice.solution.110051=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110052=Right Drive-Exception.
notice.solution.110052=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110053=Right Drive-Exception.
notice.solution.110053=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110054=Right Drive-Exception.
notice.solution.110054=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110055=Right Drive-Exception.
notice.solution.110055=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110056=Right Drive-Exception.
notice.solution.110056=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110057=Right Drive-Exception.
notice.solution.110057=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110058=Right Drive-Exception.
notice.solution.110058=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110059=Right Drive-Exception.
notice.solution.110059=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110060=Right Drive-Exception.
notice.solution.110060=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110061=Right Drive-Exception.
notice.solution.110061=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110062=Right Drive-Exception.
notice.solution.110062=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110063=Jack-Up Driver-Exception.
notice.solution.110063=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110064=Jack-Up Driver-Exception.
notice.solution.110064=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110065=Jack-Up Driver-Exception.
notice.solution.110065=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110066=Jack-Up Driver-Exception.
notice.solution.110066=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110067=Jack-Up Driver-Exception.
notice.solution.110067=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110068=Jack-Up Driver-Exception.
notice.solution.110068=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110069=Jack-Up Driver-Exception.
notice.solution.110069=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110070=Jack-Up Driver-Exception.
notice.solution.110070=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110071=Jack-Up Driver-Exception.
notice.solution.110071=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110072=Jack-Up Driver-Exception.
notice.solution.110072=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110073=Jack-Up Driver-Exception.
notice.solution.110073=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110074=Jack-Up Driver-Exception.
notice.solution.110074=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110075=Jack-Up Driver-Exception.
notice.solution.110075=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110076=Jack-Up Driver-Exception.
notice.solution.110076=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110077=Jack-Up Driver-Exception.
notice.solution.110077=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110078=Jack-Up Driver-Exception.
notice.solution.110078=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110079=Jack-Up Driver-Exception.
notice.solution.110079=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110080=Jack-Up Driver-Exception.
notice.solution.110080=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110081=Jack-Up Driver-Exception.
notice.solution.110081=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110082=Jack-Up Driver-Exception.
notice.solution.110082=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110083=Jack-Up Driver-Exception.
notice.solution.110083=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110084=Jack-Up Driver-Exception.
notice.solution.110084=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110085=Jack-Up Driver-Exception.
notice.solution.110085=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110086=Jack-Up Driver-Exception.
notice.solution.110086=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110087=Jack-Up Driver-Exception.
notice.solution.110087=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110088=Jack-Up Driver-Exception.
notice.solution.110088=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110089=Jack-Up Driver-Exception.
notice.solution.110089=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110090=Jack-Up Driver-Exception.
notice.solution.110090=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110091=Rotating Drive-Exception.
notice.solution.110091=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110092=Rotating Drive-Exception.
notice.solution.110092=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110093=Rotating Drive-Exception.
notice.solution.110093=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110094=Rotating Drive-Exception.
notice.solution.110094=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110095=Rotating Drive-Exception.
notice.solution.110095=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110096=Rotating Drive-Exception.
notice.solution.110096=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110097=Rotating Drive-Exception.
notice.solution.110097=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110098=Rotating Drive-Exception.
notice.solution.110098=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110099=Rotating Drive-Exception.
notice.solution.110099=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110100=Rotating Drive-Exception.
notice.solution.110100=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110101=Rotating Drive-Exception.
notice.solution.110101=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110102=Rotating Drive-Exception.
notice.solution.110102=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110103=Rotating Drive-Exception.
notice.solution.110103=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110104=Rotating Drive-Exception.
notice.solution.110104=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110105=Rotating Drive-Exception.
notice.solution.110105=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110106=Rotating Drive-Exception.
notice.solution.110106=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110107=Rotating Drive-Exception.
notice.solution.110107=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110108=Rotating Drive-Exception.
notice.solution.110108=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110109=Rotating Drive-Exception.
notice.solution.110109=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110110=Rotating Drive-Exception.
notice.solution.110110=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110111=Rotating Drive-Exception.
notice.solution.110111=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110112=Rotating Drive-Exception.
notice.solution.110112=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110113=Rotating Drive-Exception.
notice.solution.110113=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110114=Rotating Drive-Exception.
notice.solution.110114=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110115=Rotating Drive-Exception.
notice.solution.110115=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110116=Rotating Drive-Exception.
notice.solution.110116=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110117=Rotating Drive-Exception.
notice.solution.110117=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110118=Rotating Drive-Exception.
notice.solution.110118=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110200=Motor Initialization-Exception.
notice.solution.110200=The message sending of the walking motor failed, possibly because the walking motor is not configured with correct parameters or is not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110201=Motor Initialization-Exception.
notice.solution.110201=The message sending of the lifting motor failed, possibly because the walking motor is not configured with correct parameters or is not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110202=Motor Initialization-Exception.
notice.solution.110202=The message sending of the plug-in motor failed, possibly because the walking motor is not configured with correct parameters or is not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110203=Motor Initialization-Exception.
notice.solution.110203=The message sending of rotating motor failed, probably because the walking motor was not configured with correct parameters or was not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110204=Motor Initialization-Exception.
notice.solution.110204=Sending the message of the clamping motor failed, possibly because the walking motor is not configured with correct parameters or is not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110205=Motor Initialization-Exception.
notice.solution.110205=Sen Chuang's lifting motor message failed to be sent, probably because the walking motor was not configured with correct parameters or was not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110206=Motor Initialization-Exception.
notice.solution.110206=Sen Chuang rotating motor failed to send the message, probably because the walking motor was not configured with correct parameters or was not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110207=Motor Initialization-Exception.
notice.solution.110207=The message sending of SR motor failed, probably because the walking motor was not configured with correct parameters or was not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110300=Motor Control-Abnormal.
notice.solution.110300=The rotation command issued is out of range: -180~180. Clear the error and reissue it.
notice.description.110301=Motor Control-Abnormal.
notice.solution.110301=The rotation speed command issued is out of range. Clear the error and reissue it. Maximum speed is 8 revolutions per minute.
notice.description.110302=Motor Control-Abnormal.
notice.solution.110302=The lifting instruction issued is out of range. Clear the error and reissue it. If you can't succeed within a reasonable range, please contact the after-sales department.
notice.description.110303=Motor Control-Abnormal.
notice.solution.110303=The vertical speed instruction issued is out of range. Clear the error and reissue it. Speed is 10 mm/s.
notice.description.110400=Motor Running-Abnormal.
notice.solution.110400=trigger an emergency stop and then release it, or restart the robot and observe whether it recovers, otherwise contact the after-sales department.
notice.description.110401=Motor Running-Abnormal.
notice.solution.110401=trigger an emergency stop and then release it, or restart the robot and observe whether it recovers, otherwise contact the after-sales department.
notice.description.110402=Motor Running-Abnormal.
notice.solution.110402=trigger an emergency stop and then release it, or restart the robot and observe whether it recovers, otherwise contact the after-sales department.
notice.description.110403=Motor Running-Abnormal.
notice.solution.110403=trigger an emergency stop and then release it, or restart the robot and observe whether it recovers, otherwise contact the after-sales department.
notice.description.113001=CAN Card-Exception.
notice.solution.113001=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113002=CAN Card-Exception.
notice.solution.113002=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113003=CAN Card-Exception.
notice.solution.113003=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113004=CAN Card-Exception.
notice.solution.113004=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113005=CAN Card-Exception.
notice.solution.113005=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113006=CAN Card-Exception.
notice.solution.113006=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113007=CAN Card-Exception.
notice.solution.113007=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113008=CAN Card-Exception.
notice.solution.113008=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113009=CAN Card-Exception.
notice.solution.113009=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113010=CAN Card-Exception.
notice.solution.113010=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113011=CAN Card-Exception.
notice.solution.113011=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113012=CAN Card-Exception.
notice.solution.113012=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113013=CAN Card-Exception.
notice.solution.113013=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113015=CAN Card-Exception.
notice.solution.113015=Can card is abnormal: the CAN card may not be connected or the connecting cable may be disconnected. If the restart can't solve it, please contact the after-sales.
notice.description.113016=CAN Card-Exception.
notice.solution.113016=Can card is abnormal: the CAN card equipment may be abnormal. If it is not recovered after repeated restarts, please contact the after-sales department.
notice.description.113017=CAN Card-Exception.
notice.solution.113017=Can card is abnormal: the CAN card equipment may be abnormal. If it is not recovered after repeated restarts, please contact the after-sales department.
notice.description.114000=IMU- Exception.
notice.solution.114000=Check whether the serial port number is correct.
notice.description.114001=IMU- Exception.
notice.solution.114001=Check whether the serial port is connected properly.
notice.description.114002=IMU- Exception.
notice.solution.114002=Check whether the serial port is connected properly.
notice.description.114003=IMU- Exception.
notice.solution.114003=Check whether the serial port is connected properly and the interference.
notice.description.114004=IMU- Exception.
notice.solution.114004=Check whether the serial port is connected properly and the interference.
notice.description.120001=Charging-Abnormal
notice.solution.120001=It is possible that the communication line of the battery is not connected well, or the intermittent communication fails. Re-execute the task. If there is still an error, please contact the after-sales department.
notice.description.120002=Charging-Abnormal
notice.solution.120002=If the error is still reported after restarting and retrying, please contact after-sales.
notice.description.120003=Charging-Abnormal
notice.solution.120003=If the error is still reported after restarting and retrying, please contact after-sales.
notice.description.120004=Charging-Abnormal
notice.solution.120004=If the error is still reported after restarting and retrying, please contact after-sales.
notice.description.120005=Charging-Abnormal
notice.solution.120005=The docking information collection failed due to possible environmental factors. Adjust the environment to a good condition without interference and then try the task again. If it still fails after adjustment, please contact the after-sales department.
notice.description.120006=Charging-Abnormal
notice.solution.120006=The docking information collection failed due to possible environmental factors. Adjust the environment to a good condition without interference and then try the task again. If it still fails after adjustment, please contact the after-sales department.
notice.description.120007=Charging-Abnormal
notice.solution.120007=If the charging pile is not properly docked, please adjust the docking parameters. Ensure that the charging pile is in automatic mode.
notice.description.120008=Charging-Abnormal
notice.solution.120008=Decrease the percentage of fullness, and the default is 97%. If there is still a problem if it is reduced to 89%, please contact after-sales.
notice.description.120100=BMS- Exception
notice.solution.120100=Check whether the serial port is normal.
notice.description.120101=BMS- Exception
notice.solution.120101=Check whether the reading instruction data is correct, and check the battery communication protocol.
notice.description.120102=BMS- Exception.
notice.solution.120102=Check whether the written instruction data is correct, and check the battery communication protocol.
notice.description.120103=BMS- Exception.
notice.solution.120103=Check whether the serial port is normal.
notice.description.120104=BMS- Exception.
notice.solution.120104=Check whether the serial port is normal.
notice.description.120106=BMS- Exception.
notice.solution.120106=Check whether the serial data interferes.
notice.description.120107=BMS- Exception.
notice.solution.120107=Check the number of batteries.
notice.description.120108=BMS- Exception.
notice.solution.120108=Check the voltage of double batteries.
notice.description.121001=Audio-Exception
notice.solution.121001=Confirm that the specified audio name exists in AGV, and be careful not to add a suffix at the end of the audio name, such as.mp3.. Make sure the audio is in mp3 format.
notice.description.123004=Socket-Exception
notice.solution.123004=reconfirm whether the API port number and interface number are correct.
notice.description.123005=Socket-Exception
notice.solution.123005=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.123006=Socket-Exception
notice.solution.123006=Unable to obtain configuration information, please contact after-sales.
notice.description.127001=Exception in navigation execution.
notice.solution.127001=Abnormal navigation execution: the current task needs to be stopped and the after-sales department should be contacted.
notice.description.127002=Exception in navigation execution.
notice.solution.127002=Abnormal navigation execution: it is necessary to stop the current task and manually judge whether the robot is derailed. If it is derailed, please move the robot to the path. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127003=Exception in navigation execution.
notice.solution.127003=Exception in navigation execution: it is necessary to stop the current task and manually judge whether the positioning data exists. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127004=Exception in navigation execution.
notice.solution.127004=Abnormal navigation execution: it is necessary to stop the current task and manually judge whether the marker under the shelf is recognized normally. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127005=Exception in navigation execution.
notice.solution.127005=Abnormal navigation execution: it is necessary to stop the current task and determine whether there are lidar and pcl related errors. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127006=Exception in navigation execution.
notice.solution.127006=Abnormal navigation execution: it is necessary to stop the current task and determine whether there is a motor-related error. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127007=Exception in navigation execution.
notice.solution.127007=Abnormal navigation execution: it is necessary to stop the current task and determine whether there is any positioning-related error. If not, please determine whether there is any lidar-related error in the case of laser positioning, and whether there is any communication abnormality of QR code sensor in QR code positioning. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127008=Exception in navigation execution.
notice.solution.127008=Abnormal navigation execution: it is necessary to stop the current task and judge whether the lidar data is normal. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127009=Exception in navigation execution.
notice.solution.127009=Abnormal navigation execution: the current task needs to be stopped, and whether the artificial features around the current path are occluded. If so, please avoid occlusion. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127010=Exception in navigation execution.
notice.solution.127010=Navigation execution is abnormal: it is necessary to stop the current task and judge whether the current laser positioning data is normal. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127011=Navigation execution exception 111
notice.solution.127011=Abnormal navigation execution: it is necessary to stop the current task and judge whether the current laser positioning data is normal. If you can't recover or the abnormality appears many times, please contact after-sales. 222
notice.description.127012=The angle change range of directional curve is too large.
notice.solution.127012=Please reduce the curvature of the path to make it smoother.
notice.description.128001=Exception in execution of pair.
notice.solution.128001=Exception in docking execution: it is necessary to clear the error state, and judge whether the current robot is docking, or whether it has not been docked after the previous docking instruction. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128002=Exception in execution of pair.
notice.solution.128002=Exception in docking execution: it is necessary to clear the error state and judge whether the currently specified docking target is reasonable. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128003=Exception in execution of pair.
notice.solution.128003=Exception in docking execution: it is necessary to clear the error state and judge the running state of the feature detection module. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128004=Exception in execution of pair.
notice.solution.128004=Exception in docking execution: it is necessary to clear the error state and determine the running state of the feature detection module. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128005=Exception in execution of pair.
notice.solution.128005=Exception in docking execution: it is necessary to clear the error state and determine the running state of the feature detection module. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128006=Exception in execution of pair.
notice.solution.128006=Exception in docking execution: the error status needs to be cleared, and please contact the after-sales department.
notice.description.128007=Exception in execution of pair.
notice.solution.128007=Exception in docking execution: it is necessary to clear the error state and judge the running state of the feature detection module. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128008=Exception in execution of pair.
notice.solution.128008=Exception in docking: it is necessary to clear the error state, and it is necessary to determine whether the current robot is docking or not. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128009=Exception in execution of pair.
notice.solution.128009=It is necessary to clear the error status and determine whether there is a positioning-related error. If not, please determine whether there is a lidar-related error. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128010=Exception in execution of pair.
notice.solution.128010=Exception in docking execution: it is necessary to know the error state and judge the running state of feature detection. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128011=Abnormal alignment of QR code shelf.
notice.solution.128011=Abnormal alignment of two-dimensional code shelf: it is necessary to clear the error state and determine whether another alignment is performed during the alignment of two-dimensional code shelf.
notice.description.128012=Abnormal alignment of QR code shelf.
notice.solution.128012=Abnormal alignment of QR code shelf: check whether the QR code is illuminated, and if it is illuminated, check whether the QR code identification node is turned on and whether the node parameters are configured correctly.
notice.description.128100=Abnormal side alignment.
notice.solution.128100=Abnormal side alignment: it is necessary to clear the error state and determine whether another alignment is performed during the side alignment.
notice.description.128101=Abnormal side alignment.
notice.solution.128101=Check whether the distance between the side docking sensor and the machine is within the threshold range.
notice.description.130001=Location Exception.
notice.solution.130001=Please try to relocate. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.133001=Exception in feature detection execution.
notice.solution.133001=Abnormal execution of feature detection: manually judge whether the robot is within the docking range and whether the features are highly consistent with the robot lidar. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.133002=Exception in feature detection execution.
notice.solution.133002=Abnormal feature detection: manually judge whether there are similar features and adjust the docking distance or direction of the robot. If you can't reply or the abnormality appears many times, please contact after-sales.
notice.description.133003=Exception in feature detection execution.
notice.solution.133003=Abnormal execution of feature detection: judge whether there is an error related to lidar. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.133004=Exception in feature detection execution.
notice.solution.133004=Exception in feature detection: judge whether there is a positioning related error. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.135001=Exception in drawing execution.
notice.solution.135001=Abnormal execution of drawing: judge whether there is an error related to lidar. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.135002=Exception in drawing execution.
notice.solution.135002=Exception in drawing execution: manually judge whether the drawing process forms a loop. If there is no return, please contact after-sales.
notice.description.140000=Script Exception.
notice.solution.140000=Script exception: Please check the parameters of socket or lua instruction, such as length and type.
notice.description.140001=Script Exception.
notice.solution.140001=Script exception: The specified control script does not exist. Please check the name or ID of the script.
notice.description.140002=Script Exception.
notice.solution.140002=Script exception: The script is running, and the user's current control instruction is not running.
notice.description.140003=Script Exception.
notice.solution.140003=Script exception: The script sub-thread did not exit normally, which is a fatal BUG. Please contact the developer.
notice.description.140004=Script Exception.
notice.solution.140004=Script exception: The startup script timed out, and it is checked whether the storage path of the script is correct. If the test is correct, please contact the developer.
notice.description.140005=Script Exception.
notice.solution.140005=Script exception: Stop the script timeout, and check whether the script has exited. If the test is correct, please contact the developer.
notice.description.140006=Script Exception.
notice.solution.140006=Script exception: The issued control instruction does not exist. Please check the command of json instruction.
notice.description.140007=Script Exception.
notice.solution.140007=Script exception: The address of the specified script variable is wrong. Please check whether the address of the distributed script variable is not within the specified range [0,31].
notice.description.140008=Script Exception.
notice.solution.140008=Script exception: Please contact the developer.
notice.description.140009=Script Exception.
notice.solution.140009=Script exception: Please check whether there is a main function in lua script.
notice.description.140010=Script Exception.
notice.solution.140010=Script exception: Please check whether there is an exception function in lua script.
notice.description.140011=Script Exception.
notice.solution.140011=Script exception: Please check whether there is a cancel function in lua script.
notice.description.140012=Script Exception.
notice.solution.140012=Script exception: Check the configuration file in this case.
notice.description.140013=Script Exception.
notice.solution.140013=Script exception: There is something wrong with the json transmitted by the user. Please check the following json data and contact the developer.
notice.description.140014=Script Exception.
notice.solution.140014=Script exception: There is something wrong with the json transmitted by the user. Please check the following json data and contact the developer.
notice.description.140015=Script Exception.
notice.solution.140015=Script exception: Check the variable type required by the interface.
notice.description.140016=Script Exception.
notice.solution.140016=Script exception: 1. Check whether the target point exists; 2. Check whether there is a path leading to the target point at the current position; 3. Check whether the robot positioning is invalid; If you don't contact the developer.
notice.description.140017=Script Exception.
notice.solution.140017=Script exception: 1. Check whether the target point exists; 2. Check whether there is a path leading to the target point at the current position; 3. Check whether the robot positioning is invalid; If you don't contact the developer.
notice.description.140018=Script Exception.
notice.solution.140018=Script exception: There is something wrong with the fields sent by compass, please contact the developer.
notice.description.140019=Script Exception.
notice.solution.140019=Script exception: compass's socket service is disconnected.
notice.description.140020=Script Exception.
notice.solution.140020=Script exception: Error in linear motion, please contact the developer.
notice.description.140021=Script Exception.
notice.solution.140021=Script exception: Check whether the environment meets the docking conditions, please contact the developer.
notice.description.140022=Script Exception.
notice.solution.140022=Script exception: Check whether the algorithm node is abnormal, please contact the developer.
notice.description.140023=Script Exception.
notice.solution.140023=Script exception: Check whether the format of the script is correct, please contact the developer.
notice.description.140024=Script Exception.
notice.solution.140024=Script exception: Exception found during script execution, please contact the developer.
notice.description.140025=Script Exception.
notice.solution.140025=Script exception: The navigation of the script execution path is abnormal, please contact the developer.
notice.description.140026=Script Exception.
notice.solution.140026=Script exception: call new script exception, please contact the developer.
notice.description.140027=Script Exception.
notice.solution.140027=Script exception: Please edit the script with the suffix ".lua" correctly.
notice.description.140028=Script Exception.
notice.solution.140028=Script exception: Unknown exception, please contact the developer.
notice.description.145000=Script Exception.
notice.solution.145000=Script exception: The connection of the mechanical arm timed out. Please check whether the mechanical arm is powered on and whether the network cable is normal.
notice.description.145001=Script Exception.
notice.solution.145001=Script exception: The mechanical arm is not connected. Please check whether the mechanical arm is powered on and whether the network cable is normal.
notice.description.145002=Script Exception.
notice.solution.145002=Script exception: Please contact the developer.
notice.description.145003=Script Exception.
notice.solution.145003=Script exception: The parameters entered by the control manipulator are wrong. Please check the parameters according to the protocol.
notice.description.145004=Script Exception.
notice.solution.145004=Script exception: The message returned by the mechanical arm is wrong, please contact the developer.
notice.description.145005=Script Exception.
notice.solution.145005=Script exception: Error in issuing the command to control the manipulator. Please check the issued command according to the protocol.
notice.description.145006=Script Exception.
notice.solution.145006=Script exception: Please contact the developer.
notice.description.145007=Script Exception.
notice.solution.145007=Script exception: Please contact the developer.
notice.description.146000=Script Exception.
notice.solution.146000=Script exception: Please contact the developer.
notice.description.146001=Script Exception.
notice.solution.146001=Script exception: Please contact the developer.
notice.description.147001=Acousto-optic system-abnormal.
notice.solution.147001=If it still cannot be solved after restart, contact the after-sales department.
notice.description.147002=Single point lidar-abnormal.
notice.solution.147002=If it still cannot be solved after restart, contact the after-sales department.
notice.description.147004=Side photoelectric docking-abnormal.
notice.solution.147004=Check the network IP address.
notice.description.150000=Lidar Abnomaly.
notice.solution.150000=Check the network port and ip address.
notice.description.150002=Lidar Abnomaly.
notice.solution.150002=Check whether the setting frequency is within a reasonable range.
notice.description.150003=Lidar Abnomaly.
notice.solution.150003=Check whether the set sampling rate is within a reasonable range.
notice.description.150004=Lidar Abnomaly.
notice.solution.150004=Check whether the network is normal.
notice.description.150005=Lidar Abnomaly.
notice.solution.150005=Check whether the network is normal.
notice.description.150100=PLC Client Exception.
notice.solution.150100=Check the network ip address and port number.
notice.description.150101=PLC Client Exception.
notice.solution.150101=Check the network ip address and port number.
notice.description.150102=PLC Client Exception.
notice.solution.150102=Check whether the network is normal.
notice.description.150103=PLC Client Exception.
notice.solution.150103=Check whether the network is normal.
notice.description.150104=PLC Client Exception.
notice.solution.150104=Check whether there is an emergency stop signal triggered.
notice.description.150151=Security PLC Client Exception.
notice.solution.150151=Check the network ip address and port number.
notice.description.150152=Abnormal security PLC client.
notice.solution.150152=Check whether the network is normal.
notice.description.150153=Abnormal security PLC client.
notice.solution.150153=Check whether the network is normal.
notice.description.150154=Security PLC Client Exception.
notice.solution.150154=Check whether there is an emergency stop signal triggered.
notice.description.150155=Security PLC Client Exception.
notice.solution.150155=Check Encoder Alarm.
notice.description.150300=Abnormal QR Code.
notice.solution.150300=Check whether the serial port number is correct.
notice.description.150301=Abnormal QR Code.
notice.solution.150301=Check whether the serial port is connected properly.
notice.description.150302=Abnormal QR Code.
notice.solution.150302=Check whether the serial port is connected properly.
notice.description.150303=Abnormal QR Code.
notice.solution.150303=Check whether the serial port is connected properly and the interference.
notice.description.150304=Abnormal QR Code.
notice.solution.150304=Check whether the serial port is connected properly and the interference.
notice.description.150310=Abnormal QR Code.
notice.solution.150310=Check whether the camera is connected properly.
notice.description.150311=Abnormal QR Code.
notice.solution.150311=Check whether the camera is connected properly.
notice.description.150312=Abnormal QR Code.
notice.solution.150312=Check whether the camera is connected properly.
notice.description.150313=Abnormal QR Code.
notice.solution.150313=Check whether the camera is connected properly.
notice.description.150400=3D Camera Exception.
notice.solution.150400=Check whether the camera is connected properly.
notice.description.150401=3D Camera Exception.
notice.solution.150401=Check whether the camera is properly configured.
notice.description.150500=Ultrasonic Abnormal
notice.solution.150500=Check whether the ultrasonic connection is normal.
notice.description.150501=Ultrasonic Abnormal
notice.solution.150501=Check whether the ultrasonic wave is configured normally.
notice.description.170001=Integration Exception.
notice.solution.170001=Integration exception: Please contact the developer.
notice.description.170002=Integration Exception.
notice.solution.170002=Upper integrated alarm: Please reset according to the upper integrated operation manual.
notice.description.170003=Integration Exception.
notice.solution.170003=Integration exception: Please reset the drive to clear the fault, or power off and restart.
notice.description.170004=Integration Exception.
notice.solution.170004=Integration exception: Please reset the drive to clear the fault, or power off and restart.
notice.description.170005=Integration Exception.
notice.solution.170005=Integration exception: Please reset the drive to clear the fault, or power off and restart.
notice.description.170006=Integration Exception.
notice.solution.170006=Integration exception: Please reset the drive to clear the fault, or power off and restart.
notice.description.170007=Integration Exception.
notice.solution.170007=Integration exception: Please reset the drive to clear the fault, or power off and restart.
notice.description.170008=Integration Exception.
notice.solution.170008=Exception integration: Release the emergency stop to recover.
notice.description.171001=Exception in Haikang Yuntai (upper integration)
notice.solution.171001=Abnormality of Haikang Yuntai: 1. Check whether the network cable is loose; 2. Check whether the network communication is normal.
notice.description.171002=Sonar sensor (upper integration) is abnormal.
notice.solution.171002=Abnormal sonar sensor: Please check whether the equipment connection is normal.
notice.description.171003=Abnormal Drop Sensor.
notice.solution.171003=Abnormal drop sensor: Please check whether the equipment is connected normally.
notice.description.171004=Abnormal Drop Sensor.
notice.solution.171004=Abnormal drop sensor: Please check whether the equipment is connected normally.
notice.description.171005=Abnormal Drop Sensor.
notice.solution.171005=Abnormal drop sensor: Please check whether the equipment is connected normally.
notice.description.171006=Abnormal Drop Sensor.
notice.solution.171006=Abnormal drop sensor: Please check whether the equipment is connected normally.
notice.description.171007=Ultrasonic sensor (upper integration) is abnormal.
notice.solution.171007=Abnormal ultrasonic sensor: Please check whether the equipment is connected normally.
notice.description.171008=Ultrasonic sensor (upper integration) is abnormal.
notice.solution.171008=Abnormal ultrasonic sensor: Please check whether the equipment is connected normally.
notice.description.171009=Ultrasonic sensor (upper integration) is abnormal.
notice.solution.171009=Abnormal ultrasonic sensor: Please check whether the equipment is connected normally.
notice.description.171010=Ultrasonic sensor (upper integration) is abnormal.
notice.solution.171010=Abnormal ultrasonic sensor: Please check whether the equipment is connected normally.
notice.description.200017=Cannot find the instruction issued to Pilot.
notice.solution.200017=Please contact technical support.
notice.description.200018=Instruction Execution Failed.
notice.solution.200018=Please contact technical support.
notice.description.200101=Button Emergency Stop.
notice.solution.200101=Please check the status of the robot.
notice.description.200102=Safety Equipment Emergency Stop.
notice.solution.200102=Please check the status of the robot.
notice.description.200103=Collision Emergency Stop.
notice.solution.200103=Please check the status of the robot.
notice.description.200104=Unexpected emergency stop of route navigation.
notice.solution.200104=Please check the status of the robot.
notice.description.200105=Emergency stop of robot lifting.
notice.solution.200105=Robot lifting and emergency stop.
notice.description.200106=Robot Lifting Error.
notice.solution.200106=Robot Lifting Error.
notice.description.200107=Robot Drum Emergency Stop.
notice.solution.200107=Robot Drum Emergency Stop.
notice.description.200108=Robot Roller Error.
notice.solution.200108=Robot Roller Error.
notice.description.200109=Robot Startup Paused.
notice.solution.200109=Robot Startup Paused.
notice.description.200110=Robot Manual Control Mode
notice.solution.200110=Robot Manual Control Mode
notice.description.200111=Robot is not located.
notice.solution.200111=Robot is not located.
notice.description.200112=The control mode of this robot only supports hardware knob control!
notice.solution.200112=The control mode of this robot only supports hardware knob control!
notice.description.200113=The robot arm is not ready.
notice.solution.200113=Check the status of the robot arm.
notice.description.200114=Robot abnormality
notice.solution.200114=Check the status of the robot
notice.description.200120=[未翻译]
notice.solution.200120=[未翻译]
notice.description.300001=System internal error.
notice.solution.300001=Please contact technical support.
notice.description.300002=Statistical module program exception.
notice.solution.300002=Please contact technical support.
notice.description.300003=Map module program is abnormal.
notice.solution.300003=Please contact technical support.
notice.description.300004=Robot module program is abnormal.
notice.solution.300004=Please contact technical support.
notice.description.300005=Exception in task module program.
notice.solution.300005=Please contact technical support.
notice.description.300006=Abnormal traffic control module program.
notice.solution.300006=Please contact technical support.
notice.description.300007=Abnormal degree of event module.
notice.solution.300007=Please contact technical support.
notice.description.300008=Air shower door module is abnormal.
notice.solution.300008=Please contact technical support.
notice.description.300009=Abnormal automatic door module.
notice.solution.300009=Please contact technical support.
notice.description.300010=Elevator module is abnormal.
notice.solution.300010=Please contact technical support.
notice.description.300011=Call box module is abnormal.
notice.solution.300011=Please contact technical support.
notice.description.300012=Error
notice.solution.300012=Please contact technical support.
notice.description.300013=Excessive CPU resource usage
notice.solution.300013=Please contact technical support.
notice.description.300014=Excessive memory resource usage
notice.solution.300014=Please contact technical support.
notice.description.300015=Excessive harddisk resource usage.
notice.solution.300015=Please contact technical support.
notice.description.300016=充电桩状态模块程序异常（待翻译）
notice.solution.300016=Please contact technical support.
notice.description.300101=Robot  failed to occupy point .
notice.solution.300101=The position where the robot is located has been occupied by other robots. Please move the robot to the road network.
notice.description.300102=Robot  failed to occupy area .
notice.solution.300102=The stand-alone area where the robot is located has been occupied by other robots. Please move the robot to the road network.
notice.description.300103=Robot  failed to occupy elevator .
notice.solution.300103=The elevator where the robot is located has been occupied by other robots. Please move the robot to the road network.
notice.description.300104=Robot  has been disconnected.
notice.solution.300104=1: Please check whether the network connection between the robot and the server is normal; 2: Please check whether the power of the robot is turned on; 3: Please check whether the system IP and port configured by the robot are correct;
notice.description.300105=Robot  is out of orbit.
notice.solution.300105=Please check whether the positioning state of the robot is accurate. If it is, please move the robot to the road network.
notice.description.300106=Robot  Manual Control Mode
notice.solution.300106=The robot is in manual control mode, please switch to automatic control mode.
notice.description.300107=机器人检修控制模式（待翻译）
notice.solution.300107=机器人处于检修控制模式,请切换成自动控制模式（待翻译）
notice.description.300201=Connection of automatic door  failed.
notice.solution.300201=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the equipment IP and port number are configured correctly in the map layout interface;
notice.description.300202=Connection of air shower door  failed.
notice.solution.300202=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the equipment IP and port number are configured correctly in the map layout interface;
notice.description.300203=Connection of elevator  failed.
notice.solution.300203=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the equipment IP and port number are configured correctly in the map layout interface;
notice.description.300204=Failed to read the automatic door  command.
notice.solution.300204=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the device reading address configuration is correct in the map layout interface;
notice.description.300205=Failed to read the air shower door  command.
notice.solution.300205=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the device reading address configuration is correct in the map layout interface;
notice.description.300206=Failed to read elevator  instruction.
notice.solution.300206=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the device reading address configuration is correct in the map layout interface;
notice.description.300207=Failed to write the automatic door  command.
notice.solution.300207=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3. Please check whether the write address configuration is correct in the map layout interface;
notice.description.300208=Failed to write the instruction  to the air shower door.
notice.solution.300208=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3. Please check whether the write address configuration is correct in the map layout interface;
notice.description.300209=Failed to write elevator  command.
notice.solution.300209=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3. Please check whether the write address configuration is correct in the map layout interface;
notice.description.300210=No path is bound for automatic door .
notice.solution.300210=Please open the map editing interface and bind the path for this automatic door.
notice.description.300211=The path of air shower door  is not bound.
notice.solution.300211=Please open the map editing interface and bind the path for the air shower door.
notice.description.300212=Unbound point of elevator
notice.solution.300212=Please open the map editing interface and bind the points for the elevator.
notice.description.300213=Call box  is not connected to the dispatching system.
notice.solution.300213=1: Please check whether the network connection between the call box and the server is normal; 2: Please check whether the power of the call box is turned on; 3: Please use the call box configuration tool to check whether the server address configuration of the call box is correct;
notice.description.300214=Call box  is not configured in the system.
notice.solution.300214=Please configure the task flow of event type for this call box.
notice.description.300215=The task flow  associated with call box  has not been published.
notice.solution.300215=Please publish the task flow bound by this call box.
notice.description.300216=Duplicate call box  number.
notice.solution.300216=There are multiple call boxes configured with the same call box ID. Please use the call box configuration tool to reconfigure the call boxes.
notice.description.300217=Call box  software exception.
notice.solution.300217=Please use the call box configuration tool to connect the call box and check the program problems of the call box.
notice.description.300218=Call box  hardware exception.
notice.solution.300218=Please use the call box configuration tool to connect the call box and check the hardware problem of the call box.
notice.description.300219=Abnormal configuration of call box .
notice.solution.300219=Please use the call box configuration tool to connect the call box and check the configuration problem of the call box.
notice.description.300220=Call box has low battery.
notice.solution.300220=Please charge the call box manually.
notice.description.300222=充电桩不存在（待翻译）
notice.solution.300222=请在设备管理界面检查充电桩是否存在（待翻译）
notice.description.300223=充电桩不可用（待翻译）
notice.solution.300223=请在设备管理界面检查充电桩状态（待翻译）
notice.description.300224=充电桩已断线（待翻译）
notice.solution.300224=请检查设备和服务器的网络连接是否正常（待翻译）
notice.description.300225=充电桩状态异常（待翻译）
notice.solution.300225=请在设备管理界面检查充电桩状态（待翻译）
notice.description.300226=充电桩放电中（待翻译）
notice.solution.300226=请在设备管理界面检查充电桩状态（待翻译）
notice.description.300227=充电点未绑定充电桩（待翻译）
notice.solution.300227=请在地图编排界面检查充电点配置（待翻译）
notice.description.300301=Task Exception.
notice.solution.300301=Please contact technical support.
notice.description.300302=Task  Node Exception
notice.solution.300302=Please contact technical support.
notice.description.300303=Robot type has no robot.
notice.solution.300303=Please bind the robot to this robot type.
notice.description.300304=Robot group  has no robots.
notice.solution.300304=Please bind the robot to this robot group.
notice.description.300305=Robot  does not exist.
notice.solution.300305=Please open the pop-up window of task details and the interface of robot list, and check whether a nonexistent robot was used to create the task.
notice.description.300306=Map  has no robots.
notice.solution.300306=Please put available robots on this map.
notice.description.300307=Point  does not exist.
notice.solution.300307=Please open the pop-up window of task details and the interface of map list, and check whether non-existent points are used to create the task.
notice.description.300308=The target point  is unreachable.
notice.solution.300308=1: Please check whether the robot's point and target point are on the same map; 2. Please check whether there is an available path between the point where the robot is located and the target point;
notice.description.300309=Control illegal robots
notice.solution.300309=1: When the node does not specify the robot to be controlled, there must be only one "dynamic robot allocation" or "designated robot allocation" node before the node; 2: When the node has designated the robot to be controlled, the robot designated by the node needs to be the output parameter of the node of "Dynamic robot allocation" or "Designated robot allocation";
notice.description.300310=Failed to send robot  command.
notice.solution.300310=Please check whether the network connection between the robot and the server is normal;
notice.description.300311=Robot  cannot receive instructions.
notice.solution.300311=1: Please check whether the robot is in automatic control mode; 2: Please check whether the robot has been pressed the emergency stop button; 3: Please check whether the robot is connected; 4: Please check whether the robot is in an abnormal state;
notice.description.300312=Robot  failed to execute the instruction.
notice.solution.300312=Please contact technical support.
notice.description.300313=No point available.
notice.solution.300313=1: The node specifies a map, but no point can be found on the map; 2: The node specifies the point type, but no point can be found;
notice.description.300314=Data format conversion failed.
notice.solution.300314=Please check the input values of node parameters in the task details interface.
notice.description.300315=Unable to connect Plc.
notice.solution.300315=1: Please check whether the network connection between Plc and server is normal; 2: Please check whether the power supply of Plc is turned on; 3: Please check whether the Plc address port configured by the node is correct;
notice.description.300316=Register value is out of range.
notice.solution.300316=Please open the task details pop-up window to check whether the out-of-range register value was used to create the task.
notice.description.300317=The  attribute of the acquisition point  is empty.
notice.solution.300317=Please open the map editing page and check the properties of the point.
notice.description.300318=Location  does not exist or is not enabled.
notice.solution.300318=Please open the location page and check whether the creation task uses a location that does not exist or is not enabled.
notice.description.300319=The reservoir area  does not exist.
notice.solution.300319=Please open the library page and check whether the creation task uses a non-existent library.
notice.description.300320=Location type  does not exist.
notice.solution.300320=Please open the location type page and check whether the creation task uses a location type that does not exist.
notice.description.300321=No location available.
notice.solution.300321=Please open the location page and check whether there is a location selected by the task.
notice.description.300322=Container barcode  does not exist.
notice.solution.300322=Please open the location page and check whether the creation task uses a container code that does not exist.
notice.description.300323=The adjacent points of point  are not unique.
notice.solution.300323=Please check whether the point selected for creating the task has no adjacent points or has multiple adjacent points.
notice.description.300324=Container barcode  already exists.
notice.solution.300324=Please open the location page and check whether the same container code already exists.
notice.description.300325=Location  has been occupied.
notice.solution.300325=Please open the location page and check whether the location has been occupied.
notice.description.300326=Exception in http  request.
notice.solution.300326=1: Please check whether the Http request address is configured correctly; 2: Please check whether the network connection between the device and the server is normal;
notice.description.300327=There are multiple qualified points under this number .
notice.solution.300327=Please check whether there are multiple points selected for creating the task.
notice.description.300328=Unable to find the location that meets the requirements.
notice.solution.300328=Please open the location page and check whether there is a location selected by the task.
notice.description.300329=Area does not exist
notice.solution.300329=请打开任务流程详情页面,在对应节点填写已存在的禁入区域编码（待翻译）
notice.description.300330=Operation is prohibited in this area
notice.solution.300330=请打开任务流程详情页面,在对应节点填写可操作的区域编码（待翻译）
notice.description.300331=机器人充电停靠偏离角度过大（待翻译）
notice.solution.300331=请打开地图编辑页面，调整对应点位的偏移角度（待翻译）
notice.description.300332=作业任务不能停止（待翻译）
notice.solution.300332=请检查机器人执行的任务是否为作业任务（待翻译）
notice.description.300401=Navigation conflict of robot , no avoidance point available.
notice.solution.300401=Please contact technical support.
notice.description.300501=Failed to write the map file to disk.
notice.solution.300501=Please reconfigure the access rights of this disk directory.
notice.description.300502=Failed to read the disk map file.
notice.solution.300502=Please reconfigure the access rights of this disk directory.
notice.description.570001=Robot universal failure.
notice.solution.570001=Please contact the mos developer for handling.
notice.description.570002=Wrong interface parameters of the robot arm.
notice.solution.570002=Please contact the mos developer for handling.
notice.description.570003=Incompatible instruction interface.
notice.solution.570003=Please contact the mos developer for handling.
notice.description.570004=Robot connection failed.
notice.solution.570004=Please contact the mos developer for handling.
notice.description.570005=Exception in sending and receiving communication message of mechanical arm socket.
notice.solution.570005=Please contact the mos developer for handling.
notice.description.570006=Socket disconnected.
notice.solution.570006=Please contact the mos developer for handling.
notice.description.570007=Create request failed.
notice.solution.570007=Please contact the mos developer for handling.
notice.description.570008=Error in requesting related internal variables.
notice.solution.570008=Please contact the mos developer for handling.
notice.description.570009=The request timed out.
notice.solution.570009=Please contact the mos developer for handling.
notice.description.570010=Failed to send the requested information.
notice.solution.570010=Please contact the mos developer for handling.
notice.description.570011=The response information is empty.
notice.solution.570011=Please contact the mos developer for handling.
notice.description.570012=The response information header does not match.
notice.solution.570012=Please contact the mos developer for handling.
notice.description.570013=Failed to parse the response.
notice.solution.570013=Please contact the mos developer for handling.
notice.description.570014=Error in the correct solution.
notice.solution.570014=Terminate the current task, remove the materials from the gripper of the mechanical arm, manually return the mechanical arm to the original point, and then resume the task.
notice.description.570015=Error in inverse solution.
notice.solution.570015=Terminate the current task, remove the materials from the gripper of the mechanical arm, manually return the mechanical arm to the original point, and then resume the task.
notice.description.570016=Error in tool calibration.
notice.solution.570016=Please contact the mos developer for handling.
notice.description.570017=Error in tool calibration parameters.
notice.solution.570017=Please contact the mos developer for handling.
notice.description.570018=Coordinate system calibration failed.
notice.solution.570018=Please contact the mos developer for handling.
notice.description.570019=Failed to convert base coordinate system to user coordinate system.
notice.solution.570019=Please contact the mos developer for handling.
notice.description.570020=Failed to convert user coordinate system to base coordinate system.
notice.solution.570020=Please contact the mos developer for handling.
notice.description.570021=Robot failed to power on.
notice.solution.570021=Please contact the mos developer for handling.
notice.description.570022=Robot failed to power off.
notice.solution.570022=Please contact the mos developer for handling.
notice.description.570023=Robot enabling failed.
notice.solution.570023=Please contact the mos developer for handling.
notice.description.570024=Enabling failed under the robot.
notice.solution.570024=Please contact the mos developer for handling.
notice.description.570025=Robot reset failed.
notice.solution.570025=Click the reset button again to reset. If it still fails, please take out the teaching device and clear the error.
notice.description.570026=Robot pause failed.
notice.solution.570026=Please contact the mos developer for handling.
notice.description.570027=Robot stop failed.
notice.solution.570027=Press the emergency stop button, remove the material from the gripper of the mechanical arm, power on and enable it, and manually return the mechanical arm to the original point before resuming the task.
notice.description.570028=Robot status acquisition failed.
notice.solution.570028=Terminate the current task, remove the materials from the gripper of the mechanical arm, manually return the mechanical arm to the original point, and then resume the task.
notice.description.570029=Robot encoder status synchronization failed.
notice.solution.570029=Please contact the mos developer for handling.
notice.description.570030=Robot mode is incorrect.
notice.solution.570030=Please contact the mos developer for handling.
notice.description.570031=Robot JOG failed to move.
notice.solution.570031=Please contact the mos developer for handling.
notice.description.570032=Robot drag teaching setting failed.
notice.solution.570032=Please contact the mos developer for handling.
notice.description.570033=Robot speed setting failed.
notice.solution.570033=Please contact the mos developer for handling.
notice.description.570034=Robot waypoint removal failed.
notice.solution.570034=Please contact the mos developer for handling.
notice.description.570035=The acquisition of the current coordinate system of the robot failed.
notice.solution.570035=Please contact the mos developer for handling.
notice.description.570036=Robot coordinate system setting failed.
notice.solution.570036=Please contact the mos developer for handling.
notice.description.570037=Robot weight center of gravity setting failed.
notice.solution.570037=Please contact the mos developer for handling.
notice.description.570038=Robot IO setting failed.
notice.solution.570038=Please contact the mos developer for handling.
notice.description.570039=Robot TCP setup failed.
notice.solution.570039=Please contact the mos developer for handling.
notice.description.570040=Robot TCP acquisition failed.
notice.solution.570040=Please contact the mos developer for handling.
notice.description.570041=move instruction blocked waiting timeout.
notice.solution.570041=Terminate the current task, remove the material from the gripper of the mechanical arm, manually return the mechanical arm to the original point, and check the timeout reason. 1. If the motion is not in place because the path point trajectory fusion radius is too large, the speed of the corresponding step can be reduced appropriately. Then the single machine tests and runs the task, and if the timeout is not reported again, the task can be resumed. 2. If the mechanical arm pauses for more than 1 minute because of triggering the protective stop, the task can be resumed directly.
notice.description.570042=Error in internal variables related to motion.
notice.solution.570042=Please contact the mos developer for handling.
notice.description.570043=Motion request failed.
notice.solution.570043=Please contact the mos developer for handling.
notice.description.570044=Failed to generate the motion request.
notice.solution.570044=Please contact the mos developer for handling.
notice.description.570045=Motion was interrupted by an event.
notice.solution.570045=Terminate the current task, remove the material from the gripper of the mechanical arm, and click the reset button to reset it. If it cannot be reset, please use the teaching device to clear the error. After clearing the error, power on and enable it again, and manually return the mechanical arm to the original point, then resume the task.
notice.description.570046=The length of the waypoint container related to the movement does not meet the requirements.
notice.solution.570046=Please contact the mos developer for handling.
notice.description.570047=The server response returned an error.
notice.solution.570047=Please contact the mos developer for handling.
notice.description.570048=The real robot arm does not exist.
notice.solution.570048=Please contact the mos developer for handling.
notice.description.570049=Failed to call the slow stop interface.
notice.solution.570049=Please contact the mos developer for handling.
notice.description.570050=Calling the emergency stop interface failed.
notice.solution.570050=Please contact the mos developer for handling.
notice.description.570051=Call to pause interface failed.
notice.solution.570051=Please contact the mos developer for handling.
notice.description.570052=Call to continue interface failed.
notice.solution.570052=Please contact the mos developer for handling.
notice.description.570053=Motion was conditionally interrupted.
notice.solution.570053=Terminate the current task, remove the material from the gripper of the mechanical arm, manually return the mechanical arm to the origin, and check the failure condition. If the condition fails due to the original image of the non-sensor, please resume the task. If it is a sensor problem, please contact the after-sales personnel to solve it.
notice.description.570054=Motion was manually interrupted.
notice.solution.570054=Please contact the mos developer for handling.
notice.description.571001=The joint motion attribute is misconfigured.
notice.solution.571001=Please contact the mos developer for handling.
notice.description.571002=Wrong configuration of linear motion attribute.
notice.solution.571002=Please contact the mos developer for handling.
notice.description.571003=The trajectory motion attribute is misconfigured.
notice.solution.571003=Please contact the mos developer for handling.
notice.description.571004=Invalid motion attribute configuration.
notice.solution.571004=Please contact the mos developer for handling.
notice.description.571005=Wait for the robot to stop.
notice.solution.571005=Please contact the mos developer for handling.
notice.description.571006=Out of joint motion range.
notice.solution.571006=Please contact the mos developer for handling.
notice.description.571007=Please set the first waypoint of MODEP correctly.
notice.solution.571007=Please contact the mos developer for handling.
notice.description.571008=Incorrect conveyor tracking configuration.
notice.solution.571008=Please contact the mos developer for handling.
notice.description.571009=Wrong conveyor track type.
notice.solution.571009=Please contact the mos developer for handling.
notice.description.571010=Inverse solution of relative coordinate transformation failed.
notice.solution.571010=Please contact the mos developer for handling.
notice.description.571011=Teaching modes collide.
notice.solution.571011=Please contact the mos developer for handling.
notice.description.571012=The motion attribute is configured incorrectly.
notice.solution.571012=Please contact the mos developer for handling.
notice.description.571101=Abnormal Trajectory.
notice.solution.571101=Please contact the mos developer for handling.
notice.description.571102=Trajectory Planning Error.
notice.solution.571102=Please contact the mos developer for handling.
notice.description.571103=Type II online trajectory planning failed.
notice.solution.571103=Please contact the mos developer for handling.
notice.description.571104=Inverse Solution Failed.
notice.solution.571104=Please contact the mos developer for handling.
notice.description.571105=Kinetic Limit Protection
notice.solution.571105=Please contact the mos developer for handling.
notice.description.571106=Conveyor Tracking Failed.
notice.solution.571106=Please contact the mos developer for handling.
notice.description.571107=Out of working range of conveyor belt.
notice.solution.571107=Please contact the mos developer for handling.
notice.description.571108=The joint is out of range.
notice.solution.571108=Terminate the current task and remove the material from the gripper of the mechanical arm. Check the MOS error log to see which point caused the joint overrun. After modifying this point, return the mechanical arm to the original point and start the task again.
notice.description.571109=Joint Overspeed.
notice.solution.571109=Please contact the mos developer for handling.
notice.description.571110=Offline trajectory planning failed.
notice.solution.571110=Please contact the mos developer for handling.
notice.description.571200=The controller is abnormal, and the inverse solution failed.
notice.solution.571200=Please contact the mos developer for handling.
notice.description.571201=Abnormal Controller, Abnormal Status.
notice.solution.571201=Please contact the mos developer for handling.
notice.description.571300=The movement has entered the stop stage.
notice.solution.571300=Please contact the mos developer for handling.
notice.description.571401=Undefined failure of the robotic arm.
notice.solution.571401=Please contact the mos developer for handling.
notice.description.571501=The robot arm ListenNode is not started.
notice.solution.571501=Please contact the mos developer for handling.
notice.description.572100=PLC Client Exception.
notice.solution.572100=Please contact the mos developer for handling.
notice.description.572101=PLC Client Exception.
notice.solution.572101=Please contact the mos developer for handling.
notice.description.572102=PLC Client Exception.
notice.solution.572102=Please contact the mos developer for handling.
notice.description.572103=PLC Client Exception.
notice.solution.572103=Please contact the mos developer for handling.
notice.description.572104=PLC Client Exception.
notice.solution.572104=Please contact the mos developer for handling.
notice.description.572105=PLC Client Exception.
notice.solution.572105=Please contact the mos developer for handling.
notice.description.572106=PLC Client Exception.
notice.solution.572106=Please contact the mos developer for handling.
notice.description.572107=PLC Client Exception.
notice.solution.572107=Please contact the mos developer for handling.
notice.description.572108=PLC Client Exception.
notice.solution.572108=Please contact the mos developer for handling.
notice.description.572109=PLC Client Exception.
notice.solution.572109=Please contact the mos developer for handling.
notice.description.572110=PLC Client Exception.
notice.solution.572110=Please contact the mos developer for handling.
notice.description.572111=PLC Client Exception.
notice.solution.572111=Please contact the mos developer for handling.
notice.description.572112=PLC Client Exception.
notice.solution.572112=Please contact the mos developer for handling.
notice.description.572113=PLC Client Exception.
notice.solution.572113=Please contact the mos developer for handling.
notice.description.572114=PLC Client Exception.
notice.solution.572114=Please contact the mos developer for handling.
notice.description.572115=PLC Client Exception.
notice.solution.572115=Please contact the mos developer for handling.
notice.description.572116=PLC Client Exception.
notice.solution.572116=Please contact the mos developer for handling.
notice.description.572117=PLC Client Exception.
notice.solution.572117=Please contact the mos developer for handling.
notice.description.572118=PLC Client Exception.
notice.solution.572118=Please contact the mos developer for handling.
notice.description.572119=PLC Client Exception.
notice.solution.572119=Please contact the mos developer for handling.
notice.description.572120=PLC Client Exception.
notice.solution.572120=Please contact the mos developer for handling.
notice.description.572121=PLC Client Exception.
notice.solution.572121=Please contact the mos developer for handling.
notice.description.572122=PLC Client Exception.
notice.solution.572122=Please contact the mos developer for handling.
notice.description.572123=PLC Client Exception.
notice.solution.572123=Please contact the mos developer for handling.
notice.description.572124=PLC Client Exception.
notice.solution.572124=Please contact the mos developer for handling.
notice.description.572125=PLC Client Exception.
notice.solution.572125=Please contact the mos developer for handling.
notice.description.572126=PLC Client Exception.
notice.solution.572126=Please contact the mos developer for handling.
notice.description.572127=PLC Client Exception.
notice.solution.572127=Please contact the mos developer for handling.
notice.description.572128=PLC Client Exception.
notice.solution.572128=Please contact the mos developer for handling.
notice.description.572129=PLC Client Exception.
notice.solution.572129=Please contact the mos developer for handling.
notice.description.572130=PLC Client Exception.
notice.solution.572130=Please contact the mos developer for handling.
notice.description.572131=PLC Client Exception.
notice.solution.572131=Please contact the mos developer for handling.
notice.description.572132=PLC Client Exception.
notice.solution.572132=Please contact the mos developer for handling.
notice.description.572133=PLC Client Exception.
notice.solution.572133=Please contact the mos developer for handling.
notice.description.572134=PLC Client Exception.
notice.solution.572134=Please contact the mos developer for handling.
notice.description.572135=PLC Client Exception.
notice.solution.572135=Please contact the mos developer for handling.
notice.description.572136=PLC Client Exception.
notice.solution.572136=Please contact the mos developer for handling.
notice.description.572137=PLC Client Exception.
notice.solution.572137=Please contact the mos developer for handling.
notice.description.572138=PLC Client Exception.
notice.solution.572138=Please contact the mos developer for handling.
notice.description.572139=PLC Client Exception.
notice.solution.572139=Please contact the mos developer for handling.
notice.description.572150=The hardware controller is not initialized.
notice.solution.572150=Please contact the mos developer for handling.
notice.description.572200=PLC Hardware Exception.
notice.solution.572200=Please contact the mos developer for handling.
notice.description.572201=PLC Hardware Exception.
notice.solution.572201=Please contact the mos developer for handling.
notice.description.572202=PLC Hardware Exception.
notice.solution.572202=Please contact the mos developer for handling.
notice.description.572203=PLC Hardware Exception.
notice.solution.572203=Please contact the mos developer for handling.
notice.description.572204=PLC Hardware Exception.
notice.solution.572204=Please contact the mos developer for handling.
notice.description.572205=PLC Hardware Exception.
notice.solution.572205=Please contact the mos developer for handling.
notice.description.572206=PLC Hardware Exception.
notice.solution.572206=Please contact the mos developer for handling.
notice.description.572207=PLC Hardware Exception.
notice.solution.572207=Please contact the mos developer for handling.
notice.description.572208=PLC Hardware Exception.
notice.solution.572208=Please contact the mos developer for handling.
notice.description.572209=PLC Hardware Exception.
notice.solution.572209=Please contact the mos developer for handling.
notice.description.572210=PLC Hardware Exception.
notice.solution.572210=Please contact the mos developer for handling.
notice.description.572211=PLC Hardware Exception.
notice.solution.572211=Please contact the mos developer for handling.
notice.description.572212=PLC Hardware Exception.
notice.solution.572212=Please contact the mos developer for handling.
notice.description.572213=PLC Hardware Exception.
notice.solution.572213=Please contact the mos developer for handling.
notice.description.572214=PLC Hardware Exception.
notice.solution.572214=Please contact the mos developer for handling.
notice.description.572215=PLC Hardware Exception.
notice.solution.572215=Please contact the mos developer for handling.
notice.description.572216=PLC Hardware Exception.
notice.solution.572216=Please contact the mos developer for handling.
notice.description.572217=PLC Hardware Exception.
notice.solution.572217=Please contact the mos developer for handling.
notice.description.572218=PLC Hardware Exception.
notice.solution.572218=Please contact the mos developer for handling.
notice.description.572219=PLC Hardware Exception.
notice.solution.572219=Please contact the mos developer for handling.
notice.description.572220=PLC Hardware Exception.
notice.solution.572220=Please contact the mos developer for handling.
notice.description.572221=PLC Hardware Exception.
notice.solution.572221=Please contact the mos developer for handling.
notice.description.572222=PLC Hardware Exception.
notice.solution.572222=Please contact the mos developer for handling.
notice.description.572223=PLC Hardware Exception.
notice.solution.572223=Please contact the mos developer for handling.
notice.description.572224=PLC Hardware Exception.
notice.solution.572224=Please contact the mos developer for handling.
notice.description.572225=PLC Hardware Exception.
notice.solution.572225=Please contact the mos developer for handling.
notice.description.572226=PLC Hardware Exception.
notice.solution.572226=Please contact the mos developer for handling.
notice.description.572227=PLC Hardware Exception.
notice.solution.572227=Please contact the mos developer for handling.
notice.description.572228=PLC Hardware Exception.
notice.solution.572228=Please contact the mos developer for handling.
notice.description.572229=PLC Hardware Exception.
notice.solution.572229=Please contact the mos developer for handling.
notice.description.572230=PLC Hardware Exception.
notice.solution.572230=Please contact the mos developer for handling.
notice.description.572231=PLC Hardware Exception.
notice.solution.572231=Please contact the mos developer for handling.
notice.description.572232=PLC Hardware Exception.
notice.solution.572232=Please contact the mos developer for handling.
notice.description.572233=PLC Hardware Exception.
notice.solution.572233=Please contact the mos developer for handling.
notice.description.572234=PLC Hardware Exception.
notice.solution.572234=Please contact the mos developer for handling.
notice.description.572235=PLC Hardware Exception.
notice.solution.572235=Please contact the mos developer for handling.
notice.description.572236=PLC Hardware Exception.
notice.solution.572236=Please contact the mos developer for handling.
notice.description.572237=PLC Hardware Exception.
notice.solution.572237=Please contact the mos developer for handling.
notice.description.572238=PLC Hardware Exception.
notice.solution.572238=Please contact the mos developer for handling.
notice.description.572239=PLC Hardware Exception.
notice.solution.572239=Please contact the mos developer for handling.
notice.description.572240=PLC Hardware Exception.
notice.solution.572240=Please contact the mos developer for handling.
notice.description.572241=PLC Hardware Exception.
notice.solution.572241=Please contact the mos developer for handling.
notice.description.572242=PLC Hardware Exception.
notice.solution.572242=Please contact the mos developer for handling.
notice.description.572243=PLC Hardware Exception.
notice.solution.572243=Please contact the mos developer for handling.
notice.description.572244=PLC Hardware Exception.
notice.solution.572244=Please contact the mos developer for handling.
notice.description.572245=PLC Hardware Exception.
notice.solution.572245=Please contact the mos developer for handling.
notice.description.572246=PLC Hardware Exception.
notice.solution.572246=Please contact the mos developer for handling.
notice.description.572247=PLC Hardware Exception.
notice.solution.572247=Please contact the mos developer for handling.
notice.description.572248=PLC Hardware Exception.
notice.solution.572248=Please contact the mos developer for handling.
notice.description.572249=PLC Hardware Exception.
notice.solution.572249=Please contact the mos developer for handling.
notice.description.572250=PLC Hardware Exception.
notice.solution.572250=Please contact the mos developer for handling.
notice.description.572251=PLC Hardware Exception.
notice.solution.572251=Please contact the mos developer for handling.
notice.description.572252=PLC Hardware Exception.
notice.solution.572252=Please contact the mos developer for handling.
notice.description.572253=PLC Hardware Exception.
notice.solution.572253=Please contact the mos developer for handling.
notice.description.572254=PLC Hardware Exception.
notice.solution.572254=Please contact the mos developer for handling.
notice.description.572255=PLC Hardware Exception.
notice.solution.572255=Please contact the mos developer for handling.
notice.description.572256=PLC Hardware Exception.
notice.solution.572256=Please contact the mos developer for handling.
notice.description.572257=PLC Hardware Exception.
notice.solution.572257=Please contact the mos developer for handling.
notice.description.572258=PLC Hardware Exception.
notice.solution.572258=Please contact the mos developer for handling.
notice.description.572259=PLC Hardware Exception.
notice.solution.572259=Please contact the mos developer for handling.
notice.description.572260=PLC Hardware Exception.
notice.solution.572260=Please contact the mos developer for handling.
notice.description.572261=PLC Hardware Exception.
notice.solution.572261=Please contact the mos developer for handling.
notice.description.572262=PLC Hardware Exception.
notice.solution.572262=Please contact the mos developer for handling.
notice.description.572263=PLC Hardware Exception.
notice.solution.572263=Please contact the mos developer for handling.
notice.description.572264=PLC Hardware Exception.
notice.solution.572264=Please contact the mos developer for handling.
notice.description.572265=PLC Hardware Exception.
notice.solution.572265=Please contact the mos developer for handling.
notice.description.572266=PLC Hardware Exception.
notice.solution.572266=Please contact the mos developer for handling.
notice.description.572267=PLC Hardware Exception.
notice.solution.572267=Please contact the mos developer for handling.
notice.description.572268=PLC Hardware Exception.
notice.solution.572268=Please contact the mos developer for handling.
notice.description.572269=PLC Hardware Exception.
notice.solution.572269=Please contact the mos developer for handling.
notice.description.572270=PLC Hardware Exception.
notice.solution.572270=Please contact the mos developer for handling.
notice.description.572271=PLC Hardware Exception.
notice.solution.572271=Please contact the mos developer for handling.
notice.description.572272=PLC Hardware Exception.
notice.solution.572272=Please contact the mos developer for handling.
notice.description.572273=PLC Hardware Exception.
notice.solution.572273=Please contact the mos developer for handling.
notice.description.572274=PLC Hardware Exception.
notice.solution.572274=Please contact the mos developer for handling.
notice.description.572275=PLC Hardware Exception.
notice.solution.572275=Please contact the mos developer for handling.
notice.description.572276=PLC Hardware Exception.
notice.solution.572276=Please contact the mos developer for handling.
notice.description.572277=PLC Hardware Exception.
notice.solution.572277=Please contact the mos developer for handling.
notice.description.572278=PLC Hardware Exception.
notice.solution.572278=Please contact the mos developer for handling.
notice.description.572279=PLC Hardware Exception.
notice.solution.572279=Please contact the mos developer for handling.
notice.description.572280=PLC Hardware Exception.
notice.solution.572280=Please contact the mos developer for handling.
notice.description.572281=PLC Hardware Exception.
notice.solution.572281=Please contact the mos developer for handling.
notice.description.572282=PLC Hardware Exception.
notice.solution.572282=Please contact the mos developer for handling.
notice.description.572283=PLC Hardware Exception.
notice.solution.572283=Please contact the mos developer for handling.
notice.description.572284=PLC Hardware Exception.
notice.solution.572284=Please contact the mos developer for handling.
notice.description.572285=PLC Hardware Exception.
notice.solution.572285=Please contact the mos developer for handling.
notice.description.572286=PLC Hardware Exception.
notice.solution.572286=Please contact the mos developer for handling.
notice.description.572287=PLC Hardware Exception.
notice.solution.572287=Please contact the mos developer for handling.
notice.description.572288=PLC Hardware Exception.
notice.solution.572288=Please contact the mos developer for handling.
notice.description.572289=PLC Hardware Exception.
notice.solution.572289=Please contact the mos developer for handling.
notice.description.572290=PLC Hardware Exception.
notice.solution.572290=Please contact the mos developer for handling.
notice.description.572291=PLC Hardware Exception.
notice.solution.572291=Please contact the mos developer for handling.
notice.description.572292=PLC Hardware Exception.
notice.solution.572292=Please contact the mos developer for handling.
notice.description.572293=PLC Hardware Exception.
notice.solution.572293=Please contact the mos developer for handling.
notice.description.572294=PLC Hardware Exception.
notice.solution.572294=Please contact the mos developer for handling.
notice.description.572295=PLC Hardware Exception.
notice.solution.572295=Please contact the mos developer for handling.
notice.description.572296=PLC Hardware Exception.
notice.solution.572296=Please contact the mos developer for handling.
notice.description.572297=PLC Hardware Exception.
notice.solution.572297=Please contact the mos developer for handling.
notice.description.572298=PLC Hardware Exception.
notice.solution.572298=Please contact the mos developer for handling.
notice.description.572299=PLC Hardware Exception.
notice.solution.572299=Please contact the mos developer for handling.
notice.description.572300=PLC Hardware Exception.
notice.solution.572300=Please contact the mos developer for handling.
notice.description.572301=PLC Hardware Exception.
notice.solution.572301=Please contact the mos developer for handling.
notice.description.572302=PLC Hardware Exception.
notice.solution.572302=Please contact the mos developer for handling.
notice.description.572303=PLC Hardware Exception.
notice.solution.572303=Please contact the mos developer for handling.
notice.description.572304=PLC Hardware Exception.
notice.solution.572304=Please contact the mos developer for handling.
notice.description.572305=PLC Hardware Exception.
notice.solution.572305=Please contact the mos developer for handling.
notice.description.572306=PLC Hardware Exception.
notice.solution.572306=Please contact the mos developer for handling.
notice.description.572307=PLC Hardware Exception.
notice.solution.572307=Please contact the mos developer for handling.
notice.description.572308=PLC Hardware Exception.
notice.solution.572308=Please contact the mos developer for handling.
notice.description.572309=PLC Hardware Exception.
notice.solution.572309=Please contact the mos developer for handling.
notice.description.572310=PLC Hardware Exception.
notice.solution.572310=Please contact the mos developer for handling.
notice.description.572311=PLC Hardware Exception.
notice.solution.572311=Please contact the mos developer for handling.
notice.description.572312=PLC Hardware Exception.
notice.solution.572312=Please contact the mos developer for handling.
notice.description.572313=PLC Hardware Exception.
notice.solution.572313=Please contact the mos developer for handling.
notice.description.572314=PLC Hardware Exception.
notice.solution.572314=Please contact the mos developer for handling.
notice.description.572315=PLC Hardware Exception.
notice.solution.572315=Please contact the mos developer for handling.
notice.description.572316=PLC Hardware Exception.
notice.solution.572316=Please contact the mos developer for handling.
notice.description.572317=PLC Hardware Exception.
notice.solution.572317=Please contact the mos developer for handling.
notice.description.572318=PLC Hardware Exception.
notice.solution.572318=Please contact the mos developer for handling.
notice.description.572319=PLC Hardware Exception.
notice.solution.572319=Please contact the mos developer for handling.
notice.description.572320=PLC Hardware Exception.
notice.solution.572320=Please contact the mos developer for handling.
notice.description.572321=PLC Hardware Exception.
notice.solution.572321=Please contact the mos developer for handling.
notice.description.572322=PLC Hardware Exception.
notice.solution.572322=Please contact the mos developer for handling.
notice.description.572323=PLC Hardware Exception.
notice.solution.572323=Please contact the mos developer for handling.
notice.description.572324=PLC Hardware Exception.
notice.solution.572324=Please contact the mos developer for handling.
notice.description.572325=PLC Hardware Exception.
notice.solution.572325=Please contact the mos developer for handling.
notice.description.572326=PLC Hardware Exception.
notice.solution.572326=Please contact the mos developer for handling.
notice.description.572327=PLC Hardware Exception.
notice.solution.572327=Please contact the mos developer for handling.
notice.description.572328=PLC Hardware Exception.
notice.solution.572328=Please contact the mos developer for handling.
notice.description.572329=PLC Hardware Exception.
notice.solution.572329=Please contact the mos developer for handling.
notice.description.572330=PLC Hardware Exception.
notice.solution.572330=Please contact the mos developer for handling.
notice.description.572331=PLC Hardware Exception.
notice.solution.572331=Please contact the mos developer for handling.
notice.description.572332=PLC Hardware Exception.
notice.solution.572332=Please contact the mos developer for handling.
notice.description.572333=PLC Hardware Exception.
notice.solution.572333=Please contact the mos developer for handling.
notice.description.572334=PLC Hardware Exception.
notice.solution.572334=Please contact the mos developer for handling.
notice.description.572335=PLC Hardware Exception.
notice.solution.572335=Please contact the mos developer for handling.
notice.description.572336=PLC Hardware Exception.
notice.solution.572336=Please contact the mos developer for handling.
notice.description.572337=PLC Hardware Exception.
notice.solution.572337=Please contact the mos developer for handling.
notice.description.572338=PLC Hardware Exception.
notice.solution.572338=Please contact the mos developer for handling.
notice.description.572339=PLC Hardware Exception.
notice.solution.572339=Please contact the mos developer for handling.
notice.description.572340=PLC Hardware Exception.
notice.solution.572340=Please contact the mos developer for handling.
notice.description.572341=PLC Hardware Exception.
notice.solution.572341=Please contact the mos developer for handling.
notice.description.572342=PLC Hardware Exception.
notice.solution.572342=Please contact the mos developer for handling.
notice.description.572343=PLC Hardware Exception.
notice.solution.572343=Please contact the mos developer for handling.
notice.description.572344=PLC Hardware Exception.
notice.solution.572344=Please contact the mos developer for handling.
notice.description.572345=PLC Hardware Exception.
notice.solution.572345=Please contact the mos developer for handling.
notice.description.572346=PLC Hardware Exception.
notice.solution.572346=Please contact the mos developer for handling.
notice.description.572347=PLC Hardware Exception.
notice.solution.572347=Please contact the mos developer for handling.
notice.description.572348=PLC Hardware Exception.
notice.solution.572348=Please contact the mos developer for handling.
notice.description.572349=PLC Hardware Exception.
notice.solution.572349=Please contact the mos developer for handling.
notice.description.572350=PLC Hardware Exception.
notice.solution.572350=Please contact the mos developer for handling.
notice.description.572351=PLC Hardware Exception.
notice.solution.572351=Please contact the mos developer for handling.
notice.description.572352=PLC Hardware Exception.
notice.solution.572352=Please contact the mos developer for handling.
notice.description.572353=PLC Hardware Exception.
notice.solution.572353=Please contact the mos developer for handling.
notice.description.572354=PLC Hardware Exception.
notice.solution.572354=Please contact the mos developer for handling.
notice.description.572355=PLC Hardware Exception.
notice.solution.572355=Please contact the mos developer for handling.
notice.description.572356=PLC Hardware Exception.
notice.solution.572356=Please contact the mos developer for handling.
notice.description.572357=PLC Hardware Exception.
notice.solution.572357=Please contact the mos developer for handling.
notice.description.572358=PLC Hardware Exception.
notice.solution.572358=Please contact the mos developer for handling.
notice.description.572359=PLC Hardware Exception.
notice.solution.572359=Please contact the mos developer for handling.
notice.description.572360=PLC Hardware Exception.
notice.solution.572360=Please contact the mos developer for handling.
notice.description.572361=PLC Hardware Exception.
notice.solution.572361=Please contact the mos developer for handling.
notice.description.572362=PLC Hardware Exception.
notice.solution.572362=Please contact the mos developer for handling.
notice.description.572363=PLC Hardware Exception.
notice.solution.572363=Please contact the mos developer for handling.
notice.description.572364=PLC Hardware Exception.
notice.solution.572364=Please contact the mos developer for handling.
notice.description.572365=PLC Hardware Exception.
notice.solution.572365=Please contact the mos developer for handling.
notice.description.572366=PLC Hardware Exception.
notice.solution.572366=Please contact the mos developer for handling.
notice.description.572367=PLC Hardware Exception.
notice.solution.572367=Please contact the mos developer for handling.
notice.description.572368=PLC Hardware Exception.
notice.solution.572368=Please contact the mos developer for handling.
notice.description.572369=PLC Hardware Exception.
notice.solution.572369=Please contact the mos developer for handling.
notice.description.572370=PLC Hardware Exception.
notice.solution.572370=Please contact the mos developer for handling.
notice.description.572371=PLC Hardware Exception.
notice.solution.572371=Please contact the mos developer for handling.
notice.description.572372=PLC Hardware Exception.
notice.solution.572372=Please contact the mos developer for handling.
notice.description.572373=PLC Hardware Exception.
notice.solution.572373=Please contact the mos developer for handling.
notice.description.572374=PLC Hardware Exception.
notice.solution.572374=Please contact the mos developer for handling.
notice.description.572375=PLC Hardware Exception.
notice.solution.572375=Please contact the mos developer for handling.
notice.description.572376=PLC Hardware Exception.
notice.solution.572376=Please contact the mos developer for handling.
notice.description.572377=PLC Hardware Exception.
notice.solution.572377=Please contact the mos developer for handling.
notice.description.572378=PLC Hardware Exception.
notice.solution.572378=Please contact the mos developer for handling.
notice.description.572379=PLC Hardware Exception.
notice.solution.572379=Please contact the mos developer for handling.
notice.description.572380=PLC Hardware Exception.
notice.solution.572380=Please contact the mos developer for handling.
notice.description.572381=PLC Hardware Exception.
notice.solution.572381=Please contact the mos developer for handling.
notice.description.572382=PLC Hardware Exception.
notice.solution.572382=Please contact the mos developer for handling.
notice.description.572383=PLC Hardware Exception.
notice.solution.572383=Please contact the mos developer for handling.
notice.description.572384=PLC Hardware Exception.
notice.solution.572384=Please contact the mos developer for handling.
notice.description.572385=PLC Hardware Exception.
notice.solution.572385=Please contact the mos developer for handling.
notice.description.572386=PLC Hardware Exception.
notice.solution.572386=Please contact the mos developer for handling.
notice.description.572387=PLC Hardware Exception.
notice.solution.572387=Please contact the mos developer for handling.
notice.description.572388=PLC Hardware Exception.
notice.solution.572388=Please contact the mos developer for handling.
notice.description.572389=PLC Hardware Exception.
notice.solution.572389=Please contact the mos developer for handling.
notice.description.572390=PLC Hardware Exception.
notice.solution.572390=Please contact the mos developer for handling.
notice.description.572391=PLC Hardware Exception.
notice.solution.572391=Please contact the mos developer for handling.
notice.description.572392=PLC Hardware Exception.
notice.solution.572392=Please contact the mos developer for handling.
notice.description.572393=PLC Hardware Exception.
notice.solution.572393=Please contact the mos developer for handling.
notice.description.572394=PLC Hardware Exception.
notice.solution.572394=Please contact the mos developer for handling.
notice.description.572395=PLC Hardware Exception.
notice.solution.572395=Please contact the mos developer for handling.
notice.description.572396=PLC Hardware Exception.
notice.solution.572396=Please contact the mos developer for handling.
notice.description.572397=PLC Hardware Exception.
notice.solution.572397=Please contact the mos developer for handling.
notice.description.572398=PLC Hardware Exception.
notice.solution.572398=Please contact the mos developer for handling.
notice.description.572399=PLC Hardware Exception.
notice.solution.572399=Please contact the mos developer for handling.
notice.description.573100=E84 message waiting timeout.
notice.solution.573100=Please contact the mos developer for handling.
notice.description.573101=E84 Unsupported operation type.
notice.solution.573101=Please contact the mos developer for handling.
notice.description.573102=E84 Unsupported robot loading and unloading status.
notice.solution.573102=Please contact the mos developer for handling.
notice.description.573103=Error in calling mqtt _ client function.
notice.solution.573103=Please contact the mos developer for handling.
notice.description.573104=Mqtt got data timeout.
notice.solution.573104=Please contact the mos developer for handling.
notice.description.573105=Mqtt sent data error.
notice.solution.573105=Please contact the mos developer for handling.
notice.description.573106=Mqtt received data error.
notice.solution.573106=Please contact the mos developer for handling.
notice.description.573107=Mqtt received fault information.
notice.solution.573107=Please contact the mos developer for handling.
notice.description.573108=Lidar Area Switching Failed.
notice.solution.573108=Please contact the mos developer for handling.
notice.description.574100=gripper openning failed.
notice.solution.574100=Please contact the mos developer for handling.
notice.description.574101=Gripper closing failed.
notice.solution.574101=Please contact the mos developer for handling.
notice.description.574102=Gripper reset failed.
notice.solution.574102=Please contact the mos developer for handling.
notice.description.574103=Gripper 485 protocol reception failed.
notice.solution.574103=Please contact the mos developer for handling.
notice.description.574200=Distance sensor detection timeout.
notice.solution.574200=Please contact the mos developer for handling.
notice.description.574201=The sensor for detecting whether there is any material in the gripper has timed out.
notice.solution.574201=Please contact the mos developer for handling.
notice.description.574300=The camera is not connected.
notice.solution.574300=Please contact the mos developer for handling.
notice.description.574301=Camera internal error.
notice.solution.574301=Please contact the mos developer for handling.
notice.description.574302=Timeout.
notice.solution.574302=Please contact the mos developer for handling.
notice.description.574303=Unknown camera command.
notice.solution.574303=Please contact the mos developer for handling.
notice.description.574304=Index is out of range.
notice.solution.574304=Please contact the mos developer for handling.
notice.description.574305=There are too few independent variables.
notice.solution.574305=Please contact the mos developer for handling.
notice.description.574306=Invalid argument type.
notice.solution.574306=Please contact the mos developer for handling.
notice.description.574307=Invalid argument.
notice.solution.574307=Please contact the mos developer for handling.
notice.description.574308=Command not allowed.
notice.solution.574308=Please contact the mos developer for handling.
notice.description.574309=Disallowed combination.
notice.solution.574309=Please contact the mos developer for handling.
notice.description.574310=Camera Busy
notice.solution.574310=Please contact the mos developer for handling.
notice.description.574311=Not fully implemented.
notice.solution.574311=Please contact the mos developer for handling.
notice.description.574312=Not supported.
notice.solution.574312=Please contact the mos developer for handling.
notice.description.574313=The result string is too long.
notice.solution.574313=Please contact the mos developer for handling.
notice.description.574314=Effect camera ID
notice.solution.574314=Please contact the mos developer for handling.
notice.description.574315=Effect camera feature ID
notice.solution.574315=Please contact the mos developer for handling.
notice.description.574316=Different collocation names.
notice.solution.574316=Please contact the mos developer for handling.
notice.description.574317=Different Versions.
notice.solution.574317=Please contact the mos developer for handling.
notice.description.574318=Not Calibrated.
notice.solution.574318=Please contact the mos developer for handling.
notice.description.574319=Calibration failed.
notice.solution.574319=Please contact the mos developer for handling.
notice.description.574320=Effective calibration data.
notice.solution.574320=Please contact the mos developer for handling.
notice.description.574321=The given calibration position has not been reached.
notice.solution.574321=Please contact the mos developer for handling.
notice.description.574322=No start command
notice.solution.574322=Please contact the mos developer for handling.
notice.description.574323=The feature has not been trained.
notice.solution.574323=Please contact the mos developer for handling.
notice.description.574324=Feature not found.
notice.solution.574324=Please contact the mos developer for handling.
notice.description.574325=The feature is not mapped.
notice.solution.574325=Please contact the mos developer for handling.
notice.description.574326=Part location is not trained.
notice.solution.574326=Please contact the mos developer for handling.
notice.description.574327=Machine location is untrained.
notice.solution.574327=Please contact the mos developer for handling.
notice.description.574328=Effective part ID
notice.solution.574328=Please contact the mos developer for handling.
notice.description.574329=Not all features of this part have been located.
notice.solution.574329=Please contact the mos developer for handling.
notice.description.574330=Effective clamping correction of parts.
notice.solution.574330=Please contact the mos developer for handling.
notice.description.574331=Effective clamping correction of parts.
notice.solution.574331=Please contact the mos developer for handling.
notice.description.574350=Error in camera reading socket.
notice.solution.574350=Please contact the mos developer for handling.
notice.description.574351=The camera response information header does not match.
notice.solution.574351=Please contact the mos developer for handling.
notice.description.574352=Failed to parse the camera response.
notice.solution.574352=Please contact the mos developer for handling.
notice.description.574360=Camera internal reference calibration failed.
notice.solution.574360=Please contact the mos developer for handling.
notice.description.574361=Camera hand-eye calibration failed.
notice.solution.574361=Please contact the mos developer for handling.
notice.description.574362=No internal reference calibration data.
notice.solution.574362=Please contact the mos developer for handling.
notice.description.574363=No hand-eye calibration data.
notice.solution.574363=Please contact the mos developer for handling.
notice.description.574364=Camera data acquisition failed.
notice.solution.574364=Please contact the mos developer for handling.
notice.description.574365=Camera data storage failed.
notice.solution.574365=Please contact the mos developer for handling.
notice.description.574366=Failed to obtain the coordinates of feature points.
notice.solution.574366=Please contact the mos developer for handling.
notice.description.574367=The deviation between the clamping position calculated by taking photos and the clamping position of teaching is too large.
notice.solution.574367=Please contact the mos developer for handling.
notice.description.574368=Failed to create the template image.
notice.solution.574368=Please contact the mos developer for handling.
notice.description.574369=Failed to get algorithm parameters.
notice.solution.574369=Please contact the mos developer for handling.
notice.description.574370=Exception in camera image processing.
notice.solution.574370=Please contact the mos developer for handling.
notice.description.574390=Camera failed to take a picture.
notice.solution.574390=Please contact the mos developer for handling.
notice.description.574391=Camera recipe setting failed.
notice.solution.574391=Please contact the mos developer for handling.
notice.description.574392=Failed to get camera trigger parameters.
notice.solution.574392=Please contact the mos developer for handling.
notice.description.574393=Failed to save the camera project.
notice.solution.574393=Please contact the mos developer for handling.
notice.description.574394=Camera is not initialized.
notice.solution.574394=Please contact the mos developer for handling.
notice.description.574400=Failed to open the serial port of light source equipment.
notice.solution.574400=Please contact the mos developer for handling.
notice.description.574401=Abnormal reading and writing of serial port of light source equipment.
notice.solution.574401=Please contact the mos developer for handling.
notice.description.574410=The light source failed to turn on.
notice.solution.574410=Please contact the mos developer for handling.
notice.description.574411=The light source failed to turn off.
notice.solution.574411=Please contact the mos developer for handling.
notice.description.574412=Failed to obtain brightness of light source.
notice.solution.574412=Please contact the mos developer for handling.
notice.description.575100=Invalid location ID.
notice.solution.575100=Please contact the mos developer for handling.
notice.description.575101=Location detection timeout.
notice.solution.575101=Please contact the mos developer for handling.
notice.description.575200=Location lock timeout.
notice.solution.575200=Please contact the mos developer for handling.
notice.description.575201=Parking unlock timeout.
notice.solution.575201=Please contact the mos developer for handling.
notice.description.575300=No material information detection sensor.
notice.solution.575300=Please contact the mos developer for handling.
notice.description.575301=Smart Tag sensor is not connected.
notice.solution.575301=Please contact the mos developer for handling.
notice.description.575302=Smart tag read failed.
notice.solution.575302=Please contact the mos developer for handling.
notice.description.575303=Smart tag read timeout.
notice.solution.575303=Please contact the mos developer for handling.
notice.description.575304=Smart tag data is invalid.
notice.solution.575304=Please contact the mos developer for handling.
notice.description.575401=RFID sensor is not connected.
notice.solution.575401=Please contact the mos developer for handling.
notice.description.575402=RFID reading failed.
notice.solution.575402=Please contact the mos developer for handling.
notice.description.575403=RFID read timeout.
notice.solution.575403=Please contact the mos developer for handling.
notice.description.575404=Invalid RFID data.
notice.solution.575404=Please contact the mos developer for handling.
notice.description.575405=RFID request data error.
notice.solution.575405=Please contact the mos developer for handling.
notice.description.575900=Smart tag sensor failure.
notice.solution.575900=Please contact the mos developer for handling.
notice.description.575901=RFID sensor failure.
notice.solution.575901=Please contact the mos developer for handling.
notice.description.576100=Lifting column is out of motion range.
notice.solution.576100=Please contact the mos developer for handling.
notice.description.576101=Invalid control instruction.
notice.solution.576101=Please contact the mos developer for handling.
notice.description.576102=Invalid multi-axis control mode.
notice.solution.576102=Please contact the mos developer for handling.
notice.description.576103=The multi-axis device is not ready, or there is an exception.
notice.solution.576103=Please contact the mos developer for handling.
notice.description.576104=Can card is not connected.
notice.solution.576104=Please contact the mos developer for handling.
notice.description.576105=The equipment of the step shaft exceeds the travel.
notice.solution.576105=Please contact the mos developer for handling.
notice.description.576106=The multi-axis device cannot get the current position.
notice.solution.576106=Please contact the mos developer for handling.
notice.description.576107=Multi-axis device movement failed.
notice.solution.576107=Please contact the mos developer for handling.
notice.description.576108=The motion of multi-axis device was conditionally interrupted.
notice.solution.576108=Please contact the mos developer for handling.
notice.description.576109=The target point of multi-axis equipment exceeds the set limit.
notice.solution.576109=Please contact the mos developer for handling.
notice.description.576110=The motion of multi-axis equipment was manually interrupted.
notice.solution.576110=Please contact the mos developer for handling.
notice.description.576111=Single-axis device instruction blocking wait timeout, which did not stop for more than 600s s.
notice.solution.576111=Please contact the mos developer for handling.
notice.description.576201=Invalid multi-axis parameter.
notice.solution.576201=Please contact the mos developer for handling.
notice.description.576202=Multi-axis management initialization failed.
notice.solution.576202=Please contact the mos developer for handling.
notice.description.577100=JSON parsing of Yuntai task failed.
notice.solution.577100=Please contact the mos developer for handling.
notice.description.577101=The type of action performed by Yuntai is wrong.
notice.solution.577101=Please contact the mos developer for handling.
notice.description.577102=Yuntai channel error.
notice.solution.577102=Please contact the mos developer for handling.
notice.description.577103=Yuntai channel type is wrong.
notice.solution.577103=Please contact the mos developer for handling.
notice.description.577104=The name of Yuntai camera is wrong.
notice.solution.577104=Please contact the mos developer for handling.
notice.description.577105=Yuntai failed to take photos.
notice.solution.577105=Please contact the mos developer for handling.
notice.description.577106=Yuntai failed to perform video recording.
notice.solution.577106=Please contact the mos developer for handling.
notice.description.577107=Yuntai failed to perform parameter setting.
notice.solution.577107=Please contact the mos developer for handling.
notice.description.577108=Yuntai failed to perform ordinary temperature measurement.
notice.solution.577108=Please contact the mos developer for handling.
notice.description.577109=Remote copy of Yuntai image failed.
notice.solution.577109=Please contact the mos developer for handling.
notice.description.577110=Yuntai failed to obtain SGC parameters. Check whether the camera name issued by SGC corresponds.
notice.solution.577110=Please contact the mos developer for handling.
notice.description.578100=JSON parsing of sensor failed.
notice.solution.578100=Please contact the mos developer for handling.
notice.description.578101=Sensor name does not exist.
notice.solution.578101=Please contact the mos developer for handling.
notice.description.578102=Incoming G300M4 has the wrong mode.
notice.solution.578102=Please contact the mos developer for handling.
notice.description.578201=Initialization of XSLAB voiceprint sensor failed.
notice.solution.578201=Please contact the mos developer for handling.
notice.description.578202=G300M4 partial discharge sensor initialization failed.
notice.solution.578202=Please contact the mos developer for handling.
notice.description.578203=FS00802 Initialization of Fushen sensor failed.
notice.solution.578203=Please contact the mos developer for handling.
notice.description.578204=Failed to receive G300M4 data.
notice.solution.578204=Please contact the mos developer for handling.
notice.description.578205=G300M4 working mode error.
notice.solution.578205=Please contact the mos developer for handling.
notice.description.578206=The sensor failed to initialize or ran incorrectly. Please check the configuration.
notice.solution.578206=Please contact the mos developer for handling.
notice.description.579000=The system is not initialized.
notice.solution.579000=Please contact the mos developer for handling.
notice.description.579100=Canceling the task failed.
notice.solution.579100=Please contact the mos developer for handling.
notice.description.579101=Failed to pause the task.
notice.solution.579101=Please contact the mos developer for handling.
notice.description.579102=Recovery task failed.
notice.solution.579102=Please contact the mos developer for handling.
notice.description.579103=Buffer parsing error.
notice.solution.579103=Please contact the mos developer for handling.
notice.description.579104=Task not found.
notice.solution.579104=Please contact the mos developer for handling.
notice.description.579105=The task list was not updated.
notice.solution.579105=Please contact the mos developer for handling.
notice.description.579106=There are unfinished tasks.
notice.solution.579106=Please contact the mos developer for handling.
notice.description.579107=The task was manually interrupted.
notice.solution.579107=Please contact the mos developer for handling.
notice.description.579201=Invalid step type.
notice.solution.579201=Please contact the mos developer for handling.
notice.description.579202=Pose value not found.
notice.solution.579202=Please contact the mos developer for handling.
notice.description.579203=Joint value not found.
notice.solution.579203=Please contact the mos developer for handling.
notice.description.579204=Offset not found.
notice.solution.579204=Please contact the mos developer for handling.
notice.description.579205=Invalid feature ID.
notice.solution.579205=Please contact the mos developer for handling.
notice.description.579206=Invalid condition type.
notice.solution.579206=Please contact the mos developer for handling.
notice.description.579207=Invalid condition parameter.
notice.solution.579207=Please contact the mos developer for handling.
notice.description.579208=Failed to get the action list.
notice.solution.579208=Please contact the mos developer for handling.
notice.description.579209=The mechanical arm is not at the origin.
notice.solution.579209=Please contact the mos developer for handling.
notice.description.579210=Integrated lock, chassis is moving.
notice.solution.579210=Please contact the mos developer for handling.
notice.description.579211=Failed to parse the socket protocol.
notice.solution.579211=Please contact the mos developer for handling.
notice.description.620001=充电机的输出电压高于预设值(待翻译)
notice.solution.620001=请检查电压设置(待翻译)
notice.description.620002=充电桩的温度高于预设值(待翻译)
notice.solution.620002=请检查刷块(待翻译)
notice.description.620004=充电桩的输入电压高于或低于输入电压范围(待翻译)
notice.solution.620004=请检查输入接线(待翻译)
notice.description.620005=充电桩的输出短路(待翻译)
notice.solution.620005=请检查输出端(待翻译)
notice.description.620006=充电桩的风扇故障(待翻译)
notice.solution.620006=请检查模块风扇(待翻译)
notice.description.620007=充电桩的输出电流高于预设值(待翻译)
notice.solution.620007=请检查电流设置(待翻译)
notice.description.620008=刷块温度过高(待翻译)(待翻译)
notice.solution.620008=请检查散热系统(待翻译)
notice.description.610001=发射端逆变电流超限(待翻译)
notice.solution.610001=将距离控制到合理范围及检查线圈(待翻译)
notice.description.610002=发射端逆变电流突变(待翻译)
notice.solution.610002=如果周期出现，送回返修(待翻译)
notice.description.610003=发射端无线通信掉线(待翻译)
notice.solution.610003=如果周期出现或者频繁出现则检查天线是否松动(待翻译)
notice.description.610004=发射端输入母线电压过高(待翻译)
notice.solution.610004=如果频繁出现，送回返修(待翻译)
notice.description.610005=发射端输入母线电压过低(待翻译)
notice.solution.610005=如果频繁出现，送回返修(待翻译)
notice.description.610006=发射端 FAULT 保护(待翻译)
notice.solution.610006=如果频繁出现，送回返修(待翻译)
notice.description.610007=发射端温度过高(待翻译)
notice.solution.610007=检查风扇是否正常转动(待翻译)
notice.description.610008=发射端外部命令停止使能(待翻译)
notice.solution.610008=无(待翻译)
notice.description.610009=发射端判断电压达到无电流(待翻译)
notice.solution.610009=无(待翻译)
notice.description.610010=发射端判定充满停机(待翻译)
notice.solution.610010=无(待翻译)
notice.description.610011=发射端电源模块故障(待翻译)
notice.solution.610011=如果频繁出现，送回返修(待翻译)
notice.description.610012=发射端耦合度测试不合格(待翻译)
notice.solution.610012=(待翻译)
notice.description.610013=发射端母线电流硬件保护(待翻译)
notice.solution.610013=如果频繁出现，送回返修(待翻译)
notice.description.610014=发射端母线电压硬件保护(待翻译)
notice.solution.610014=如果频繁出现，送回返修(待翻译)
notice.description.610015=发射端交流接触器异常停止(待翻译)
notice.solution.610015=(待翻译)
notice.description.610016=发射端线圈/导轨电流异常(待翻译)
notice.solution.610016=检查线圈间距离是否在规定范围内(待翻译)
notice.description.610017=TX导轨/线圈电流过流(待翻译)
notice.solution.610017=检查线圈间距离是否在规定范围内(待翻译)
notice.description.610018=发射端充电超时(待翻译)
notice.solution.610018=无(待翻译)
notice.description.610019=发射端FAULT保护1(待翻译)
notice.solution.610019=如果频繁出现，送回返修(待翻译)
notice.description.610020=发射端FAULT保护2(待翻译)
notice.solution.610020=如果频繁出现，送回返修(待翻译)
notice.description.610021=发射端FAULT保护3(待翻译)
notice.solution.610021=如果频繁出现，送回返修(待翻译)
notice.description.610022=发射端FAULT保护4(待翻译)
notice.solution.610022=如果频繁出现，送回返修(待翻译)
notice.description.610023=发射端FAULT保护5(待翻译)
notice.solution.610023=如果频繁出现，送回返修(待翻译)
notice.description.610024=发射端FAULT保护6(待翻译)
notice.solution.610024=如果频繁出现，送回返修(待翻译)
notice.description.610025=发射端温度保护1(待翻译)
notice.solution.610025=如果频繁出现，送回返修(待翻译)
notice.description.610026=发射端温度保护2(待翻译)
notice.solution.610026=如果频繁出现，送回返修(待翻译)
notice.description.610027=发射端温度保护3(待翻译)
notice.solution.610027=如果频繁出现，送回返修(待翻译)
notice.description.610028=发射端温度保护4(待翻译)
notice.solution.610028=如果频繁出现，送回返修(待翻译)
notice.description.610029=发射端温度保护5(待翻译)
notice.solution.610029=如果频繁出现，送回返修(待翻译)
notice.description.610030=发射端温度保护6(待翻译)
notice.solution.610030=如果频繁出现，送回返修(待翻译)
notice.description.610031=TX导轨/线圈电流电流过低(待翻译)
notice.solution.610031=无(待翻译)
notice.description.610032=调度系统命令停止(待翻译)
notice.solution.610032=请确认调度系统终止放电原因(待翻译)
notice.description.610101=接收端输出过压(待翻译)
notice.solution.610101=检查充电机接收端是否空载(待翻译)
notice.description.610102=接收端输出过流(待翻译)
notice.solution.610102=更换电池重试，排查电池问题，查看上位机设置值(待翻译)
notice.description.610103=接收端短路保护(待翻译)
notice.solution.610103=万用表测量输出两端是否短路(待翻译)
notice.description.610104=接收端判断充满停止(待翻译)
notice.solution.610104=无(待翻译)
notice.description.610105=接收端温度过高(待翻译)
notice.solution.610105=查看风扇是否正常转动(待翻译)
notice.description.610106=接收端输入电压过低(待翻译)
notice.solution.610106=如果频繁出现，送回返修(待翻译)
notice.description.610107=接收端外部命令停止(待翻译)
notice.solution.610107=无(待翻译)
notice.description.610108=接收端电池故障停止(待翻译)
notice.solution.610108=设置开启充电(待翻译)
notice.description.610109=接收端硬件输出过压(待翻译)
notice.solution.610109=检查是否突然断载(待翻译)
notice.description.610110=接收端硬件输出过流(待翻译)
notice.solution.610110=检查电池(待翻译)
notice.description.610111=接收端硬件短路保护(待翻译)
notice.solution.610111=万用表量取是否断路(待翻译)
notice.description.610112=接收端 BMS未使能(待翻译)
notice.solution.610112=设置开启充电(待翻译)
notice.description.610113=接收端风扇故障(待翻译)
notice.solution.610113=检查风扇(待翻译)
notice.description.300108=急停触发时间过长(待翻译)
notice.solution.300108=请检查机器人状态(待翻译)
notice.description.300402=避让计算时间过长(待翻译)
notice.solution.300402=请检查点位配置(待翻译)
notice.description.300109=分配充电点/泊车点时间过长(待翻译)
notice.solution.300109=请检查充电点/泊车点配置(待翻译)
notice.description.300110=申请点位/区域时间过长(待翻译)
notice.solution.300110=请检查点位/区域配置(待翻译)
notice.description.300403=避让次数异常(待翻译)
notice.solution.300403=请联系Fleet开发人员处理(待翻译)
log.export.interfaceLog.excelName=Interface Log
log.export.operationLog.excelName=Operation Log
log.operation.description.success=Success
log.operation.description.fail=Fail
log.third.system.operator=Third party system
log.controller.api.task.create=[API]Create Task
log.controller.api.task.cancel=[API]Cancel Task
log.controller.api.task.overNode=[API]Skip task node
log.controller.api.traffic.occupy=[API]Apply traffic area
log.controller.api.traffic.release=[API]Release traffic area
log.controller.api.vehicle.operation=[API]Operate vehicle
log.controller.api.vehicle.globalPause=[API]Global Pause
log.controller.api.vehicle.globalResume=[API]Global Resume
log.controller.language.delete=Delete Language
log.controller.language.import=Import Language
log.controller.language.export=Export Language
log.controller.language.switch=Switch Language
log.controller.license.upload=Upload License
log.controller.license.delete=Delete License
log.controller.operationLog.delete=Delete Operation Log
log.controller.sysLog.delete=Delete System Log
log.controller.airShowerDoor.insert=Add Air Shower Door
log.controller.airShowerDoor.update=Edit Air Shower Door
log.controller.airShowerDoor.delete=Delete Air Shower Door
log.controller.airShowerDoor.open=Open Air Shower Door
log.controller.airShowerDoor.close=Close Air Shower Door
log.controller.autoDoor.insert=Add Auto Door
log.controller.autoDoor.update=Edit Auto Door
log.controller.autoDoor.delete=Delete Auto Door
log.controller.autoDoor.open=Open Auto Door
log.controller.autoDoor.close=Close Auto Door
log.controller.elevator.insert=Add Elevator
log.controller.elevator.update=Edit Elevator
log.controller.elevator.delete=Delete Elevator
log.controller.elevator.import=Import Elevator
log.controller.elevator.export=Export Elevator
log.controller.elevator.open=Open Elevator
log.controller.elevator.close=Close Elevator
log.controller.mapArea.insert=Add Area
log.controller.mapArea.enable=Enable Area
log.controller.mapArea.disable=Disable Area
log.controller.mapArea.update=Edit Element
log.controller.mapArea.delete=Delete Element
log.controller.marker.insert=Add Marker
log.controller.marker.transcribe=Record Marker
log.controller.marker.update=Edit Element
log.controller.marker.delete=Delete Element
log.controller.path.insert=Add Path
log.controller.path.update=Edit Element
log.controller.path.delete=Delete Element
log.controller.vehicleMap.insert=Add Map
log.controller.vehicleMap.update=Edit Map
log.controller.vehicleMap.delete=Delete Map
log.controller.vehicleMap.batchDelete=Batch Delete Map
log.controller.vehicleMap.deleteDraft=Delete Map Draft
log.controller.vehicleMap.batchGenerateElement=Batch Generate Marker and Path
log.controller.vehicleMap.batchUpdateElement=Edit Element
log.controller.vehicleMap.batchDeleteElement=Delete Element
log.controller.vehicleMap.import=Import Map
log.controller.vehicleMap.export=Export Map
log.controller.vehicleMap.copy=Copy Map
log.controller.vehicleMap.pause=Map Global Pause
log.controller.vehicleMap.recover=Map Global Resume
log.controller.vehicleMap.publish=PublishMap
log.controller.vehicleMap.locatingMap.update=Edit Location Map
log.controller.vehicleMap.locatingMap.import=Import Location Map
log.controller.vehicleMap.locatingMap.changeDefault=Switch Default Location Map
log.controller.vehicleMap.locatingMap.delete=Delete Locating Map
log.controller.noticeConfig.insert=Add Notice Config
log.controller.noticeConfig.update=Edit Notice Config
log.controller.noticeConfig.delete=Delete Notice Config
log.controller.noticeConfig.export=Export Notice Config
log.controller.noticeConfig.import=Import Notice Config
log.controller.noticeRecord.insert=Add Notice Record
log.controller.noticeRecord.update=Edit Notice Record
log.controller.noticeRecord.delete=Delete Notice Record
log.controller.noticeRecord.activation=Activation Notice Record
log.controller.noticeRecord.ignore=Ignore Notice Record
log.controller.noticeRecord.ignoreVehicle=Ignore Notice Record About Vehicle
log.controller.noticeRecord.export=Export Notice Record
log.controller.pda.config=(PDA)Configure Version Info
log.controller.pda.containerEnter=(PDA)Container Enter
log.controller.pda.containerExit=(PDA)Container Exit
log.controller.pda.execute=(PDA)Create Task
log.controller.security.login=Login System
log.controller.security.logout=Logout System
log.controller.sys.menu.insert=Add Menu
log.controller.sys.menu.update=Edit Menu
log.controller.sys.menu.delete=Delete Menu
log.controller.sys.role.insert=Add Role
log.controller.sys.role.update=Edit Role
log.controller.sys.role.delete=Delete Role
log.controller.sys.property.batchUpdate=Batch Modify System Property
log.controller.sys.property.insert=Add System Property
log.controller.sys.property.update=Edit System Property
log.controller.sys.property.delete=Delete System Property
log.controller.sys.user.password=Edit Password
log.controller.sys.user.password.reset=Reset Password
log.controller.sys.user.insert=Add User
log.controller.sys.user.update=Edit User
log.controller.sys.user.delete=Delete User
log.controller.task.nodeConfig.insert=Add Node Config
log.controller.task.nodeConfig.update=Edit Node Config
log.controller.task.nodeConfig.delete=Delete Node Config
log.controller.task.nodeConfig.batchCommon=Set Node Common
log.controller.task.nodeConfig.export=Export Node Config
log.controller.task.nodeConfig.import=Import Node Config
log.controller.task.insert=Add Task
log.controller.task.cancel=Cancel Task
log.controller.task.delete=Delete Task
log.controller.task.skip=Skip Task Node
log.controller.task.retry=Retry Task Node
log.controller.task.batchCancel=Batch Cancel Task
log.controller.task.cancelAll=One Key Cancel All Task
log.controller.task.export=Export Task Record
log.controller.task.import=Import Task Record
log.controller.task.remark=Remark Task Remark
log.controller.task.type.insert=Add Task Type
log.controller.task.type.update=Edit Task Type
log.controller.task.type.copy=Copy Task Type
log.controller.task.type.delete=Delete Task Type
log.controller.task.type.enable=Enable Task Type
log.controller.task.type.disable=Disable Task Type
log.controller.task.type.export=Export Task Type
log.controller.task.type.import=Import Task Type
log.controller.vehicle.stop.open=Pause Vehicle
log.controller.vehicle.stop.close=Recover Vehicle
log.controller.vehicle.delete=Delete Vehicle
log.controller.vehicle.restart=Restart Vehicle
log.controller.vehicle.shutdown=Shutdown Vehicle
log.controller.vehicle.controls.manualMode=Switch Manual Control
log.controller.vehicle.controls.autoMode=Switch Auto Control
log.controller.vehicle.scheduler.manualMode=Switch Manual Schedule
log.controller.vehicle.scheduler.autoMode=Switch Auto Scheduler
log.controller.vehicle.update=Edit Robot Config
log.controller.vehicle.updateBatch=Batch Edit Robot config
log.controller.vehicle.updateGroupBatch=Batch Edit Robot group
log.controller.vehicle.updateTypeBatch=Batch Edit Robot type
log.controller.vehicle.resource.clear=Clear Robot resource
log.controller.vehicle.reset=Reset Robot
log.controller.vehicle.dockingReset=Docking Reset
log.controller.vehicle.closeSoundLightAlarm=Close sound and light alarm
log.controller.vehicle.charge=Common Charge
log.controller.vehicle.group.insert=Add Robot Group
log.controller.vehicle.group.update=Edit Robot Group
log.controller.vehicle.group.delete=Delete Robot Group
log.controller.vehicle.group.export=Export Robot Group
log.controller.vehicle.group.import=Import Robot Group
log.controller.vehicle.type.insert=Insert Robot Type
log.controller.vehicle.type.update=Edit Robot Type
log.controller.vehicle.type.delete=Delete Robot Type
log.controller.vehicle.type.export=Export Robot Type
log.controller.vehicle.type.import=Import Robot Type
log.controller.vehicle.map.appoint=Assign Map
log.controller.vehicle.map.relocation=Relocation Robot
log.controller.warehouse.area.insert=Import Storage Location Area
log.controller.warehouse.area.update=Edit Storage Location Area
log.controller.warehouse.area.delete=Delete Storage Location Area
log.controller.warehouse.area.export=Export Storage Location Area
log.controller.warehouse.area.import=Import Storage Location Area
log.controller.warehouse.type.insert=Insert Storage Location Type
log.controller.warehouse.type.update=Update Storage Location Type
log.controller.warehouse.type.delete=Delete Storage Location Type
log.controller.warehouse.type.export=Export Storage Location Type
log.controller.warehouse.type.import=Import Storage Location Type
log.controller.warehouse.insert=Add Storage Location
log.controller.warehouse.batchInsert=Batch Add Storage Location
log.controller.warehouse.update=Edit Storage Location
log.controller.warehouse.delete=Delete Storage Location
log.controller.warehouse.enable=Enable Storage Location
log.controller.warehouse.disable=Disable Storage Location
log.controller.warehouse.export=Export Storage Location
log.controller.warehouse.import=Import Storage Location
log.controller.event.insert=Add Event
log.controller.event.update=Edit Event
log.controller.event.copy=Copy Event
log.controller.event.delete=Delete Event
log.controller.event.enable=Enable Event
log.controller.event.disable=Disable Event
log.controller.event.export=Export Event
log.controller.event.import=Import Event
log.controller.charge.station.update=Update charge station
log.controller.charge.station.delete=Delete charge station
log.controller.charge.station.enable=Enable charge station
log.controller.charge.station.disable=Disable charge station
log.controller.charge.station.reset=Reset charge station
log.controller.charge.station.stopCharge=Stop Charging
log.operation.excel.head.operator=User
log.operation.excel.head.description=Operation
log.operation.excel.head.success=Result
log.operation.excel.head.errorMsg=Fail Reason
log.operation.excel.head.wasteTime=Response Time
log.operation.excel.head.ip=Client Ip Address
log.operation.excel.head.paramsIn=Request Info
log.operation.excel.head.paramsOut=Response Info
log.operation.excel.head.operationTime=Operation Time
log.interface.excel.head.description=Description
log.interface.excel.head.success=Result
log.interface.excel.head.errorMsg=Fail Reason
log.interface.excel.head.wasteTime=Response Time
log.interface.excel.head.url=URL
log.interface.excel.head.paramsIn=Request Info
log.interface.excel.head.paramsOut=Response Info
log.interface.excel.head.operationTime=Operation Time
log.system.module.task=Task
log.system.module.task.allocation=Task Allocation
log.system.module.charge.allocation=Charging Allocation
log.system.module.park.allocation=Parking Allocation
log.system.module.resource.apply=Resource Apply
log.system.module.traffic.avoid=Traffic Avoid
log.system.module.vehicle=Robot
log.system.module.event=Event
log.system.module.system=System
log.system.module.autodoor=Auto Door
log.system.module.airshowerdoor=Air Shower Door
log.system.module.elevator=Elevator
log.system.module.charge.station=Charge station
log.system.type.Running=Running Log
log.system.type.Warning=Warning Log
log.system.type.Error=Error Log
log.system.system.start=The scheduling system has started successfully
log.system.path.plan.is.unreachable=Robot movement node, robot  failed to plan path, target point  is unreachable
log.system.instruction.status.upload=Robot  feedback instruction status
log.system.resource.elevator.apply.success=Robot  applied for elevator  to open successfully
log.system.resource.elevator.ride.success=Elevator  completed lifting, door opened successfully
log.system.resource.vehicle.clear.resource=Robot  network connection timed out, automatically clearing resources occupied by the robot
log.system.vehicle.connect=Robot  connected to scheduling system
log.system.vehicle.disconnect=Robot  disconnected from scheduling system
log.system.vehicle.out.of.trace=Out of Trace
log.system.vehicle.on.trace=Not Out of Trace
log.system.vehicle.pause.close=Pause Closed
log.system.vehicle.pause.open=Pause Opened
log.system.vehicle.close.stop=Robot  emergency stop button restored
log.system.vehicle.open.stop=Robot  emergency stop button pressed
log.system.vehicle.manual.control=Robot  switched to manual control mode
log.system.vehicle.auto.control=Robot  switched to automatic control mode
log.system.vehicle.repair.control=机器人已被切换为检修控制模式（待翻译）
log.system.vehicle.work.status.work=Work
log.system.vehicle.work.status.free=Free
log.system.vehicle.connect.status.connect=Connect
log.system.vehicle.connect.status.disconnect=Disconnected
log.system.vehicle.control.status.manual=Manual
log.system.vehicle.control.status.auto=Auto
log.system.vehicle.control.status.repair=Repair
log.system.vehicle.abnormal.status.abnormal=Abnormal
log.system.vehicle.abnormal.status.normal=Normal
log.system.vehicle.position.status.notLocated=Not Located
log.system.vehicle.position.status.located=Located
log.system.charge.scheduler.error=Robot charging scheduling encountered an exception
log.system.charge.create.task.success=Robot  created charging task  successfully, current battery level , charging point
log.system.charge.create.task.fail=Robot  failed to create charging task, task flow [-]
log.system.charge.vehicle.disable=Robot  is not enabled for automatic charging
log.system.charge.battery.value.is.null=Robot  current battery level is null
log.system.charge.no.usable.charge.marker=Robot  unable to obtain charging point, blocked charging points , occupied charging points , unreachable charging points
log.system.charge.get.other.charge.marker=Robot  low battery , preempting robot  charging point
log.system.park.scheduler.error=Robot parking scheduling encountered an exception
log.system.park.vehicle.disable=Robot  is not enabled for automatic parking
log.system.park.no.usable.park.marker=Robot  unable to obtain parking point, blocked parking points , occupied parking points , unreachable parking points
log.system.park.create.task.success=Robot  created parking task  successfully, parking point
log.system.park.create.task.fail=Robot  failed to create parking task, task flow [-]
log.system.traffic.marker.is.not.avaliable.error=Robot  cannot go to avoidance marker , replanning path
log.system.traffic.resource.conflict=Conflict between , robot  replanning path to marker
log.system.traffic.detect.obstacle=Robot  detected obstacle, start replanning path
log.system.traffic.detect.vehicle=Robot  detected obstacle ahead, start replanning path
log.system.traffic.detect.control.area=Robot  encountered controlled area , start replanning path
log.system.traffic.detect.map.publish=Robot  replanning path, user manually published map
log.system.traffic.detect.vehicle.error=Path navigation detected obstacle ahead, triggered detour error, robot
log.system.traffic.detect.vehicle.drive=Robot  detected obstacle ahead, driving away robot  to marker
log.system.traffic.detect.vehicle.drive.error=Path navigation detected obstacle ahead, triggered driving away error, robot
log.system.auto.door.thread.error=Automatic door program encountered an exception
log.system.auto.door.connect.error=Failed to connect to automatic door
log.system.auto.door.no.bind.path.error=Automatic door  is not bound to a path
log.system.auto.door.read.error=Failed to read command from automatic door
log.system.auto.door.write.error=Failed to write command to automatic door
log.system.auto.door.open.ok=Automatic door  opened successfully
log.system.auto.door.close.ok=Automatic door  closed successfully
log.system.air.shower.thread.error=Air shower door program encountered an exception
log.system.air.shower.no.bind.path.error=Air shower door  is not bound to a path
log.system.air.shower.connect.error=Failed to connect to air shower door
log.system.air.shower.read.error=Failed to read command from air shower door
log.system.air.shower.write.error=Failed to write command to air shower door
log.system.air.shower.open.ok=Air shower door  opened successfully
log.system.air.shower.close.ok=Air shower door  closed successfully
log.system.elevator.thread.error=Elevator program encountered an exception
log.system.elevator.no.bind.path.error=Elevator  is not bound to points
log.system.elevator.vehicle.leave=Robot  left elevator  successfully used
log.system.elevator.vehicle.apply.run=Robot  applied for elevator  lift
log.system.elevator.connect.error=Failed to connect to elevator
log.system.elevator.read.error=Failed to read command from elevator
log.system.elevator.write.error=Failed to write command to elevator
log.system.task.allocation.error=Robot task scheduling encountered an exception
log.system.task.allocation.cancel.old.task.success=Task has been interrupted, new task  is being bound to robot
log.system.task.allocation.interrupt.current.task=Task dispatch to robot, interrupt robot's current running task
log.system.task.allocation.success=Task  allocated successfully, robot
log.system.task.allocation.fail.vehicle.no.exist=Failed to allocate specified robot, robot  does not exist
log.system.task.allocation.fail.vehicle.state.error=Failed to allocate specified robot, robot  state does not match, current state is
log.system.task.allocation.fail.vehicle.locked=Failed to allocate specified robot, robot  is already occupied by task
log.system.task.start.run=Task started execution
log.system.task.run.error=Task  execution encountered an error
log.system.task.run.finish=Task  execution completed
log.system.task.cancel.run=Task  execution canceled
log.system.task.status.change.callback.request.param=Task  status change callback request param
log.system.task.status.change.callback.response.param=Task  status change callback response param
log.system.node.start=Node started execution
log.system.node.end=Node completed execution
log.system.node.cancel=Node execution canceled
log.system.node.error=Node execution encountered an error
log.system.node.no.available.marker=Failed to allocate marker, no available markers
log.system.node.start.send=Node execution, robot  started sending commands
log.system.node.send.succeed=Node execution, robot  command sent successfully
log.system.node.send.error=Node execution, robot  failed to send command
log.system.node.run.succeed=Node execution, robot  completed the command
log.system.node.run.error=Node execution, robot  failed to execute the command
log.system.node.start.send.cancel=Node execution, robot  started sending stop command
log.system.node.send.cancel.succeed=Node execution, robot  stop command sent successfully
log.system.node.send.cancel.error=Node execution, robot  failed to send stop command
log.system.node.stop.succeed=Node execution, robot  stop command succeeded
log.system.node.stop.error=Node execution, robot  failed to execute stop command
log.system.node.cancel.timeout=Node execution, robot  stop command timed out, system forcefully stopped the task
log.system.node.button.release.succeed=Release button node, device  released successfully
log.system.node.button.release.error=Release button node encountered an exception, unable to connect to device
log.system.node.button.reset.succeed=Reset button node execution succeeded, device
log.system.node.button.reset.error=Reset button node execution encountered an exception, unable to connect to device
log.system.node.vehicle.no.assign.error=Node execution, robot  not assigned lock
log.system.node.vehicle.no.exist.error=Node execution, robot  does not exist
log.system.node.vehicle.disconnect.error=Robot movement node, robot  state verification failed, robot disconnected
log.system.node.vehicle.abnormal.error=Robot movement node, robot  state verification failed, robot has abnormality
log.system.node.vehicle.state.error=Node execution, robot  state does not match
log.system.node.vehicle.move.state.change.re.pathplan=Robot movement node, robot  state changed, re-planning path
log.system.node.vehicle.move.send.instruction=Robot movement node, robot  sent move command
log.system.node.vehicle.move.finish=Robot movement node, robot  move command completed
log.system.node.vehicle.move.error=Robot movement node, robot  move command execution failed
log.system.node.vehicle.move.send.cancel=Robot movement node, sending cancel move command
log.system.node.vehicle.move.send.cancel.fail=Robot movement node, failed to send cancel move command
log.system.node.vehicle.move.cancel.success=Robot movement node, move command canceled successfully
log.system.node.vehicle.move.cancel.fail=Robot movement node, move command cancel failed
log.system.node.vehicle.move.no.position=Robot movement node, robot  has no position data
log.system.node.vehicle.move.stay.tartget.marker=Robot movement node, robot  is already at the target point
log.system.node.vehicle.move.pathplan.start=Robot movement node, robot  starts path planning
log.system.node.vehicle.move.pathplan.success=Robot movement node, robot  path planning succeeded
log.system.node.vehicle.move.pathplan.message=Robot movement node, Generated path planning data
log.system.node.vehicle.move.pathplan.cancel=Robot movement node, Detected path navigation cancellation
log.system.node.vehicle.move.re.pathplan=Robot movement node, robot  is not on the planned path, re-planning the path
log.system.node.vehicle.move.stop.button=Robot movement node, robot  has been stopped by emergency stop button
log.system.node.set.variable.param.is.empty=Set variable node, cannot find the variable
log.system.node.set.variable.param.change.error=Set variable node, variable  is not number, cannot be change to be number
log.system.node.set.variable.param.source.is.unknown=Set variable node, the source  of the variable  is unknown
log.system.node.set.variable.param.is.change=Set variable node, the value of variable  is changed from  to
log.system.node.finish.request.param=Finish task node, The request param of http
log.system.node.finish.response.param=Finish task node, The response data of http
log.system.node.http.request.param=Http task node, The request param of http
log.system.node.http.response.param=Http task node, The response data of http
log.system.node.http.check.param=Description HTTP parity node is missing
log.system.node.http.check.path=Description HTTP parameter verification failed because no corresponding field was found
log.system.node.http.check.fail=HTTP parameter verification fails
log.system.trigger.callbox.success=Triggered call box  button  event successfully, created task
log.system.trigger.callbox.fail=Failed to trigger call box  button  event, task flow
log.system.trigger.fix.success=Triggered fixed-time event successfully, created task
log.system.trigger.fix.fail=Failed to trigger fixed-time event, task flow
log.system.trigger.plc.success=Triggered PLC event successfully, created task
log.system.trigger.plc.fail=Failed to trigger PLC event, task flow
log.system.trigger.task.cancel.success=Triggered task  cancellation event successfully, created task
log.system.trigger.task.cancel.fail=Failed to trigger task  cancellation event, task flow
log.system.trigger.task.finish.success=Triggered task  completion event successfully, created task
log.system.trigger.task.finish.fail=Failed to trigger task  completion event, task flow
log.system.trigger.vehicle.abnormal.success=Triggered robot  abnormal event successfully, created task
log.system.trigger.vehicle.abnormal.fail=Failed to trigger robot  abnormal event, task flow
log.system.trigger.vehicle.plc.success=Triggered robot  PLC event successfully, created task
log.system.trigger.vehicle.plc.fail=Failed to trigger robot  PLC event, task flow
log.system.export.error=The quantity exported at a time cannot exceed a fixed quantity. Please re filter
log.system.export.name=Running Log
log.system.download.file.not.exist=Cannot find the file:
log.system.download.file.error=Failed to download the file
log.system.excel.head.module=Type
log.system.excel.head.type=Level
log.system.excel.head.content=Description
log.system.excel.head.data=Data
log.system.excel.head.message=Body
log.system.excel.head.vehicleCodes=Robots
log.system.excel.head.taskNos=Task
log.system.excel.head.createDate=Create Date
log.system.excel.head.lastTime=Last Update Time
log.system.charge.station.connect=Charging station successfully connected
log.system.charge.station.disconnect=Disconnecting the charging station
log.system.charge.station.operate=Operation results of charging station
log.system.charge.station.cancel.charge.task=由于充电被强制停止，节点取消执行[未翻译]
validation.id.require=ID can not be empty
validation.id.null=ID has to be empty
validation.pid.require=Parent ID, cannot be empty
validation.sort.number=The sort value cannot be less than 0
validation.sysparams.paramcode.require=Parameter encoding cannot be empty
validation.sysparams.paramvalue.require=Parameter values cannot be empty
validation.sysuser.username.require=The username cannot be empty
validation.sysuser.password.require=The password cannot be empty
validation.sysuser.realname.require=The realname cannot be empty
validation.sysuser.email.require=Mailbox cannot be empty
validation.sysuser.email.error=Incorrect email format
validation.sysuser.mobile.require=The phone number cannot be empty
validation.sysuser.superadmin.range=Super administrator values range from 0 to 1
validation.sysuser.status.range=State ranges from 0 to 1
validation.sysmenu.pid.require=Please select superior menu
validation.sysmenu.name.require=Menu name cannot be empty
validation.sysmenu.type.range=Menu type ranges from 0 to 1
validation.sysrole.name.require=The role name cannot be empty
validation.schedule.status.range=Status ranges from 0 to 1
validation.schedule.cron.require=Cron expression cannot be empty
validation.schedule.bean.require=Bean name cannot be empty
validation.news.title.require=The title cannot be empty
validation.news.content.require=Content cannot be empty
validation.news.pubdate.require=The release time cannot be empty
validation.map.marker.name=The marker name can be null, or consist of char,digit,underline,and startWith char, the length of name must not be more then 20
validation.map.marker.type.require=The marker type cannot be empty
validation.map.marker.code.require=The marker code cannot be empty
validation.map.marker.type=The marker type must be in (ChargingMarker,NavigationMarker,WorkMarker)
validation.map.marker.x.require=The marker X coordinate cannot be empty
validation.map.marker.y.require=The marker Y coordinate cannot be empty
validation.map.path.type.require=The path type cannot be empty
validation.map.path.type=The path type must be in (Common、QR_Down、Shelflegs、Symbol_V、Reflector、LeaveDocking、Pallet)
validation.map.path.startMarkerCode.require=The startMarkerId of path cannot be empty
validation.map.path.endMarkerCode.require=The endMarkerId of path cannot be empty
validation.map.path.weightRatio.require=The value of weightRatio must be Positive
validation.map.area.areaType.require=The areaType cannot be empty, the value must be in(SingleAgvArea、ShowArea、ControlArea、ChannelArea、NoRotatingArea、NoParkingArea)
validation.map.area.areaType=The areaType must be in (SingleAgvArea、ShowArea、ControlArea、ChannelArea、NoRotatingArea、NoParkingArea)
validation.map.area.polygon.require=The polygon cannot be empty
validation.map.area.operateType.require=The operateType cannot be empty, the value must be in（1: polygon、2:Rectangle Area）
validation.map.type.require=The map type cannot be empty
validation.map.code.require=The map code cannot be empty
validation.map.name.require=The map name cannot be empty
validation.map.originX.require=The map originX cannot be empty
validation.map.originY.require=The map originY cannot be empty
validation.map.resolution.require=The map resolution cannot be empty
validation.map.height.require=The map height cannot be empty
validation.map.width.require=The map width cannot be empty
validation.door.code.require=The door code cannot be empty
validation.door.ip.require=The door ip cannot be empty
validation.door.port.require=The door port cannot be empty
validation.door.openAddress.require=The door openAddress cannot be empty
validation.door.openStatusAddress.require=The door openStatusAddress cannot be empty
validation.door.closeAddress.require=The door closeAddress cannot be empty
validation.door.closeStatusAddress.require=The door closeStatusAddress cannot be empty
validation.door.pathCodes.require=The door pathCodes cannot be empty
validation.elevator.code.require=The elevator code cannot be empty
validation.elevator.ip.require=The elevator ip cannot be empty
validation.elevator.port.require=The elevator port cannot be empty
validation.elevator.controlAddress.require=The elevator controlAddress cannot be empty
validation.elevator.destAddress.require=The elevator destAddress cannot be empty
validation.elevator.openAddress.require=The elevator openAddress cannot be empty
validation.elevator.readFunctionCode.require=The elevator readFunctionCode cannot be empty
validation.elevator.writeFunctionCode.require=The elevator writeFunctionCode cannot be empty
validation.property.type.require=The property type cannot be empty
validation.property.category.require=The property category cannot be empty
validation.property.propertyKey.require=The property propertyKey cannot be empty
validation.property.valueType.require=The property valueType cannot be empty
vehicleMap.airShowerDoor.not.exist.error=Operation failed, air shower door [%s] does not exist
vehicleMap.airShowerDoor.add.error=Abnormal addition of air shower door, please check the operation log
vehicleMap.airShowerDoor.update.error=Abnormal modification of air shower door [%s], please check the operation log
vehicleMap.airShowerDoor.delete.error=Abnormal deletion of air shower door [%s], please check the operation log
vehicleMap.airShowerDoor.bind.path.error=Abnormal bind of air shower door [%s], please check the duplicate path code
vehicleMap.autoDoor.not.exist.error=Operation failed, automatic door [%s] does not exist
vehicleMap.autoDoor.already.bind.other.device.error=Operation failed, path [%s] is already bound to device
vehicleMap.autoDoor.add.error=New automatic door exception, please check the operation log
vehicleMap.autoDoor.update.error=Abnormal modification of automatic door [%s], please check the operation log
vehicleMap.autoDoor.delete.error=Abnormal deletion of automatic door [%s], please check the operation log
vehicleMap.elevator.add.error=Abnormal addition of elevator, please check the operation log
vehicleMap.elevator.update.error=Abnormal modification of elevator [%s], please check the operation log
vehicleMap.elevator.delete.error=Abnormal deletion of elevator [%s], please check the operation log
vehicleMap.elevator.not.exist.error=Operation failed, elevator [%s] does not exist
vehicleMap.elevator.file.format.error=Elevator file Format Error
vehicleMap.elevator.import.already.exist.error=Elevator import exception, elevator [%s] already exists
vehicleMap.elevator.import.error=Import elevator abnormal, reason:%s
vehicleMap.elevator.export.error=Export elevator abnormal, reason:%s
vehicleMap.elevator.publish.check.error=Map release check, the associated elevator is currently in use. Should it be forcibly released
vehicleMap.elevator.import.bind.map.error=Elevator [%s] import failed, please import all maps bound to the elevator first
vehicleMap.elevator.bind.multi.marker.error=Elevator binding exception, elevator cannot bind multiple points on the same map
vehicleMap.mapArea.not.exist.error=Operation failed, region [%s] does not exist
vehicleMap.mapArea.add.error=New region exception, please check the operation log
vehicleMap.mapArea.update.error=Exception in modifying area [%s], please check the operation log
vehicleMap.mapArea.update.occupied.error=Exception in modifying area [%s],The current resource is being used
vehicleMap.mapArea.delete.error=Abnormal deletion of area [%s], please check the operation log
vehicleMap.marker.add.error=New point exception, please check the operation log
vehicleMap.marker.update.error=Abnormal modification of point [%s], please check the operation log
vehicleMap.marker.delete.error=Abnormal deletion of point [%s], please check the operation log
vehicleMap.marker.not.exist.error=Operation failed, point [%s] does not exist
vehicleMap.marker.already.bind.other.device.error=Operation failed, device bound to point [%s]
vehicleMap.marker.spacing.error=The distance between points is less than the set value [%s]
vehicleMap.path.bind.marker.no.exist.error=Operation failed, point [%s] bound to path does not exist
vehicleMap.path.already.bind.device.error=Operation failed, path [%s] is already bound to device
vehicleMap.path.already.exist.error=Operation failed, path [%s] already exists
vehicleMap.path.not.exist.error=Operation failed, path [%s] does not exist
vehicleMap.path.add.error=New path exception, please check the operation log
vehicleMap.path.update.error=Exception in modifying path [%s], please check the operation log
vehicleMap.path.delete.error=Abnormal deletion path [%s], please check the operation log
vehicleMap.map.operating.duplicate.error=During execution, please do not repeat the operation
vehicleMap.map.not.exist.error=Map [%s] does not exist, please exit the map editing page
vehicleMap.map.file.format.error=The format of the imported file [%s] is incorrect
vehicleMap.map.add.error=Abnormal addition of map, please check the operation log
vehicleMap.map.update.error=Abnormal modification of map [%s], please check the operation log
vehicleMap.map.delete.error=Abnormal deletion of map [%s], please check the operation log
vehicleMap.map.roadnet.update.error=Modifying map [%s] road network elements is abnormal, please check the operation log
vehicleMap.map.roadnet.delete.error=Deleting map [%s] road network elements is abnormal, please check the operation log
vehicleMap.map.draft.delete.error=Deleting map [%s] road network draft is abnormal, please check the operation log
vehicleMap.map.is.not.publish.error=Map [%s] does not have an official version
vehicleMap.map.import.error=Map import exception, reason: %s
vehicleMap.map.import.structure.error=The imported map file format is incorrect
vehicleMap.map.import.in.edit.page.error=Please import the location map on the map editing page
vehicleMap.map.import.missing.info.error=The imported map is missing the info file
vehicleMap.map.import.missing.png.error=The imported map is missing the png file
vehicleMap.map.import.missing.locating.error=The imported map file format is incorrect: the road network file does not have location map information
vehicleMap.map.import.appoint.default.error=The imported map file format is incorrect: the road network file does not specify a default positioning map
vehicleMap.map.export.error=Map export exception, reason:%s
vehicleMap.map.copy.error=Map copying exception, reason:%s
vehicleMap.map.reset.error=There are no revocable operations on the current map [%s]
vehicleMap.map.recover.error=There are no recoverable operations on the current map [%s]
vehicleMap.map.global.recover.error=Robot recovery failed:%s
vehicleMap.map.publish.occupied.error=Failed to publish map, The current resource [%s] is being used
vehicleMap.map.locatingmap.is.empty.error=Map [%s] location map data is empty!
vehicleMap.map.locatingmap.code.is.empty.error=Map [%s] location map code is empty!
vehicleMap.map.locatingmap.not.exist.error=Location map [%s] does not exist, please check the operation log
vehicleMap.map.locatingmap.update.error=Abnormal modification of positioning map [%s], please check the operation log
vehicleMap.map.locatingmap.default.error=Cannot delete default location map [%s] for [%s]
vehicleMap.map.locatingmap.import.structure.error=The directory structure of the imported location map file is abnormal
vehicleMap.map.locatingmap.import.file.is.empty.error=The list of imported location map files is empty
vehicleMap.map.locatingmap.import.file.is.missing.error=The imported location map [%s] file is missing
vehicleMap.map.locatingmap.import.file.is.duplicate.error=该定位图%s已存在于地图%s中，请检查后再操作(待翻译)
vehicleMap.map.locatingmap.import.file.is.forbidden.error=该编码定位图%s不允许导入，请检查后再操作(待翻译)
vehicleMap.map.locatingmap.export.error=Export location map [%s] abnormal, reason:%s
device.connect.error=Device operation failed, device [%s] is not connected to the network
device.open.error=Device [%s] activation exception, please check the operation log
device.close.error=Device shutdown [%s] exception, please check the operation log
device.is.in.use.error=Device operation failed, device [%s] is currently in use
task.node.config.export.file.name=Node settings
task.node.config.export.error=Node settings export exception, reason:%s
task.node.config.import.error=Node settings import exception, reason:%s
task.node.is.not.exist.error=Task node [%s] does not exist
task.node.is.not.allow.retry.error=Task node [%s] does not allow retry
task.node.is.not.allow.skip.error=Task node [%s] is not allowed to be skipped
task.type.is.not.published.error=Failed to add task, using unpublished task process [%s]
task.type.is.not.exist.error=Task process [%s] does not exist
task.type.export.error=Task process export exception, reason:%s
task.type.import.error=Task process import exception, reason:%s
task.type.node.is.empty.error=Failed to enable task, task process [%s] is missing available nodes
task.type.enable.el.parse.error=Enabling failed due to:%s
task.type.enable.param.is.null.error=There are nodes with undefined parameters: [%s]
task.type.node.while.is.empty.error=Node with empty loop: [%s]
task.type.prefix.name=Task Type
task.is.not.exist.error=Task [%s] does not exist
task.delete.running.error=Cannot delete ongoing tasks
task.cancel.running.error=Cancel failed, this task is prohibited from cancellation
task.export.file.name=Task
task.export.error=Task export exception, reason:%s
task.import.error=Task import exception, reason:%s
task.import.code.duplicate.error=Failed to upload task record, duplicate task code [%s]
task.cancel.timeout.error=Cancel robot command timeout, please restart the robot [%s] to clear the command
task.insert.vehicle.not.exist.error=Failed to add task, the input robot [%s] does not exist
task.insert.marker.not.exist.error=Failed to add task, the input point [%s] does not exist
task.insert.marker.lock.error=This point [%s] is already occupied by another task
task.insert.map.not.exist.error=Failed to add task, the input map [%s] does not exist
task.insert.dynamic.param.format.error=The format [%s] of the dynamic parameter [%s] passed in is incorrect
task.excel.head.taskNo=Task No
task.excel.head.externalTaskNo=External Task No
task.excel.head.name=Name
task.excel.head.status=Status
task.excel.head.priority=Priority
task.excel.head.vehicleCodes=Robot Codes
task.excel.head.source=Source
task.excel.head.createDate=Create Date
task.excel.head.startTime=Start Time
task.excel.head.endTime=End Time
task.excel.head.remark=Remark
task.excel.head.callbackUrl=Callback Url
task.event.not.exist.error=Event[%s]is not exist
task.event.bound.taskType.is.null.error=Event [%s] is not associated with task flow
task.event.running.duplicate.error=The current event cannot be repeated and there is a running task
task.event.plc.condition.check.fail.error=Register trigger condition verification failed, please check
task.event.vehicle.condition.check.fail.error=Register of robot trigger condition verification failed, please check
task.event.fix.interval.time.error=The interval time setting is incorrect, please check
task.event.relate.task.contain.cancel.node.error=The task [%s] associated with the cancellation task node cannot contain a cancellation task node
task.event.relate.task.create.error=Failed to create task [%s] associated with task node cancellation, reason: [%s]
task.event.type.fixedTime=FixedTime Event
task.event.type.button=Button Event
task.event.type.plc=Register Event
task.event.type.vehiclePlc=Robot Register Event
task.event.type.vehicleAbnormal=Robot Abnormal Event
task.event.type.taskCancel=Task Cancel Event
task.event.type.taskFinished=Task Finished Event
task.event.status.enable=Enable
task.event.status.disable=Disable
task.event.repeat.allow=Allow
task.event.repeat.disallow=Disallow
task.event.export.file.name=Event
task.event.excel.head.code=Event Code
task.event.excel.head.name=Even Name
task.event.excel.head.type=Event Type
task.event.excel.head.isAllowRepeat=Allow Repetition
task.event.excel.head.taskTypeId=Task Type
task.event.excel.head.status=Status
task.event.excel.head.param=Event Param
task.event.excel.head.taskParam=Task Param
task.node.name.Wait=Wait
task.node.name.DynamicAllocationVehicle=Dynamic Allocation Vehicle
task.node.name.AllocationMarker=Random Allocation Position
task.node.name.ReleaseVehicle=Release Vehicle
task.node.name.VehicleRotation=Robot Rotation
task.node.name.TrayRotation=Tray Rotation
task.node.name.TrayLifting=Tray Lifting
task.node.name.ReadPlc=Read Register
task.node.name.WritePlc=Write Register
task.node.name.AssignAllocationVehicle=Designate Allocation Robot
task.node.name.VehicleMove=Robot Move
task.node.name.GetMarkerAttribute=Get Marker Attribute
task.node.name.DockingCharge=Docking Charge
task.node.name.ButtonRelease=Button Release
task.node.name.ButtonReset=Button Reset
task.node.name.Alarm=Alarm
task.node.name.ReadVehiclePlc=Read Robot register
task.node.name.WriteVehiclePlc=Write Robot register
task.node.name.Rotation=Combined Rotation
task.node.name.RobotArmScript=Robot Arm Control
task.node.name.CheckPlc=Check Register
task.node.name.PlayAudio=Play Audio
task.node.name.SwitchSpeedArea=Switch speed area
task.node.name.SwitchDockingArea=Switch Docking Area
task.node.name.TrayFollowControl=Tray Mode Switch
task.node.name.CheckVehiclePlc=Check Robot Register
task.node.name.ForkArmLifting=Fork Arm Lifting
task.node.name.GetTaskAttribute=Get Task Attribute
task.node.name.FinishTask=Finish Task
task.node.name.HttpRequest=Http Communication
task.node.name.NobodyForkCharge=Forklift Charging
task.node.name.DockingNavigation=Docking
task.node.name.SwitchScheduleMode=Switch Schedule Mode
task.node.name.GetVehicleAttribute=Get Vehicle Attribute
task.node.name.AllocationWarehouse=Allocate storage location
task.node.name.UpdateWarehouse=Update storage location
task.node.name.GetWarehouseAttribute=Get Warehouse Attribute
task.node.name.GetBarcodeAttribute=Get Barcode Attribute
task.node.name.GetAdjacentMarker=Get Adjacent Marker
task.node.name.Ajust=Fine-tune
task.node.name.LeaveDocking=Leave the dock
task.node.name.StopAudio=Stop Playing Audio
task.node.name.ReadQrCode=Read QR Code
task.node.name.CheckSensorObstacleStatus=Forklift unloading inspection
task.node.name.SetVariable=Set Variable
task.node.name.Notice=Notice Alarm
task.node.name.CancelTask=Cancel Task
task.node.name.ForbidCancelTask=Forbid task cancellation
task.node.name.OperateMapArea=Operate Area
task.node.name.HttpCheck=Http verification
task.node.name.JavaScript=Java Script
task.node.name.ReadPLCStr=Read Register(string)
task.node.name.WritePLCStr=Write Register(string)
task.node.name.LockMarker=锁定点位(待翻译)
task.node.name.StopCharge=停止充电(待翻译)
task.node.notice.Wait=Wait for a period of time before executing the subsequent task node
task.node.notice.DynamicAllocationVehicle=Randomly allocate and occupy the robot, execute subsequent task nodes after successful occupation
task.node.notice.AllocationMarker=Randomly allocate a position marker
task.node.notice.ReleaseVehicle=Release the occupation of the robot by other tasks，the released robot can be immediately taken up for other tasks
task.node.notice.VehicleRotation=Control the robot to rotate
task.node.notice.TrayRotation=Control the robot tray to rotate
task.node.notice.TrayLifting=Control the robot traylifting
task.node.notice.ReadPlc=Read the remote register value
task.node.notice.WritePlc=Write values to the external register
task.node.notice.AssignAllocationVehicle=Designate and occupy the specified robot, execute subsequent task nodes after successful occupation
task.node.notice.VehicleMove=Plan the robot path and control it to move toward the target point
task.node.notice.GetMarkerAttribute=Acquire and output the attribute of the marker at this node
task.node.notice.DockingCharge=Control the robot to dock to the charger automatically
task.node.notice.ButtonRelease=Press the Youi call box release button, then execute subsequent task nodes
task.node.notice.ButtonReset=Reset the Youi call box button, then the reset button can be re-triggered
task.node.notice.Alarm=Control the sound alarm to be triggered
task.node.notice.ReadVehiclePlc=Read the register value inside the robot
task.node.notice.WriteVehiclePlc=Write values to the register inside the robot
task.node.notice.Rotation=Control the robot and tray to rotate together
task.node.notice.RobotArmScript=Use Json script to control robot arm movement
task.node.notice.CheckPlc=Check Register
task.node.notice.PlayAudio=Play audio
task.node.notice.SwitchSpeedArea=Switch speed area
task.node.notice.SwitchDockingArea=Switch docking area
task.node.notice.TrayFollowControl=Tray follow control
task.node.notice.CheckVehiclePlc=Check Robot Register
task.node.notice.ForkArmLifting=Control the agv forklift arm to lift
task.node.notice.GetTaskAttribute=Get Task Attribute
task.node.notice.FinishTask=Finish task
task.node.notice.HttpRequest=Send a request to the external system and wait for the execution result
task.node.notice.NobodyForkCharge=Forklift Charging
task.node.notice.DockingNavigation=Docking
task.node.notice.SwitchScheduleMode=Switch scheduling mode
task.node.notice.GetVehicleAttribute=Acquire robot attribute
task.node.notice.AllocationWarehouse=Allocate storage location
task.node.notice.UpdateWarehouse=Update storage location
task.node.notice.GetWarehouseAttribute=Acquire storage location attribute
task.node.notice.GetBarcodeAttribute=Acquire barcode attribute
task.node.notice.GetAdjacentMarker=Acquire adjacent marker
task.node.notice.Ajust=Fine-tune
task.node.notice.LeaveDocking=Leave the dock
task.node.notice.StopAudio=Stop playing audio
task.node.notice.ReadQrCode=Read Qr code
task.node.notice.CheckSensorObstacleStatus=whether the forklift clamp sensor is triggered (true: triggered, false: not triggered)
task.node.notice.SetVariable=This node can reset the value of the variable
task.node.notice.Notice=Notice Alarm
task.node.notice.CancelTask=Cancel Task
task.node.notice.ForbidCancelTask=Forbid task cancellation
task.node.notice.OperateMapArea=Operate map area
task.node.notice.HttpCheck=HTTP (POST request) result verification
task.node.notice.JavaScript=Java script node
task.node.notice.ReadPLCStr=Read PLC ASCII in chracter string
task.node.notice.WritePLCStr=Write ASCII string into PLC
task.node.notice.LockMarker=锁定点位(待翻译)
task.node.notice.StopCharge=停止充电(待翻译)
task.node.param.name.1718202092436357121.vehicleCode.In=Robot
task.node.param.name.1718202092436357121.position.In=Target Height
task.node.param.name.1718202092436357121.offsetHeight.In=Deviation height
task.node.param.name.1718202092436357121.speed.In=speed
task.node.param.name.1738448135527288834.vehicleCode.In=Robot
task.node.param.name.1738448135527288834.markerCode.In=Charge Point
task.node.param.name.1738448135527288834.chargeTime.In=Charging time
task.node.param.name.1738448135527288834.batteryCharge.In=Charging capacity
task.node.param.name.1738443040093851650.finishType.In=End type
task.node.param.name.1738443040093851650.noticeMsg.In=End reminder
task.node.param.name.1646764086215663617.vehicleCode.In=Robot
task.node.param.name.1646764086215663617.vehicleAngle1.In=Robot angle 1
task.node.param.name.1646764086215663617.vehicleAngle2.In=Robot angle 2
task.node.param.name.1646764086215663617.trayRotationSpeed.In=Tray rotation speed
task.node.param.name.1646764086215663617.trayAngle1.In=Tray angle 1
task.node.param.name.1646764086215663617.trayAngle2.In=Tray angle 2
task.node.param.name.1715183824889581570.vehicleCode.In=Robot
task.node.param.name.1715183824889581570.ladarSwitch.In=Speed zone
task.node.param.name.1715184972354686978.vehicleCode.In=Robot
task.node.param.name.1715184972354686978.obstacleArea.In=Avoidance range
task.node.param.name.1738467719873515521.vehicleCode.In=Robot
task.node.param.name.1738467719873515521.scheduleMode.In=Scheduling mode
task.node.param.name.1715183168871075842.vehicleCode.In=Robot
task.node.param.name.1715183168871075842.audioName.In=Audio name
task.node.param.name.1715183168871075842.audioVolume.In=Volume
task.node.param.name.1715183168871075842.playCount.In=Play count
task.node.param.name.1630863227598438401.markerCode.In=Point
task.node.param.name.1630863227598438401.vehicleGroupCode.In=Robot group
task.node.param.name.1630863227598438401.vehicleTypeCode.In=Robot type
task.node.param.name.1630863227598438401.vehicleMapCodeList.In=Map
task.node.param.name.1630863227598438401.limitBattery.In=Quantity of electricity
task.node.param.name.1630863227598438401.outVehicleCode.Out=Robot
task.node.param.name.1630863227745239041.vehicleCode.In=Robot
task.node.param.name.1630863227745239041.outVehicleCode.Out=Robot
task.node.param.name.1630863227644575745.vehicleCode.In=Robot
task.node.param.name.1630863227623604225.vehicleMapCodeList.In=Map
task.node.param.name.1630863227623604225.markerType.In=Point type
task.node.param.name.1630863227623604225.outMarkerCode.Out=Select a point
task.node.param.name.1630863227623604225.vehicleCode.In=Robot
task.node.param.name.1738440272671100929.taskNo.Out=Task code
task.node.param.name.1738440272671100929.externalTaskNo.Out=External task code
task.node.param.name.1738440272671100929.priority.Out=Priority
task.node.param.name.1630863227707490306.ip.In=IP
task.node.param.name.1630863227707490306.port.In=Port
task.node.param.name.1630863227707490306.code.In=Function code
task.node.param.name.1630863227707490306.slaveId.In=Slave ID
task.node.param.name.1630863227707490306.address.In=Read address
task.node.param.name.1630863227707490306.executeMode.In=Execution method
task.node.param.name.1630863227707490306.vehicleCode.In=Robot
task.node.param.name.1630863227707490306.outValue.Out=Output Value
task.node.param.name.1645676364679905282.vehicleCode.In=Robot
task.node.param.name.1645676364679905282.code.In=Function code
task.node.param.name.1645676364679905282.slaveId.In=Slave ID
task.node.param.name.1645676364679905282.address.In=Read address
task.node.param.name.1645676364679905282.outValue.Out=Register value
task.node.param.name.1630863227724267521.ip.In=IP
task.node.param.name.1630863227724267521.port.In=Port
task.node.param.name.1630863227724267521.code.In=Function code
task.node.param.name.1630863227724267521.slaveId.In=Slave ID
task.node.param.name.1630863227724267521.address.In=Write address
task.node.param.name.1630863227724267521.value.In=Write value
task.node.param.name.1630863227724267521.executeMode.In=Execution method
task.node.param.name.1630863227724267521.vehicleCode.In=Robot
task.node.param.name.1645678201743114241.vehicleCode.In=Robot
task.node.param.name.1645678201743114241.code.In=Function code
task.node.param.name.1645678201743114241.slaveId.In=Slave ID
task.node.param.name.1645678201743114241.address.In=Write address
task.node.param.name.1645678201743114241.value.In=Write value
task.node.param.name.1715188504537468930.vehicleCode.In=Robot
task.node.param.name.1715188504537468930.code.In=Function code
task.node.param.name.1715188504537468930.slaveId.In=Slave ID
task.node.param.name.1715188504537468930.address.In=Register Address
task.node.param.name.1715188504537468930.successVal.In=Normal exit
task.node.param.name.1715188504537468930.failVal.In=Failed to exit
task.node.param.name.1715188504537468930.timeout.In=Timeout
task.node.param.name.1715188504537468930.outValue.Out=Output Value
task.node.param.name.1641376178617024513.callBoxCode.In=Call box number
task.node.param.name.1641376178617024513.buttonCode.In=Button number
task.node.param.name.1641376178617024513.timeout.In=Timeout release (seconds)
task.node.param.name.1641376553134817282.callBoxCode.In=Call box number
task.node.param.name.1641376553134817282.buttonCode.In=Button number
task.node.param.name.1641377688272863233.ip.In=IP
task.node.param.name.1641377688272863233.port.In=Port
task.node.param.name.1641377688272863233.type.In=Alarm Type
task.node.param.name.1641377688272863233.time.In=Duration (seconds)
task.node.param.name.1742437277025456130.warehouseCode.In=StorageLocation
task.node.param.name.1742437277025456130.containerBarcode.In=container
task.node.param.name.1742437277025456130.dispatchPolicy.In=allocation policy
task.node.param.name.1742437277025456130.occupyStatus.In=Storage Location status
task.node.param.name.1742437277025456130.warehouseTypeCode.In=Storage location type
task.node.param.name.1742437277025456130.warehouseAreaCode.In=Storage Location area
task.node.param.name.1742437277025456130.extendParam1.In=Extended attribute 1
task.node.param.name.1742437277025456130.extendParam2.In=Extended attribute 2
task.node.param.name.1742437277025456130.extendParam3.In=Extended attribute 3
task.node.param.name.1742437277025456130.extendParam4.In=Extended attribute 4
task.node.param.name.1742437277025456130.extendParam5.In=Extended attribute 5
task.node.param.name.1742437277025456130.warehouseCode.Out=Storage location code
task.node.param.name.1742441148997193730.containerBarCode.In=Container barcode
task.node.param.name.1742441148997193730.warehouseCode.Out=Storage Location area code
task.node.param.name.1742441148997193730.workMarkerCode.Out=Assignment point code
task.node.param.name.1742441148997193730.workHeight.Out=Working height
task.node.param.name.1742441148997193730.warehouseTypeCode.Out=Storage location type code
task.node.param.name.1742441148997193730.warehouseAreaCode.Out=Storage location area code
task.node.param.name.1742440444115046401.warehouseCode.In=Storage location
task.node.param.name.1742440444115046401.containerBarcode.Out=Container barcode
task.node.param.name.1742440444115046401.workMarkerCode.Out=Assignment point code
task.node.param.name.1742440444115046401.workHeight.Out=Working height
task.node.param.name.1742440444115046401.warehouseTypeCode.Out=Storage location type code
task.node.param.name.1742440444115046401.warehouseAreaCode.Out=Storage location area code
task.node.param.name.1742441529047273474.markerCode.In=point
task.node.param.name.1742441529047273474.markerCode.Out=Adjacent point code
task.node.param.name.1630863227652964354.vehicleCode.In=Robot
task.node.param.name.1630863227652964354.angle.In=Rotation angle
task.node.param.name.1630863227652964354.rateType.In=Rotation type
task.node.param.name.1630863227673935874.vehicleCode.In=Robot
task.node.param.name.1630863227673935874.angle1.In=Rotation angle 1
task.node.param.name.1630863227673935874.angle2.In=Rotation angle 2
task.node.param.name.1630863227673935874.speed.In=Rotation speed
task.node.param.name.1630863227673935874.rateType.In=Rotation type
task.node.param.name.1750822156822622210.vehicleCode.In=Robot
task.node.param.name.1750822156822622210.offsetX.In=Deviation X
task.node.param.name.1750822156822622210.offsetY.In=Deviation Y
task.node.param.name.1750822156822622210.offsetAngle.In=Deviation angle
task.node.param.name.1750822156822622210.rotationAngleRange.In=Rotation range
task.node.param.name.1750822156822622210.moveDistanceRange.In=Moving range
task.node.param.name.1750822156822622210.obstacleRegion.In=Obstacle avoidance area
task.node.param.name.1738450017482133505.vehicleCode.In=Robot
task.node.param.name.1738450017482133505.dockingType.In=Type
task.node.param.name.1738450017482133505.startX.In=Start point X coordinate
task.node.param.name.1738450017482133505.startY.In=Start point Y coordinate
task.node.param.name.1738450017482133505.startAngle.In=Start angle
task.node.param.name.1738450017482133505.endX.In=End point X coordinate
task.node.param.name.1738450017482133505.endY.In=End point Y coordinate
task.node.param.name.1738450017482133505.endAngle.In=End angle
task.node.param.name.1738450017482133505.offsetX.In=Deviation X
task.node.param.name.1738450017482133505.offsetY.In=Deviation Y
task.node.param.name.1738450017482133505.offsetAngle.In=Deviation angle
task.node.param.name.1738450017482133505.workStationCode.In=Workstation code
task.node.param.name.1738450017482133505.templateNo.In=Template Number
task.node.param.name.1738450017482133505.cameraObstacle.In=Camera obstacle avoidance
task.node.param.name.1738450017482133505.obstacleRegion.In=Obstacle avoidance area
task.node.param.name.1751081370463637506.vehicleCode.In=Robot
task.node.param.name.1738450017482133505.dockingDirection.In=Docking direction
task.node.param.name.1715186203621986306.vehicleCode.In=Robot
task.node.param.name.1715186203621986306.type.In=Rotate Mode
task.node.param.name.1630863227694907394.vehicleCode.In=Robot
task.node.param.name.1630863227694907394.speed.In=Lifting speed
task.node.param.name.1630863227694907394.targetTicks.In=Elevating height
task.node.param.name.1751878094551769090.vehicleCode.In=Robot
task.node.param.name.1751878094551769090.outValue.Out=QR code information
task.node.param.name.1751825844022235138.vehicleCode.In=Robot
task.node.param.name.1630863227787182081.markerCode.In=Point
task.node.param.name.1630863227787182081.markerName.In=Custom code
task.node.param.name.1630863227787182081.code.Out=Point code
task.node.param.name.1630863227787182081.name.Out=Custom code
task.node.param.name.1630863227787182081.type.Out=Point type
task.node.param.name.1630863227787182081.isPark.Out=Is it possible to park
task.node.param.name.1630863227787182081.angle.Out=Point angle
task.node.param.name.1630863227787182081.chargeEnable.Out=是否允许工作时充电(待翻译)
task.node.param.name.1790203373283213314.oriValue.In=Variable Name
task.node.param.name.1790203373283213314.newValue.In=Expected variable values
task.node.param.name.1738444988633272322.url.In=URL address
task.node.param.name.1738444988633272322.customParams.In=Customize parameters
task.node.param.name.1738444988633272322.customValues.Out=Customize parameters
task.node.param.name.1630863227757821953.vehicleCode.In=Robot
task.node.param.name.1630863227757821953.markerCode.In=Target point
task.node.param.name.1630863227757821953.dockingMove.In=Docking navigation
task.node.param.name.1630863227757821953.accurate.In=fine positioning
task.node.param.name.1630863227757821953.fallPrevent.In=Fall prevention
task.node.param.name.1630863227757821953.safety3D.In=3D obstacle avoidance
task.node.param.name.1630863227757821953.featureFusion.In=Fusion features
task.node.param.name.1630863227757821953.movingSpeed.In=Move speed
task.node.param.name.1630863227757821953.rotationSpeed.In=Rotation speed
task.node.param.name.1630863227757821953.moveObstacleRegion.In=Mobile obstacle avoidance area
task.node.param.name.1630863227757821953.rotationObstacleRegion.In=Rotating obstacle avoidance area
task.node.param.name.1630863227757821953.obstacleAvoidance.In=automatic obstacle avoidance
task.node.param.name.1630863227757821953.agvDirection.In=Front angle
task.node.param.name.1630863227757821953.extendParams.In=Extended parameters
task.node.param.name.1630863227577466882.time.In=Waiting time (seconds)
task.node.param.name.1714957947186581506.ip.In=IP
task.node.param.name.1714957947186581506.port.In=Port
task.node.param.name.1714957947186581506.code.In=Function code
task.node.param.name.1714957947186581506.slaveId.In=Slave ID
task.node.param.name.1714957947186581506.address.In=Register address
task.node.param.name.1714957947186581506.successVal.In=Normal exit
task.node.param.name.1714957947186581506.failVal.In=Failed to exit
task.node.param.name.1714957947186581506.timeout.In=Timeout
task.node.param.name.1714957947186581506.executeMode.In=Execution method
task.node.param.name.1714957947186581506.vehicleCode.In=Robot
task.node.param.name.1714957947186581506.outValue.Out=Output Value
task.node.param.name.1788855369901137921.result_code.Out=Return Code
task.node.param.name.1788855369901137921.error_message.Out=Return Message
task.node.param.name.1630863227787182081.extendParam1.Out=Extended attribute 1
task.node.param.name.1630863227787182081.extendParam2.Out=Extended attribute 2
task.node.param.name.1630863227787182081.extendParam3.Out=Extended attribute 3
task.node.param.name.1630863227787182081.extendParam4.Out=Extended attribute 4
task.node.param.name.1630863227787182081.extendParam5.Out=Extended attribute 5
task.node.param.name.1742440444115046401.extendParam1.Out=Extended attribute 1
task.node.param.name.1742440444115046401.extendParam2.Out=Extended attribute 2
task.node.param.name.1742440444115046401.extendParam3.Out=Extended attribute 3
task.node.param.name.1742440444115046401.extendParam4.Out=Extended attribute 4
task.node.param.name.1742440444115046401.extendParam5.Out=Extended attribute 5
task.node.param.name.1630863227799764993.vehicleCode.In=Robot
task.node.param.name.1630863227799764993.markerCode.In=Charge Point
task.node.param.name.1630863227799764993.chargeTime.In=Charging duration (minutes)
task.node.param.name.1630863227799764993.batteryCharge.In=Charging capacity
task.node.param.name.1630863227799764993.correctChargeCycle.In=Calibration charging cycle (days)
task.node.param.name.1821375525306884097.message.In=Error message
task.node.param.name.1742439427101184002.warehouseCode.In=Storage location
task.node.param.name.1742439427101184002.occupyStatus.In=Occupation status
task.node.param.name.1742439427101184002.containerBarcode.In=Container barcode
task.node.param.name.1742439427101184002.extendParam1.In=Extended attribute 1
task.node.param.name.1742439427101184002.extendParam2.In=Extended attribute 2
task.node.param.name.1742439427101184002.extendParam3.In=Extended attribute 3
task.node.param.name.1742439427101184002.extendParam4.In=Extended attribute 4
task.node.param.name.1742439427101184002.extendParam5.In=Extended attribute 5
task.node.param.name.1831609346757263362.taskTypeId.In=perform tasks
task.node.param.name.1831620038075908097.taskTypeId.In=Task Type
task.node.param.name.1831620038075908097.executionMode.In=Execution Mode
task.node.param.name.1831620038075908097.outValue.Out=Task Number
task.node.param.name.1750822156822622210.QR_dock_id.In=Workstation ID
task.node.param.name.1750822156822622210.dockingType.In=Type
task.node.param.name.1852196093673111553.url.In=URL address
task.node.param.name.1852196093673111553.request.In=Customize request parameters
task.node.param.name.1852196093673111553.response.Out=Output result
task.node.param.name.1852196093673111553.checkParam.Out=Verify parameters
task.node.param.name.1738470004770951170.vehicleCode.In=Robot
task.node.param.name.1738470004770951170.vehicleCode.Out=Robot code
task.node.param.name.1738470004770951170.batteryValue.Out=quantity of electricity
task.node.param.name.1738470004770951170.vehicleTypeCode.Out=Robot type
task.node.param.name.1738470004770951170.vehicleGroupCode.Out=Robot group
task.node.param.name.1738470004770951170.vehicleMapCode.Out=Current map
task.node.param.name.1738470004770951170.markerCode.Out=Current location
task.node.param.name.1738470004770951170.storageInfoList.Out=Storage information
task.node.param.name.1654731794802663426.vehicleCode.In=Robot
task.node.param.name.1654731794802663426.scriptData.In=Script Data
task.node.param.name.1654731794802663426.HAND_VISION.Out=Scanning results
task.node.param.name.1654731794802663426.OPERATE.Out=Operational data
task.node.param.name.1654731794802663426.STATUS.Out=Results of execution
task.node.param.name.1856960932295598081.ip.In=IP
task.node.param.name.1856960932295598081.port.In=Port
task.node.param.name.1856960932295598081.slaveId.In=Slave ID
task.node.param.name.1856960932295598081.address.In=Start address
task.node.param.name.1856960932295598081.value.In=Write value
task.node.param.name.1856959739322294274.ip.In=IP
task.node.param.name.1856959739322294274.port.In=Port
task.node.param.name.1856959739322294274.slaveId.In=Slave ID
task.node.param.name.1856959739322294274.address.In=Start address
task.node.param.name.1856959739322294274.length.In=Read length
task.node.param.name.1856959739322294274.result.Out=Output Value
task.node.param.name.1851551331579158530.mapAreaCode.In=Region code
task.node.param.name.1851551331579158530.operation.In=Operate
task.node.param.name.1856960932295598081.executeMode.In=Execution method
task.node.param.name.1856960932295598081.vehicleCode.In=Robot
task.node.param.name.1856959739322294274.executeMode.In=Execution method
task.node.param.name.1856959739322294274.vehicleCode.In=Robot
task.node.param.name.1856959739322294274.code.In=Function code
task.node.param.name.1856960932295598081.code.In=Function code
task.node.param.name.1856613384389165058.script.In=Script
task.node.param.name.1856613384389165058.param1.In=Parameter 1
task.node.param.name.1856613384389165058.param2.In=Parameter 2
task.node.param.name.1856613384389165058.param3.In=Parameter 3
task.node.param.name.1856613384389165058.param4.In=Parameter 4
task.node.param.name.1856613384389165058.param5.In=Parameter 5
task.node.param.name.1856613384389165058.param1.Out=Parameter 1
task.node.param.name.1856613384389165058.param2.Out=Parameter 2
task.node.param.name.1856613384389165058.param3.Out=Parameter 3
task.node.param.name.1856613384389165058.param4.Out=Parameter 4
task.node.param.name.1856613384389165058.param5.Out=Parameter 5
task.node.param.name.1912414958361030657.markerCode.In=点位(待翻译)
task.node.param.name.1912414958361030657.vehicleCode.In=机器人(待翻译)
task.node.param.name.1912415217493520385.vehicleCode.In=机器人(待翻译)
task.node.param.notice.1718202092436357121.vehicleCode.In=[未翻译]
task.node.param.notice.1718202092436357121.position.In=Unit: millimeter
task.node.param.notice.1718202092436357121.offsetHeight.In=Unit: millimeter
task.node.param.notice.1718202092436357121.speed.In=Unit: millimeter/second
task.node.param.notice.1738448135527288834.vehicleCode.In=[未翻译]
task.node.param.notice.1738448135527288834.markerCode.In=[未翻译]
task.node.param.notice.1738448135527288834.chargeTime.In=can interrupt charging and execute tasks when greater than this charging time (minutes)
task.node.param.notice.1738448135527288834.batteryCharge.In=can interrupt charging and execute tasks when exceeding this battery power
task.node.param.notice.1738443040093851650.finishType.In=[未翻译]
task.node.param.notice.1738443040093851650.noticeMsg.In=Report this task completion notification to the upstream system, when the task finished
task.node.param.notice.1646764086215663617.vehicleCode.In=use the robot occupied by the task by default when the value is empty
task.node.param.notice.1646764086215663617.vehicleAngle1.In=Target angle of the robot relative to the map
task.node.param.notice.1646764086215663617.vehicleAngle2.In=Target angle of the robot relative to the map
task.node.param.notice.1646764086215663617.trayRotationSpeed.In=[未翻译]
task.node.param.notice.1646764086215663617.trayAngle1.In=Target angle of the tray relative to the map
task.node.param.notice.1646764086215663617.trayAngle2.In=Target angle of the tray relative to the map
task.node.param.notice.1715183824889581570.vehicleCode.In=[未翻译]
task.node.param.notice.1715183824889581570.ladarSwitch.In=[未翻译]
task.node.param.notice.1715184972354686978.vehicleCode.In=[未翻译]
task.node.param.notice.1715184972354686978.obstacleArea.In=[未翻译]
task.node.param.notice.1738467719873515521.vehicleCode.In=[未翻译]
task.node.param.notice.1738467719873515521.scheduleMode.In=[未翻译]
task.node.param.notice.1715183168871075842.vehicleCode.In=[未翻译]
task.node.param.notice.1715183168871075842.audioName.In=[未翻译]
task.node.param.notice.1715183168871075842.audioVolume.In=[未翻译]
task.node.param.notice.1715183168871075842.playCount.In=[未翻译]
task.node.param.notice.1630863227598438401.markerCode.In=Allocate the robot near the marker, if this value is empty, randomly allocate a robot
task.node.param.notice.1630863227598438401.vehicleGroupCode.In=Only allocate robots belonging to this robot group
task.node.param.notice.1630863227598438401.vehicleTypeCode.In=Only allocate robots belonging to this robot type
task.node.param.notice.1630863227598438401.vehicleMapCodeList.In=Only allocate robots within this map
task.node.param.notice.1630863227598438401.limitBattery.In=Only allocate robots with battery level higher than this value and higher than battery level set in the charging strategy
task.node.param.notice.1630863227598438401.outVehicleCode.Out=[未翻译]
task.node.param.notice.1630863227745239041.vehicleCode.In=[未翻译]
task.node.param.notice.1630863227745239041.outVehicleCode.Out=[未翻译]
task.node.param.notice.1630863227644575745.vehicleCode.In=[未翻译]
task.node.param.notice.1630863227623604225.vehicleMapCodeList.In=Only assign the marker within the map
task.node.param.notice.1630863227623604225.markerType.In=Only assign the marker with this type
task.node.param.notice.1630863227623604225.outMarkerCode.Out=[未翻译]
task.node.param.notice.1630863227623604225.vehicleCode.In=[未翻译]
task.node.param.notice.1738440272671100929.taskNo.Out=The unique No of this task
task.node.param.notice.1738440272671100929.externalTaskNo.Out=The external task No of this task
task.node.param.notice.1738440272671100929.priority.Out=The priority level of this task
task.node.param.notice.1630863227707490306.ip.In=PLC IP address
task.node.param.notice.1630863227707490306.port.In=PLC port address
task.node.param.notice.1630863227707490306.code.In=[未翻译]
task.node.param.notice.1630863227707490306.slaveId.In=[未翻译]
task.node.param.notice.1630863227707490306.address.In=The register address to read
task.node.param.notice.1630863227707490306.executeMode.In=[未翻译]
task.node.param.notice.1630863227707490306.vehicleCode.In=[未翻译]
task.node.param.notice.1630863227707490306.outValue.Out=[未翻译]
task.node.param.notice.1645676364679905282.vehicleCode.In=Use the robot occupied by the task by default when the value is empty
task.node.param.notice.1645676364679905282.code.In=Read the coil register（01），value range[0-1] 。Read holding register（03），value range[0-30000]
task.node.param.notice.1645676364679905282.slaveId.In=[未翻译]
task.node.param.notice.1645676364679905282.address.In=Read the robot register address
task.node.param.notice.1645676364679905282.outValue.Out=[未翻译]
task.node.param.notice.1630863227724267521.ip.In=PLC IP address
task.node.param.notice.1630863227724267521.port.In=PLC port No
task.node.param.notice.1630863227724267521.code.In=[未翻译]
task.node.param.notice.1630863227724267521.slaveId.In=[未翻译]
task.node.param.notice.1630863227724267521.address.In=Write the register address
task.node.param.notice.1630863227724267521.value.In=Write the value to the register
task.node.param.notice.1630863227724267521.executeMode.In=[未翻译]
task.node.param.notice.1630863227724267521.vehicleCode.In=[未翻译]
task.node.param.notice.1645678201743114241.vehicleCode.In=Use the robot occupied by the task when the value is empty
task.node.param.notice.1645678201743114241.code.In=write the single coil register（05），value range[0-1]wirte the single holding register（06），value rang[0-30000]
task.node.param.notice.1645678201743114241.slaveId.In=[未翻译]
task.node.param.notice.1645678201743114241.address.In=The register address that needs to be written in
task.node.param.notice.1645678201743114241.value.In=The register value that needs to be written in
task.node.param.notice.1715188504537468930.vehicleCode.In=[未翻译]
task.node.param.notice.1715188504537468930.code.In=[未翻译]
task.node.param.notice.1715188504537468930.slaveId.In=[未翻译]
task.node.param.notice.1715188504537468930.address.In=[未翻译]
task.node.param.notice.1715188504537468930.successVal.In=[未翻译]
task.node.param.notice.1715188504537468930.failVal.In=[未翻译]
task.node.param.notice.1715188504537468930.timeout.In=[未翻译]
task.node.param.notice.1715188504537468930.outValue.Out=[未翻译]
task.node.param.notice.1641376178617024513.callBoxCode.In=Factory number of the call box，which can be viewed using the call box configuration tool
task.node.param.notice.1641376178617024513.buttonCode.In=Button code in the call box
task.node.param.notice.1641376178617024513.timeout.In=After exceeding this value, the node action is completed automatically
task.node.param.notice.1641376553134817282.callBoxCode.In=Factory number of the call box，which can be viewed using the call box configuration tool
task.node.param.notice.1641376553134817282.buttonCode.In=Button code in the call box
task.node.param.notice.1641377688272863233.ip.In=IP address of the sound and light alarm
task.node.param.notice.1641377688272863233.port.In=Port of the sound and light alarm
task.node.param.notice.1641377688272863233.type.In=[未翻译]
task.node.param.notice.1641377688272863233.time.In=Alarm duration
task.node.param.notice.1742437277025456130.warehouseCode.In=[未翻译]
task.node.param.notice.1742437277025456130.containerBarcode.In=[未翻译]
task.node.param.notice.1742437277025456130.dispatchPolicy.In=Allocation Strategy | Allocation strategy: Random allocation, First In First Out FIFO
task.node.param.notice.1742437277025456130.occupyStatus.In=[未翻译]
task.node.param.notice.1742437277025456130.warehouseTypeCode.In=[未翻译]
task.node.param.notice.1742437277025456130.warehouseAreaCode.In=[未翻译]
task.node.param.notice.1742437277025456130.extendParam1.In=[未翻译]
task.node.param.notice.1742437277025456130.extendParam2.In=[未翻译]
task.node.param.notice.1742437277025456130.extendParam3.In=[未翻译]
task.node.param.notice.1742437277025456130.extendParam4.In=[未翻译]
task.node.param.notice.1742437277025456130.extendParam5.In=[未翻译]
task.node.param.notice.1742437277025456130.warehouseCode.Out=[未翻译]
task.node.param.notice.1742441148997193730.containerBarCode.In=[未翻译]
task.node.param.notice.1742441148997193730.warehouseCode.Out=[未翻译]
task.node.param.notice.1742441148997193730.workMarkerCode.Out=[未翻译]
task.node.param.notice.1742441148997193730.workHeight.Out=[未翻译]
task.node.param.notice.1742441148997193730.warehouseTypeCode.Out=[未翻译]
task.node.param.notice.1742441148997193730.warehouseAreaCode.Out=[未翻译]
task.node.param.notice.1742440444115046401.warehouseCode.In=[未翻译]
task.node.param.notice.1742440444115046401.containerBarcode.Out=[未翻译]
task.node.param.notice.1742440444115046401.workMarkerCode.Out=[未翻译]
task.node.param.notice.1742440444115046401.workHeight.Out=[未翻译]
task.node.param.notice.1742440444115046401.warehouseTypeCode.Out=[未翻译]
task.node.param.notice.1742440444115046401.warehouseAreaCode.Out=[未翻译]
task.node.param.notice.1742441529047273474.markerCode.In=[未翻译]
task.node.param.notice.1742441529047273474.markerCode.Out=[未翻译]
task.node.param.notice.1630863227652964354.vehicleCode.In=Use the robot occupied by the task by default when the value is empty
task.node.param.notice.1630863227652964354.angle.In=Target angle of the robot relative to the map
task.node.param.notice.1630863227652964354.rateType.In=[未翻译]
task.node.param.notice.1630863227673935874.vehicleCode.In=Use the robot occupied by the task by default when the value is empty
task.node.param.notice.1630863227673935874.angle1.In=Target angle of the tray relative to the map
task.node.param.notice.1630863227673935874.angle2.In=Target angle of the tray relative to the map
task.node.param.notice.1630863227673935874.speed.In=[未翻译]
task.node.param.notice.1630863227673935874.rateType.In=[未翻译]
task.node.param.notice.1750822156822622210.vehicleCode.In=[未翻译]
task.node.param.notice.1750822156822622210.offsetX.In=Unit in meter
task.node.param.notice.1750822156822622210.offsetY.In=Unit in meter
task.node.param.notice.1750822156822622210.offsetAngle.In=[未翻译]
task.node.param.notice.1750822156822622210.rotationAngleRange.In=Use the chasis default setting, when the value is 0
task.node.param.notice.1750822156822622210.moveDistanceRange.In=Use the chasis default setting, when the value is 0
task.node.param.notice.1750822156822622210.obstacleRegion.In=[未翻译]
task.node.param.notice.1738450017482133505.vehicleCode.In=[未翻译]
task.node.param.notice.1738450017482133505.dockingType.In=[未翻译]
task.node.param.notice.1738450017482133505.startX.In=Unit in meter
task.node.param.notice.1738450017482133505.startY.In=Unit in meter
task.node.param.notice.1738450017482133505.startAngle.In=[未翻译]
task.node.param.notice.1738450017482133505.endX.In=Unit in meter
task.node.param.notice.1738450017482133505.endY.In=Unit in meter
task.node.param.notice.1738450017482133505.endAngle.In=[未翻译]
task.node.param.notice.1738450017482133505.offsetX.In=[未翻译]
task.node.param.notice.1738450017482133505.offsetY.In=[未翻译]
task.node.param.notice.1738450017482133505.offsetAngle.In=[未翻译]
task.node.param.notice.1738450017482133505.workStationCode.In=Only can be used when using the QR code docking
task.node.param.notice.1738450017482133505.templateNo.In=[未翻译]
task.node.param.notice.1738450017482133505.cameraObstacle.In=[未翻译]
task.node.param.notice.1738450017482133505.obstacleRegion.In=[未翻译]
task.node.param.notice.1751081370463637506.vehicleCode.In=[未翻译]
task.node.param.notice.1738450017482133505.dockingDirection.In=[未翻译]
task.node.param.notice.1715186203621986306.vehicleCode.In=[未翻译]
task.node.param.notice.1715186203621986306.type.In=1、Stationary relative to the robot: The tray moves together with the chassis. 2、Stationary relative to the map: The tray remains stationary when the chassis moves.
task.node.param.notice.1630863227694907394.vehicleCode.In=Use the robot occupied by the task by default when the value is empty
task.node.param.notice.1630863227694907394.speed.In=[未翻译]
task.node.param.notice.1630863227694907394.targetTicks.In=[未翻译]
task.node.param.notice.1751878094551769090.vehicleCode.In=[未翻译]
task.node.param.notice.1751878094551769090.outValue.Out=[未翻译]
task.node.param.notice.1751825844022235138.vehicleCode.In=[未翻译]
task.node.param.notice.1630863227787182081.markerCode.In=[未翻译]
task.node.param.notice.1630863227787182081.markerName.In=[未翻译]
task.node.param.notice.1630863227787182081.code.Out=Unique No of the code
task.node.param.notice.1630863227787182081.name.Out=Customize code No that can be empty
task.node.param.notice.1630863227787182081.type.Out=Enum value： ChargingMarker:充电点, NavigationMarker:导航点, WorkMarker:工作点
task.node.param.notice.1630863227787182081.isPark.Out=Enum value：true、false
task.node.param.notice.1630863227787182081.angle.Out=[未翻译]
task.node.param.notice.1630863227787182081.chargeEnable.Out=是否允许工作时充电(待翻译)
task.node.param.notice.1790203373283213314.oriValue.In=[未翻译]
task.node.param.notice.1790203373283213314.newValue.In=[未翻译]
task.node.param.notice.1738444988633272322.url.In=Acquire the Url address
task.node.param.notice.1738444988633272322.customParams.In=[未翻译]
task.node.param.notice.1738444988633272322.customValues.Out=[未翻译]
task.node.param.notice.1630863227757821953.vehicleCode.In=Use the robot occupied by the task by default when the value is empty
task.node.param.notice.1630863227757821953.markerCode.In=[未翻译]
task.node.param.notice.1630863227757821953.dockingMove.In=Disable this setting, the docking type path will no longer be effective.
task.node.param.notice.1630863227757821953.accurate.In=[未翻译]
task.node.param.notice.1630863227757821953.fallPrevent.In=[未翻译]
task.node.param.notice.1630863227757821953.safety3D.In=[未翻译]
task.node.param.notice.1630863227757821953.featureFusion.In=[未翻译]
task.node.param.notice.1630863227757821953.movingSpeed.In=[未翻译]
task.node.param.notice.1630863227757821953.rotationSpeed.In=[未翻译]
task.node.param.notice.1630863227757821953.moveObstacleRegion.In=[未翻译]
task.node.param.notice.1630863227757821953.rotationObstacleRegion.In=[未翻译]
task.node.param.notice.1630863227757821953.obstacleAvoidance.In=whether to enable automatic obstacle avoidance in setting.
task.node.param.notice.1630863227757821953.agvDirection.In=[未翻译]
task.node.param.notice.1630863227757821953.extendParams.In=[未翻译]
task.node.param.notice.1630863227577466882.time.In=[未翻译]
task.node.param.notice.1714957947186581506.ip.In=PLC IP address
task.node.param.notice.1714957947186581506.port.In=PLC port address
task.node.param.notice.1714957947186581506.code.In=[未翻译]
task.node.param.notice.1714957947186581506.slaveId.In=[未翻译]
task.node.param.notice.1714957947186581506.address.In=Read the register address
task.node.param.notice.1714957947186581506.successVal.In=[未翻译]
task.node.param.notice.1714957947186581506.failVal.In=[未翻译]
task.node.param.notice.1714957947186581506.timeout.In=[未翻译]
task.node.param.notice.1714957947186581506.executeMode.In=[未翻译]
task.node.param.notice.1714957947186581506.vehicleCode.In=[未翻译]
task.node.param.notice.1714957947186581506.outValue.Out=[未翻译]
task.node.param.notice.1788855369901137921.result_code.Out=1001: obstacle avoidance not be triggered, 1002 :obstacle avoidance triggered
task.node.param.notice.1788855369901137921.error_message.Out=Message return from the obstacle avoidance area of the sensor (emergency stop, stop, deceleration, not triggered)
task.node.param.notice.1630863227787182081.extendParam1.Out=[未翻译]
task.node.param.notice.1630863227787182081.extendParam2.Out=[未翻译]
task.node.param.notice.1630863227787182081.extendParam3.Out=[未翻译]
task.node.param.notice.1630863227787182081.extendParam4.Out=[未翻译]
task.node.param.notice.1630863227787182081.extendParam5.Out=[未翻译]
task.node.param.notice.1742440444115046401.extendParam1.Out=[未翻译]
task.node.param.notice.1742440444115046401.extendParam2.Out=[未翻译]
task.node.param.notice.1742440444115046401.extendParam3.Out=[未翻译]
task.node.param.notice.1742440444115046401.extendParam4.Out=[未翻译]
task.node.param.notice.1742440444115046401.extendParam5.Out=[未翻译]
task.node.param.notice.1630863227799764993.vehicleCode.In=Use the robot occupied by the task when the value is empty
task.node.param.notice.1630863227799764993.markerCode.In=[未翻译]
task.node.param.notice.1630863227799764993.chargeTime.In=The robot can only be interrupted when the charging time exceeds this value
task.node.param.notice.1630863227799764993.batteryCharge.In=The robot can be interrupted during charging and execute tasks when the current battery power exceeds this value
task.node.param.notice.1630863227799764993.correctChargeCycle.In=The robot cannot be interrupted during charging during the charging cycle correction
task.node.param.notice.1821375525306884097.message.In=error message
task.node.param.notice.1742439427101184002.warehouseCode.In=[未翻译]
task.node.param.notice.1742439427101184002.occupyStatus.In=[未翻译]
task.node.param.notice.1742439427101184002.containerBarcode.In=[未翻译]
task.node.param.notice.1742439427101184002.extendParam1.In=[未翻译]
task.node.param.notice.1742439427101184002.extendParam2.In=[未翻译]
task.node.param.notice.1742439427101184002.extendParam3.In=[未翻译]
task.node.param.notice.1742439427101184002.extendParam4.In=[未翻译]
task.node.param.notice.1742439427101184002.extendParam5.In=[未翻译]
task.node.param.notice.1831609346757263362.taskTypeId.In=[未翻译]
task.node.param.notice.1831620038075908097.taskTypeId.In=[未翻译]
task.node.param.notice.1831620038075908097.executionMode.In=[未翻译]
task.node.param.notice.1831620038075908097.outValue.Out=[未翻译]
task.node.param.notice.1750822156822622210.QR_dock_id.In=QR dock id
task.node.param.notice.1750822156822622210.dockingType.In=[未翻译]
task.node.param.notice.1852196093673111553.url.In=The requested address
task.node.param.notice.1852196093673111553.request.In=[未翻译]
task.node.param.notice.1852196093673111553.response.Out=[未翻译]
task.node.param.notice.1852196093673111553.checkParam.Out=[未翻译]
task.node.param.notice.1738470004770951170.vehicleCode.In=[未翻译]
task.node.param.notice.1738470004770951170.vehicleCode.Out=[未翻译]
task.node.param.notice.1738470004770951170.batteryValue.Out=[未翻译]
task.node.param.notice.1738470004770951170.vehicleTypeCode.Out=[未翻译]
task.node.param.notice.1738470004770951170.vehicleGroupCode.Out=[未翻译]
task.node.param.notice.1738470004770951170.vehicleMapCode.Out=[未翻译]
task.node.param.notice.1738470004770951170.markerCode.Out=[未翻译]
task.node.param.notice.1738470004770951170.storageInfoList.Out=[未翻译]
task.node.param.notice.1654731794802663426.vehicleCode.In=Use the robot occupied by the task by default when the value is empty
task.node.param.notice.1654731794802663426.scriptData.In=Mos system executable Json script
task.node.param.notice.1654731794802663426.HAND_VISION.Out=[未翻译]
task.node.param.notice.1654731794802663426.OPERATE.Out=[未翻译]
task.node.param.notice.1654731794802663426.STATUS.Out=1: In progress; 2: Execution exception (not returned to original); 3: Execution completed; 4: Execution exception (returned to original)
task.node.param.notice.1856960932295598081.ip.In=[未翻译]
task.node.param.notice.1856960932295598081.port.In=[未翻译]
task.node.param.notice.1856960932295598081.slaveId.In=[未翻译]
task.node.param.notice.1856960932295598081.address.In=[未翻译]
task.node.param.notice.1856960932295598081.value.In=[未翻译]
task.node.param.notice.1856959739322294274.ip.In=[未翻译]
task.node.param.notice.1856959739322294274.port.In=[未翻译]
task.node.param.notice.1856959739322294274.slaveId.In=[未翻译]
task.node.param.notice.1856959739322294274.address.In=[未翻译]
task.node.param.notice.1856959739322294274.length.In=[未翻译]
task.node.param.notice.1856959739322294274.result.Out=[未翻译]
task.node.param.notice.1851551331579158530.mapAreaCode.In=[未翻译]
task.node.param.notice.1851551331579158530.operation.In=[未翻译]
task.node.param.notice.1856960932295598081.executeMode.In=[未翻译]
task.node.param.notice.1856960932295598081.vehicleCode.In=[未翻译]
task.node.param.notice.1856959739322294274.executeMode.In=[未翻译]
task.node.param.notice.1856959739322294274.vehicleCode.In=[未翻译]
task.node.param.notice.1856959739322294274.code.In=[未翻译]
task.node.param.notice.1856960932295598081.code.In=[未翻译]
task.node.param.notice.1856613384389165058.script.In=[未翻译]
task.node.param.notice.1856613384389165058.param1.In=[未翻译]
task.node.param.notice.1856613384389165058.param2.In=[未翻译]
task.node.param.notice.1856613384389165058.param3.In=[未翻译]
task.node.param.notice.1856613384389165058.param4.In=[未翻译]
task.node.param.notice.1856613384389165058.param5.In=[未翻译]
task.node.param.notice.1856613384389165058.param1.Out=[未翻译]
task.node.param.notice.1856613384389165058.param2.Out=[未翻译]
task.node.param.notice.1856613384389165058.param3.Out=[未翻译]
task.node.param.notice.1856613384389165058.param4.Out=[未翻译]
task.node.param.notice.1856613384389165058.param5.Out=[未翻译]
task.node.param.notice.1912414958361030657.markerCode.In=[未翻译]
task.node.param.notice.1912414958361030657.vehicleCode.In=[未翻译]
task.node.param.notice.1912415217493520385.vehicleCode.In=[未翻译]
task.node.param.value.desc.1738443040093851650.finishType.In.Finished=task finished
task.node.param.value.desc.1738443040093851650.finishType.In.Cancel=Cancel task
task.node.param.value.desc.1715183824889581570.ladarSwitch.In.0=Area 0
task.node.param.value.desc.1715183824889581570.ladarSwitch.In.1=Area 1
task.node.param.value.desc.1715184972354686978.obstacleArea.In.1=Obstacle Area 1
task.node.param.value.desc.1715184972354686978.obstacleArea.In.2=Obstacle Area 2
task.node.param.value.desc.1715184972354686978.obstacleArea.In.3=Obstacle Area 3
task.node.param.value.desc.1715184972354686978.obstacleArea.In.4=Obstacle Area 4
task.node.param.value.desc.1715184972354686978.obstacleArea.In.5=Obstacle Area 5
task.node.param.value.desc.1715184972354686978.obstacleArea.In.6=Obstacle Area 6
task.node.param.value.desc.1715184972354686978.obstacleArea.In.7=Obstacle Area 7
task.node.param.value.desc.1715184972354686978.obstacleArea.In.8=Obstacle Area 8
task.node.param.value.desc.1715184972354686978.obstacleArea.In.9=Obstacle Area 9
task.node.param.value.desc.1715184972354686978.obstacleArea.In.10=Obstacle Area 10
task.node.param.value.desc.1715184972354686978.obstacleArea.In.11=Obstacle Area 11
task.node.param.value.desc.1715184972354686978.obstacleArea.In.12=Obstacle Area 12
task.node.param.value.desc.1715184972354686978.obstacleArea.In.13=Obstacle Area 13
task.node.param.value.desc.1715184972354686978.obstacleArea.In.14=Obstacle Area 14
task.node.param.value.desc.1715184972354686978.obstacleArea.In.15=Obstacle Area 15
task.node.param.value.desc.1715184972354686978.obstacleArea.In.16=Obstacle Area 16
task.node.param.value.desc.1738467719873515521.scheduleMode.In.AutoSchedule=AutoSchedule
task.node.param.value.desc.1738467719873515521.scheduleMode.In.ManualSchedule=ManualSchedule
task.node.param.value.desc.1630863227623604225.markerType.In.NavigationMarker=Navigation Marker
task.node.param.value.desc.1630863227623604225.markerType.In.WorkMarker=Work Marker
task.node.param.value.desc.1630863227623604225.markerType.In.ChargingMarker=Charging Marker
task.node.param.value.desc.1630863227707490306.code.In.01=Read the coil register（01）
task.node.param.value.desc.1630863227707490306.code.In.02=Read discrete data input register（02）
task.node.param.value.desc.1630863227707490306.code.In.03=Read holding register（03）
task.node.param.value.desc.1630863227707490306.code.In.04=Read input register（04）
task.node.param.value.desc.1630863227707490306.executeMode.In.Server=Server
task.node.param.value.desc.1630863227707490306.executeMode.In.Vehicle=Robot
task.node.param.value.desc.1645676364679905282.code.In.01=Read the coil register（01）
task.node.param.value.desc.1645676364679905282.code.In.03=Read holding register（03）
task.node.param.value.desc.1630863227724267521.code.In.05=write the single coil register（05）
task.node.param.value.desc.1630863227724267521.code.In.06=write the single discrete data input register（06）
task.node.param.value.desc.1630863227724267521.code.In.15=write multiple coil registers（15）
task.node.param.value.desc.1630863227724267521.code.In.16=write multiple discrete data input registers（16）
task.node.param.value.desc.1630863227724267521.executeMode.In.Server=server
task.node.param.value.desc.1630863227724267521.executeMode.In.Vehicle=robot
task.node.param.value.desc.1645678201743114241.code.In.05=write the single coil register（05）
task.node.param.value.desc.1645678201743114241.code.In.06=write the single holding register（06）
task.node.param.value.desc.1715188504537468930.code.In.03=Read the holding register（03）
task.node.param.value.desc.1641377688272863233.type.In.SoundAndLight=Sound and light
task.node.param.value.desc.1641377688272863233.type.In.Sound=Sound
task.node.param.value.desc.1641377688272863233.type.In.Light=Light
task.node.param.value.desc.1742437277025456130.dispatchPolicy.In.FIFO=FIFO
task.node.param.value.desc.1742437277025456130.dispatchPolicy.In.RANDOM=Random allocation
task.node.param.value.desc.1742437277025456130.occupyStatus.In.Store=[未翻译]
task.node.param.value.desc.1742437277025456130.occupyStatus.In.Free=Free
task.node.param.value.desc.1630863227652964354.rateType.In.Relative=Relative Robot Rotation
task.node.param.value.desc.1630863227652964354.rateType.In.Absolute=Relative map rotation
task.node.param.value.desc.1630863227673935874.rateType.In.Relative=Relative robot
task.node.param.value.desc.1630863227673935874.rateType.In.Absolute=Absolute map
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.1=Obstacle area 1
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.2=Obstacle area 2
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.3=Obstacle area 3
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.4=Obstacle area 4
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.5=Obstacle area  5
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.6=Obstacle area  6
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.7=Obstacle area 7
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.8=[未翻译]
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.9=Obstacle area  9
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.10=Obstacle area  10
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.11=Obstacle area 11
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.12=Obstacle area 12
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.13=Obstacle area 13
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.14=Obstacle area 14
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.15=Obstacle area15
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.16=Obstacle area 16
task.node.param.value.desc.1738450017482133505.dockingType.In.QR_Down=QR code docking
task.node.param.value.desc.1738450017482133505.dockingType.In.Reflector=Reflector docking
task.node.param.value.desc.1738450017482133505.dockingType.In.Symbol_V=V dashboard docking
task.node.param.value.desc.1738450017482133505.dockingType.In.Shelflegs=shelfleg docking
task.node.param.value.desc.1738450017482133505.dockingType.In.Pallet=Pallet docking
task.node.param.value.desc.1738450017482133505.dockingType.In.LeaveDocking=Leave docking
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.1=Obstacle area 1
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.2=Obstacle area 2
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.3=Obstacle area 3
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.4=Obstacle area 4
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.5=Obstacle area 5
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.6=Obstacle area 6
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.7=Obstacle area 7
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.8=Obstacle area  8
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.9=Obstacle area  9
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.10=Obstacle area  10
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.11=Obstacle area 11
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.12=Obstacle area 12
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.13=Obstacle area 13
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.14=Obstacle area 14
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.15=Obstacle area 15
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.16=Obstacle area 16
task.node.param.value.desc.1738450017482133505.dockingDirection.In.Head=robot front side
task.node.param.value.desc.1738450017482133505.dockingDirection.In.Tail=backside of robot
task.node.param.value.desc.1715186203621986306.type.In.Open=Stationary relative to the robot
task.node.param.value.desc.1715186203621986306.type.In.Close=Stationary relative to the map
task.node.param.value.desc.1630863227757821953.accurate.In.=default
task.node.param.value.desc.1630863227757821953.accurate.In.true=enable
task.node.param.value.desc.1630863227757821953.accurate.In.false=Disable
task.node.param.value.desc.1630863227757821953.fallPrevent.In.=default
task.node.param.value.desc.1630863227757821953.fallPrevent.In.true=enable
task.node.param.value.desc.1630863227757821953.fallPrevent.In.false=Disable
task.node.param.value.desc.1630863227757821953.safety3D.In.=default
task.node.param.value.desc.1630863227757821953.safety3D.In.true=Enable
task.node.param.value.desc.1630863227757821953.safety3D.In.false=Disable
task.node.param.value.desc.1630863227757821953.featureFusion.In.=default
task.node.param.value.desc.1630863227757821953.featureFusion.In.true=enable
task.node.param.value.desc.1630863227757821953.featureFusion.In.false=Disable
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.=default
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.1=Obstacle area1
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.2=obstacle Area2
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.3=obstacle Area3
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.4=obstacle Area4
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.5=obstacle Area5
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.6=obstacle Area6
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.7=bstacle Area7
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.8=obstacle Area8
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.9=obstacle Area9
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.10=obstacle Area110
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.11=obstacle Area11
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.12=obstacle Area12
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.13=obstacle Area13
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.14=obstacle Area14
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.15=obstacle Area15
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.16=obstacle Area16
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In=[未翻译]
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.1=Obstacle area1
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.2=Obstacle area2
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.3=Obstacle area3
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.4=Obstacle area4
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.5=obstacle Area5
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.6=obstacle Area6
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.7=obstacle Area7
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.8=obstacle Area8
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.9=obstacle Area9
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.10=obstacle Area10
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.11=obstacle Area11
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.12=obstacle Area12
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.13=obstacle Area13
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.14=obstacle Area14
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.15=obstacle Area15
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.16=obstacle Area16
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.=default
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.true=enable
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.false=disable
task.node.param.value.desc.1630863227757821953.agvDirection.In.0=0
task.node.param.value.desc.1630863227757821953.agvDirection.In.90=90
task.node.param.value.desc.1630863227757821953.agvDirection.In.-90=-90
task.node.param.value.desc.1630863227757821953.agvDirection.In.180=180
task.node.param.value.desc.1714957947186581506.code.In.01=Read the coil register（01）
task.node.param.value.desc.1714957947186581506.code.In.02=Read the discret date input register（02）
task.node.param.value.desc.1714957947186581506.code.In.03=Read the holding register（03）
task.node.param.value.desc.1714957947186581506.code.In.04=Read the input register（04）
task.node.param.value.desc.1714957947186581506.executeMode.In.Server=Server
task.node.param.value.desc.1714957947186581506.executeMode.In.Vehicle=Robot
task.node.param.value.desc.1742439427101184002.occupyStatus.In.Free=Free
task.node.param.value.desc.1742439427101184002.occupyStatus.In.Store=[未翻译]
task.node.param.value.desc.1831620038075908097.executionMode.In.Independent=Independent execution
task.node.param.value.desc.1831620038075908097.executionMode.In.Embedded=Embedded Execution
task.node.param.value.desc.1750822156822622210.dockingType.In.UP=Up_QR code（low speed）
task.node.param.value.desc.1750822156822622210.dockingType.In.QR_Up=Up_QR code（high speed）
task.node.param.value.desc.1750822156822622210.dockingType.In.DOWN=down_ QR code
task.node.param.value.desc.1750822156822622210.dockingType.In.LEFT=Left_ QR code
task.node.param.value.desc.1750822156822622210.dockingType.In.RIGHT=Right_QR code
task.node.param.value.desc.1750822156822622210.dockingType.In.Laser_Side=Side laser
task.node.param.value.desc.1750822156822622210.dockingType.In.Line_Straight=Single cantilever
task.node.param.value.desc.1750822156822622210.dockingType.In.Reflector_Adjust=Reflector adjustment
task.node.param.value.desc.1851551331579158530.operation.In.Enable=Enable
task.node.param.value.desc.1851551331579158530.operation.In.Disable=Disable
task.node.param.value.desc.1856960932295598081.executeMode.In.Server=Server
task.node.param.value.desc.1856960932295598081.executeMode.In.Vehicle=Robot
task.node.param.value.desc.1856959739322294274.executeMode.In.Server=Server
task.node.param.value.desc.1856959739322294274.executeMode.In.Vehicle=Robot
task.node.param.value.desc.1856959739322294274.code.In.03=Read the holding register（03）
task.node.param.value.desc.1856960932295598081.code.In.16=Write multiple registers（16）