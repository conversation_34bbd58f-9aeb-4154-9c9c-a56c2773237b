package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.WorkScheduler;

import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-13 14:43
 */
public interface WorkSchedulerService extends BaseService<WorkScheduler> {

    List<WorkScheduler> selectCreate();

    List<WorkScheduler> selectRunning();

    List<WorkScheduler> selectPrepareAndRunning();

    List<WorkScheduler> selectPrepare();

    WorkScheduler selectRunningByWork(String workId);

    WorkScheduler selectPrepareAndRunningByWork(String workId);

    void updateCancel(String workId);

    void updateSuccess(String workId);

    void updateStart(String workId);

    void updateFault(String workId, String faultMessage);

    WorkScheduler selectLastOne(String vehicleId);

    /**
     * 根据agvCode获取当前agv正在执行的作业调度
     */
    WorkScheduler selectRunningByVehicleId(String vehicleId);

    WorkScheduler selectPreAllocationWorkByVehicleId(String vehicleId);

    /**
     * 根据时间设置删除过期数据
     * @param missionWork
     */
    void deleteExpireDataByTime(Integer missionWork);

    WorkScheduler selectPrepareAndRunningByVehicleId(String vehicleId);

    /**
     * 根据作业id获取最近一次对应的被取消的计划
     * @param workId
     * @return
     */
    WorkScheduler selectLastCancelSchedulerByWorkId(String workId);
}
