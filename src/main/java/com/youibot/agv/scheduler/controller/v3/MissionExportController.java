package com.youibot.agv.scheduler.controller.v3;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.youibot.agv.scheduler.entity.Mission;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionActionParameter;
import com.youibot.agv.scheduler.entity.MissionGlobalVariable;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.param.MissionExportParam;
import com.youibot.agv.scheduler.service.MissionActionParameterService;
import com.youibot.agv.scheduler.service.MissionActionService;
import com.youibot.agv.scheduler.service.MissionGlobalVariableService;
import com.youibot.agv.scheduler.service.MissionService;
import com.youibot.agv.scheduler.util.CommonUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@RestController("missionExportController")
@RequestMapping("/api/v3/export")
@Api(value = "任务数据导入导出", tags = "任务数据导入导出", description = "将任务相关数据以json文件方式导出到本地进行备份，如需备份可该文件导入")
public class MissionExportController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(MissionExportController.class);
    private final String MISSION = "mission";
    private final String MISSION_ACTION = "missionAction";
    private final String MISSION_ACTION_PARAMETER = "MissionActionParameter";
    private final String MISSION_GLOBAL_VARIABLE = "MissionGlobalVariable";

    private static Map<String, List<String>> missionMap = new ConcurrentHashMap<>();

    @Autowired
    private MissionService missionService;
    @Autowired
    private MissionActionService missionActionService;
    @Autowired
    private MissionActionParameterService missionActionParameterService;
    @Autowired
    private MissionGlobalVariableService missionGlobalVariableService;


    @ApiOperation(value = "导出任务任务", notes = "返回key")
    @ApiImplicitParam(name = "missionIds", value = "任务id数组", dataType = "String")
    @PostMapping("/exportMission")
    @ResponseStatus(value = HttpStatus.OK)
    public Map<String, String> exportMission(@RequestBody MissionExportParam missionExportParam) {
        List<String> missionIds = missionExportParam.getMissionIds();
        if (CollectionUtils.isEmpty(missionIds)) {
            throw new ExecuteException(MessageUtils.getMessage("service.mission_select_export_is_null"));
        }
        String currentTime = String.valueOf(System.currentTimeMillis());
        missionMap.put(currentTime, missionIds);
        Map<String, String> result = new HashMap<>();
        result.put("taskId", currentTime);
        return result;
    }

    @ApiOperation(value = "导出任务数据", notes = "导出任务数据JSON文件")
    @ApiImplicitParam(name = "taskId", value = "导出任务key", required = true, dataType = "String")
    @GetMapping("/exportMissionData")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void exportMissionData(HttpServletResponse response, @RequestParam(value = "taskId") String taskId) {
        OutputStream os = null;
        try {
            List<String> missionIds = missionMap.get(taskId);
            if (CollectionUtils.isEmpty(missionIds)) {
                throw new ExecuteException(MessageUtils.getMessage("service.mission_select_export_is_null"));
            }
            //获取JSON字符串
            String missionDateJsonString = getMissionDateJsonString(new ArrayList<>(missionIds));
            byte[] bytes = missionDateJsonString.getBytes();
            // 将格式化后的字符串写入文件
            response.addHeader("Content-Disposition", "attachment;filename=" + new String("mission".getBytes(), StandardCharsets.ISO_8859_1) + ".json");
            response.addHeader("Content-Length", "" + bytes.length);
            os = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            os.write(bytes);// 输出文件
            os.flush();
            os.close();
        } catch (Exception e) {
            logger.error("exportMapData exception : " + e.getMessage());
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    logger.error(e.getMessage());
                }
            }
            cleaCacheData(taskId);
        }
    }

    private void cleaCacheData(String taskId) {
        missionMap.remove(taskId);
        Long currentTimeMillis = System.currentTimeMillis();
        for (Map.Entry<String, List<String>> entry : missionMap.entrySet()) {
            Long mapKey = Long.valueOf(entry.getKey());
            if (currentTimeMillis - mapKey > 300000) {
                missionMap.remove(mapKey.toString());
            }
        }
    }


    @ApiOperation(value = "导入任务数据", notes = "导入任务数据JSON文件")
    @ApiImplicitParam(name = "mutiPartFile", value = "文件", required = true, dataType = "file")
    @PostMapping("/importMission")
    @ResponseStatus(HttpStatus.OK)
    public void importMissionData(@RequestBody MultipartFile mutiPartFile) {
        if(mutiPartFile==null){
            throw new ExecuteException("文件为空");
        }

        //转换为字符串
        String jsonStr = null;
        try {
            byte[] byteArray = CommonUtils.getByteArray(mutiPartFile.getInputStream());
            if(byteArray!=null && byteArray.length>0){
                jsonStr = new String(byteArray);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isEmpty(jsonStr)) {
            throw new ExecuteException("文件内容为空");
        }

        //批量新增地图数据到数据库中
        try {
            batchInsertMissionData(jsonStr);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage());
            throw new ExecuteException(e.getMessage());
        }

    }

    private void batchInsertMissionData(String jsonStr) {
        Map<String, Object> dataMap = JSON.parseObject(jsonStr, Map.class);
        List<Mission> missionList = new ArrayList<>();
        List missionObj = (List) dataMap.get(MISSION);
        if (CollectionUtils.isEmpty(missionObj)) {
            throw new ADSParameterException(MessageUtils.getMessage("service.mission_export_data_is_null"));
        }
        missionObj.forEach(object -> missionList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<Mission>() {
        })));
        List<String> newMissionIds = missionList.stream().map(Mission::getId).collect(Collectors.toList());

        //存在mission,则删除
//        List<Mission> existMissionList = missionService.selectByIds(newMissionIds);
        if (!CollectionUtils.isEmpty(newMissionIds)) {
//            List<String> deletedMissionIds = existMissionList.stream().map(Mission::getId).collect(Collectors.toList());
            missionService.deleteByIds(newMissionIds);
            List<MissionAction> deletedMissionActions = missionActionService.selectByMissionIdBatch(newMissionIds);
            if (!CollectionUtils.isEmpty(deletedMissionActions)) {
                missionActionService.deleteByIds(deletedMissionActions.stream().map(MissionAction::getId).collect(Collectors.toList()));
            }
        }

        //添加默认值
        if (CollUtil.isNotEmpty(missionList)) {
            missionList.forEach(m -> {
                if (m.getIsDeleted() == null) {
                    m.setIsDeleted(0);
                }
            });
        }

        for (String key : dataMap.keySet()) {
            List obj = (List) dataMap.get(key);
            if (CollectionUtils.isEmpty(obj)) {
                continue;
            }
            switch (key) {
                case MISSION:
                    missionService.batchInsert(missionList);
                    break;

                case MISSION_ACTION:
                    List<MissionAction> missionActionList = new ArrayList<>();
                    obj.forEach(object -> missionActionList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<MissionAction>() {
                    })));
                    missionActionService.batchInsert(missionActionList);
                    break;

                case MISSION_ACTION_PARAMETER:
                    List<MissionActionParameter> missionActionParameterList = new ArrayList<>();
                    obj.forEach(object -> missionActionParameterList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<MissionActionParameter>() {
                    })));
                    missionActionParameterService.deleteByIds(missionActionParameterList.stream().map(path -> path.getId()).collect(Collectors.toList()));
                    missionActionParameterService.batchInsert(missionActionParameterList);
                    break;

                case MISSION_GLOBAL_VARIABLE:
                    List<MissionGlobalVariable> missionGlobalVariableList = new ArrayList<>();
                    obj.forEach(object -> missionGlobalVariableList.add(JSON.parseObject(JSON.toJSONString(object), new TypeReference<MissionGlobalVariable>() {
                    })));
                    missionGlobalVariableService.deleteByIds(missionGlobalVariableList.stream().map(path -> path.getId()).collect(Collectors.toList()));
                    missionGlobalVariableService.batchInsert(missionGlobalVariableList);
                    break;

                default:
                    break;
            }
        }
    }

    private String getMissionDateJsonString(List<String> missionIds) {
        Map<String, Object> dataMap = new HashedMap<>();
        dataMap.put(MISSION, missionService.selectByIds(missionIds));
        Example missionActionExample = new Example(MissionAction.class);
        missionActionExample.createCriteria().andIn("missionId", missionIds);
        List<MissionAction> missionActionList = missionActionService.selectByExample(missionActionExample);
        dataMap.put(MISSION_ACTION, missionActionList);

        List<MissionActionParameter> missionActionParameterList = null;
        if (!CollectionUtils.isEmpty(missionActionList)) {
            List<String> missionActionIds = missionActionList.stream().map(missionAction -> missionAction.getId()).collect(Collectors.toList());
            Example missionActionParameterExample = new Example(MissionActionParameter.class);
            missionActionParameterExample.createCriteria().andIn("missionActionId", missionActionIds);
            missionActionParameterList = missionActionParameterService.selectByExample(missionActionParameterExample);
        }
        dataMap.put(MISSION_ACTION_PARAMETER, missionActionParameterList);

        Example missionGlobalVariableExample = new Example(MissionGlobalVariable.class);
        missionGlobalVariableExample.createCriteria().andIn("missionId", missionIds);
        dataMap.put(MISSION_GLOBAL_VARIABLE, missionGlobalVariableService.selectByExample(missionGlobalVariableExample));

        String jsonString = JSON.toJSONString(dataMap);
        logger.debug("getMissionDateJsonString jsonString value : " + jsonString);
        return StringUtils.isEmpty(jsonString) ? "" : jsonString;

    }
}
