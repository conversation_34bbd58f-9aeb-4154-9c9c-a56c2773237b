package com.youibot.agv.scheduler.mapper;

import com.youibot.agv.scheduler.entity.MqMessage;
import com.youibot.agv.scheduler.util.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Author：yangpeilin
 * @Date: 2020/10/13 16:36
 */
public interface MqMessageMapper extends BaseMapper<MqMessage> {

    @Select("select id from mq_message where create_time < DATE_SUB(NOW(), INTERVAL #{mqMessage} day)")
    List<String> selectExpireDataByTime(@Param("mqMessage") Integer mqMessage);
}
