package com.youibot.agv.scheduler.constant;

import lombok.Getter;

/**
 * @Author：yang<PERSON>ilin
 * @Date: 2020/4/27 19:32
 */
@Getter
public enum ExceptionInfoEnum {

    SYSTEM_ERROR(300000, "fleet系统内部异常"),

    COMPASS_SERVICE_ERROR(301000, "机器人compass服务异常"),

    MISSION_WORK_ERROR(302000, "作业执行异常"),

    MISSION_WORK_ALLOCATION_ERROR(302001, "作业分配异常"),

    MISSION_WORK_SEND_ERROR(302002, "作业下发异常"),

    MISSION_WORK_STOP(302003, "停止作业失败!"),

    MISSION_WORK_NOT_MISSION(302004, "作业没有对应的任务"),

    MISSION_WORK_NOT_PARK_BAND(302005, "无法查找关联的泊车点，请检查地图配置"),


    PATH_PLAN_ERROR(302100, "路径规划异常"),

    AGV_OUT_SIDE_ERROR(302101, "AGV脱轨"),

    AIM_MARKED_UNREACHABLE_ERROR(302102, "目标点不可达"),

    VEHICLE_IS_DISCONNECT(300530, "机器人离线"),

    ;


    private Integer errorCode;

    private String message;

    ExceptionInfoEnum(Integer errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }


}
