package com.youibot.agv.scheduler.mqtt.subscribe.messageHandle;

import com.youibot.agv.scheduler.util.ApplicationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

import static com.youibot.agv.scheduler.mqtt.constants.MqttConstant.*;

public class MessageHandleFactory {

    private static final Logger logger = LoggerFactory.getLogger(MessageHandleFactory.class);
    private static final Map<String, Object> topicsToClassName = new HashMap<String, Object>() {
        {
            put(WILL, (WillMessageHandle) ApplicationUtils.getBean("willMessageHandle"));
            put(POSITION, (PositionMessageHandle) ApplicationUtils.getBean("positionMessageHandle"));
            put(STATUS, (StatusMessageHandle) ApplicationUtils.getBean("statusMessageHandle"));
            put(INFO, (InfoMessageHandle) ApplicationUtils.getBean("infoMessageHandle"));
            put(LOGIN, (LoginMessageHandle) ApplicationUtils.getBean("loginMessageHandle"));
            put(ONLINE, (OnlineMessageHandle) ApplicationUtils.getBean("onlineMessageHandle"));
            put(LOGOUT, (LogoutMessageHandle) ApplicationUtils.getBean("logoutMessageHandle"));
            put(NOTIFY, (NotifyMessageHandle) ApplicationUtils.getBean("notifyMessageHandle"));
            put(MISSION_WORK, (MissionWorkMessageHandle) ApplicationUtils.getBean("missionWorkMessageHandle"));
            put(MISSION_WORK_ACTION, (MissionWorkActionMessageHandle) ApplicationUtils.getBean("missionWorkActionMessageHandle"));
            put(MISSION_WORK_COMMAND_RESULT, (MissionWorkCommandMessageHandle) ApplicationUtils.getBean("missionWorkCommandMessageHandle"));
            put(MAP_RESULT, (MapResultMessageHandle) ApplicationUtils.getBean("mapResultMessageHandle"));
            put(RELOCATION_RESULT, (RelocationMessageHandle) ApplicationUtils.getBean("relocationMessageHandle"));
            put(LASER_DATA, (LaserDataMessageHandle) ApplicationUtils.getBean("laserDataMessageHandle"));
            put(COMMAND_RESULT, (CommandMessageHandle) ApplicationUtils.getBean("commandMessageHandle"));
            put(REQUEST_PATH_PLAN, (RequestPathPlanMessageHandle) ApplicationUtils.getBean("requestPathPlanMessageHandle"));
            put(SIDE_PATH, (SidePathMessageHandle) ApplicationUtils.getBean("sidePathMessageHandle"));
            put(ELEVATOR, (ElevatorMessageHandle) ApplicationUtils.getBean("elevatorMessageHandle"));
            put(ELEVATOR_APPLY, (ElevatorApplyMessageHandle) ApplicationUtils.getBean("elevatorApplyMessageHandle"));
            put(CALL_BOX_APPLY, (CallBoxMessageHandle) ApplicationUtils.getBean("callBoxMessageHandle"));
            put(SUBSCRIBE_CHARGE_TOPIC, (ChargeMessageHandle) ApplicationUtils.getBean("chargeMessageHandle"));
            put(SUBSCRIBE_PARK_TOPIC, (ParkMessageHandle) ApplicationUtils.getBean("parkMessageHandle"));
            put(AUTO_DOOR, (AutoDoorMessageHandle) ApplicationUtils.getBean("autoDoorMessageHandle"));
            put(SUBSCRIBE_ROBOT_COACH_TOPIC, (RobotCoachMessageHandle) ApplicationUtils.getBean("robotCoachMessageHandle"));
            put(SUBSCRIBE_MODBUS_READ_TOPIC, (ModbusReadMessageHandle) ApplicationUtils.getBean("modbusReadMessageHandle"));
            put(SUBSCRIBE_FOREIGN_ACTION_TOPIC, (ForeignActionMessageHandle) ApplicationUtils.getBean("foreignActionMessageHandle"));
            put(SUBSCRIBE_FOREIGN_STATUS_TOPIC, (ForeignStatusMessageHandle) ApplicationUtils.getBean("foreignStatusMessageHandle"));
            put(NAVIGATION_COMPLETE, ApplicationUtils.getBean(NavigationCompleteHandle.class));
            put(SUBSCRIBE_SWITCH_CONFIG_TOPIC, (SwitchConfigMessageHandle) ApplicationUtils.getBean("switchConfigMessageHandle"));
        }
    };

    public static MessageHandle getMessageHandle(String topic) {
        return (MessageHandle) topicsToClassName.get(topic);
    }
}
