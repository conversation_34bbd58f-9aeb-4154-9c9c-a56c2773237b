package com.youibot.agv.scheduler.controller.v3;

import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.entity.AGVMarkerOffset;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.service.AGVMarkerOffsetService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.util.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;
import java.util.stream.Collectors;

@RestController("AGVMarkerOffsetController")
@RequestMapping(value = "/api/v3/agvs/marker/offset", produces = "application/json")
@Api(value = "AGV导航点偏移参数", tags = "AGV导航点偏移参数")
public class AGVMarkerOffsetController extends BaseController {

    @Resource
    private AGVMarkerOffsetService agvMarkerOffsetService;
    @Resource
    private MarkerService markerService;



    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", example = "create_time.desc", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "agvCode", value = "AGV编码", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<AGVMarkerOffset> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        PageInfo<AGVMarkerOffset> result = agvMarkerOffsetService.findPage(searchMap);
        if (result != null && result.getList() != null && !result.getList().isEmpty()) {
            //如果结果不为空，查询关联信息
            result.getList().forEach(agvMarkerOffset -> {
                Marker marker = markerService.selectById(agvMarkerOffset.getAgvMapId(), agvMarkerOffset.getMarkerId(), false);
                if (marker != null) {
                    agvMarkerOffset.setOriginX(marker.getX());
                    agvMarkerOffset.setOriginY(marker.getY());
                    agvMarkerOffset.setMarkerCode(marker.getCode());
                }
            });
        }
        return result;
    }

    @ApiOperation(value = "创建")
    @PostMapping
    @ResponseStatus(value = HttpStatus.CREATED)
    public AGVMarkerOffset save(@RequestBody @Valid AGVMarkerOffset offset, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new YOUIFleetException(bindingResult.getAllErrors().stream().map(objectError -> {
                String key = objectError.getDefaultMessage();
                return MessageUtils.getMessage(key);
            }).collect(Collectors.joining()));
        }
        //校验是否已存在
        AGVMarkerOffset old = agvMarkerOffsetService.findByAGVCodeAndMarkerId(offset.getAgvCode(), offset.getMarkerId());
        if (old != null) {
            throw new YOUIFleetException(MessageUtils.getMessage("service.agv_marker_offset_existed"));
        }
        agvMarkerOffsetService.insert(offset);
        return offset;
    }

    @ApiOperation(value = "编辑")
    @PutMapping
    @ResponseStatus(value = HttpStatus.OK)
    public AGVMarkerOffset edit(@RequestBody @Valid AGVMarkerOffset offset, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new YOUIFleetException(bindingResult.getAllErrors().stream().map(objectError -> {
                String key = objectError.getDefaultMessage();
                return MessageUtils.getMessage(key);
            }).collect(Collectors.joining()));
        }
        agvMarkerOffsetService.update(offset);
        return offset;
    }

    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public int delete(@PathVariable("id") String id) {
        return agvMarkerOffsetService.deleteById(id);
    }

}
