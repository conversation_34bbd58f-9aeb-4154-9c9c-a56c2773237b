package com.youibot.agv.scheduler.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "agv_marker_offset")
public class AGVMarkerOffset implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "select uuid()")
    @ApiModelProperty(value = "ID", position = 0)
    private String id;

    @Column
    @ApiModelProperty(value = "机器人编号", position = 1)
    @NotBlank(message = "service.agv_marker_offset.agv_code_is_null")
    private String agvCode;

    @Column
    @ApiModelProperty(value = "地图ID", position = 2)
    @NotBlank(message = "service.agv_marker_offset.agv_map_is_null")
    private String agvMapId;

    @Column
    @ApiModelProperty(value = "导航点ID", position = 3)
    @NotBlank(message = "service.agv_marker_offset.marker_is_null")
    private String markerId;

    @Transient
    @ApiModelProperty(value = "导航点编号", position = 3)
    private String markerCode;

    @Transient
    @ApiModelProperty(value = "原始X坐标", position = 4)
    private Double originX;

    @Transient
    @ApiModelProperty(value = "原始Y坐标", position = 5)
    private Double originY;

    @Column
    @ApiModelProperty(value = "X方向偏移量(m)", position = 6)
    private Double offsetX;

    @Column
    @ApiModelProperty(value = "Y方向偏移量(m)", position = 7)
    private Double offsetY;

    @Column
    @ApiModelProperty(value = "角度旋转量", position = 8)
    private Double offsetAngle;

    @Column
    @ApiModelProperty(value = "创建时间", position = 9)
    private Date createTime;

    @Column
    @ApiModelProperty(value = "更新时间", position = 10)
    private Date updateTime;
}
