package com.youibot.vehicle.scheduler.modules.map.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.validator.ValidatorUtils;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.dao.PathDao;
import com.youibot.vehicle.scheduler.modules.map.dao.PathDraftDao;
import com.youibot.vehicle.scheduler.modules.map.dto.*;
import com.youibot.vehicle.scheduler.modules.map.entity.*;
import com.youibot.vehicle.scheduler.modules.map.service.*;
import com.youibot.vehicle.scheduler.modules.map.utils.*;
import com.youibot.vehicle.scheduler.modules.map.webSocket.controller.MapUpdateSocketController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PathServiceImpl implements PathService, MapElementService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PathServiceImpl.class);

    @Autowired
    private PathDao pathDao;
    @Autowired
    private PathDraftDao pathDraftDao;
    @Autowired
    private MarkerService markerService;
    @Autowired
    private AutoDoorService autoDoorService;
    @Autowired
    private AirShowerDoorService airShowerDoorService;
    @Autowired
    private UndoManageService undoManageService;
    @Autowired
    private DbUtils dbUtils;

    @Override
    public List<Path> getAllPath(boolean isDraft) {
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("isDraft", isDraft);
        return searchAll(searchMap);
    }

    @Override
    public List<Path> searchAll(Map<String, Object> searchMap) {
        List<Path> list;
        boolean isDraft = Boolean.parseBoolean(searchMap.get("isDraft").toString());
        if (isDraft) {
            List<PathDraft> pathDrafts = pathDraftDao.selectList(getWrapper(searchMap));
            list = ConvertUtils.sourceToTarget(pathDrafts, Path.class);
        } else {
            list = pathDao.selectList(getWrapper(searchMap));
        }
        return list;
    }

    @Override
    public List<ReducePathDTO> searchAllWithLocatingCode(Map<String, Object> searchMap) {
        String locatingCode = (String) searchMap.remove("locatingCode");
        boolean isDraft = Boolean.parseBoolean(searchMap.get("isDraft").toString());
        if (isDraft) {
            List<PathDraft> draftList = pathDraftDao.selectList(getWrapper(searchMap));
            return MapUtils.draftToPathDTO(locatingCode, draftList);
        } else {
            List<Path> list = pathDao.selectList(getWrapper(searchMap));
            return MapUtils.toReducePathDTO(locatingCode, list);
        }
    }

    @Override
    public List<Path> selectCodeByVehicleMapCode(String vehicleMapCode, boolean isDraft) {
        List<Path> paths;
        if (isDraft) {
            QueryWrapper<PathDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.select("code", "start_marker_code", "end_marker_code");
            List<PathDraft> drafts = pathDraftDao.selectList(queryWrapper);
            paths = ConvertUtils.sourceToTarget(drafts, Path.class);
        } else {
            QueryWrapper<Path> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.select("code", "start_marker_code", "end_marker_code");
            paths = pathDao.selectList(queryWrapper);
        }
        return paths;
    }

    @Override
    public List<Path> selectByVehicleMapCode(String vehicleMapCode, boolean isDraft) {
        List<Path> paths;
        if (isDraft) {
            List<PathDraft> drafts = getListByVehicleMapCode(vehicleMapCode, pathDraftDao);
            paths = ConvertUtils.sourceToTarget(drafts, Path.class);
        } else {
            paths = getListByVehicleMapCode(vehicleMapCode, pathDao);
        }
        return paths;
    }

    /**
     * 获取路径详情，不区分定位图
     */
    @Override
    public Path selectByCode(String vehicleMapCode, String code, boolean isDraft) {
        Path path;
        if (isDraft) {
            QueryWrapper<PathDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.eq("code", code);
            PathDraft pathDraft = pathDraftDao.selectOne(queryWrapper);
            path = ConvertUtils.sourceToTarget(pathDraft, Path.class);
        } else {
            QueryWrapper<Path> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.eq("code", code);
            path = pathDao.selectOne(queryWrapper);
        }
        return path;
    }

    /**
     * 获取路径详情，区分定位图
     */
    @Override
    public PathDTO selectByCodeWithLocatingCode(String vehicleMapCode, String locatingCode, String code, boolean isDraft) {
        Path path = selectByCode(vehicleMapCode, code, isDraft);
        return MapUtils.convertToPathDTO(locatingCode, path);
    }

    /**
     * 获取路径集合，不区分定位图
     */
    @Override
    public List<Path> selectByCodes(String vehicleMapCode, List<String> codes, boolean isDraft) {
        List<Path> paths;
        if (isDraft) {
            QueryWrapper<PathDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.in("code", codes);
            List<PathDraft> pathDrafts = pathDraftDao.selectList(queryWrapper);
            paths = ConvertUtils.sourceToTarget(pathDrafts, Path.class);
        } else {
            QueryWrapper<Path> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("vehicle_map_code", vehicleMapCode);
            queryWrapper.in("code", codes);
            paths = pathDao.selectList(queryWrapper);
        }
        return paths;
    }

    /**
     * 获取路径集合，区分定位图
     */
    @Override
    public List<PathDTO> selectByCodesWithLocatingCode(String vehicleMapCode, String locatingCode, List<String> codes, boolean isDraft) {
        List<Path> paths = selectByCodes(vehicleMapCode, codes, isDraft);
        if (CollectionUtils.isEmpty(paths)) {
            return new ArrayList<>();
        }
        return MapUtils.toPathDTO(locatingCode, paths);
    }

    @Override
    @Transactional
    public List<PathDTO> insert(List<PathDTO> paths) {
        if (CollectionUtils.isEmpty(paths)) {
            return null;
        }
        String mapCode = paths.get(0).getVehicleMapCode();
        String locatingCode = paths.get(0).getLocatingCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            Map<String, Path> pathMap = new HashMap<>();
            List<Path> unReDoPaths = new ArrayList<>();
            for (PathDTO pathDTO : paths) {
                Path insertPath = insert(pathDTO);
                unReDoPaths.add(insertPath);
                List<Path> list = this.selectByMarkerCodes(pathDTO.getStartMarkerCode(), pathDTO.getEndMarkerCode(), true);
                list.forEach(it -> {
                    if (!pathMap.containsKey(it.getCode())) {
                        pathMap.put(it.getCode(), it);
                    }
                });
            }
            //发送消息到打开当前地图的窗口
            List<Path> result = new ArrayList<>(pathMap.values());
            MapUpdateSocketController.sendMessageOfAdd(mapCode, "Path", result);
            undoManageService.pushToUndoPool(mapCode, MapConstant.CREATE_ELEMENTS, null, null, null, null, ConvertUtils.sourceToTarget(unReDoPaths, PathDraft.class), null);
            return MapUtils.toPathDTO(locatingCode, result);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("insert path failed:{}", e);
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.path.add.error"));
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    private Path insert(PathDTO pathDTO) {
        String mapCode = pathDTO.getVehicleMapCode();
        String startId = pathDTO.getStartMarkerCode();
        String endId = pathDTO.getEndMarkerCode();
        if (StringUtils.isEmpty(startId) || StringUtils.isEmpty(endId) || endId.equals(startId)) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }

        //若存在，判断方向
        //先判断是否已存在path数据
        List<Path> pathList = this.selectByAgvMapIdAndStartIdOrEndId(startId, endId, mapCode, true);
        //1.查询起始点和终点是否有停靠点，没有：可创建
        if (!CollectionUtils.isEmpty(pathList)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.path.already.exist.error", startId + "," + endId);
            throw new FleetException(message);
        }

        Marker startMarker = markerService.selectByCode(mapCode, pathDTO.getStartMarkerCode(), true);
        Marker endMarker = markerService.selectByCode(mapCode, pathDTO.getEndMarkerCode(), true);
        //关联的点位是否存在
        if (startMarker == null || endMarker == null) {
            String message = I18nMessageUtils.getMessage("vehicleMap.marker.not.exist.error", pathDTO.getStartMarkerCode() + "," + pathDTO.getEndMarkerCode());
            throw new FleetException(message);
        }

        Path path = ConvertUtils.sourceToTarget(pathDTO, Path.class);
        if (path.getWeightRatio() == null || path.getWeightRatio() < 0) {
            path.setWeightRatio(1.0);
        }
        if (path.getParams() == null) {
            path.setParams(new PathParam());
        }
        if (path.getExtendParamList() == null) {
            path.setExtendParamList(new ArrayList<>());
        }
        //形式类似： mapCode_L_1_2
        String newCodeNum = CodeFormatUtils.getIntegerCode(path.getStartMarkerCode()) + CodeFormatUtils.SPLIT_CHAR + CodeFormatUtils.getIntegerCode(path.getEndMarkerCode());
        path.setCode(CodeFormatUtils.getFormatCode(mapCode, newCodeNum, CodeFormatUtils.PATH_CODE_PREFIX));

        //获取当前路径的反向路径
        QueryWrapper<PathDraft> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("start_marker_code", path.getEndMarkerCode());
        queryWrapper.eq("end_marker_code", path.getStartMarkerCode());
        PathDraft reversePath = pathDraftDao.selectOne(queryWrapper);

        path.setPathInfos(buildPathInfos(pathDTO, reversePath, startMarker, endMarker));
        pathDraftDao.insert(ConvertUtils.sourceToTarget(path, PathDraft.class));
        return path;
    }

    private List<PathInfo> buildPathInfos(PathDTO pathDTO, PathDraft reversePath, Marker startMarker, Marker endMarker) {
        List<PathInfo> infoList = new ArrayList<>();

        List<String> locatingCodes = Optional.ofNullable(startMarker.getMarkInfos())
                .map(markInfos -> markInfos.stream().map(MarkerInfo::getLocatingCode).collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        for (String locatingCode : locatingCodes) {
            MarkerInfo startMarkerInfo = startMarker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
            MarkerInfo endMarkerInfo = endMarker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
            if (startMarkerInfo != null && endMarkerInfo != null) {
                PathInfo pathInfo = new PathInfo();
                pathInfo.setLocatingCode(locatingCode);
                //处理控制点
                this.handleControlPoint(pathDTO, pathInfo, reversePath, startMarkerInfo, endMarkerInfo);
                pathInfo.setLength(PathUtils.getLength(pathInfo, startMarkerInfo, endMarkerInfo));
                Double[] inOutAngle = PathUtils.getInOutAngle2(pathInfo, startMarkerInfo, endMarkerInfo);
                pathInfo.setInRadian(inOutAngle[0]);
                pathInfo.setOutRadian(inOutAngle[1]);
                infoList.add(pathInfo);
            }
        }
        return infoList;
    }

    /**
     * 处理控制点：如果有反向路径，则用反向路径的控制点覆盖
     */
    private void handleControlPoint(PathDTO pathDTO, PathInfo pathInfo, PathDraft reversePath, MarkerInfo startMarkerInfo, MarkerInfo endMarkerInfo) {
        String currentLocatingCode = pathDTO.getLocatingCode();
        if (reversePath == null) {
            if (pathInfo.getLocatingCode().equals(currentLocatingCode)) {
                pathInfo.setStartControl(pathDTO.getStartControl());
                pathInfo.setEndControl(pathDTO.getEndControl());
            } else {
                //重新计算控制点
                pathInfo.setStartControl(PathUtils.getControl(startMarkerInfo.getX(), startMarkerInfo.getY(), endMarkerInfo.getX(), endMarkerInfo.getY(), null));
                pathInfo.setEndControl(PathUtils.getControl(endMarkerInfo.getX(), endMarkerInfo.getY(), startMarkerInfo.getX(), startMarkerInfo.getY(), null));
            }
        } else {
            //用反向路径的控制点覆盖
            Optional.ofNullable(reversePath.getPathInfos())
                    .map(infoList -> infoList.stream().filter(it -> it.getLocatingCode().equals(pathInfo.getLocatingCode())).findFirst().orElse(null))
                    .ifPresent(reversePathInfo -> {
                        pathInfo.setStartControl(reversePathInfo.getEndControl());
                        pathInfo.setEndControl(reversePathInfo.getStartControl());
                    });
        }
    }

    @Override
    @Transactional
    public Path update(PathDTO pathDTO) {
        String mapCode = pathDTO.getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            PathDraft pathDraftOfDb = pathDraftDao.selectById(pathDTO.getCode());
            Map<String, PathDraft> pathDraftOfDbMap = new HashMap<>();
            pathDraftOfDbMap.put(pathDTO.getCode(), pathDraftOfDb);
            List<PathDraft> pathDrafts = this.getUpdatedPaths(mapCode, Arrays.asList(pathDTO), pathDraftOfDbMap);
            PathDraft pathDraft = pathDrafts.get(0);
            pathDraftDao.updateById(pathDraft);

            Path path = ConvertUtils.sourceToTarget(pathDraft, Path.class);
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfUpdate(mapCode, "Path", Collections.singletonList(path));
            undoManageService.pushToUndoPool(mapCode, MapConstant.UPDATE_ELEMENTS, null, Collections.singletonList(pathDraftOfDb), null, null, Collections.singletonList(pathDraft), null);
            return path;
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("update path failed", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.path.update.error", pathDTO.getCode());
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    private List<PathDraft> getUpdatedPaths(String mapCode,
                                            List<PathDTO> pathDTOS,
                                            Map<String, PathDraft> pathDraftOfDbMap) {
        List<Marker> markersList = markerService.selectByVehicleMapCode(mapCode, true);
        List<PathDraft> pathDrafts = new ArrayList<>();
        for (PathDTO pathDTO : pathDTOS) {
            String locatingCode = pathDTO.getLocatingCode();
            Map<String, Marker> markers = markersList.stream().collect(Collectors.toMap(Marker::getCode, Function.identity()));
            Marker startMarker = markers.get(pathDTO.getStartMarkerCode());
            Marker endMarker = markers.get(pathDTO.getEndMarkerCode());
            if (startMarker == null || endMarker == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.path.bind.marker.no.exist.error", pathDTO.getStartMarkerCode() + "," + pathDTO.getEndMarkerCode());
                throw new FleetException(message);
            }
            PathDraft pathDraftOfDb = pathDraftOfDbMap.get(pathDTO.getCode());
            if (pathDraftOfDb == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.path.not.exist.error", pathDTO.getCode());
                throw new FleetException(message);
            }

            if (pathDTO.getWeightRatio() == null || pathDTO.getWeightRatio() <= 0.0) {
                pathDTO.setWeightRatio(1.0);
            }
            if (pathDTO.getParams() == null) {
                pathDTO.setParams(new PathParam());
            }
            if (pathDTO.getExtendParamList() == null) {
                pathDTO.setExtendParamList(new ArrayList<>());
            }
            PathDraft pathDraftOfParam = ConvertUtils.sourceToTarget(pathDTO, PathDraft.class);
            pathDraftOfParam.setPathInfos(pathDraftOfDb.getPathInfos());

            //只改locatingCode对应的路径
            pathDraftOfParam.getPathInfos().forEach(pathInfo -> {
                if (pathInfo.getLocatingCode().equals(locatingCode)) {
                    MarkerInfo startMarkerInfo = startMarker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                    MarkerInfo endMarkerInfo = endMarker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);

                    pathInfo.init(pathDTO);
                    pathInfo.setLength(PathUtils.getLength(pathInfo, startMarkerInfo, endMarkerInfo));
                    Double[] inOutAngle = PathUtils.getInOutAngle2(pathInfo, startMarkerInfo, endMarkerInfo);
                    pathInfo.setInRadian(inOutAngle[0]);
                    pathInfo.setOutRadian(inOutAngle[1]);
                }
            });
            pathDrafts.add(pathDraftOfParam);
        }
        return pathDrafts;
    }

    @Override
    public List<PathDTO> batchUpdate(List<PathDTO> pathDTOS) {
        if (CollectionUtils.isEmpty(pathDTOS)) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        String mapCode = pathDTOS.get(0).getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            //效验数据
            if (!CollectionUtils.isEmpty(pathDTOS)) {
                pathDTOS.forEach(path -> ValidatorUtils.validateEntity(path));
            }
            List<PathDraft> oriPaths = pathDraftDao.selectBatchIds(pathDTOS.stream().map(PathDTO::getCode).collect(Collectors.toList()));

            Map<String, PathDraft> pathDraftOfDbMap = oriPaths.stream().collect(Collectors.toMap(PathDraft::getCode, k -> k, (k1, k2) -> k1));
            List<PathDraft> pathDrafts = this.getUpdatedPaths(mapCode, pathDTOS, pathDraftOfDbMap);
            //扩展参数用原数据替代
            pathDrafts.forEach(pd -> pd.setExtendParamList(Optional.ofNullable(pathDraftOfDbMap.get(pd.getCode())).map(PathDraft::getExtendParamList).orElse(new ArrayList<>())));
            dbUtils.updateBatchById(PathDraft.class, pathDrafts, 100);

            //发送消息到打开当前地图的窗口
            List<Path> paths = ConvertUtils.sourceToTarget(pathDrafts, Path.class);
            MapUpdateSocketController.sendMessageOfUpdate(mapCode, "Path", paths);
            undoManageService.pushToUndoPool(mapCode, MapConstant.UPDATE_ELEMENTS, null, oriPaths, null, null, pathDrafts, null);
            return pathDTOS;
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("update path failed", e);
            String collect = pathDTOS.stream().map(PathDTO::getCode).collect(Collectors.joining(","));
            String message = I18nMessageUtils.getMessage("vehicleMap.path.update.error", collect);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    @Override
    public List<PathExtendsParamDTO> batchUpdateExtendParams(List<PathExtendsParamDTO> extendsParamList) {
        if (CollectionUtils.isEmpty(extendsParamList)) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        String mapCode = extendsParamList.get(0).getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            Map<String, PathDraft> pathDraftMap = new HashMap<>();
            List<PathDraft> relatePaths = pathDraftDao.selectBatchIds(extendsParamList.stream().map(PathExtendsParamDTO::getCode).collect(Collectors.toList()));
            for (PathDraft pathDraft : relatePaths) {
                pathDraftMap.put(pathDraft.getCode(), pathDraft);
                List<PathDraft> reversePaths = selectByStartMarkerCodeAndEndMarkerCode(pathDraft.getEndMarkerCode(), pathDraft.getStartMarkerCode(), null);
                if (CollUtil.isNotEmpty(reversePaths)) {
                    reversePaths.forEach(p -> pathDraftMap.put(p.getCode(), p));
                }
            }
            List<PathDraft> oriPaths = new ArrayList<>(pathDraftMap.values());
            List<PathDraft> pathDrafts = JSON.parseArray(JSON.toJSONString(oriPaths), PathDraft.class);

            //扩展参数用新数据替代
            Map<String, PathExtendsParamDTO> pathExtendsParamMap = extendsParamList.stream().collect(Collectors.toMap(PathExtendsParamDTO::getCode, k -> k, (k1, k2) -> k1));
            for (PathDraft pd : pathDrafts) {
                PathExtendsParamDTO pathExtendsParam = pathExtendsParamMap.get(pd.getCode());
                if (pathExtendsParam != null) {
                    pd.setExtendParamList(pathExtendsParam.getExtendParamList());
                }
            }
            dbUtils.updateBatchById(PathDraft.class, pathDrafts, 100);

            //发送消息到打开当前地图的窗口
            List<Path> paths = ConvertUtils.sourceToTarget(pathDrafts, Path.class);
            MapUpdateSocketController.sendMessageOfUpdate(mapCode, "Path", paths);
            undoManageService.pushToUndoPool(mapCode, MapConstant.UPDATE_ELEMENTS, null, oriPaths, null, null, pathDrafts, null);
            return extendsParamList;
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("update path failed", e);
            String collect = extendsParamList.stream().map(PathExtendsParamDTO::getCode).collect(Collectors.joining(","));
            String message = I18nMessageUtils.getMessage("vehicleMap.path.update.error", collect);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    private List<PathDraft> selectByStartMarkerCodeAndEndMarkerCode(String startCode, String endCode, String vehicleMapCode) {
        QueryWrapper<PathDraft> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(vehicleMapCode != null, "vehicle_map_code", vehicleMapCode);
        queryWrapper.eq("start_marker_code", startCode);
        queryWrapper.eq("end_marker_code", endCode);
        List<PathDraft> pathDrafts = pathDraftDao.selectList(queryWrapper);
        return pathDrafts;
    }

    @Override
    public void checkBindDoors(List<Path> paths) {
        if (CollectionUtils.isEmpty(paths)) {
            return;
        }
        List<String> pathIds = paths.stream().map(Path::getCode).collect(Collectors.toList());
        checkBindDoorsWithCodes(pathIds);
    }

    @Override
    public void checkBindDoorsWithCodes(List<String> pathIds) {
        if (CollectionUtils.isEmpty(pathIds)) {
            return;
        }
        //3、绑定的路径是否已经绑定其他门：风淋门、自动门
        List<AutoDoorDTO> autoDoors = autoDoorService.getAllAutoDoors(true);
        if (!CollectionUtils.isEmpty(autoDoors)) {
            autoDoors.forEach(item -> {
                if (!CollectionUtils.isEmpty(item.getPathCodes())) {
                    pathIds.stream().forEach(pathId -> {
                        if (item.getPathCodes().contains(pathId)) {
                            String message = I18nMessageUtils.getMessage("vehicleMap.path.already.bind.device.error", pathId);
                            throw new FleetException(message);
                        }
                    });
                }
            });
        }

        List<AirShowerDoorDTO> airShowerDoors = airShowerDoorService.getAllAirShowerDoors(true);
        Set<String> tmpIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(airShowerDoors)) {
            airShowerDoors.forEach(airShowerDoorDTO -> {
                List<AutoDoorDTO> autoDoorDTOS = airShowerDoorDTO.getDoors();
                if (!CollectionUtils.isEmpty(autoDoorDTOS)) {
                    tmpIds.addAll(autoDoorDTOS.get(0).getPathCodes());
                    tmpIds.addAll(autoDoorDTOS.get(1).getPathCodes());
                }
            });
        }
        if (!CollectionUtils.isEmpty(tmpIds)) {
            pathIds.forEach(pathCode -> {
                if (tmpIds.contains(pathCode)) {
                    String message = I18nMessageUtils.getMessage("vehicleMap.path.already.bind.device.error", pathCode);
                    throw new FleetException(message);
                }
            });
        }
    }

    @Override
    @Transactional
    public void delete(String mapCode, String code) {
        batchDel(mapCode, Arrays.asList(code));
    }

    @Override
    @Transactional
    public void batchDel(String mapCode, List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        List<PathDraft> oriPaths = pathDraftDao.selectBatchIds(codes);
        if (!MapPublishUtil.applyLockForMap(mapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            List<Path> paths = selectByCodes(mapCode, codes, true);
            if (CollectionUtils.isEmpty(paths)) {
                String message = I18nMessageUtils.getMessage("vehicleMap.path.not.exist.error", String.join(",", codes));
                throw new FleetException(message);
            }
            checkBindDoors(paths);
            pathDraftDao.deleteBatchIds(codes);
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfDelete(mapCode, "Path", paths);
            undoManageService.pushToUndoPool(mapCode, MapConstant.DELETE_ELEMENTS, null, oriPaths, null, null, null, null);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("delete path failed:{}", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.path.delete.error", String.join(",", codes));
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    @Override
    public List<Path> selectByVehicleMapCodeAndMarkerCode(String markerCode, String vehicleMapCode, boolean isDraft) {
        List<Path> paths = selectByVehicleMapCode(vehicleMapCode, isDraft);
        return paths.stream().filter(path -> path.getEndMarkerCode().equals(markerCode) || path.getStartMarkerCode().equals(markerCode)).collect(Collectors.toList());
    }

    @Override
    public List<Path> selectByAgvMapIdAndStartIdOrEndId(String startCode, String endCode, String vehicleMapCode, boolean isDraft) {
        List<Path> paths;
        if (isDraft) {
            QueryWrapper<PathDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(vehicleMapCode != null, "vehicle_map_code", vehicleMapCode);
            queryWrapper.eq("start_marker_code", startCode);
            queryWrapper.eq("end_marker_code", endCode);
            List<PathDraft> pathDrafts = pathDraftDao.selectList(queryWrapper);
            paths = ConvertUtils.sourceToTarget(pathDrafts, Path.class);
        } else {
            QueryWrapper<Path> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(vehicleMapCode != null, "vehicle_map_code", vehicleMapCode);
            queryWrapper.eq("start_marker_code", startCode);
            queryWrapper.eq("end_marker_code", endCode);
            paths = pathDao.selectList(queryWrapper);
        }
        return paths;
    }

    @Override
    public List<Path> selectByMarker(Marker marker, boolean isDraft) {
        List<Path> paths = new ArrayList<>();
        if (Objects.isNull(marker)) {
            return paths;
        }
        if (isDraft) {
            QueryWrapper<PathDraft> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(marker.getVehicleMapCode() != null, "vehicle_map_code", marker.getVehicleMapCode());
            queryWrapper.eq("start_marker_code", marker.getCode());
            queryWrapper.or().eq("end_marker_code", marker.getCode());
            List<PathDraft> pathDrafts = pathDraftDao.selectList(queryWrapper);
            paths.addAll(ConvertUtils.sourceToTarget(pathDrafts, Path.class));
        } else {
            QueryWrapper<Path> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(marker.getVehicleMapCode() != null, "vehicle_map_code", marker.getVehicleMapCode());
            queryWrapper.eq("start_marker_code", marker.getCode());
            queryWrapper.or().eq("end_marker_code", marker.getCode());
            paths.addAll(pathDao.selectList(queryWrapper));
        }
        return paths;
    }

    @Override
    public List<Path> selectByStartMarkerCodeAndEndMarkerCode(String startMarkerCode, String endMarkerCode, boolean isDraft) {
        return selectByAgvMapIdAndStartIdOrEndId(startMarkerCode, endMarkerCode, null, isDraft);
    }

    @Override
    public void deleteByVehicleMapCode(String vehicleMapCode) {
        deleteByVehicleMapCode(vehicleMapCode, pathDraftDao);
        deleteByVehicleMapCode(vehicleMapCode, pathDao);
    }

    @Override
    public Map<String, List<Path>> getGroupPath(String vehicleMapCode, boolean isDraft) {
        Map<String, List<Path>> map = new HashMap<>();
        List<Path> pathList = selectByVehicleMapCode(vehicleMapCode, isDraft);
        if (CollectionUtils.isEmpty(pathList)) {
            return map;
        }
        pathList.forEach(path -> {
            String start = CodeFormatUtils.getIntegerCode(path.getStartMarkerCode());
            String end = CodeFormatUtils.getIntegerCode(path.getEndMarkerCode());
            Integer st = start == null ? 0 : Integer.valueOf(start);
            Integer en = end == null ? 0 : Integer.valueOf(end);
            String key = st < en ? start + "_" + end : end + "_" + start;
            List<Path> paths = map.computeIfAbsent(key, k -> new ArrayList<>());
            paths.add(path);
        });
        return map;
    }

    @Override
    public void deleteDraftByVehicleMapCode(String mapCode) {
        deleteByVehicleMapCode(mapCode, pathDraftDao);
    }

    @Override
    public List<Path> selectByMarkerCodes(String markerCode1, String markerCode2, boolean isDraft) {
        List<Path> paths = new ArrayList<>();
        if (isDraft) {
            QueryWrapper<PathDraft> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("start_marker_code", markerCode1);
            queryWrapper1.eq("end_marker_code", markerCode2);
            List<PathDraft> pathDrafts1 = pathDraftDao.selectList(queryWrapper1);
            if (!CollectionUtils.isEmpty(pathDrafts1)) {
                paths.addAll(ConvertUtils.sourceToTarget(pathDrafts1, Path.class));
            }
            QueryWrapper<PathDraft> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.eq("start_marker_code", markerCode2);
            queryWrapper2.eq("end_marker_code", markerCode1);
            List<PathDraft> pathDrafts2 = pathDraftDao.selectList(queryWrapper2);
            if (!CollectionUtils.isEmpty(pathDrafts2)) {
                paths.addAll(ConvertUtils.sourceToTarget(pathDrafts2, Path.class));
            }
        } else {
            QueryWrapper<Path> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("start_marker_code", markerCode1);
            queryWrapper1.eq("end_marker_code", markerCode2);
            List<Path> path1 = pathDao.selectList(queryWrapper1);
            if (!CollectionUtils.isEmpty(path1)) {
                paths.addAll(path1);
            }
            QueryWrapper<Path> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.eq("start_marker_code", markerCode2);
            queryWrapper2.eq("end_marker_code", markerCode1);
            List<Path> path2 = pathDao.selectList(queryWrapper2);
            if (!CollectionUtils.isEmpty(path2)) {
                paths.addAll(path2);
            }
        }
        return paths;
    }

}
