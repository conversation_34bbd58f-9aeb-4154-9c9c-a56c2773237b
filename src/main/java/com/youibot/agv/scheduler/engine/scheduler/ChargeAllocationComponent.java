package com.youibot.agv.scheduler.engine.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerScope;
import com.youibot.agv.scheduler.engine.pathplan.service.MarkerAllocationService;
import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.entity.ChargeScheduler;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.ChargeSchedulerService;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-13 11:37
 * <p>
 * 充电分配流程。可以查看流程图:
 * https://ones.ai/wiki/#/team/81v9LxmG/space/VsJMGqzZ/page/YXudVYRt
 * 1：获取可以充电的机器人，包括空闲和泊车中的。过滤掉作业和充电中的。
 * 2：获取所有的充电桩。根据机器人的位置进行评分。选择跑离最近且空闲的优先。如果非空闲的，根据充电桩上机器人的电量进行评分。
 * 3：分配充电流程。
 */
@Component
public class ChargeAllocationComponent {

    private static final Logger logger = LoggerFactory.getLogger(ChargeAllocationComponent.class);

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private MarkerAllocationService markerAllocationService;

    @Autowired
    private ChargeSchedulerService chargeSchedulerService;

    @Autowired
    private SchedulerConfigService schedulerConfigService;

    @Autowired
    private AGVService agvService;

    /**
     * 充电分配流程
     * 先进行校正充电遍历，再进行普通充电遍历，tip:两种充电类型分开遍历而不是只做一次遍历，是为了防止出现分配不到校正充电的情况
     * 校正充电和分配充电的代码目前没有其他关联，后续可以分别做一层封装
     */
    public void allocation() {
        /**
         * 校正充电遍历
         */
        List<Vehicle> vehicles = vehiclePoolService.getWaitCharge();
        if (CollectionUtils.isEmpty(vehicles)) {
            return;
        }

        // sort vehicle by battery value.
        Collections.sort(vehicles, new Comparator<Vehicle>() {
            @Override
            public int compare(Vehicle o1, Vehicle o2) {
                // 是越小电量的越往前排。升序排序
                return o1.getBatteryValue().compareTo(o2.getBatteryValue());
            }
        });
        //获取调度配置参数
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        int runningCorrectChargeNum = 0;
        for (Vehicle vehicle : vehicles) {
            ThreadContext.put("ROUTINGKEY", vehicle.getId());
            /**
             * 判断当前正在进行校正充电的agv数量，如果当前校正充电的agv数量 >= 最大同时进行校正充电的agv数量,则不进行校正充电分配
             */
            List<ChargeScheduler> runningCorrectCharges = chargeSchedulerService.selectRunningCorrectCharge();
            if (!CollectionUtils.isEmpty(runningCorrectCharges)) {
                runningCorrectChargeNum = runningCorrectCharges.size();
            }
            if (runningCorrectChargeNum >= schedulerConfig.getMaximumCorrectChargeNum()) {
                logger.info("agvCode:[{}],event:[校正充电分配],content:[当前进行校正充电的agv数量为{}，已达到设定的最大值，不进行校正充电分配]", vehicle.getId(), runningCorrectChargeNum);
                break;
            }
            //判断agv电量
            Double currentBatteryValue = vehicle.getBatteryValue();
            if (currentBatteryValue == null) {
                continue;
            }
            /**
             *根据上一次的校正充电时间 > 调度校正充电间隔，则判断需要进行校正充电
             *如果第一次进行校正充电，那么上一次的校正充电时间为null，此时取第一次完成普通充电的时间
             */
            ChargeScheduler scheduler = chargeSchedulerService.selectLatestSuccessCorrectCharge(vehicle.getId());
            if (null == scheduler) {
                scheduler = chargeSchedulerService.selectFirstSuccessCommonCharge(vehicle.getId());
            }
            if (scheduler != null && Math.abs(System.currentTimeMillis() - scheduler.getCreateTime().getTime()) >= schedulerConfig.getCorrectChargeInterval() * 3600 * 1000) {
                /**
                 * 从所有的空闲的充电点中获取评分最高的充电点。
                 * 如果没有则不分配校正充电
                 * 如果有空闲充电点，并且当前进行校正充电的agv数量 < 最大同时进行校正充电的agv数量，进行校正充电分配
                 */
                logger.debug("agvCode:[{}],event:[校正充电分配],content:[机器人上一次校正充电时间{},满足校正充电时间条件]", vehicle.getId(), scheduler.getCreateTime());
                MarkerScope scope = markerAllocationService.getChargeMarkerFromEnable(vehicle);
                if (scope != null) {
                    // add vehicle allocation to list.
                    ChargeScheduler newChargeScheduler = new ChargeScheduler();
                    newChargeScheduler.setVehicleId(vehicle.getId());
                    newChargeScheduler.setChargeId(scope.getMarker().getId());
                    newChargeScheduler.setChargePointCode(scope.getMarker().getCode());
                    newChargeScheduler.setChargePointMapName(scope.getMarker().getAgvMapName());
                    newChargeScheduler.setChargeType(ChargeScheduler.CORRECT_CHARGE);
                    newChargeScheduler.setTriggerType(ChargeScheduler.CORRECT_CHARGE_TRIGGER);
                    chargeSchedulerService.insert(newChargeScheduler);
                    logger.debug("agvCode:[{}],event:[校正充电分配],content:[机器人完成校正充电分配,{}]", vehicle.getId(), JSON.toJSONString(newChargeScheduler));
                    continue;
                }
            }
            ThreadContext.remove("ROUTINGKEY");
        }


        /**
         * 普通充电遍历
         */
        vehicles = vehiclePoolService.getWaitCharge();
        if (CollectionUtils.isEmpty(vehicles)) {
            return;
        }

        // sort vehicle by battery value.
        Collections.sort(vehicles, new Comparator<Vehicle>() {
            @Override
            public int compare(Vehicle o1, Vehicle o2) {
                // 是越小电量的越往前排。升序排序
                return o1.getBatteryValue().compareTo(o2.getBatteryValue());
            }
        });
        for (Vehicle vehicle : vehicles) {
            ThreadContext.put("ROUTINGKEY", vehicle.getId());
            Double currentBatteryValue = vehicle.getBatteryValue();
            if (currentBatteryValue == null) {
                logger.debug("agvCode:[{}],event:[充电分配],content:[机器人电量为空，无法进行充电分配]", vehicle.getId());
                continue;
            }
            /**
             * 普通充电分配
             * 如果电量<=必须充电电量。优先分配空闲的充电点。如果没有空闲的，则按照距离和充电的机器人电量打分，选择分配。
             * 充电点是存在重复分配的。需要单独处理。
             */
            if (currentBatteryValue <= schedulerConfig.getLowBatterValue()) {
                /**
                 * 从所有的充电点中获取评分最高的充电点。
                 *
                 * 1：根据距离进行评分及当前正在充电的机器人进行评分。获取评分最高的充电点。
                 * 2：如果选择的充电点中有机器人占用了，则取消充电的机器人。替换充电。
                 * 3：保存当前机器人的充电调度。
                 *
                 */
                MarkerScope markerScope = markerAllocationService.getChargeMarkerFromAll(vehicle);
                if (markerScope != null) {
                    // 充电点是否已被占用。
                    ChargeScheduler chargeScheduler = chargeSchedulerService.selectRunningByChargeId(markerScope.getMarker().getId());
                    if (chargeScheduler != null) {
                        // 满足可取消充电的条件，他会自动泊车。
                        Vehicle chargeVehicle = vehiclePoolService.selectById(chargeScheduler.getVehicleId());
                        if (chargeVehicle != null && chargeVehicle.getBatteryValue() != null) {
                            //如果任务是低电量触发则必须要等到小车充电到可取消电量才可以取消
                            if (chargeScheduler.getTriggerType() == ChargeScheduler.LOW_BATTERY_TRIGGER) {
                                if (chargeVehicle.getBatteryValue() < schedulerConfig.getCancelBatteryValue()) {
                                    logger.debug("agvCode:[{}],event:[充电分配],content:[取消充电机器人:{}充电失败，未达到可取消电量]", vehicle.getId(), chargeVehicle.getId());
                                    continue;
                                }
                            } else if (chargeScheduler.getTriggerType() == ChargeScheduler.CORRECT_CHARGE_TRIGGER){
                                logger.debug("agvCode:[{}],event:[充电分配],content:[取消充电机器人:{}充电失败，机器人正在进行校正充电]", vehicle.getId(), chargeVehicle.getId());
                                continue;
                            }
                            if (System.currentTimeMillis() - chargeScheduler.getStartTime().getTime() < schedulerConfig.getMinimumChargeTime() * 1000) {
                                logger.debug("agvCode:[{}],event:[充电分配],content:[取消充电机器人:{}充电失败，未达到最短充电时间]", vehicle.getId(), chargeVehicle.getId());
                                continue;
                            }
                            chargeVehicle.cancelCharge();
                            logger.debug("agvCode:[{}],event:[充电分配],content:[取消正在充电机器人:{}]", vehicle.getId(), chargeVehicle.getId());
                            ChargeScheduler newChargeScheduler = new ChargeScheduler();
                            newChargeScheduler.setVehicleId(vehicle.getId());
                            newChargeScheduler.setChargeId(markerScope.getMarker().getId());
                            newChargeScheduler.setTriggerType(ChargeScheduler.LOW_BATTERY_TRIGGER);
                            newChargeScheduler.setChargePointCode(markerScope.getMarker().getCode());
                            newChargeScheduler.setChargeType(ChargeScheduler.COMMON_CHARGE);
                            newChargeScheduler.setChargePointMapName(markerScope.getMarker().getAgvMapName());
                            chargeSchedulerService.insert(newChargeScheduler);
                            logger.debug("agvCode:[{}],event:[充电分配],content:[分配充电完成,ChargeScheduler:{}]", vehicle.getId(), JSON.toJSONString(newChargeScheduler));
                        }
                    } else {
                        ChargeScheduler newChargeScheduler = new ChargeScheduler();
                        newChargeScheduler.setVehicleId(vehicle.getId());
                        newChargeScheduler.setChargeId(markerScope.getMarker().getId());
                        newChargeScheduler.setChargePointCode(markerScope.getMarker().getCode());
                        newChargeScheduler.setChargeType(ChargeScheduler.COMMON_CHARGE);
                        newChargeScheduler.setTriggerType(ChargeScheduler.LOW_BATTERY_TRIGGER);
                        newChargeScheduler.setChargePointMapName(markerScope.getMarker().getAgvMapName());
                        chargeSchedulerService.insert(newChargeScheduler);
                        logger.debug("agvCode:[{}],event:[充电分配],content:[分配充电完成,ChargeScheduler:{}]", vehicle.getId(), JSON.toJSONString(newChargeScheduler));
                    }

                }
            } else {
                /**
                 * 从所有的空闲的充电点中获取评分最高的充电点。
                 * 如果没有则不分配。
                 */
                MarkerScope markerScope = markerAllocationService.getChargeMarkerFromEnable(vehicle);
                if (markerScope != null) {
                    // add vehicle allocation to list.
                    ChargeScheduler newChargeScheduler = new ChargeScheduler();
                    newChargeScheduler.setVehicleId(vehicle.getId());
                    newChargeScheduler.setChargeId(markerScope.getMarker().getId());
                    newChargeScheduler.setChargePointCode(markerScope.getMarker().getCode());
                    newChargeScheduler.setChargeType(ChargeScheduler.COMMON_CHARGE);
                    newChargeScheduler.setTriggerType(ChargeScheduler.FREE_TRIGGER);
                    newChargeScheduler.setChargeId(markerScope.getMarker().getId());
                    newChargeScheduler.setChargePointMapName(markerScope.getMarker().getAgvMapName());
                    chargeSchedulerService.insert(newChargeScheduler);
                    logger.debug("agvCode:[{}],event:[充电分配],content:[分配充电完成,ChargeScheduler:{}]", vehicle.getId(), JSON.toJSONString(newChargeScheduler));
                }
            }
            ThreadContext.remove("ROUTINGKEY");
        }
    }


}
