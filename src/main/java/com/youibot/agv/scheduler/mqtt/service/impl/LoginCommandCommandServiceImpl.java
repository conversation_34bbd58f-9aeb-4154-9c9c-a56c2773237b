package com.youibot.agv.scheduler.mqtt.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.TrafficManager;
import com.youibot.agv.scheduler.engine.pathplan.util.ColorUtils;
import com.youibot.agv.scheduler.entity.AGVLog;
import com.youibot.agv.scheduler.entity.AbnormalPrompt;
import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.Notification;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.mqtt.bean.push.login.LoginResultMessage;
import com.youibot.agv.scheduler.mqtt.bean.push.login.LogoutResultMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.login.LoginMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.login.LogoutMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.InfoMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.LaserDataMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.PositionMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.status.StatusMessage;
import com.youibot.agv.scheduler.mqtt.constants.MqttConstant;
import com.youibot.agv.scheduler.mqtt.constants.StatusMessageHolder;
import com.youibot.agv.scheduler.mqtt.result.ResultEnum;
import com.youibot.agv.scheduler.mqtt.service.LoginCommandService;
import com.youibot.agv.scheduler.mqtt.service.MqttService;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.param.ErrorParam;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.DateUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import org.apache.commons.lang3.ArrayUtils;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.Semaphore;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_FAULT;
import static com.youibot.agv.scheduler.constant.NotificationConstant.UNREAD;
import static com.youibot.agv.scheduler.constant.VehicleConstant.*;

/**
 * <AUTHOR>
 * @date 2020/9/1 20:01
 */
@Service
public class LoginCommandCommandServiceImpl implements LoginCommandService {

    private Logger logger = LoggerFactory.getLogger(LoginCommandCommandServiceImpl.class);

    @Autowired
    private AGVService agvService;

    @Autowired
    private VehiclePool vehiclePool;

    @Autowired
    private PathPlanService pathPlanService;

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;

    @Autowired
    private AGVLogService agvLogService;

    @Autowired
    private TrafficManager trafficManager;

    @Autowired
    private MqttService mqttService;

    @Autowired
    private AGVMapService agvMapService;

    @Autowired
    private AbnormalPromptService abnormalPromptService;

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private NotificationService notificationService;

    @Override
    public void detachVehicle(String agvCode) {
        if (vehiclePool.getVehicle(agvCode) == null) {
            logger.debug("删除的vehicle不存在");
            return;
        }
        logger.debug("agvCode:[{}],event:[登出流程]，小车状态改为断线", agvCode);
        agvService.updateOnlineStatusByAgvCode(agvCode, OFFLINE);
        vehiclePool.detachVehicle(agvCode);
        logger.debug("agvCode:[{}],event:[登出流程]，解绑小车完成", agvCode);
    }

    @Override
    public void attachVehicle(Agv agv) {
        vehiclePool.attachAGV(agv);
    }

    @Override
    public void updateVehiclePosition(PositionMessage positionMessage) {
        Vehicle vehicle = getVehicle(positionMessage.getAgvCode());
        DefaultVehicleStatus defaultVehicleStatus = vehicle.getDefaultVehicleStatus();
        if (defaultVehicleStatus == null) {
            defaultVehicleStatus = new DefaultVehicleStatus();
        }
        defaultVehicleStatus.setPosition(positionMessage.getPositionStatus());
        vehicle.setDefaultVehicleStatus(defaultVehicleStatus);
        checkAndSendPathService.agvPositionCallBack(positionMessage);
    }

    private Vehicle getVehicle(String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        if (vehicle == null) {
            logger.debug("机器人未登录");
            throw new YOUIFleetException("机器人未登录");
        }
        return vehicle;
    }

    @Override
    public void updateVehicleStatus(StatusMessage statusMessage) {
        Vehicle vehicle = getVehicle(statusMessage.getAgvCode());
        if (vehicle == null) {
            Agv agv = agvService.selectByAgvCode(statusMessage.getAgvCode());
            vehiclePool.attachAGV(agv);
            vehicle = vehiclePool.getVehicle(statusMessage.getAgvCode());
        }

        //记录小车错误日志记录
        recordErrorlog(vehicle, statusMessage);

        vehicle.setUseElevator(statusMessage.isUseElevator());
        vehicle.setWorkStatus(statusMessage.getWorkStatus());
        vehicle.setAbnormalStatus(statusMessage.getAbnormalStatus());
        vehicle.setErrorMessage(statusMessage.getErrorMessage());
        vehicle.setControlMode(statusMessage.getControlMode());
        vehicle.setMapStatus(statusMessage.getMapStatus());
        vehicle.setAppointStatus(statusMessage.getAppointStatus());
        vehicle.setAgvMapId(statusMessage.getAgvMapId());
        vehicle.setSimulation(statusMessage.isSimulation());
        //是否可执行状态
//        vehicle.setIsExecutable(statusMessage.getIsExecutable());
//        vehicle.setWorkSequence(statusMessage.getWorkSequence());
        /**
         * TODO 需要添加。
         * 如果状态是 RuntimeStatus.agv_status=3 表示被阻档，并且 RuntimeStatus.Jam_start_time 为空。则设置Jam_start_time=当前时间。
         * 如果状态是 RuntimeStatus.agv_status!=3 且RuntimeStatus.Jam_start_time 不为空 。则设置Jam_start_time=null。
         */

        DefaultVehicleStatus defaultVehicleStatus = vehicle.getDefaultVehicleStatus();
        if (defaultVehicleStatus == null) {
            defaultVehicleStatus = new DefaultVehicleStatus();
        }
        defaultVehicleStatus.setSpeed(statusMessage.getSpeedStatus());
        defaultVehicleStatus.setEmec(statusMessage.getEmecStatus());
        defaultVehicleStatus.setBattery(statusMessage.getBatteryStatus());
        defaultVehicleStatus.setMotorStatus(statusMessage.getDeviceStatus());

        //异常编码集合
        List<Integer> errorDataCodes = new ArrayList<>();
        //异常对象集合
        List<ErrorParam> errorParams = new ArrayList<>();

        List<ErrorParam> missionErrorParams = new ArrayList<>();
        //获取当前任务
        MissionWork runningMissionWork = missionWorkService.selectFaultMissionWorkByAgvCode(statusMessage.getAgvCode());
        if (Objects.nonNull(runningMissionWork)) {
            missionErrorParams.add(new ErrorParam(runningMissionWork.getErrorCode(), runningMissionWork.getMessage()));
        }
        vehicle.setMissionErrorData(missionErrorParams);
        //设置充电、泊车异常,异常码统一为204000
        if (!StringUtils.isEmpty(statusMessage.getErrorMessage())) {
            errorParams.add(new ErrorParam(204000, statusMessage.getErrorMessage()));
        }
        //设置机器人自身的异常编码
        DefaultVehicleStatus.ErrorStatus errorStatus = statusMessage.getErrorStatus();
        defaultVehicleStatus.setStatus(errorStatus);
        if (null != statusMessage.getErrorStatus()) {
            Integer[] errorCodes = statusMessage.getErrorStatus().getErr_code();
            if (!ArrayUtils.isEmpty(errorCodes)) {
                errorDataCodes.addAll(Arrays.asList(errorCodes));
            }
        }
        //设置机器人急停，低电量相关的异常编码
        List<Integer> ecs = statusMessage.getErrorCodes();
        if (!CollectionUtils.isEmpty(ecs)) {
            errorDataCodes.addAll(ecs);
        }

        StringBuilder errorMessageBuffer = new StringBuilder();
        String errorMsg = null;
        for (Integer ec : errorDataCodes) {
            errorMsg = abnormalPromptService.getAbnormalMsgByCode(ec);
            if (!StringUtils.isEmpty(errorMsg)) {
                errorMessageBuffer.append(errorMsg).append(";");
                errorParams.add(new ErrorParam(ec, errorMsg));
            } else {
                errorMessageBuffer.append("消息管理中找不到异常码").append(ec).append("的配置信息").append(";");
                errorParams.add(new ErrorParam(ec, "机器人系统异常，请检查异常配置"));
            }
        }

        //设置机器人的异常状态信息
        if (errorMessageBuffer.length() > 0) {
            defaultVehicleStatus.setErrorMessages(errorMessageBuffer.toString());
        } else {
            defaultVehicleStatus.setErrorMessages("");
        }

        //设置机器人的异常对象（含充电、泊车异常）
        if (!CollectionUtils.isEmpty(errorParams)) {
            vehicle.setErrorData(errorParams);
        } else {
            vehicle.setErrorData(new ArrayList<>());
        }

        vehicle.setDefaultVehicleStatus(defaultVehicleStatus);
        vehicle.setMustChargeBatteryValue(statusMessage.getMustChargeBatteryValue());
        vehicle.setLastMarkerId(statusMessage.getLastMarkerId());
        this.syncAGVFiled(vehicle);

        pathPlanService.agvStatusCallBack(statusMessage);
    }

    //记录小车错误日志记录
    private void recordErrorlog(Vehicle vehicle, StatusMessage statusMessage) {
        //小车正常、上报上来的状态异常，则需要记录
        if (ABNORMAL_STATUS_NO.equals(vehicle.getAbnormalStatus()) && !ABNORMAL_STATUS_NO.equals(statusMessage.getAbnormalStatus())) {
            //插入记录
            AGVLog agvLog = new AGVLog();
            agvLog.setCreateTime(new Date());
            agvLog.setStartTime(agvLog.getCreateTime().getTime());
            agvLog.setAgvCode(vehicle.getId());
            agvLog.setType(AGV_LOG_TYPE_ERROR);//
            agvLogService.insert(agvLog);
        } else if (!ABNORMAL_STATUS_NO.equals(vehicle.getAbnormalStatus()) && ABNORMAL_STATUS_NO.equals(statusMessage.getAbnormalStatus())) {
            //更新记录，最近一条记录
            AGVLog agvLog = agvLogService.selectlastestByAgvCodeAndType(vehicle.getId(), AGV_LOG_TYPE_ERROR);
            if (agvLog != null) {
                agvLog.setEndTime(new Date().getTime());
                agvLogService.update(agvLog);
            }
        }
    }


    private void syncAGVFiled(Vehicle vehicle) {
        Agv agv = agvService.selectByAgvCode(vehicle.getId());
        boolean change = false;
        //任务状态
        if (!Objects.equals(vehicle.getWorkStatus(), agv.getWorkStatus())) {
            agv.setWorkStatus(vehicle.getWorkStatus());
            change = true;
        }
        //控制模式
        if (!Objects.equals(vehicle.getControlMode(), agv.getControlMode())) {
            agv.setControlMode(vehicle.getControlMode());
            change = true;
        }
        //指定地图状态
        if (!Objects.equals(vehicle.getAppointStatus(), agv.getAppointStatus())) {
            agv.setAppointStatus(vehicle.getAppointStatus());
            change = true;
        }
        //地图同步状态
        if (!Objects.equals(vehicle.getMapStatus(), agv.getMapStatus())) {
            agv.setMapStatus(vehicle.getMapStatus());
            change = true;
        }
        //异常状态
        if (!Objects.equals(vehicle.getAbnormalStatus(), agv.getAbnormalStatus())) {
            agv.setAbnormalStatus(vehicle.getAbnormalStatus());
            change = true;
        }

        if (change) {
            agvService.update(agv);
        }
    }

    @Override
    public void updateAgvInfo(InfoMessage infoMessage) {
        //2021.7.28调度添加机器人类型管理，此处接收到的类型不做处理
//        infoMessage.setAgvType(null);
        String agvCode = infoMessage.getAgvCode();
        Vehicle vehicle = getVehicle(agvCode);
        if (vehicle == null) {
            Agv agv = new Agv();
            BeanUtils.copyProperties(infoMessage, agv);
            vehiclePool.attachAGV(agv);
        } else {
            BeanUtils.copyProperties(infoMessage, vehicle);
            vehicle.setName(infoMessage.getAgvName());
        }
        //设置agv
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (agv == null) {
            logger.error("更新agv数据时,agv数据不存在");
            return;
        }
        BeanUtils.copyProperties(infoMessage, agv);
        agv.setShapeInfo(JSON.toJSONString(infoMessage.getShapeInfo()));
        agvService.update(agv);
    }

    @Async("asyncTaskExecutor")
    @Override
    public void login(LoginMessage loginMessage) {
        //设置登录返回内容
        LoginResultMessage loginResultMessage = new LoginResultMessage(ResultEnum.SUCCESS.getCode(), ResultEnum.SUCCESS.getMessage(), System.currentTimeMillis());
        String agvCode = loginMessage.getAgvCode();

        //此处目的：为了防止机器人先进行登出，后进行登录，但最终登录先完成，登出后完成，导致的调度和机器人的登录状态不一致问题。
        //获取对应机器人的信号量。若不存在，则直接进行登录逻辑；若存在，则获取信号量，获取成功后，执行后续逻辑。
        Semaphore semaphore = MqttUtils.semaphoresMap.get(agvCode);
        MqttClient client = null;
        try {
            if (semaphore != null) {
                semaphore.acquire();
                logger.debug("agvCode:[{}],event:[登录流程]，登录获取锁成功", agvCode);
            }
            Agv agvDto = agvService.selectByAgvCode(agvCode);
            if (agvDto == null) {//未查询到数据，新增
                agvDto = new Agv();
                agvDto.setAgvCode(agvCode);
                agvDto.setAgvId(loginMessage.getAgvId());
                agvDto.setStatus(DISABLE);//默认机器人未启用
                //设置机器人颜色
                agvDto.setAgvColor(ColorUtils.getRandomColorString());
                agvService.insert(agvDto);
                client = this.createVehicleAndClient(agvDto);

                logger.debug("agvCode:[{}],event:[登录流程]，新建agv记录，设置为默认未启用", agvCode);

            } else if (StringUtils.isEmpty(agvDto.getAgvId()) || loginMessage.getAgvId().equals(agvDto.getAgvId())) {//查询到数据,不管在线状态,尝试创建连接
                if (StringUtils.isEmpty(agvDto.getAgvId())) {
                    agvDto.setAgvId(loginMessage.getAgvId());
                }
                agvDto.setOnlineStatus(ONLINE);
                agvService.updateByPrimaryKeySelective(agvDto);
                //创建该机器人对应的emq客户端
                client = this.createVehicleAndClient(agvDto);

                logger.debug("agvCode:[{}],event:[登录流程]，小车记录已存在，建立mq连接", agvCode);

            } else {//agvId不一致，登录冲突
                loginResultMessage = new LoginResultMessage(ResultEnum.LOGIN_FAIL.getCode(), ResultEnum.LOGIN_FAIL.getMessage());

                logger.debug("agvCode:[{}],event:[登录流程]，agvId不一致，登录冲突，不建立mq连接", agvCode);

            }
        } catch (Exception e) {
            logger.error("agvCode:[{}],event:[登录流程], 登录失败，失败原因:{}", agvCode, e);
            loginResultMessage = new LoginResultMessage(ResultEnum.FAIL.getCode(), ResultEnum.FAIL.getMessage() + e.getMessage());
        } finally {
            //释放信号量
            if (semaphore != null) {
                semaphore.release();
                logger.debug("agvCode:[{}],event:[登录流程]，登录释放锁成功", agvCode);
            }
        }

        MqttClient newClient = null;
        try {
            //若连接不存在，则创建连接发送消息。
            if (client == null) {
                client = mqttService.createSendClient();
                newClient = client;
            }
            //发送登录结果消息
            MqttUtils.sendMqttMsg(MqttConstant.LOGIN_RESULT, agvCode, loginResultMessage, client);
            //同步地图版本给所有在线的机器人
            //this.syncMapVersion(agvCode);
            logger.debug("agvCode:[{}],event:[登录流程],发送mq消息成功：[{}]", agvCode, JSONObject.toJSONString(loginResultMessage));
        } finally {
            //关闭client连接
            if (newClient != null) {
                closeClient(newClient);
            }
        }
    }

    private void syncMapVersion(String agvCode) {
//        List<AGVMap> enableAGVMap = agvMapService.getEnableAGVMap();
//        MapUpdateMessage mapUpdateMessage = new MapUpdateMessage();
//        if (!CollectionUtils.isEmpty(enableAGVMap)) {
//            List<MapVersionMessage> list = new ArrayList<>();
//            enableAGVMap.forEach(agvMap -> {
//                MapVersionMessage mapVersionMessage = new MapVersionMessage();
//                mapVersionMessage.setAgvMapId(agvMap.getId());
//                mapVersionMessage.setLayerVersion(agvMap.getLayerVersion());
//                mapVersionMessage.setVersion(agvMap.getVersion());
//                list.add(mapVersionMessage);
//            });
//            mapUpdateMessage.setMapVersionMessageList(list);
//            MqttUtils.sendMqttMsg(MqttConstant.MAP_UPDATE, agvCode, mapUpdateMessage);
//        }
    }

    /**
     * 创建vehicle和mqtt客户端连接
     *
     * @param agv
     */
    @Override
    public MqttClient createVehicleAndClient(Agv agv) throws YOUIFleetException {
        String agvCode = agv.getAgvCode();
        MqttClient client = null;
        logger.debug("---------------start创建client和vehicle：----------------{}", agvCode);
        synchronized (agvCode) {
            logger.debug("---------------ing创建client和vehicle：----------------{}", agvCode);
            vehiclePool.attachAGV(agv);
            client = mqttService.createClient(agvCode);
            if (client == null) {
                throw new YOUIFleetException("create mqtt client fail");
            }
        }
        return client;
    }

    @Async("asyncTaskExecutor")
    @Override
    public void logout(LogoutMessage logoutMessage) {
        LogoutResultMessage logoutResultMessage;
        String agvCode = logoutMessage.getAgvCode();
        Semaphore semaphore = MqttUtils.semaphoresMap.get(agvCode);
        try {
            if (semaphore == null) {
                semaphore = new Semaphore(1);
                MqttUtils.semaphoresMap.put(agvCode, semaphore);
            }
            semaphore.acquire();
            logger.debug("agvCode:[{}],event:[登出流程],获取锁成功", agvCode);
            //清楚状态消息缓存
            StatusMessageHolder.getInstance().clearCache(agvCode);
            this.detachVehicle(agvCode);
            logoutResultMessage = new LogoutResultMessage(ResultEnum.LOGOUT_SUCCESS.getCode(), ResultEnum.LOGOUT_SUCCESS.getMessage());
            //1.离线日志
            agvLogService.updateEndTimeByAgvCodeAndType(agvCode, AGV_LOG_TYPE_ON_LINE);
            MapGraphUtil.clearAGVPosition(agvCode);

            trafficManager.releaseApplyByAGV(agvCode);
            logger.debug("agvCode:[{}],event:[登出流程],释放资源完成", agvCode);
        } catch (Exception e) {
            logger.error("agvCode:[{}],event:[登出流程], 登出失败,异常原因:[{}]", agvCode, e);
            logoutResultMessage = new LogoutResultMessage(ResultEnum.LOGOUT_FAIL.getCode(), ResultEnum.LOGOUT_FAIL.getMessage());
        } finally {
            semaphore.release();
            logger.debug("agvCode:[{}],event:[登出流程],释放锁成功", agvCode);
        }

        MqttClient client = mqttService.getClientByAgvCode(agvCode);
        boolean clientIsExist = client == null || !client.isConnected();
        try {
            if (clientIsExist) {
                logger.debug("agvCode:[{}],event:[登出流程],创建Client", agvCode);
                client = mqttService.createSendClient();
            }

            MqttUtils.sendMqttMsg(MqttConstant.LOGOUT_RESULT, agvCode, logoutResultMessage, client);
            logger.debug("agvCode:[{}],event:[登出流程],发送登出结果：[{}]", agvCode, JSONObject.toJSONString(logoutResultMessage));
            mqttService.closeClient(agvCode);
        } finally {
            if (clientIsExist) {
                closeClient(client);
            } else {
                mqttService.closeClient(agvCode);
            }
        }
    }

    private void closeClient(MqttClient client) {
        try {
            if (!client.isConnected()) {
                client.disconnect();
            }
            client.close();
        } catch (MqttException e) {
            logger.error("销毁发送消息emq连接失败");
        }
    }

    @Override
    public void updateLaserData(LaserDataMessage laserDataMessage) {
        vehiclePool.getVehicle(laserDataMessage.getAgvCode()).setLaserData(laserDataMessage.getLaserData());
    }

    @Override
    public void agvOutline(String agvCode) {
        StatusMessageHolder.getInstance().clearCache(agvCode);
        //1.离线日志
        agvLogService.updateEndTimeByAgvCodeAndType(agvCode, AGV_LOG_TYPE_ON_LINE);
        //2.更新agv在线状态
        //vehiclePool.getVehicle(agvCode).setWorkStatus(null);
        updateOnlineStatus(agvCode, OUTLINE);
        logger.debug("agvCode:[{}],event:[遗言], 设置小车状态为离线", agvCode);

        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        if (vehicle != null) {//清除机器人的状态未知信息
            vehicle.setDefaultVehicleStatus(null);
        }

        //推送断线通知
        pushNotification(agvCode);
    }

    private void pushNotification(String agvCode){
        Notification notification = new Notification();
        //根据异常码获取异常信息
        Integer errorCode = ExceptionInfoEnum.VEHICLE_IS_DISCONNECT.getErrorCode();
        AbnormalPrompt abnormalByCode = abnormalPromptService.getAbnormalByCode(errorCode);
        if (abnormalByCode != null) {
            notification.setScale(abnormalByCode.getAbnormalLevel());
            notification.setType(abnormalByCode.getAbnormalType());
            notification.setDescription(abnormalByCode.getAbnormalDescription());
            notification.setHelp(abnormalByCode.getHelp());
        } else {
            notification.setDescription("消息管理中找不到异常码" + errorCode + "的配置信息");
        }
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        notification.setErrorCode(errorCode);
        notification.setAgvCode(agvCode);
        notification.setAgvName(Optional.ofNullable(vehicle).map(Vehicle::getName).orElse(null));
        notification.setMissionWorkId(Optional.ofNullable(vehicle).map(Vehicle::getMissionWorkId).orElse(null));
        notification.setMissionWorkName(Optional.ofNullable(vehicle).map(Vehicle::getMissionName).orElse(null));
        notification.setHaveRead(UNREAD);
        notificationService.sendMessage(notification);
    }


    @Override
    public void agvOnline(String agvCode) {
        //查询该机器人是否存在未完成的在线日志信息
        AGVLog agvLog = agvLogService.selectUnCompletedByAgvCodeAndType(agvCode, AGV_LOG_TYPE_ON_LINE);
        boolean saveOnlineLog = false;
        if (agvLog == null) {
            saveOnlineLog = true;
        } else if (agvLog != null && DateUtils.greaterThanOneday(System.currentTimeMillis(), agvLog.getCreateTime().getTime())) {
            agvLogService.setCurrentEndTimeByCreateTime(agvLog, System.currentTimeMillis());
            saveOnlineLog = true;
            logger.debug("agvCode:[{}],event:[上线通知],设置结束日期，创建时间为：{}----------------", agvCode, agvLog.getCreateTime());
        }
        if (saveOnlineLog) {
            onlineLog(agvCode);
        }

        //2.更新agv在线状态
        updateOnlineStatus(agvCode, ONLINE);
        logger.debug("agvCode:[{}],event:[上线通知],设置小车状态为在线", agvCode);
    }

    private void onlineLog(String agvCode) {
        //1.在线日志
        AGVLog onLineLog = new AGVLog(agvCode, AGV_LOG_TYPE_ON_LINE, System.currentTimeMillis() / 1000);
        logger.debug("agvCode:[{}],event:[上线通知],开始新增agvLog----------------", agvCode);
        agvLogService.insert(onLineLog);
        logger.debug("agvCode:[{}],event:[上线通知],结束新增agvLog----------------", agvCode);
    }

    private void updateOnlineStatus(String agvCode, Integer status) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        if (vehiclePool.getVehicle(agvCode) == null) {
            return;
        }
        vehicle.setOnlineStatus(status);
        //vehicle.setWorkStatus(null);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (agv == null) {
            logger.error("服务器数据异常");
            return;
        }
        agv.setOnlineStatus(status);
        agvService.update(agv);
    }
}
