package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.vo.MissionActionStatisticVO;
import com.youibot.agv.scheduler.constant.vo.MissionStatisticVO;
import com.youibot.agv.scheduler.constant.vo.VehicleStatisticVO;
import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.AGVStatusLogStatisticService;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.util.DateUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据统计
 * @Author：xyh
 * @Date: 2022/3/3 9:00
 */
@RestController("dataStatisticController")
@RequestMapping("/api/v3/statistic")
@Api(value = "数据统计", tags = "提供给tms,做大屏展示，搜集小车的数据、任务数据", description = "")
@Slf4j
public class DataStatisticController {

    private static Logger logger = LoggerFactory.getLogger(DataStatisticController.class);

    @Autowired
    private VehiclePoolService vehiclePoolService;
    @Autowired
    private MissionWorkActionService missionWorkActionService;
    @Autowired
    private AGVStatusLogStatisticService agvStatusLogStatisticService;
    @Autowired
    private AGVMapService agvMapService;





    @ApiOperation(value = "返回机器人状态集合信息", notes = "返回机器人状态集合信息,由调用方决定调用频次")
    @GetMapping("/getVehicleBasicInfo")
    @ResponseStatus(HttpStatus.OK)
    public List<VehicleStatisticVO> getVehicleBasicInfo(@RequestParam(value = "agvCode", required = false) String agvCode) {

        List<VehicleStatisticVO> list = new ArrayList<>();
        //1、如果小车id不为空，查单个
        if(StringUtils.isNotBlank(agvCode)){
            Vehicle vehicle = vehiclePoolService.selectById(agvCode);
            if(vehicle!=null){
                VehicleStatisticVO tmp = serialize(vehicle);
                list.add(tmp);
            }
            fillMapName(list);
            return list;
        }

        //2、查询所有
        List<Vehicle> vehicles = vehiclePoolService.selectAll();
        if(CollectionUtils.isEmpty(vehicles)){
            return list;
        }
        for(Vehicle vehicle : vehicles){
            VehicleStatisticVO tmp = serialize(vehicle);
            list.add(tmp);
        }
        fillMapName(list);

        return list;
    }


    //序列化
    private VehicleStatisticVO serialize(Vehicle vehicle){
        String jsonString = JSONObject.toJSONString(vehicle);
        VehicleStatisticVO tmp = JSONObject.parseObject(jsonString,VehicleStatisticVO.class);
        tmp.setAgvCode(vehicle.getId());

        //如果为空，设置一个默认值：未充电
        if(tmp.getDefaultVehicleStatus()!=null
                && tmp.getDefaultVehicleStatus().getBattery()!=null
                && tmp.getDefaultVehicleStatus().getBattery().getBattery_status()==null){
            tmp.getDefaultVehicleStatus().getBattery().setBattery_status(0);
        }
        return tmp;
    }

    //填充地图名称
    private void fillMapName(List<VehicleStatisticVO> list){

        if(CollectionUtils.isEmpty(list)){
           return;
        }
        List<String> mapIdList = list.stream().map(VehicleStatisticVO::getAgvMapId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(mapIdList)){
            return;
        }

        List<AGVMap> agvMaps = new ArrayList<>();
        mapIdList.forEach(mapId->{
            AGVMap agvMap = agvMapService.selectById(mapId);
            if(agvMap!=null){
                agvMaps.add(agvMap);
            }
        });
        if(CollectionUtils.isEmpty(agvMaps)){
            return;
        }

        Map<String, AGVMap> collect = agvMaps.stream().collect(Collectors.toMap(AGVMap::getId, Function.identity()));
        if(collect==null || collect.size()<=0){
            return;
        }
        for(VehicleStatisticVO item : list){
            String agvMapId = item.getAgvMapId();
            AGVMap agvMap = collect.get(agvMapId);
            if(agvMap!=null){
                item.setAgvMapName(agvMap.getName());
            }
        }
    }


    @ApiOperation(value = "报表小车集合", notes = "报表小车集合")
    @GetMapping("/getAgvCodeList")
    @ResponseStatus(HttpStatus.OK)
    public List<String> getAgvCodeList() {
        return agvStatusLogStatisticService.getAgvCodeList();
    }


    @ApiOperation(value = "返回机器人在某个时间段的作业统计信息：类型为NONE时，时间可不传，其他类型时间必传", notes = "返回机器人在某个时间段的统计信息,传入agvCode,查询单个、多个，不穿则查询所有")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "agvCode", value = "agvCode,形如：agvCode1,agvCode2,agvCode3,agvCode4", required = false, dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd HH:mm:ss", required = false, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd HH:mm:ss", required = false, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "枚举值：NONE、HOUR、DAY、MONTH", required = true, dataType = "String")
    })
    @GetMapping("/getMissionWorkStatistic")
    @ResponseStatus(HttpStatus.OK)
    public Map<String, Map<String, MissionStatisticVO>> getMissionWorkStatistic(@RequestParam(value = "agvCode", required = false) String agvCode,
                                                        @RequestParam(value = "startTime", required = false) String startTime,
                                                        @RequestParam(value = "endTime", required = false) String endTime,
                                                        @RequestParam(value = "type", required = true) String type) {

        log.info("TMS 调用作业统计开始:agvCode:{},startTime:{},endTime:{},type:{}", agvCode,startTime,endTime,type);
        if(StringUtils.isBlank(type)){
            type = "NONE";
        }
        //NONE类型可以不传时间
        if("NONE".equals(type)){
            if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
                Date startDate = DateUtils.getFormatDate(startTime);
                Date endDate = DateUtils.getFormatDate(endTime);
                if(startDate==null || endDate==null || startDate.after(endDate)){
                    throw new ExecuteException(MessageUtils.getMessage("http.params_error"));
                }
            }
        }else{
            if(StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
                throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
            }
            Date startDate = DateUtils.getFormatDate(startTime);
            Date endDate = DateUtils.getFormatDate(endTime);
            if(startDate==null || endDate==null || startDate.after(endDate)){
                throw new ExecuteException(MessageUtils.getMessage("http.params_error"));
            }
        }

        if(StringUtils.isBlank(agvCode)){
            agvCode = null;
        }
        Map<String, Map<String, MissionStatisticVO>> missionActionStatistic = agvStatusLogStatisticService.getAGVStatusLogStatistic(agvCode, startTime, endTime,type);
        log.info("TMS 调用作业统计结束:结果:{}", JSONObject.toJSONString(missionActionStatistic));
        return missionActionStatistic;
    }


    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 格式：yyyy-MM-dd HH:mm:ss", required = false, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 格式：yyyy-MM-dd HH:mm:ss", required = false, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "枚举值：NONE、HOUR、DAY、MONTH", required = true, dataType = "String")
    })
    @ApiOperation(value = "返回任务动作在某个时间段的统计信息：类型为NONE时，时间可不传，其他类型时间必传", notes = "返回任务动作在某个时间段的统计信息,传入agvCode,查询单个，否则查询所有")
    @GetMapping("/getMissionWorkActionStatistic")
    @ResponseStatus(HttpStatus.OK)
    public Map<String,List<MissionActionStatisticVO>> getMissionWorkActionStatistic(@RequestParam(value = "agvCode", required = false) String agvCode,
                                                                        @RequestParam(value = "startTime", required = false) String startTime,
                                                                        @RequestParam(value = "endTime", required = false) String endTime,
                                                                        @RequestParam(value = "type", required = true) String type) {

        log.info("TMS 调用动作统计开始:agvCode:{},startTime:{},endTime:{},type:{}", agvCode,startTime,endTime,type);
        if(StringUtils.isBlank(type)){
            type = "NONE";
        }
        //NONE类型可以不传时间
        if("NONE".equals(type)){
            if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
                Date startDate = DateUtils.getFormatDate(startTime);
                Date endDate = DateUtils.getFormatDate(endTime);
                if(startDate==null || endDate==null || startDate.after(endDate)){
                    throw new ExecuteException(MessageUtils.getMessage("http.params_error"));
                }
            }
        }else{
            if(StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
                throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
            }
            Date startDate = DateUtils.getFormatDate(startTime);
            Date endDate = DateUtils.getFormatDate(endTime);
            if(startDate==null || endDate==null || startDate.after(endDate)){
                throw new ExecuteException(MessageUtils.getMessage("http.params_error"));
            }
        }
        if(StringUtils.isBlank(agvCode)){
            agvCode = null;
        }
        Map<String,List<MissionActionStatisticVO>> missionActionStatistic = missionWorkActionService.getMissionActionStatistic(agvCode, startTime, endTime,type);
        log.info("TMS 调用动作统计结束:结果:{}", JSONObject.toJSONString(missionActionStatistic));
        return missionActionStatistic;
    }



    @ApiOperation(value = "统计数据补偿接口", notes = "")
    @GetMapping("/updateHistoryData")
    @ResponseStatus(HttpStatus.OK)
    public void updateHistoryData(@RequestParam(value = "agvCode", required = false) String agvCode,
                                                                                    @RequestParam(value = "startTime", required = false) String startTime,
                                                                                    @RequestParam(value = "endTime", required = false) String endTime) {
        Date startDate = DateUtils.getFormatDate(startTime);
        Date endDate = DateUtils.getFormatDate(endTime);
        if(StringUtils.isBlank(agvCode) || startDate==null || endDate==null || startDate.after(endDate)){
            throw new ExecuteException(MessageUtils.getMessage("http.params_error"));
        }
        agvStatusLogStatisticService.updateSingleHistoryData(agvCode,startTime,endTime);
    }
}
