package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 15:34
 */
public class MissionConstant {

    // mission action type  RuntimeParameter
    public static final String MISSION_ACTION_PARAM_TYPE_NORMAL = "NORMAL_PARAMETER";//正常参数
    public static final String MISSION_ACTION_PARAM_TYPE_RUNTIME = "RUNTIME_PARAMETER";//运行时参数

    // mission work action type
    public static final String MISSION_WORK_ACTION_TYPE_RUNTIME = "RUNTIME_INPUT";//运行时输入action

    // mission work status
    public static final String MISSION_WORK_STATUS_CREATE = "CREATE";//创建(未执行)
//    public static final String MISSION_WORK_STATUS_ASSIGNED = "ASSIGNED";//已分配
    public static final String MISSION_WORK_STATUS_WAIT = "WAIT";//等待(继续)执行
//    public static final String MISSION_WORK_STATUS_START = "START";//开始执行
    public static final String MISSION_WORK_STATUS_RUNNING = "RUNNING";//执行中
    public static final String MISSION_WORK_STATUS_SUCCESS = "SUCCESS";//执行成功
    public static final String MISSION_WORK_STATUS_FAULT = "FAULT";//执行错误
    public static final String MISSION_WORK_STATUS_PAUSE = "PAUSE";//已暂停
    public static final String MISSION_WORK_STATUS_BEING_PAUSE = "BEING_PAUSE";//暂停中
    public static final String MISSION_WORK_STATUS_BEING_RESUME = "BEING_RESUME";//恢复中
    public static final String MISSION_WORK_STATUS_SHUTDOWN = "SHUTDOWN";//已停止
    public static final String MISSION_WORK_STATUS_BEING_SHUTDOWN = "BEING_SHUTDOWN";//停止中
    public static final String MISSION_WORK_STATUS_WAIT_INPUT = "WAITINPUT";//等待输入
    public static final String MISSION_WORK_STATUS_REJECT = "REJECT";//拒绝
    public static final String MISSION_WORK_STATUS_DOCKING_ERROR = "DOCKING_ERROR";// 对接异常

    // mission work action status.
    public static final String MISSION_WORK_ACTION_STATUS_START = "START";//开始执行
    public static final String MISSION_WORK_ACTION_STATUS_RUNNING = "RUNNING";//执行中
    public static final String MISSION_WORK_ACTION_STATUS_SUCCESS = "SUCCESS";//执行成功
    public static final String MISSION_WORK_ACTION_STATUS_FAULT = "FAULT";//执行错误

    //检测mission work是否是否有冲突
    public static final Integer MISSION_WORK_CREATE_NO_CONFLICT = 0;//无冲突
    public static final Integer MISSION_WORK_CREATE_ASSIGN_CONFLICT = 1;//AGV分配规则冲突

    //返回的数据类型
    public static final String RESULT_TYPE_IMAGE = "IMAGE";//图片

    //任务呼叫状态
    public static final String MISSION_CALL_NORMAL_STATUS = "NORMAL";//正常
    public static final String MISSION_CALL_ABNORMAL_STATUS = "ABNORMAL";//异常

    public static final String CRATE_AND_EXECUTE="1";// 创建任务并执行任务



    // 是否启用末端精定位: 0不启用 1启用，默认是不启用
    public static final String MISSION_ACTION_PARAM_ACCURATE_KEY = "accurate_stop";//开启
    public static final Integer MISSION_ACTION_PARAM_ACCURATE_OPEN = 1;//开启
    public static final Integer MISSION_ACTION_PARAM_ACCURATE_CLOSE = 0;//关闭

    //任务、动作统计类型
    public static final String MISSION_statistic_type_none = "NONE";//
    public static final String MISSION_statistic_type_hour = "HOUR";//
    public static final String MISSION_statistic_type_day = "DAY";//
    public static final String MISSION_statistic_type_month = "MONTH";//

}
