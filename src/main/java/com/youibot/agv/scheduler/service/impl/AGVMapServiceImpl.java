package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.util.MapCompressUtil;
import com.youibot.agv.scheduler.engine.pathplan.util.PathUtils;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.entity.vo.PathVo;
import com.youibot.agv.scheduler.entity.vo.SidePathVo;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.*;
import com.youibot.agv.scheduler.map.utils.MapFileUtils;
import com.youibot.agv.scheduler.map.utils.ZipUtils;
import com.youibot.agv.scheduler.mqtt.bean.push.map.BatchOperateMessage;
import com.youibot.agv.scheduler.mqtt.bean.push.map.MapCurrentMessage;
import com.youibot.agv.scheduler.mqtt.bean.push.map.MapDataMessage;
import com.youibot.agv.scheduler.mqtt.bean.push.map.MapPushMessage;
import com.youibot.agv.scheduler.mqtt.constants.MqttConstant;
import com.youibot.agv.scheduler.mqtt.service.MapCommandService;
import com.youibot.agv.scheduler.mqtt.service.MqttService;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.*;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.MAP_INFO_SUFFIX;
import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.MD5_SUFFIX;
import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.ROAD_NETWORK_SUFFIX;
import static com.youibot.agv.scheduler.map.utils.MapFileUtils.*;

@Slf4j
@Service
public class AGVMapServiceImpl implements AGVMapService {

    @Autowired
    public MapCommandService mapCommandService;
    @Autowired
    public DefaultVehiclePool defaultVehiclePool;
    @Autowired
    public MqttService mqttService;
    @Autowired
    public PathService pathService;
    @Autowired
    public SidePathService sidePathService;
    @Autowired
    public AGVMapUpdateQueueService agvMapUpdateQueueService;
    @Autowired
    public AdjustActionService adjustActionService;
    @Autowired
    private MarkerService markerService;

    private static final Logger LOGGER = LoggerFactory.getLogger(AGVMapServiceImpl.class);

    @Override
    public AGVMap insert(AGVMap agvMap) {

        //校验地图名称是否合法
        boolean mapNameValid = MapFileUtils.checkMapNameValid(agvMap.getName());
        if (!mapNameValid) {
            throw new ExecuteException("地图名称不合法");
        }
        FTPClient client = null;
        try {
            if (MapFileUtils.nameExists(agvMap.getName())) {
                throw new ExecuteException(MessageFormat.format(MessageUtils.getMessage("service.name_is_exists"), agvMap.getName()));
            }
            client = FtpUtils.getConnectClient();
            agvMap.setId(agvMap.getName());
            agvMap.setNegate(0.0);
            agvMap.setFree_thresh(0.196);
            agvMap.setOccupied_thresh(0.65);
            if (agvMap.getOriginYaw() == null) {
                agvMap.setOriginYaw(0.0);
            }
            // 生成对应物理像素的白色底图
            String base64 = MapCompressUtil.generatePic(agvMap.getHeight(), agvMap.getWidth());
            String imageFilePath = MAP_INFO_PATH + agvMap.getName() + LOCATION_DATA_PATH;
            Base64Utils.base64ToImage(base64, imageFilePath, agvMap.getName() + ".png", client);
            agvMap.setImage(agvMap.getName() + ".png");
            String infoFilePath = MapFileUtils.getFilePath(MAP_INFO_SUFFIX, agvMap.getName(), false);
            MapFileUtils.writeValue(JSONObject.toJSONString(agvMap), infoFilePath, client);
            AGVMapMd5 agvMapMd5 = new AGVMapMd5();
            agvMapMd5.setInfoMd5(DigestUtils.md5DigestAsHex(client.retrieveFileStream(infoFilePath)));
            client.completePendingCommand();
            agvMapMd5.setPngMd5(DigestUtils.md5DigestAsHex(client.retrieveFileStream(imageFilePath + agvMap.getName() + ".png")));
            client.completePendingCommand();
            String md5FilePath = MAP_INFO_PATH + agvMap.getName() + LOCATION_DATA_PATH + agvMap.getName() + ".md5";
            MapFileUtils.writeValue(JSONObject.toJSONString(agvMapMd5), md5FilePath, client);
            refresh(agvMap.getName());
            return agvMap;
        } catch (ExecuteException e) {
            LOGGER.error("add map failed ： {}", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("add map failed ： {}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.add_map_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public AGVMap selectById(String id) {
        MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(id);
        if (mapGraphInfo != null) {
            return mapGraphInfo.getAgvMap();
        }
        return null;
    }

    @Override
    public AGVMapResult selectAGVMapResultById(String id) {
        MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(id);
        if (mapGraphInfo != null) {
            AGVMapResult agvMapResult = new AGVMapResult();
            agvMapResult.setAgvMap(mapGraphInfo.getAgvMap());
            agvMapResult.setLastUpdateTime(mapGraphInfo.getLastUpdateTime());
            return agvMapResult;
        }
        return null;
    }

    @Override
    public AGVMap update(AGVMap agvMap) {
        FTPClient client = null;
        try {
            MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(agvMap.getName());
            if (mapGraphInfo == null) {
                throw new ExecuteException(MessageUtils.getMessage("service.agv_map_is_null"));
            }
            client = FtpUtils.getConnectClient();
            MapFileUtils.writeValue(JSONObject.toJSONString(agvMap), MapFileUtils.getFilePath(MAP_INFO_SUFFIX, agvMap.getName(), false), client);
            return agvMap;
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("update map failed ： {}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.update_map_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public void delete(String id) {
        FTPClient client = null;
        try {
            MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(id);
            if (mapGraphInfo == null) {
                throw new ExecuteException(MessageUtils.getMessage("service.agv_map_is_null"));
            }
            client = FtpUtils.getConnectClient();
            MapFileUtils.deleteFiles(MapFileUtils.MAP_INFO_PATH + id, client);
            AGVMapInfoCache.removeCache(id);
            MapGraphUtil.removeAGVMap(id);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("delete map failed ： {}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.delete_map_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public boolean selectDraftFile(String id) {
        FTPClient client = null;
        try {
            String filePath = MAP_INFO_PATH + id + DRAFT_DATA_PATH;
            client = FtpUtils.getConnectClient();
            if (!client.changeWorkingDirectory(filePath)) {
                return false;
            }
            FTPFile[] ftpFiles = client.listFiles();
            return null != ftpFiles && ftpFiles.length > 0;
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("read draft file failed:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_draft_file_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public void deleteDraftFile(String id) {
        FTPClient client = null;
        try {
            String filePath = MAP_INFO_PATH + id + DRAFT_DATA_PATH;
            client = FtpUtils.getConnectClient();
            MapFileUtils.deleteFiles(filePath, client);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("delete draft file failed:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_draft_file_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public synchronized void batchUpdatePathInfo(PathResultData pathResult, String agvMapName) {
        FTPClient client = null;
        try {
            Map<String, Marker> markers = pathResult.getMarkers();
            Map<String, Path> paths = pathResult.getPaths();
            Map<String, SidePath> sidePaths = pathResult.getSidePaths();
            Map<String, MapArea> mapAreas = pathResult.getMapAreas();
            Map<String, AdjustAction> adjustActions = pathResult.getAdjustActions();
            if (!CollectionUtils.isEmpty(markers)) {
                Set<Path> updatePaths = new HashSet<>();
                markers.keySet().forEach(id -> {
                    List<Path> updatePath = pathService.selectByAgvMapIdMarkerId(id, agvMapName, true);
                    if (!CollectionUtils.isEmpty(updatePath)) {
                        updatePaths.addAll(updatePath);
                    }
                });
                Map<String, Path> collect = updatePaths.stream().collect(Collectors.toMap(Path::getId, path -> path));
                collect.putAll(paths);
                paths = collect;
            }
            client = FtpUtils.getConnectClient();
            String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapName, true);
            String result = MapFileUtils.getDraftFileData(filePath, agvMapName, client);
            if (StringUtils.isEmpty(result)) {
                LOGGER.error("read path file is null");
                throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
            }
            PathResultData roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);
            if (!CollectionUtils.isEmpty(markers)) {
                roadNetwork.getMarkers().putAll(markers);
            }
            List<SidePath> delSidePaths = new ArrayList<>();
            if (!CollectionUtils.isEmpty(paths)) {
                for (Path path : paths.values()) {
                    path.setLength(PathUtils.getLength(path, roadNetwork.getMarkers().get(path.getStartMarkerId()), roadNetwork.getMarkers().get(path.getEndMarkerId())));
                    List<SidePath> delSidePath = sidePathService.selectByPathId(path.getId(), agvMapName, true);

                    PathVo pathVo = new PathVo();
                    pathVo.setPath(path);
                    pathVo.setSidePathVos(delSidePath.stream().map(sidePath->ConvertUtils.sourceToTarget(sidePath,SidePathVo.class)).collect(Collectors.toList()));
                    Map<String, SidePath> addSidePath = pathService.createSidePathsByPathVo(pathVo, roadNetwork.getMarkers().get(path.getStartMarkerId()), roadNetwork.getMarkers().get(path.getEndMarkerId()))
                            .stream().collect(Collectors.toMap(SidePath::getId, sidePath -> sidePath));
                    delSidePaths.addAll(delSidePath);
                    sidePaths.putAll(addSidePath);
                }
            }

            if (!CollectionUtils.isEmpty(paths)) {
                roadNetwork.getPaths().putAll(paths);
            }
            if (!CollectionUtils.isEmpty(sidePaths)) {
                Map<String, SidePath> sidePathList = roadNetwork.getSidePaths();
                delSidePaths.forEach(sidePath -> sidePathList.remove(sidePath.getId()));
                sidePathList.putAll(sidePaths);
                roadNetwork.setSidePaths(sidePathList);
            }
            if (!CollectionUtils.isEmpty(mapAreas)) {
                roadNetwork.getMapAreas().putAll(mapAreas);
            }
            if (!CollectionUtils.isEmpty(adjustActions)) {
                roadNetwork.getAdjustActions().putAll(adjustActions);
            }
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(roadNetwork), filePath, client);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("Failed to execute batchUpdatePathInfo method e:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.update_path_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public synchronized void batchDelPathInfo(PathResultData pathResult, String agvMapName) {
        FTPClient client = null;
        try {
            Map<String, Marker> markers = pathResult.getMarkers();
            Map<String, Path> paths = pathResult.getPaths();
            Map<String, SidePath> sidePaths = pathResult.getSidePaths();
            Map<String, MapArea> mapAreas = pathResult.getMapAreas();
            Map<String, AdjustAction> adjustActions = pathResult.getAdjustActions();
            client = FtpUtils.getConnectClient();
            String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapName, true);
            String result = MapFileUtils.getDraftFileData(filePath, agvMapName, client);
            if (StringUtils.isEmpty(result)) {
                LOGGER.error("read path file is null");
                throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
            }
            PathResultData roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);
            if (!CollectionUtils.isEmpty(markers)) {
                Set<Path> delPaths = new HashSet<>();
                List<AdjustAction> adjustActionList = new ArrayList<>();
                markers.keySet().forEach(id -> {
                    List<Path> delPath = pathService.selectByAgvMapIdMarkerId(id, agvMapName, true);
                    if (!CollectionUtils.isEmpty(delPath)) {
                        delPaths.addAll(delPath);
                    }
                    List<AdjustAction> adjustAction = adjustActionService.selectByMarkerId(id, agvMapName, true);
                    adjustActionList.addAll(adjustAction);
                });
                if (!CollectionUtils.isEmpty(delPaths)) {
                    paths.putAll(delPaths.stream().collect(Collectors.toMap(Path::getId, path -> path)));
                }
                if (!CollectionUtils.isEmpty(adjustActionList)) {
                    adjustActions.putAll(adjustActionList.stream().collect(Collectors.toMap(AdjustAction::getId, adjustAction -> adjustAction)));
                }
            }
            if (!CollectionUtils.isEmpty(paths)) {
                for (Path path : paths.values()) {
                    List<SidePath> delSidePath = sidePathService.selectByPathId(path.getId(), agvMapName, true);
                    sidePaths.putAll(delSidePath.stream().collect(Collectors.toMap(SidePath::getId, sidePath -> sidePath)));
                }
                paths.keySet().forEach(s -> roadNetwork.getPaths().remove(s));
                sidePaths.keySet().forEach(s -> roadNetwork.getSidePaths().remove(s));
            }
            if (!CollectionUtils.isEmpty(markers)) markers.keySet().forEach(s -> roadNetwork.getMarkers().remove(s));
            if (!CollectionUtils.isEmpty(mapAreas)) mapAreas.keySet().forEach(s -> roadNetwork.getMapAreas().remove(s));
            if (!CollectionUtils.isEmpty(adjustActions))
                adjustActions.keySet().forEach(s -> roadNetwork.getAdjustActions().remove(s));
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(roadNetwork), filePath, client);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            throw new ExecuteException(MessageUtils.getMessage("service.delete_path_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public void pushMapData(String mapName) {

        if (org.apache.commons.lang3.StringUtils.isBlank(mapName)) {
            throw new ExecuteException(MessageUtils.getMessage(MessageUtils.getMessage("http.missing_parameter")));
        }

        Date today = new Date();

        AGVMapUpdateQueue task1 = new AGVMapUpdateQueue();
        task1.setId(UUID.randomUUID().toString());
        task1.setCreateTime(today);
        task1.setMapName(mapName);
        task1.setStatus(AGVMapUpdateQueue.STATUS_CREATE);
        task1.setLevel(AGVMapUpdateQueue.LEVEL_HIGH);
        task1.setType("draft_2_current");
        agvMapUpdateQueueService.insert(task1);

        //修改创建时间保证task2在task1之后执行
        AGVMapUpdateQueue task2 = new AGVMapUpdateQueue();
        task2.setId(UUID.randomUUID().toString());
        task2.setCreateTime(new Date(today.getTime() + 1000));//让这三个高优先级的任务顺序执行
        task2.setMapName(mapName);
        task2.setStatus(AGVMapUpdateQueue.STATUS_CREATE);
        task2.setLevel(AGVMapUpdateQueue.LEVEL_HIGH);
        task2.setType("current_2_memory");
        agvMapUpdateQueueService.insert(task2);

        //检查队列中添加到内存中任务完成后再返回发布成功
        while (true) {
            AGVMapUpdateQueue agvMapUpdateTask = agvMapUpdateQueueService.selectById(task2.getId());
            if (agvMapUpdateTask.getStatus().equals("finish") || agvMapUpdateTask.getStatus().equals("failed")) {
                break;
            }
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                LOGGER.error("sleep error e:{}", e);
            }
        }

    }

    @Override
    public String importMapData(MultipartFile multiPartFile) throws Exception {

        if (multiPartFile == null) {
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }
        byte[] data = CommonUtils.getByteArray(multiPartFile.getInputStream());
        if (data == null || data.length <= 0) {
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }

        String originalFilename = multiPartFile.getOriginalFilename();
        originalFilename = originalFilename.lastIndexOf(".") >= 0 ? originalFilename.substring(0, originalFilename.lastIndexOf(".")) : originalFilename;
        String tmpDir = MAP_INFO_PATH + "tmp/" + originalFilename + "/";
        File tmpDirFile = new File(tmpDir);
        String mapName = null;
        FTPClient ftpClient = FtpUtils.getConnectClient();
        try {
            //写入临时文件夹
            try {
                CommonUtils.writeZipData(tmpDir, new ByteArrayInputStream(data, 0, data.length));
            } catch (Exception e) {
                e.printStackTrace();
                LOGGER.error("将地图数据写入临时文件夹{}异常：", tmpDir, e);
                throw new ExecuteException("地图文件写入临时目录操作失败！");
            }


            if (!tmpDirFile.exists() || tmpDirFile.listFiles() == null) {
                throw new ExecuteException("地图文件写入临时目录操作失败！");
            }
            mapName = tmpDirFile.listFiles()[0].getName();
            File mapFile = new File(tmpDir + mapName + LOCATION_DATA_PATH + mapName + ".info");
            if (!mapFile.exists()) {
                throw new ExecuteException("地图文件名与地图名称不一致");
            }

            AGVMap agvMap = JSONObject.parseObject(CommonUtils.readFileData(mapFile.getPath()), AGVMap.class);
            if (agvMap == null || !mapName.equals(agvMap.getName())) {
                throw new ExecuteException("info文件为空，或者地图文件名与地图名称不一致");
            }
            //校验地图名称是否合法
            boolean mapNameValid = MapFileUtils.checkMapNameValid(mapName);
            if (!mapNameValid) {
                LOGGER.error("地图名称不合法,{}", mapName);
                throw new ExecuteException(MessageUtils.getMessage("service.agv_map_name_is_invalid"));
            }

            if (!checkMapDataValid(tmpDir, mapName,agvMap)) {
                throw new ExecuteException("地图文件校验失败！");
            }

            //3、写入数据

            //切换到  /home/<USER>/map
            ftpClient.changeWorkingDirectory(MapFileUtils.MAP_INFO_PATH);

            //有没有当前地图，不存在的话创建
            boolean hasMap = false;
            FTPFile ftpFile = null;
            FTPFile[] ftpFiles = ftpClient.listDirectories();
            if (ftpFiles != null && ftpFiles.length > 0) {
                for (FTPFile item : ftpFiles) {
                    if (item.getName().equalsIgnoreCase(mapName)) {
                        ftpFile = item;
                        hasMap = true;
                        break;
                    }
                }
            }
            if (hasMap) {
                //1、存在的话，备份，删除后覆盖
                CommonUtils.doTransMapDataFromSrcToDest(ftpClient, ftpFile, "current", "back", true);

                //2、删除对应地图的current目录
                MapFileUtils.deleteFiles(MapFileUtils.MAP_INFO_PATH + mapName + "/locating/current/*", ftpClient);
                MapFileUtils.deleteFiles(MapFileUtils.MAP_INFO_PATH + mapName + "/path/current/*", ftpClient);
            }

            //feat: 检查path属性
            byte[] newData = movePathParamToSidePathAndCheckWeightRatio(tmpDir, mapName);
            if (newData != null && newData.length > 1) {
                data = newData;
            }

            //3、上传
            CommonUtils.uploadZipToFtp(ftpClient, MapFileUtils.MAP_INFO_PATH, new ByteArrayInputStream(data, 0, data.length));
        } catch (ExecuteException e) {
            LOGGER.error("导入地图{}异常：", mapName, e);
            throw e;
        } catch (Exception e) {
            //throw new RuntimeException("upload map data failed");
            LOGGER.error("导入地图{}异常：", mapName, e);
            throw new ExecuteException(MessageUtils.getMessage("service.save_ftp_file_fail"));
        } finally {
            //删除临时目录
            MapFileUtils.deleteFile(new File(tmpDir));
//            MapFileUtils.deleteFiles(MAP_INFO_PATH + "tmp/", ftpClient);
            //关闭连接
            FtpUtils.disconnect(ftpClient);
        }

        waitUntilUpdateMemoryOK(mapName);

        return "ok";
    }

    /**
     * 1.检查path上面的pathParam属性并转移到SidePath上面
     * 2.检查SidePath的权重属性,如果为null或者复数则更新为1
     * 3.更新路径md5值
     *
     * @param tmpDir 路径
     * @param mapName 地图名称
     * @return 更新后的压缩包字节流
     */
    private byte[] movePathParamToSidePathAndCheckWeightRatio(String tmpDir, String mapName) {
        boolean updateMap = false;
        String pathFilePath = tmpDir + mapName + ROAD_NETWORK_PATH + mapName + ".path";
        String md5FilePath = tmpDir + mapName + ROAD_NETWORK_PATH + mapName + ".md5";

        String mapInfoContent = MapFileUtils.getFileStrContent(pathFilePath);
        if (StringUtils.isEmpty(mapInfoContent)) {
            return null;
        }
        JSONObject mapInfoJson = JSON.parseObject(mapInfoContent);
        JSONArray sidePathArrays = mapInfoJson.getJSONArray("sidePaths");
        if (sidePathArrays == null) {
            LOGGER.warn("当前地图中没有sidePaths数据");
            return null;
        }

        JSONArray pathArray = mapInfoJson.getJSONArray("paths");
        if (pathArray == null) {
            LOGGER.warn("当前地图中没有paths数据");
            return null;
        }

        List<Path> newPath = new ArrayList<>(pathArray.size());
        for (int i = 0; i < pathArray.size(); i++) {
            JSONObject jsonObject = pathArray.getJSONObject(i);
            Path path = jsonObject.toJavaObject(Path.class);
            newPath.add(path);
        }
        mapInfoJson.put("paths", newPath);

        // 新的sidePath集合
        List<SidePath> sidePathsList = sidePathArrays.toJavaList(SidePath.class);
        for (int i = 0; i < sidePathsList.size(); i++) {
            SidePath sidePath = sidePathsList.get(i);
            if (sidePath.getWeightRatio() == null || sidePath.getWeightRatio() <= 0) {
                updateMap = true;
                sidePath.setWeightRatio(1.0);
            }
        }
        mapInfoJson.put("sidePaths", sidePathsList);

        try {
            if(updateMap){
                // 更新path文件
                String newData = JSONObject.toJSONString(mapInfoJson, SerializerFeature.DisableCircularReferenceDetect);
                File pathFile = new File(pathFilePath);
                MapFileUtils.writeValue(newData, pathFile);
                // 更新md5值
                String md5String = MD5Utils.fileEncryption(pathFilePath);
                PathMd5 pathMd5 = new PathMd5();
                pathMd5.setPathMd5(md5String);
                File pathMd5File = new File(md5FilePath);
                MapFileUtils.writeValue(JSON.toJSONString(pathMd5), pathMd5File);

                // 压缩文件返回流字节
                ByteArrayOutputStream output = new ByteArrayOutputStream();
                ZipUtils.toZip(tmpDir + "/" + mapName, output, true);
                return output.toByteArray();
            }
        } catch (IOException e) {
            LOGGER.error("地图重新写入失败", e);
            return null;
        }
        return null;
    }

    private boolean checkMapDataValid(String path, String mapName,AGVMap agvMap) {

        path = path.replace("\\", "/").replace("//", "/");

        //校验定位文件
        String md5Suffix = ".md5";
        String locatingPath = path + mapName + "/locating/current/" + mapName + md5Suffix;
        boolean locatingValid = true;
        //存在才校验
        File locatingMd5File = new File(locatingPath);
        if (locatingMd5File.exists()) {
            String locatingStr = MapFileUtils.getFileStrContent(locatingPath);
            AGVMapMd5 locatingMd5 = JSONObject.parseObject(locatingStr, AGVMapMd5.class);
            if (locatingMd5 == null) {
                LOGGER.error("获取压缩包内 locating md5文件为空");
                return false;
            }
            locatingValid = compareLocalMapInfoMd5(locatingMd5, path + mapName + "/locating/current/", mapName,agvMap);
        }

        //校验路网文件
        String pathPath = path + mapName + "/path/current/" + mapName + md5Suffix;
        boolean pathValid = true;
        //存在才校验
        File pathMd5File = new File(pathPath);
        if (pathMd5File.exists()) {
            String pathStr = MapFileUtils.getFileStrContent(pathPath);
            PathMd5 pathMd5 = JSONObject.parseObject(pathStr, PathMd5.class);
            if (pathMd5 == null) {
                LOGGER.error("获取压缩包内 path md5文件为空");
                return false;
            }
            pathValid = pathMd5.getPathMd5().equals(MD5Utils.fileEncryption(path + mapName + "/path/current/" + mapName + ".path"));
            LOGGER.warn("校验{}的path结果：{}", mapName, pathValid);
        }

        return locatingValid && pathValid;
    }

    private boolean compareLocalMapInfoMd5(AGVMapMd5 localMapInfoMd5, String localMapInfoPath, String mapName,AGVMap agvMap) {
        //比对locating/current/.info
        LOGGER.warn("校验{}的Info文件", mapName);
        if (!localMapInfoMd5.getInfoMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + mapName + ".info"))) {
            LOGGER.warn("本地{}的Info文件损坏或者不存在", mapName);
            return false;
        }

        LOGGER.warn("校验{}的png文件", mapName);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(localMapInfoMd5.getPngMd5()) && !localMapInfoMd5.getPngMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + mapName + ".png"))) {
            LOGGER.warn("本地{}的png文件损坏或者不存在", mapName);
            return false;
        }

        //比对locating/current/.pcd
        LOGGER.warn("校验{}的pcd文件", mapName);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(localMapInfoMd5.getPcdMd5()) && !localMapInfoMd5.getPcdMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + mapName + ".pcd"))) {
            LOGGER.warn("本地{}的pcd文件损坏或者不存在", mapName);
            return false;
        }

        //比对locating/current/.pgd
        LOGGER.warn("校验{}的pgd文件", mapName);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(localMapInfoMd5.getPgdMd5()) && !localMapInfoMd5.getPgdMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + mapName + ".pgd"))) {
            LOGGER.warn("本地{}的pgd文件损坏或者不存在", mapName);
            return false;
        }

        //比对locating/current/.yaml
        LOGGER.warn("校验{}的yaml文件", mapName);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(localMapInfoMd5.getYamlMd5()) && !localMapInfoMd5.getYamlMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + mapName + ".yaml"))) {
            LOGGER.warn("本地{}的yaml文件损坏或者不存在", mapName);
            return false;
        }

        //比对locating/current/ _og.yaml
        LOGGER.warn("校验{}的og yaml文件", mapName);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(localMapInfoMd5.getOgYamlMd5()) && !localMapInfoMd5.getOgYamlMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + mapName + "_og.yaml"))) {
            LOGGER.warn("本地{}的og yaml文件损坏或者不存在", mapName);
            return false;
        }

        //比对locating/current/ _og.png
        LOGGER.warn("校验{}的og png文件", mapName);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(localMapInfoMd5.getOgPngMd5()) && !localMapInfoMd5.getOgPngMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + mapName + "_og.png"))) {
            LOGGER.warn("本地{}的og png文件损坏或者不存在", mapName);
            return false;
        }

        //比对locating/current/ .g2o
        LOGGER.warn("校验{}的G2o文件", mapName);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(localMapInfoMd5.getG2oMd5()) && !localMapInfoMd5.getG2oMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + mapName + "_graph.g2o"))) {
            LOGGER.warn("本地{}的G2o文件损坏或者不存在", mapName);
            return false;
        }

        //比对locating/current/ .g2o.kernels
        LOGGER.warn("校验{}的G2oKernels文件", mapName);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(localMapInfoMd5.getG2oKernelsMd5()) && !localMapInfoMd5.getG2oKernelsMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + mapName + "_graph.g2o.kernels"))) {
            LOGGER.warn("本地{}的G2oKernels文件损坏或者不存在", mapName);
            return false;
        }

        //比对locating/current/ .kernels
        LOGGER.warn("校验{}的QR文件", mapName);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(localMapInfoMd5.getQrMd5()) && !localMapInfoMd5.getQrMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + mapName + ".qr"))) {
            LOGGER.warn("本地{}的QR文件损坏或者不存在", mapName);
            return false;
        }

        //比对locating/current/ _bgImg.png
        LOGGER.warn("校验{}的背景图文件", mapName);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(agvMap.getBackgroundPicture()) &&
                org.apache.commons.lang3.StringUtils.isNotBlank(localMapInfoMd5.getBgImgMd5())
                && !localMapInfoMd5.getBgImgMd5().equals(MD5Utils.fileEncryption(localMapInfoPath + agvMap.getBackgroundPicture()))) {
            LOGGER.warn("本地{}的背景图文件损坏或者不存在", mapName);
            return false;
        }
        return true;
    }


    /**
     * 阻塞，直到更新内存成功，
     *
     * @param mapName
     */
    private void waitUntilUpdateMemoryOK(String mapName) {
        Date today = new Date();

        List<AGVMapUpdateQueue> list = new ArrayList<>();
        AGVMapUpdateQueue task2 = new AGVMapUpdateQueue();
        task2.setId(UUID.randomUUID().toString());
        task2.setCreateTime(new Date(today.getTime() + 10));//让这三个高优先级的任务顺序执行
        task2.setMapName(mapName);
        task2.setStatus(AGVMapUpdateQueue.STATUS_CREATE);
        task2.setLevel(AGVMapUpdateQueue.LEVEL_HIGH);
        task2.setType("current_2_memory");
        list.add(task2);

        agvMapUpdateQueueService.batchInsert(list);

        //检查队列中添加到内存中任务完成后再返回发布成功
        while (true) {
            AGVMapUpdateQueue agvMapUpdateTask = agvMapUpdateQueueService.selectById(task2.getId());
            if (agvMapUpdateTask.getStatus().equals("finish") || agvMapUpdateTask.getStatus().equals("failed")) {
                break;
            }
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                LOGGER.error("sleep error e:{}", e);
            }
        }
    }


    @Override
    public String importBackgroundImgData(MultipartFile multiPartFile,String mapName) throws Exception {

        if (multiPartFile == null || org.apache.commons.lang3.StringUtils.isBlank(mapName)) {
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }
        byte[] bgImgData = CommonUtils.getByteArray(multiPartFile.getInputStream());
        if (bgImgData == null || bgImgData.length <= 0) {
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }
        MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(mapName);
        if (mapGraphInfo == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_is_null"));
        }

        FTPClient client = null;
        try {
            client = FtpUtils.getConnectClient();

            //1、上传图片 到 map/locating/current/ 目录下
            int index = multiPartFile.getOriginalFilename() ==null ? -1 : multiPartFile.getOriginalFilename().lastIndexOf(".");
            String imgSuffix = index < 0 ? "png" : multiPartFile.getOriginalFilename().substring(index+1);
            String backgroundPicName = mapName + "_bgImg";
            String bgImgPath = MapFileUtils.MAP_INFO_PATH + mapName + MapFileUtils.LOCATION_DATA_PATH + backgroundPicName + "." + imgSuffix;
            MapFileUtils.writeValue(bgImgData, bgImgPath, client);

            //2、修改
            AGVMap agvMap = mapGraphInfo.getAgvMap();
            agvMap.setBackgroundPicture(backgroundPicName + "." + imgSuffix);
            String agvMapPath = MapFileUtils.getFilePath(MAP_INFO_SUFFIX, agvMap.getName(), false);
            MapFileUtils.writeValue(JSONObject.toJSONString(agvMap), agvMapPath, client);

            //3、修改md5值，使得地图更新线程，将数据重新加载
            AGVMapMd5 agvMapMd5 = mapGraphInfo.getAgvMapMd5();

            ByteArrayOutputStream bao = new ByteArrayOutputStream();
            boolean retrieveFile = client.retrieveFile(agvMapPath, bao);
            if(!retrieveFile){
                throw new ExecuteException("read ftp map file failed!");
            }

            String infoMd5Str = MD5Utils.dataEncryption(bao.toByteArray());
            agvMapMd5.setInfoMd5(infoMd5Str);
            agvMapMd5.setBgImgMd5(MD5Utils.dataEncryption(bgImgData));
            MapFileUtils.writeValue(JSONObject.toJSONString(agvMapMd5), MapFileUtils.getFilePath(MD5_SUFFIX, agvMap.getName(), false), client);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("update map background img failed ： {}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.update_map_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
        return "ok";
    }

    @Override
    public String delBackgroundImgData(String mapName) {

        if (org.apache.commons.lang3.StringUtils.isBlank(mapName)) {
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }
        MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(mapName);
        if (mapGraphInfo == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.agv_map_is_null"));
        }

        FTPClient client = null;
        try {
            client = FtpUtils.getConnectClient();

            AGVMap agvMap = mapGraphInfo.getAgvMap();
            //1、删除对应的文件
            String backgroundPicture = agvMap.getBackgroundPicture();
            if(StringUtils.isEmpty(backgroundPicture)){
                return null;
            }

            int index = backgroundPicture.lastIndexOf(".");
            String imgSuffix = index <0 ? "png" : backgroundPicture.substring(index+1);
            String bgImgWithOutSuffix = index <0 ? agvMap.getName() + "_bgImg" : backgroundPicture.substring(0,index);
            String bgImgPath = MapFileUtils.MAP_INFO_PATH + mapName + MapFileUtils.LOCATION_DATA_PATH + bgImgWithOutSuffix + "." + imgSuffix;
            if(!client.deleteFile(bgImgPath)){
                throw new ExecuteException("delete file failed!");
            }

            //2、修改
            agvMap.setBackgroundPicture(null);
            String agvMapPath = MapFileUtils.getFilePath(MAP_INFO_SUFFIX, agvMap.getName(), false);
            MapFileUtils.writeValue(JSONObject.toJSONString(agvMap), agvMapPath, client);

            //3、修改md5值，使得地图更新线程，将数据重新加载
            AGVMapMd5 agvMapMd5 = mapGraphInfo.getAgvMapMd5();

            ByteArrayOutputStream bao = new ByteArrayOutputStream();
            boolean retrieveFile = client.retrieveFile(agvMapPath, bao);
            if(!retrieveFile){
                throw new ExecuteException("read map file failed!");
            }

            String infoMd5Str = MD5Utils.dataEncryption(bao.toByteArray());
            agvMapMd5.setInfoMd5(infoMd5Str);
            agvMapMd5.setBgImgMd5(null);
            MapFileUtils.writeValue(JSONObject.toJSONString(agvMapMd5), MapFileUtils.getFilePath(MD5_SUFFIX, agvMap.getName(), false), client);

        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("del map background img failed ： {}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.update_map_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
        return "ok";
    }


    @Override
    public void exportMapData(HttpServletResponse response, String mapId) {
        if (StringUtils.isEmpty(mapId)) {
            throw new ExecuteException(MessageUtils.getMessage("http.params_error"));
        }
        //1、创建链接，进入ftp
        //2、查询是否有 mapid对应的地图，存在就返回，不存在就跑异常
        //3、存在的话，就切换到对应的目录，将目录下的文件下载，并打包

        //1、获取ftpclient
        FTPClient ftpClient = null;
        try {
            ftpClient = FtpUtils.getConnectClient();
            //切换到  /home/<USER>/map
            boolean changeWorkingDirectory = ftpClient.changeWorkingDirectory(FtpUtils.server_path);
            if (!changeWorkingDirectory) {
                throw new ExecuteException(MessageUtils.getMessage("service.change_folder_fail"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }

        //2、有没有当前地图，不存在的话报错
        try {
            boolean hasMap = false;
            FTPFile[] ftpFiles = ftpClient.listDirectories();
            if (ftpFiles != null && ftpFiles.length > 0) {
                for (FTPFile ftpFile : ftpFiles) {
                    if (ftpFile.getName().equalsIgnoreCase(mapId)) {
                        hasMap = true;
                        break;
                    }
                }
            }
            if (!hasMap) {
                throw new ExecuteException(MessageUtils.getMessage("service.agv_map_is_null"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }

        //3、将 mapName/locating/current、mapName/path/current 写入zip
        byte[] bytes = null;
        try {
            String targetPath = MapFileUtils.MAP_INFO_PATH + mapId;

            String locatingBack = targetPath + "/locating/back";
            String locatingTmp = targetPath + "/locating/tmp";
            String pathBack = targetPath + "/path/back";
            String pathTmp = targetPath + "/path/tmp";
            String pathDraft = targetPath + "/path/draft";
            List<String> excludePaths = Arrays.asList(locatingBack,locatingTmp,pathBack,pathTmp,pathDraft);

            bytes = CommonUtils.compressDirToZip(ftpClient,FtpUtils.server_path,targetPath,excludePaths);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }finally {
            FtpUtils.disconnect(ftpClient);
        }

        if(bytes==null){
            bytes = new byte[100];
        }
        //4、返回前端
        OutputStream os = null;
        try {
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(mapId.getBytes(), StandardCharsets.ISO_8859_1) + ".zip");
            response.addHeader("Content-Length", "" + bytes.length);
            os = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            os.write(bytes);// 输出文件
            os.flush();
            os.close();
        } catch (Exception e) {
            LOGGER.error("exportMapData exception : " + e.getMessage());
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    LOGGER.error(e.getMessage());
                }
            }
        }

    }


    @Override
    public void refresh(String mapName) {
        if (org.apache.commons.lang3.StringUtils.isBlank(mapName)) {
            throw new ExecuteException(MessageUtils.getMessage(MessageUtils.getMessage("http.missing_parameter")));
        }

        Date today = new Date();

        AGVMapUpdateQueue task2 = new AGVMapUpdateQueue();
        task2.setId(UUID.randomUUID().toString());
        task2.setCreateTime(today);
        task2.setMapName(mapName);
        task2.setStatus(AGVMapUpdateQueue.STATUS_CREATE);
        task2.setLevel(AGVMapUpdateQueue.LEVEL_HIGH);
        task2.setType(AGVMapUpdateQueue.TYPE_CURRENT_2_MEMORY);

        agvMapUpdateQueueService.insert(task2);

        while (true) {
            if (AGVMapInfoCache.getInstance().containsKey(mapName)) {
                break;
            }
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                LOGGER.error("sleep error e:{}", e);
            }
        }
    }


    @Override
    public void batchAppointMap(String mapId, List<String> agvCodeList) {
        // 仿真系统的小车需要先同步地图再指定
        syncSimulationAgvMap(selectById(mapId));
        // 指定地图
        mqttService.bathOperate(new BatchOperateMessage(agvCodeList, MqttConstant.BATCH_APPOINT_MAP, null, new MapCurrentMessage(mapId)));

    }

    @Override
    public PageInfo<AGVMap> findPage(Map<String, String> searchMap) {
        Map<String, MapGraphInfo> mapGraphInfoMap = AGVMapInfoCache.getInstance();
        List<AGVMap> agvMaps = mapGraphInfoMap.values().stream().map(MapGraphInfo::getAgvMap).collect(Collectors.toList());
        return MapFileUtils.getPageList(agvMaps, Integer.valueOf(searchMap.get("pageNum")), Integer.valueOf(searchMap.get("pageSize")));
    }


    private List<AGVMapResult> getAGVMapsAndFileLastUpdateTime() {
        Map<String, MapGraphInfo> mapGraphInfoMap = AGVMapInfoCache.getInstance();
        List<AGVMapResult> agvMaps = new ArrayList<>();
        for (MapGraphInfo mapGraphInfo : mapGraphInfoMap.values()) {
            AGVMapResult agvMapResult = new AGVMapResult();
            AGVMap agvMap = mapGraphInfo.getAgvMap();
            Long lastUpdateTime = mapGraphInfo.getLastUpdateTime();
            agvMapResult.setAgvMap(agvMap);
            agvMapResult.setLastUpdateTime(lastUpdateTime);
            agvMaps.add(agvMapResult);
        }
        return agvMaps;
    }


    @Override
    public List<AGVMap> findAll() {
        Map<String, MapGraphInfo> mapGraphInfoMap = AGVMapInfoCache.getInstance();
        return mapGraphInfoMap.values().stream().map(MapGraphInfo::getAgvMap).collect(Collectors.toList());
    }

    @Override
    public List<AGVMapResult> searchAll(Map<String, String> searchMap) {
        try {
            Map<String, MapGraphInfo> mapGraphInfoMap = AGVMapInfoCache.getInstance();
            List<AGVMap> agvMaps = mapGraphInfoMap.values().stream().map(MapGraphInfo::getAgvMap).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(searchMap)) {
                return getAGVMapsAndFileLastUpdateTime();
            }
            Set<AGVMap> result = new HashSet<>();
            for (String attribute : searchMap.keySet()) {
                for (AGVMap agvMap : agvMaps) {
                    if (MapFileUtils.getGetMethod(agvMap, attribute, searchMap.get(attribute))) {
                        result.add(agvMap);
                    }
                }
            }
            List<AGVMap> agvMapsList = new ArrayList<>(result);
            List<AGVMapResult> agvMapResults = new ArrayList<>();
            for (AGVMap agvMap : agvMapsList) {
                AGVMapResult agvMapResult = new AGVMapResult();
                agvMapResult.setAgvMap(agvMap);
                agvMapResult.setLastUpdateTime(mapGraphInfoMap.get(agvMap.getId()).getLastUpdateTime());
                agvMapResults.add(agvMapResult);
            }
            return agvMapResults;
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("search map data error : {}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.search_map_fail"));
        }
    }

    @Override
    public void syncMap(String agvCode, List<AGVMap> agvMaps) {
        MapPushMessage mapPushMessage = getMapPushMessage(agvMaps);
        mapCommandService.syncMap(agvCode, mapPushMessage);
    }

    @Override
    public void syncSimulationAgvMap(AGVMap agvMap) {
        /**
         * 同步仿真地图；因为仿真系统地图都是存在内存中，不区分agv小车;所以只需要发送一个地图同步数据即可，不需根据仿真小车去发送同步消息
         */
        List<Vehicle> vehicles = defaultVehiclePool.getAll().stream().filter(vehicle -> vehicle.isSimulation() && VehicleConstant.ONLINE.equals(vehicle.getOnlineStatus())).collect(Collectors.toList());
        Optional<Vehicle> optionalVehicle = vehicles.stream().findAny();
        if (optionalVehicle.isPresent()) {
            List<AGVMap> agvMaps = Arrays.asList(agvMap);
            this.syncMap(optionalVehicle.get().getId(), agvMaps);
        }
    }

    private MapPushMessage getMapPushMessage(List<AGVMap> agvMaps) {
        List<String> agvMapList = agvMaps.stream().map(AGVMap::getId).collect(Collectors.toList());
        List<MapDataMessage> mapDataMessageList = new ArrayList<>(agvMaps.size() * 4 / 3);
        Map<String, List<Marker>> markerMap = markerService.mapByAgvMapName(agvMapList, false);
        Map<String, List<SidePath>> sidePathMap = sidePathService.mapByAgvMapName(agvMapList, false);
        Map<String, List<Path>> pathMap = pathService.mapByAgvMapName(agvMapList, false);
        agvMaps.forEach(agvMap -> {
            String agvMapId = agvMap.getId();
            MapDataMessage mapDataMessage = new MapDataMessage(agvMap);
            mapDataMessage.setMarkers(markerMap.get(agvMapId));
            mapDataMessage.setSidePaths(sidePathMap.get(agvMapId));
            mapDataMessage.setPaths(pathMap.get(agvMapId));
            mapDataMessageList.add(mapDataMessage);
        });
        return new MapPushMessage(mapDataMessageList);
    }
}
