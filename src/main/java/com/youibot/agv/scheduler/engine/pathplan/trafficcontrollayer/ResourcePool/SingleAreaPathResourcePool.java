package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.MathematicalGraphicsAlgorithm;
import com.youibot.agv.scheduler.engine.pathplan.entity.Point;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.SingleAreaPathResource;
import com.youibot.agv.scheduler.engine.pathplan.util.ConversionUtils;
import com.youibot.agv.scheduler.entity.MapArea;
import com.youibot.agv.scheduler.entity.Marker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date :Created in 下午7:32 2020/8/21
 * @Description :
 * @Modified By :
 * @Version :
 */
@Component
public class SingleAreaPathResourcePool extends ZonePathResourcePool<SingleAreaPathResource> {
    private static final Logger logger = LoggerFactory.getLogger(SingleAreaPathResourcePool.class);

    //all singleAreas saved with points
    private Map<String, Point[]> singleAreas = new HashMap<>();

    public void addSingleAreaResources(List<MapArea> mapAreas) {
        if (mapAreas == null) return;
        for (MapArea mapArea : mapAreas) {
            String ploy = mapArea.getPolygon();
            if (ploy != null) {
                //将多边形区域的json转化为Point[]数组
                Point[] points = ConversionUtils.conversion(ploy);
                if (points != null) {
                    singleAreas.put(mapArea.getId(), points);
                    List<Marker> activeMarkers = MapGraphUtil.getMarkersByAGVMapId(mapArea.getAgvMapId());
                    List<Marker> singleAreaMarkers = findSingleAreaMarkers(points, activeMarkers);
                    Set<String> zonePathResource = singleAreaMarkers.stream().map(Marker::getId).collect(Collectors.toSet());
                    SingleAreaPathResource singleAreaResource = new SingleAreaPathResource();
                    singleAreaResource.setId(mapArea.getId());
                    singleAreaResource.setZonePathResource(zonePathResource);
                    super.addZonePathResources(singleAreaResource);
                } else {
                    logger.error("mapArea:" + mapArea.getId() + " has error polygon points");
                }
            } else {
                logger.error("mapArea:" + mapArea.getId() + " has error polygon");
            }
        }
    }

    public void removeSingleAreaResources(List<MapArea> mapAreas) {
        if (mapAreas == null) return;
        mapAreas.forEach(mapArea -> {
            singleAreas.remove(mapArea.getId());
            super.removeZonePathResources(mapArea.getId());
        });
    }

    private static List<Marker> findSingleAreaMarkers(Point[] poly, Collection<Marker> markers) {
        List<Marker> singleMarkers = new ArrayList<>();
        for (Marker marker : markers) {
            if (MathematicalGraphicsAlgorithm.isPtInPoly(marker.getX(), marker.getY(), poly)) {
                singleMarkers.add(marker);
            }
        }
        return singleMarkers;
    }

    public Set<String> querySingleAreaResourceIdsByMarkerIds(LinkedList<String> markerIds) {
        return queryZonePathResourceIdsByPathResourcesIds(markerIds);
    }

    public HashSet<String> querySingleAreaResourceIdByMarkerId(String markerId) {
        return queryZonePathResourceIdByPathResourcesId(markerId);
    }

    public Set<String> queryMarkerIdsBySingleAreaPathResourceId(String singleAreaPathResourceId) {
        return queryPathResourceIdsByZonePathResourceId(singleAreaPathResourceId);
    }

    public List<String> queryVehicleSingleAreaPathResourcesById(String vehicleId) {
        List<SingleAreaPathResource> singleAreaPathResource = this.getPoolEntries().values().stream().filter(x -> vehicleId.equals(x.getApplyAgvCode())).collect(Collectors.toList());
        List<String> markerIds = new ArrayList<>();
        singleAreaPathResource.forEach(x -> markerIds.addAll(x.getZonePathResource()));
        return markerIds;
    }
}
