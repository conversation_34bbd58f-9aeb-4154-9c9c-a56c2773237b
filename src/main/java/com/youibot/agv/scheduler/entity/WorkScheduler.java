package com.youibot.agv.scheduler.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "work_scheduler")
@ApiModel(value = "WorkScheduler", description = "作业分配队列")
public class WorkScheduler {

    public final static String STATUS_PREPARE = "PREPARE";
    public final static String STATUS_CREATE = "CREATE";
    public final static String STATUS_START = "START";
    public final static String STATUS_CANCEL = "CANCEL";
    public final static String STATUS_SUCCESS = "SUCCESS";
    public final static String STATUS_FAULT = "FAULT";

    @Id
    @ApiModelProperty(value = "ID", position = 0)
    private Long id;

    @Column
    @ApiModelProperty(value = "车辆ID", position = 1)
    private String vehicleId;

    @Column
    @ApiModelProperty(value = "任务ID", position = 2)
    private String workId;

    @Column
    @ApiModelProperty(value = "任务名称", position = 3)
    private String workName;

    @Column
    @ApiModelProperty(value = "状态：PREPARE,CREATE,START,CANCEL,SUCCESS,FAULT", position = 4)
    private String status;

    @Column
    @ApiModelProperty(value = "错误信息，状态为FAULT时有值", position = 5)
    private String faultMessage;

    @Column
    @ApiModelProperty(value = "创建时间", position = 6)
    private Date createTime;

    @Column
    @ApiModelProperty(value="更新时间",position = 7)
    private Date updateTime;

    @Column
    @ApiModelProperty(value = "开始时间", position = 8)
    private Date startTime;

    @Column
    @ApiModelProperty(value = "完成时间", position = 9)
    private Date finishTime;

}
