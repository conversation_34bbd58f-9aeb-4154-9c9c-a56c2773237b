package com.youibot.agv.scheduler.entity;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.youibot.agv.scheduler.entity.vo.SidePathMission;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.*;
import java.util.UUID;

import static com.youibot.agv.scheduler.constant.MapConstant.PATH_ALLOW_STOP_NAVIGATION;

/**
 * 边
 * 类名称：Edge
 * 创建时间：2019年4月2日 下午3:50:30
 */
@Data
@ApiModel(value = "SidePath", description = "边路径")
public class SidePath implements Cloneable {
    @Transient
    private static final Logger LOGGER = LoggerFactory.getLogger(SidePath.class);

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "地图ID", position = 1)
    private String agvMapName;

    @ApiModelProperty(value = "路径ID", position = 2)
    private String pathId;

    @ApiModelProperty(value = "开始标记点", position = 3)
    private String startMarkerId;

    @ApiModelProperty(value = "结束标记点", position = 4)
    private String endMarkerId;

    @ApiModelProperty(value = "开始点的控制点xy坐标", position = 5)
    private String startControl;

    @ApiModelProperty(value = "结束点的控制点xy坐标", position = 6)
    private String endControl;

    @ApiModelProperty(value = "长度", position = 7)
    private Double length;

    @JsonIgnore
    @ApiModelProperty(value = "进入路径角度", position = 8)
    private Double inAngle;

    @JsonIgnore
    @ApiModelProperty(value = "走出路径角度", position = 9)
    private Double outAngle;

    @ApiModelProperty(value = "线类型 1、直线 2、曲线（只要不为1,目前都默认为曲线）", position = 10)
    private Integer lineType;

    @ApiModelProperty(value = "agv方向: 0、双向 1、正向 2、反向", position = 11)
    private Integer agvDirection;

    @ApiModelProperty(value = "agv方向2 : -180 ~ 180", position = 24)
    private Integer agvDirection2;

    @ApiModelProperty(value = "使用状态 ENABLE:启用  DISABLE:禁用", position = 12)
    private String usageStatus;

    @ApiModelProperty(value = "自动门ID", position = 13)
    private String autoDoorId;

    @ApiModelProperty(value = "路径类型: 0、普通路径 1、二维码对接路径 2、脱离对接路径 3、货架腿对接+位姿精调 4、货架腿对接", position = 15)
    private Integer pathType;

    @ApiModelProperty(value = "工位编码", position = 16)
    private Integer workStationCode;

    @ApiModelProperty(value = "AGV要朝向的角度（地图坐标系下）  -180 ~ 180", position = 14)
    private Double directionAgv;

    @ApiModelProperty(value = "AGV要朝向的角度2（地图坐标系下）（预留，暂不使用）  -180 ~ 180", position = 15)
    private Double directionAgv2;

    @ApiModelProperty(value = "货架要朝向的角度1（地图坐标系下）  -180 ~ 180", position = 16)
    private Double directionShelf;

    @ApiModelProperty(value = "货架要朝向的角度2（地图坐标系下）  -180 ~ 180", position = 17)
    private Double directionShelf2;

    @ApiModelProperty(value = "uint, 货架旋转速度，单位，转/每分钟", position = 18)
    private Integer rpmShelf;

    @ApiModelProperty(value = "位置，1、起始点 2、结束点", position = 17)
    private Integer position;

    @ApiModelProperty(value = "生效路径 1、全部路径 2、第一段路径", position = 18)
    private Integer effectivePath;

    @ApiModelProperty(value = "是否是首段路径 1、是 0、否", position = 19)
    private Integer firstPath;

    @ApiModelProperty(value = "是否有动作,1、有 0、没有", position = 20)
    private Integer hasAction;

    @ApiModelProperty(value = "是否可路径上停止路径导航  1、允许  2、禁止", position = 16)
    private Integer allowStopNavigation = PATH_ALLOW_STOP_NAVIGATION;

    //三阶贝塞尔曲线的起始端点
    @Transient
    @JsonIgnore
    private Double t0;

    //三阶贝塞尔曲线的结束端点
    @Transient
    @JsonIgnore
    private Double t1;

    @Transient
    @JsonIgnore
    private Integer navigationType;//导航类型  0、正常导航 1、方向调整+导航 2、进入电梯 3、出来电梯 4、乘坐电梯 5、经过自动门

    @ApiModelProperty(value = "是否启用末端精定位: 0不启用 1启用，默认是不启用", position = 20)
    private Integer accurateStop;  //是否启用末端精定位: 0不启用 1启用，默认是不启用

    @ApiModelProperty(value = "路径参数")
    private PathParam pathParam;

    @ApiModelProperty(value = "目标导航点偏移量参数")
    private OffsetParam offsetParam;

    @ApiModelProperty(value = "路径权重系数")
    private Double weightRatio;

    //可选
    @ApiModelProperty(value = "路径前任务")
    private SidePathMission beforeMission;

    //可选
    @ApiModelProperty(value = "路径后任务")
    private SidePathMission afterMission;

    @ApiModelProperty(value = "货架类型", position = 16)
    private Integer shelfType;

    @ApiModelProperty(value = "相机类型, 位姿精调的相机类型：DOWN底部相机 UP顶部相机 LEFT左侧相机  RIGHT右侧相机", position = 16)
    private String camType;

    @ApiModelProperty(value = "顶部二维码工位ID")
    private Integer upQrDockId;

    @ApiModelProperty(value = "底部二维码工位ID")
    private Integer downQrDockId;

    public SidePath() {
    }


    public SidePath(String pathId, String startMarkerId, String endMarkerId, String agvMapId, Double length) {
        this.id = UUID.randomUUID().toString();
        this.pathId = pathId;
        this.startMarkerId = startMarkerId;
        this.endMarkerId = endMarkerId;
        this.agvMapName = agvMapId;
        this.length = length;
        this.inAngle = 0D;
        this.outAngle = 0D;
    }


    @Override
    public SidePath clone() {
        try {
            return (SidePath) super.clone();
        } catch (CloneNotSupportedException e) {
            LOGGER.error("Clone side path error.", e.getMessage());
            throw new AssertionError();
        }
    }

    public void setStartControl(String startControl) {
        JSONObject jsonObject = JSONObject.parseObject(startControl);
        this.startControl = jsonObject.toJSONString();
    }

    public String getStartControl() {
        return this.startControl;
    }

    public void setEndControl(String endControl) {
        JSONObject jsonObject = JSONObject.parseObject(endControl);
        this.endControl = jsonObject.toJSONString();
    }
}
