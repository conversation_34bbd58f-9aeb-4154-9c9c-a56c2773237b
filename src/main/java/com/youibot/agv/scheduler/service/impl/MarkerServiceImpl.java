package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.service.PathPlanService;
import com.youibot.agv.scheduler.engine.pathplan.util.PathUtils;
import com.youibot.agv.scheduler.entity.AdjustAction;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.entity.vo.PathVo;
import com.youibot.agv.scheduler.entity.vo.SidePathVo;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.MapGraphInfo;
import com.youibot.agv.scheduler.map.entity.PageInfo;
import com.youibot.agv.scheduler.map.entity.PathResultData;
import com.youibot.agv.scheduler.map.utils.MapFileUtils;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.service.PathService;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.ConvertUtils;
import com.youibot.agv.scheduler.util.FtpUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MapConstant.*;
import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.ROAD_NETWORK_SUFFIX;

@Service
public class MarkerServiceImpl implements MarkerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarkerServiceImpl.class);

    @Autowired
    private PathService pathService;

    @Autowired
    private MarkerService markerService;

    @Autowired
    private FloorServiceImpl floorService;

    @Autowired
    private PathPlanService pathPlanService;

    @Override
    public PageInfo<Marker> findPage(Map<String, String> searchMap) throws Exception {
        List<Marker> markerList = selectByAGVMapId(searchMap.get("agvMapId"), false);

        // 过滤
        for (String attribute : searchMap.keySet()) {
            if ("code".equals(attribute)) {
                for (int i = 0; i < markerList.size(); i++) {
                    if (!MapFileUtils.getGetMethod(markerList.get(i), attribute, searchMap.get(attribute))) {
                        markerList.remove(i);
                        i--;
                    }
                }
            }
        }

        return MapFileUtils.getPageList(markerList, Integer.valueOf(searchMap.get("pageNum")),
                Integer.valueOf(searchMap.get("pageSize")));
    }

    @Override
    public Marker insert(Marker marker) {
        FTPClient client = null;
        try {
            String agvMapName = marker.getAgvMapName();
            if (StringUtils.isEmpty(agvMapName)) {
                LOGGER.error("missing agvMapName");
                throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
            }
            marker.setId(UUID.randomUUID().toString());
            client = FtpUtils.getConnectClient();
            String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapName, true);
            PathResultData pathResult = new PathResultData();
            Map<String, Marker> markers = new HashMap<>();
            String result = MapFileUtils.getDraftFileData(filePath, agvMapName, client);
            if (!StringUtils.isEmpty(result)) {
                pathResult = MapFileUtils.pathFileDataToPathResultData(result);
                markers = pathResult.getMarkers();
            }
            marker.setCode(getMaxCode(markers));
            marker.setIsAvoid(MARKER_AVOID_ENABLE);//默认可避让
            marker.setNetworkMarkerType(MARKER_COMMON_TYPE);//默认普通路网点
            markers.put(marker.getId(), marker);
            markers.forEach((k, m) -> {
                if (m.getIsAvoid() == null) {
                    m.setIsAvoid(1);
                }
            });
            pathResult.setMarkers(markers);
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(pathResult), filePath, client);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("add marker failed :{}", e);
            throw new ExecuteException();
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }

        return marker;
    }

    private String getMaxCode(Map<String, Marker> markers) {
        if (CollectionUtils.isEmpty(markers)) {
            return "1";
        }
        int[] codes = markers.values().stream().mapToInt(marker -> Integer.valueOf(marker.getCode())).toArray();
        for (int i = 0; i < codes.length - 1; i++) {
            if (codes[i] > codes[i + 1]) {
                int temp = codes[i];
                codes[i] = codes[i + 1];
                codes[i + 1] = temp;
            }
        }
        Integer maxCode = codes[codes.length - 1];
        maxCode++;
        return maxCode.toString();
    }

    @Override
    public synchronized Marker update(Marker marker) {
        FTPClient client = null;
        try {
            String file = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, marker.getAgvMapName(), true);
            client = FtpUtils.getConnectClient();
            //获取草稿文件数据
            String result = MapFileUtils.getDraftFileData(file, marker.getAgvMapName(), client);
            PathResultData pathResult = MapFileUtils.pathFileDataToPathResultData(result);
            if (pathResult == null) {
                LOGGER.error("marker data is null");
                throw new ExecuteException(MessageUtils.getMessage("service.read_map_file_fail"));
            }
            //将marker重新put到数据中
            Map<String, Marker> markers = pathResult.getMarkers();

            //校验markerCode 是否重复
            if (markers != null && !markers.isEmpty()) {
                markers.values().forEach(item -> {
                    if (!item.getId().equals(marker.getId()) && item.getCode().equals(marker.getCode())) {
                        throw new ADSParameterException(MessageUtils.getMessage("service.marker_code_already_exists"));
                    }
                });
            }

            //如果是避让点，则默认为可避让
            if (MARKER_TYPE_AVOID.equals(marker.getType())) {
                marker.setIsAvoid(MARKER_AVOID_ENABLE);
            }
            Map<String, Path> paths = pathResult.getPaths();
            if (markers.get(marker.getId()) != null) {
                markers.put(marker.getId(), marker);
            }
            //修改影响到的数据 path sidePath adjustAction
            List<Path> newPaths = paths.values().stream().filter(path -> path.getEndMarkerId().equals(marker.getId()) || path.getStartMarkerId().equals(marker.getId())).collect(Collectors.toList());
            List<SidePath> delSidePaths = new ArrayList<>();
            List<SidePath> newSidePaths = new ArrayList<>();
            if (!CollectionUtils.isEmpty(newPaths)) {
                Map<String, SidePath> sidePaths = pathResult.getSidePaths();
                for (Path path : newPaths) {
                    path.setLength(PathUtils.getLength(path, markers.get(path.getStartMarkerId()), markers.get(path.getEndMarkerId())));
                    List<SidePath> delSidePath = sidePaths.values().stream().filter(sidePath -> sidePath.getPathId().equals(path.getId())).collect(Collectors.toList());
                    delSidePaths.addAll(delSidePath);
                    PathVo pathVo = new PathVo();
                    pathVo.setPath(path);
                    pathVo.setSidePathVos(delSidePath.stream().map(sidePath -> ConvertUtils.sourceToTarget(sidePath, SidePathVo.class)).collect(Collectors.toList()));
                    List<SidePath> newSidePath = pathService.createSidePathsByPathVo(pathVo, markers.get(path.getStartMarkerId()), markers.get(path.getEndMarkerId()));
                    newSidePaths.addAll(newSidePath);
                }

                delSidePaths.forEach(sidePath -> sidePaths.remove(sidePath.getId()));
                newSidePaths.forEach(sidePath -> sidePaths.put(sidePath.getId(), sidePath));
                newPaths.forEach(path -> paths.put(path.getId(), path));
                pathResult.setPaths(paths);
                pathResult.setSidePaths(sidePaths);
            }
            pathResult.setMarkers(markers);
            //写入文件中
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(pathResult), file, client);
            return marker;
        } catch (ExecuteException | ADSParameterException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("update marker failed :{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.update_marker_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public List<Marker> searchAll(Map<String, String> searchMap, boolean isDraft) {
        if (CollectionUtils.isEmpty(searchMap)) {
            return null;
        }
        try {
            List<Marker> markerList = selectByAGVMapId(searchMap.get("agvMapName"), isDraft);
            if (CollectionUtils.isEmpty(markerList)) {
                return new ArrayList<>();
            }
            if (searchMap.containsKey("agvMapName") && searchMap.containsKey("isDraft") && searchMap.size() == 2) {
                return markerList;
            }
            Set<Marker> result = new HashSet<>();
            for (String attribute : searchMap.keySet()) {
                if (attribute.equals("agvMapName") || attribute.equals("isDraft")) {
                    continue;
                }
                for (Marker marker : markerList) {
                    if (MapFileUtils.getGetMethod(marker, attribute, searchMap.get(attribute))) {
                        result.add(marker);
                    }
                }
            }
            return new ArrayList<>(result);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("searchAll marker failed ：{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
        }
    }


    /**
     * 查询所有的地图的点位（非草稿）
     *
     * @return
     */
    @Override
    public List<Marker> getAllWithOutCondition() {
        try {
            Map<String, MapGraphInfo> instance = AGVMapInfoCache.getInstance();
            if (!CollectionUtils.isEmpty(instance)) {
                return Collections.EMPTY_LIST;
            }

            List<Marker> list = new ArrayList<>();
            instance.values().forEach(item -> {
                Map<String, Marker> markers = item.getMarkers();
                if (!CollectionUtils.isEmpty(markers)) {
                    list.addAll(markers.values());
                }
            });
            return list;
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("searchAll marker failed ：{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
        }
    }


    @Override
    public synchronized void deleteById(String agvMapName, String id) {
        FTPClient client = null;
        try {
            client = FtpUtils.getConnectClient();
            String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapName, true);
            String result = MapFileUtils.getDraftFileData(filePath, agvMapName, client);
            PathResultData pathResult = MapFileUtils.pathFileDataToPathResultData(result);
            if (pathResult == null) {
                LOGGER.error("marker data is null");
                throw new ExecuteException(MessageUtils.getMessage("service.read_map_file_fail"));
            }
            Map<String, Marker> markers = pathResult.getMarkers();
            Marker marker = markers.get(id);
            if (MARKER_TYPE_ELEVATOR.equals(marker.getType()) && floorService.selectByMarkerId(id) != null) {
                throw new ExecuteException(MessageUtils.getMessage("service.marker_binding_floor_not_delete"));
            }
            List<SidePath> delSidePaths = new ArrayList<>();
            Map<String, Path> paths = pathResult.getPaths();
            List<Path> delPaths = paths.values().stream().filter(path -> path.getEndMarkerId().equals(id) || path.getStartMarkerId().equals(id)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delPaths)) {
                Map<String, SidePath> sidePaths = pathResult.getSidePaths();
                for (Path path : delPaths) {
                    List<SidePath> delSidePath = sidePaths.values().stream().filter(sidePath -> sidePath.getPathId().equals(path.getId())).collect(Collectors.toList());
                    delSidePaths.addAll(delSidePath);
                }
                delSidePaths.forEach(sidePath -> sidePaths.remove(sidePath.getId()));
                delPaths.forEach(path -> paths.remove(path.getId(), path));
                pathResult.setPaths(paths);
                pathResult.setSidePaths(sidePaths);
            }
            Map<String, AdjustAction> adjustActions = pathResult.getAdjustActions();
            List<AdjustAction> adjustAction = adjustActions.values().stream().filter(adjust -> adjust != null && adjust.getMarkerId().equals(id)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(adjustAction)) {
                adjustAction.forEach(adjust -> {
                    if (adjustActions.keySet().contains(adjust.getId())) {
                        adjustActions.remove(adjust.getId());
                    }
                });
                pathResult.setAdjustActions(adjustActions);
            }
            markers.remove(id);
            pathResult.setMarkers(markers);
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(pathResult), filePath, client);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("delete marker failed :{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.delete_marker_file_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public List<Marker> selectByIds(String agvMapId, List<String> strings, boolean isDraft) {
        List<Marker> markers = selectByAGVMapId(agvMapId, isDraft);
        return markers.stream().filter(marker -> strings.contains(marker.getId())).collect(Collectors.toList());
    }

    @Override
    public List<Marker> selectByCodes(String agvMapId, List<String> codes, boolean isDraft) {
        List<Marker> markers = selectByAGVMapId(agvMapId, isDraft);
        return markers.stream().filter(marker -> codes.contains(marker.getCode())).collect(Collectors.toList());
    }

    @Override
    public List<Marker> selectParkMarkerByCodes(String agvMapId, List<String> codes, boolean isDraft) {
        List<Marker> markers = selectByAGVMapId(agvMapId, isDraft);
        return markers.stream().filter(marker -> codes.contains(marker.getCode()) && marker.getIsPark().equals(MARKER_PARK_STATUS_ENABLE)).collect(Collectors.toList());
    }

    @Override
    public List<Marker> selectByAGVMapId(String agvMapId, boolean isDraft) {
        FTPClient client = null;
        try {
            List<Marker> markers = new ArrayList<>();
            if (isDraft) {
                client = FtpUtils.getConnectClient();
                String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapId, true);
                String result = MapFileUtils.getDraftFileData(filePath, agvMapId, client);
                if (StringUtils.isEmpty(result)) {
                    return new ArrayList<>();
                }
                PathResultData roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);
                markers = new ArrayList<>(roadNetwork.getMarkers().values());
            } else {
                if (AGVMapInfoCache.getCache(agvMapId) != null && !CollectionUtils.isEmpty(AGVMapInfoCache.getCache(agvMapId).getMarkers())) {
                    markers = new ArrayList<>(AGVMapInfoCache.getCache(agvMapId).getMarkers().values());
                }
            }
            return markers;
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("Failed to selectByAGVMapId  e:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_map_file_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public List<Marker> selectByAGVMapIds(List<String> agvMapIds, boolean isDraft) {
        List<Marker> result = new ArrayList<>();
        agvMapIds.forEach(agvMapId -> {
            List<Marker> markers = selectByAGVMapId(agvMapId, isDraft);
            result.addAll(markers);
        });
        return result;
    }

    @Override
    public List<Marker> selectByAGVMapIdAndUsageStatus(String agvMapId, String usageStatus, boolean isDraft) {
        List<Marker> markers = selectByAGVMapId(agvMapId, isDraft);
        return markers.stream().filter(marker -> marker.getUsageStatus().equals(usageStatus)).collect(Collectors.toList());
    }

    @Override
    public List<Marker> selectByAGVMapIdAndType(String agvMapId, String type, boolean isDraft) {
        List<Marker> markers = selectByAGVMapId(agvMapId, isDraft);
        return markers.stream().filter(marker -> marker.getType().equals(type)).collect(Collectors.toList());
    }

    @Override
    public Marker selectById(String agvMapId, String id, boolean isDraft) {
        List<Marker> markers = selectByAGVMapId(agvMapId, isDraft);
        return markers.stream().filter(marker -> marker.getId().equals(id)).findFirst().orElse(null);
    }

    @Override
    public Marker selectById(String id) {
        List<Marker> markers = MapFileUtils.getDataList(Marker.class);
        return markers.stream().filter(marker -> marker.getId().equals(id)).findFirst().orElse(null);
    }

    @Override
    public Map<String, List<Marker>> mapByAgvMapName(List<String> agvMapList, boolean isDraft) {
        return BeanUtils.listToMapByFiled(this.selectByAGVMapIds(agvMapList, isDraft), "agvMapName");
    }

    @Override
    public List<Marker> selectRelatePathMarkerIdsByType(String markerId, String agvMapId, String type, boolean isDraft) {
        List<Path> paths = pathService.selectByAGVMapId(agvMapId, isDraft);
        List<Marker> markers = markerService.selectByAGVMapId(agvMapId, isDraft);
        if (!CollectionUtils.isEmpty(markers)) {
            List<String> startPath = paths.stream().filter(path -> path.getAgvMapName().equals(agvMapId) && path.getStartMarkerId().equals(markerId))
                    .map(Path::getEndMarkerId).collect(Collectors.toList());
            List<String> endPath = paths.stream().filter(path -> path.getAgvMapName().equals(agvMapId) && path.getEndMarkerId().equals(markerId))
                    .map(Path::getStartMarkerId).collect(Collectors.toList());
            Set<String> markerIds = new HashSet<>();
            markerIds.addAll(startPath);
            markerIds.addAll(endPath);
            if (CollectionUtils.isEmpty(markerIds)) {
                return new ArrayList<>();
            }
            List<Marker> markerList = markers.stream().filter(marker -> markerIds.contains(marker.getId())).collect(Collectors.toList());
            if (type != null) {
                markerList = markerList.stream().filter(marker -> marker.getType().equals(type)).collect(Collectors.toList());
            }
            return markerList;
        }
        return null;
    }

    @Override
    public void deleteByAGVMapId(String agvMapId) {
        FTPClient client = null;
        try {
            MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(agvMapId);
            if (mapGraphInfo != null && !CollectionUtils.isEmpty(mapGraphInfo.getMarkers())) {
                client = FtpUtils.getConnectClient();
                String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapId, true);
                String result = MapFileUtils.getDraftFileData(filePath, agvMapId, client);
                PathResultData pathResult = MapFileUtils.pathFileDataToPathResultData(result);
                if (pathResult == null) {
                    LOGGER.error("marker data is null");
                    throw new ExecuteException(MessageUtils.getMessage("service.read_map_file_fail"));
                }
                pathResult.setMarkers(null);
                pathResult.setPaths(null);
                pathResult.setSidePaths(null);
                MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(pathResult), filePath, client);
            }
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("delete marker failed :{}", e);
            throw new ExecuteException(MessageUtils.getMessage(""));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public void deleteByAgvMapIds(List<String> agvMapIdList) {
        agvMapIdList.forEach(this::deleteByAGVMapId);
    }


    @Override
    public List<Marker> selectEnableChargeMarker() {
        List<Marker> markers = MapFileUtils.getDataList(Marker.class);
        return markers.stream().filter(marker -> marker.getUsageStatus().equals(MARKER_USAGE_STATUS_ENABLE) && marker.getType().equals(MARKER_TYPE_CHARGING)).collect(Collectors.toList());
    }

    /**
     * 查询所有的可用的未分配的泊车点。
     *
     * @return
     */
    @Override
    public List<Marker> selectEnableParkMarkers() {
        List<Marker> markers = MapFileUtils.getDataList(Marker.class);
        return markers.stream().filter(marker -> marker.getUsageStatus().equals(MARKER_USAGE_STATUS_ENABLE) && marker.getIsPark().equals(MARKER_PARK_STATUS_ENABLE)).collect(Collectors.toList());
    }

    /**
     * 查询所有的可用的未分配的避让点。
     *
     * @return
     */
    @Override
    public List<Marker> selectEnableAvoidMarkers() {
        List<Marker> markers = MapFileUtils.getDataList(Marker.class);
        return markers.stream().filter(marker -> marker.getUsageStatus().equals(MARKER_USAGE_STATUS_ENABLE) && marker.getType().equals(MARKER_TYPE_AVOID)).collect(Collectors.toList());
    }

    /**
     * 查询地图可用的未分配的避让点。
     *
     * @return
     */
    @Override
    public List<Marker> selectEnableAvoidMarkersByMapId(String agvMapId) {
        if (StringUtils.isEmpty(agvMapId)) {
            return null;
        }
        List<Marker> markers = selectByAGVMapId(agvMapId, false);
        return markers.stream().filter(marker -> marker.getUsageStatus().equals(MARKER_USAGE_STATUS_ENABLE) && marker.getType().equals(MARKER_TYPE_AVOID)).collect(Collectors.toList());
    }

    @Override
    public List<Marker> selectEnableChargeMarkerByMapId(String agvMapId) {
        if (StringUtils.isEmpty(agvMapId)) {
            return null;
        }
        List<Marker> markers = selectByAGVMapId(agvMapId, false);
        return markers.stream().filter(marker -> marker.getUsageStatus().equals(MARKER_USAGE_STATUS_ENABLE) && marker.getType().equals(MARKER_TYPE_CHARGING)).collect(Collectors.toList());
    }

    @Override
    public List<Marker> selectEnableParkMarkersByMapId(String agvMapId) {
        if (StringUtils.isEmpty(agvMapId)) {
            return null;
        }
        List<Marker> markers = selectByAGVMapId(agvMapId, false);
        return markers.stream().filter(marker -> marker.getUsageStatus().equals(MARKER_USAGE_STATUS_ENABLE) && marker.getIsPark().equals(MARKER_PARK_STATUS_ENABLE)).collect(Collectors.toList());
    }

    @Override
    public List<Marker> getListByIds(List<String> ids, boolean isUsable) {
        if (StringUtils.isEmpty(ids)) {
            return Collections.EMPTY_LIST;
        }
        List<Marker> markers = MapFileUtils.getDataList(Marker.class);
        if (StringUtils.isEmpty(markers)) {
            return Collections.EMPTY_LIST;
        }
        if (isUsable) {
            markers = markers.stream().filter(item -> item.getUsageStatus().equalsIgnoreCase(MARKER_USAGE_STATUS_ENABLE)).collect(Collectors.toList());
        }
        return markers.stream().filter(item -> ids.contains(item.getId())).collect(Collectors.toList());
    }

    @Override
    public MarkerPathResult searchMarkCodeToMarkCode(String startMarkMapId, String startMarkCode, String endMarkMapId, String endMarkCode, boolean isDraft) throws InterruptedException {
        if (StringUtils.isEmpty(startMarkMapId) || StringUtils.isEmpty(startMarkCode) || StringUtils.isEmpty(endMarkMapId) || StringUtils.isEmpty(endMarkCode)) {
            LOGGER.error("startMarkMapId or startMarkCode or endMarkMapId or endMarkCode is null");
            throw new ExecuteException(MessageUtils.getMessage("http.missing_parameter"));
        }
        List<Marker> startMarkList = this.selectByCodes(startMarkMapId, Collections.singletonList(startMarkCode), isDraft);
        if (CollectionUtils.isEmpty(startMarkList)) {
            LOGGER.error("startMarks is Empty");
            throw new ExecuteException(MessageUtils.getMessage("http.marker_is_not_exist"));
        }
        List<Marker> endMarkList = this.selectByCodes(endMarkMapId, Collections.singletonList(endMarkCode), isDraft);
        if (CollectionUtils.isEmpty(endMarkList)) {
            LOGGER.error("endMarks is Empty");
            throw new ExecuteException(MessageUtils.getMessage("http.marker_is_not_exist"));
        }
        Marker startMark = startMarkList.stream().findFirst().get();
        Marker endMark = endMarkList.stream().findFirst().get();

        return pathPlanService.searchMarkerToMarker(startMark.getId(), endMark.getId());
    }

    @Override
    public Marker selectMarkerByMapIdAndMarkerCode(String agvMapId, String markerCode, Boolean isDraft) {
        List<Marker> markers = this.selectByAGVMapId(agvMapId, isDraft);
        if (!CollectionUtils.isEmpty(markers)) {
            return markers.stream().filter(m -> m.getCode().equals(markerCode)).findFirst().orElse(null);
        }
        return null;
    }
}
