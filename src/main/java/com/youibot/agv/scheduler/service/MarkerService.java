package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.map.entity.PageInfo;

import java.util.List;
import java.util.Map;

public interface MarkerService {


    List<Marker> selectByAGVMapId(String agvMapId, boolean isDraft);

    List<Marker> selectByAGVMapIds(List<String> agvMapIds, boolean isDraft);

    List<Marker> selectByAGVMapIdAndUsageStatus(String agvMapId, String usageStatus, boolean isDraft);

    /**
     * 根据 agvMapId和marker点类型选择marker点
     *
     * @param agvMapId
     * @param type
     * @return
     */
    List<Marker> selectByAGVMapIdAndType(String agvMapId, String type, boolean isDraft);

    Marker selectById(String agvMapId, String id, boolean isDraft);


    Marker selectById(String id);

    /**
     * 根据agvMapId列表查询出对应的标记点列表 map
     * key:agvMapId
     *
     * @param agvMapList
     * @return
     */
    Map<String, List<Marker>> mapByAgvMapName(List<String> agvMapList, boolean isDraft);

    /**
     * 根据标记点类型查询与当前标记点有路径的所有点位列表
     *
     * @param markerId
     * @param agvMapId
     * @param type
     * @return
     */
    List<Marker> selectRelatePathMarkerIdsByType(String markerId, String agvMapId, String type, boolean isDraft);

    void deleteByAGVMapId(String agvMapId);

    /**
     * 根据地图id列表删除所有的标记点
     *
     * @param agvMapIdList
     */
    void deleteByAgvMapIds(List<String> agvMapIdList);

    /**
     * 查询所有启用的充电点。
     * 非草稿数据
     *
     * @return
     */
    List<Marker> selectEnableChargeMarker();

    /**
     * 查询所有可分配的泊车点。
     * 过滤掉禁用的，
     * 已分配的泊车点。
     * 非草稿数据
     *
     * @return
     */
    List<Marker> selectEnableParkMarkers();

    /**
     * 查询所有的可用的避让点。
     * 非草稿数据
     *
     * @return
     */
    List<Marker> selectEnableAvoidMarkers();

    /**
     * 查询地图的可用避让点。
     * 非草稿数据
     *
     * @return
     */
    List<Marker> selectEnableAvoidMarkersByMapId(String agvMapId);

    /**
     * 查询地图可用的充电点
     * 非草稿数据
     *
     * @param agvMapId
     * @return
     */
    List<Marker> selectEnableChargeMarkerByMapId(String agvMapId);

    /**
     * 查询地图可用的泊车点
     * 非草稿数据
     *
     * @param agvMapId
     * @return
     */
    List<Marker> selectEnableParkMarkersByMapId(String agvMapId);

    Marker insert(Marker marker);

    Marker update(Marker marker);

    List<Marker> searchAll(Map<String, String> searchMap, boolean isDraft);

    List<Marker> getAllWithOutCondition();

    void deleteById(String agvMapName, String id);

    /**
     * 根据地图名称和标记点ID列表查询标记点数据列表
     * @param agvMapId
     * @param strings
     * @param isDraft
     * @return
     */
    List<Marker> selectByIds(String agvMapId, List<String> strings, boolean isDraft);

    List<Marker> selectByCodes(String agvMapId,List<String> codes,boolean isDraft);

    List<Marker> selectParkMarkerByCodes(String agvMapId,List<String> codes,boolean isDraft);

    PageInfo<Marker> findPage(Map<String, String> searchMap) throws Exception;

    List<Marker> getListByIds(List<String> ids, boolean isUsable);

    /**
     * 查询两点可达路径
     * @param startMarkMapId 起点地图
     * @param startMarkCode 起点编号
     * @param endMarkMapId 终点地图
     * @param endMarkCode 终点编号
     * @param isDraft 草稿
     * @return 路径结果
     */
    MarkerPathResult searchMarkCodeToMarkCode(String startMarkMapId, String startMarkCode, String endMarkMapId, String endMarkCode, boolean isDraft) throws InterruptedException;

    Marker selectMarkerByMapIdAndMarkerCode(String agvMapId, String markerCode,Boolean isDraft);
}
