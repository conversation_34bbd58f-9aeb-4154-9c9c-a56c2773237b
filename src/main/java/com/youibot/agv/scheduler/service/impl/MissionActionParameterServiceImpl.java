package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.MissionActionParameter;
import com.youibot.agv.scheduler.service.MissionActionParameterService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年5月8日 下午2:52:47
 */
@Service
public class MissionActionParameterServiceImpl extends BaseServiceImpl<MissionActionParameter> implements MissionActionParameterService{

//	@Autowired
//	private MissionService missionService;
//
//	@Autowired
//	private MissionActionService missionActionService;

	@Override
	public List<MissionActionParameter> selectByMissionActionId(String missionActionId) {
		Example example=new Example(MissionActionParameter.class);
		example.createCriteria().andEqualTo("missionActionId", missionActionId);
		return super.selectByExample(example);
	}

	@Override
	public List<MissionActionParameter> selectByMissionActionIds(List<String> missionActionIds) {
		if (CollectionUtils.isEmpty(missionActionIds)){
			return Collections.emptyList();
		}
		Example example=new Example(MissionActionParameter.class);
		example.createCriteria().andIn("missionActionId", missionActionIds);
		return super.selectByExample(example);
	}

	@Override
	public Map<String, List<MissionActionParameter>> mapMissionActionParameterByMissionActionIds(List<String> missionActionIds) {
		List<MissionActionParameter> missionActionParameters = this.selectByMissionActionIds(missionActionIds);
		if (CollectionUtils.isEmpty(missionActionParameters)){
			return Collections.emptyMap();
		}
		Map<String, List<MissionActionParameter>> map = new HashMap<>();
		missionActionParameters.forEach(missionActionParameter -> {
			String missionActionId = missionActionParameter.getMissionActionId();
			List<MissionActionParameter> list ;
			if (!map.containsKey(missionActionId)){
				list = new ArrayList<>();
			}else {
				list = map.get(missionActionId);
			}
			list.add(missionActionParameter);
			map.put(missionActionId, list);
		});
		return map;
	}

//	@Override
//	public Integer update(MissionActionParameter missionActionParameter) {
//		MissionAction missionAction = missionActionService.selectById(missionActionParameter.getMissionActionId());
//		if (missionAction == null){
//			return 0;
//		}
//		missionService.updateMissionVersion(missionAction.getMissionId());
//		return super.update(missionActionParameter);
//	}

	@Override
	public void deleteByMissionActionId(String missionActionId) {
		List<MissionActionParameter> missionActionParameters = this.selectByMissionActionId(missionActionId);
		if (!CollectionUtils.isEmpty(missionActionParameters)) {
			List<String> ids = missionActionParameters.stream().map(MissionActionParameter::getId).collect(Collectors.toList());
			super.deleteByIds(ids);
		}
	}

	@Override
	public void deleteByMissionActionIds(List<String> missionActionIds) {
		Example example = new Example(MissionActionParameter.class);
		example.createCriteria().andIn("missionActionId", missionActionIds);
		List<MissionActionParameter> missionActionParameters = selectByExample(example);
		if (!CollectionUtils.isEmpty(missionActionParameters)) {
			List<String> ids = missionActionParameters.stream().map(MissionActionParameter::getId).collect(Collectors.toList());
			super.deleteByIds(ids);
		}
	}

	@Override
	public List<MissionActionParameter> selectByMissionActionIdAndKey(String missionActionId, String parameterKey) {
		Example example = new Example(MissionActionParameter.class);
		example.createCriteria().andEqualTo("missionActionId", missionActionId).andEqualTo("parameterKey",parameterKey);
		return super.selectByExample(example);
	}
}
