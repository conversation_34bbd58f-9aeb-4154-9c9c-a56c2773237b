package com.youibot.agv.scheduler.mqtt.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.entity.vo.AGVBindMarkerVo;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mqtt.bean.push.cmd.CommandMessage;
import com.youibot.agv.scheduler.mqtt.bean.push.cmd.RelocationMessage;
import com.youibot.agv.scheduler.mqtt.constants.MqttConstant;
import com.youibot.agv.scheduler.mqtt.service.VehicleCommandService;
import com.youibot.agv.scheduler.mqtt.util.MqttUtils;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MapConstant.MAP_STATUS_APPOINT;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_FAULT;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_SHUTDOWN;

@Service
public class VehicleCommandServiceImpl implements VehicleCommandService {

    Logger logger = LoggerFactory.getLogger(VehicleCommandServiceImpl.class);

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;


    @Autowired
    private MissionService missionService;

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private WorkSchedulerService workSchedulerService;

    @Autowired
    private ParkSchedulerService parkSchedulerService;

    @Autowired
    private ChargeSchedulerService chargeSchedulerService;

    @Autowired
    private MissionActionService missionActionService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private AbnormalPromptService abnormalPromptService;

    @Autowired
    private MissionActionParameterService missionActionParameterService;

    @Autowired
    private MissionGlobalVariableService missionGlobalVariableService;

    @Autowired
    private MarkerService markerService;

    @Autowired
    private AGVService agvService;

    //设定泊车点的动作类型
    private static final String SET_PARK_MARKER = "SET_PARK_MARKER";
    //设定泊车点的地图key
    private static final String AGV_MAP_ID = "agvMapId";
    //设定泊车点的标记点key
    private static final String MARKER_CODE = "markerCode";

    @Override
    public void oneKeyStop(String agvCode) {
        MqttUtils.sendMqttMsg(MqttConstant.COMMAND, agvCode, new CommandMessage(agvCode, "oneKeyStop"));
    }

    @Override
    public void oneKeyResume(String agvCode) {
        MqttUtils.sendMqttMsg(MqttConstant.COMMAND, agvCode, new CommandMessage(agvCode, "oneKeyResume"));
    }

    @Override
    public void oneKeyReset(String agvCode) {
        MqttUtils.sendMqttMsg(MqttConstant.COMMAND, agvCode, new CommandMessage(agvCode, "oneKeyReset"));
    }

    @Override
    public void oneKeyResetLocation(String agvCode) {

        MqttUtils.sendMqttMsg(MqttConstant.COMMAND, agvCode, new CommandMessage(agvCode, "oneKeyResetLocation"));

    }

    @Override
    public void autoRelocation(String agvCode, String type, String agvMapId) {
        MqttUtils.sendMqttMsg(MqttConstant.RELOCATION, agvCode, new RelocationMessage(agvCode, type, agvMapId));
    }

    @Override
    public void manualRelocation(RelocationMessage relocationMessage) {
        MqttUtils.sendMqttMsg(MqttConstant.RELOCATION, relocationMessage.getAgvCode(), relocationMessage);
    }

    @Override
    public void agvRestart(String agvCode) {
        MqttUtils.sendMqttMsg(MqttConstant.COMMAND, agvCode, new CommandMessage(agvCode, "reboot"));
    }

    @Override
    public void switchManualMode(String agvCode) {
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
        /*if (!TASK_STATUS_FREE.equals(vehicle.getWorkStatus()) && ABNORMAL_STATUS_NO.equals(vehicle.getAbnormalStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("http.agv_execute_task_now"));
        }*/
        MqttUtils.sendMqttMsg(MqttConstant.COMMAND, agvCode, new CommandMessage(agvCode, "manualMode"));
    }

    @Override
    public void switchAutoMode(String agvCode) {
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
        if (!MAP_STATUS_APPOINT.equals(vehicle.getAppointStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.map_not_sync"));
        }
        MqttUtils.sendMqttMsg(MqttConstant.COMMAND, agvCode, new CommandMessage(agvCode, "autoMode"));
    }

    /**
     * 开始作业
     *
     * @param workScheduler
     */
    public void startMissionWork(WorkScheduler workScheduler) {
        try {

            //开始任务。
            MissionWork missionWork = missionWorkService.selectById(workScheduler.getWorkId());
            // 如果作业不存在，则取消分配。
            if (missionWork == null) {
                workSchedulerService.updateCancel(workScheduler.getWorkId());
                logger.error("agvCode:[{}],event[作业下发]，content:[mission work不存在,missionWorkId:{}]", workScheduler.getVehicleId(), workScheduler.getWorkId());
                return;
            }

            Mission mission = missionService.selectById(missionWork.getMissionId());
            // 如果任务不存在，则作业异常并且取消分配。
            if (mission == null) {
                missionWorkService.updateStatus(missionWork, MISSION_WORK_STATUS_SHUTDOWN);
                workSchedulerService.updateCancel(workScheduler.getWorkId());
                logger.error("agvCode:[{}],event[作业下发]，content:[作业关联的mission不存在,missionWorkId:{}]", workScheduler.getVehicleId(), workScheduler.getWorkId());
                return;
            }
            missionWork.setAgvCode(workScheduler.getVehicleId());
            missionWorkService.update(missionWork);
            // 获取MissionAction列表
            List<MissionAction> missionActions = missionActionService.selectByMissionId(missionWork.getMissionId());
            // 由于有子动作的存在，不再进行排序
//            missionActions = MissionActionSortUtils.sortMissionActionListBySequence(missionActions);
            //获取missionActionParameter
            List<MissionActionParameter> missionActionParameters = missionActionParameterService.selectByMissionActionIds(missionActions.stream().map(MissionAction::getId).collect(Collectors.toList()));
            //获取MissionGlobalVariable列表
            List<MissionGlobalVariable> missionGlobalVariables = missionGlobalVariableService.selectByMissionId(missionWork.getMissionId());

            //获取设定泊车点的动作类型，在fleet直接处理
            List<MissionAction> missionActionsForParking = missionActions.stream().filter(missionAction -> missionAction.getActionType().equals(SET_PARK_MARKER)).collect(Collectors.toList());
            if (!missionActionsForParking.isEmpty()) {
                //以创建时间倒序，取最后一个动作来更新绑定的泊车点
                MissionAction parkAction = missionActionsForParking.stream().max(Comparator.comparing(MissionAction::getCreateTime)).get();
                List<MissionActionParameter> parkActionParameters = missionActionParameters.stream().filter(missionActionParameter -> missionActionParameter.getMissionActionId().equals(parkAction.getId())).collect(Collectors.toList());
                String mapId = getParam(AGV_MAP_ID, missionGlobalVariables, parkActionParameters, missionWork);
                String markerCode = getParam(MARKER_CODE, missionGlobalVariables, parkActionParameters, missionWork);

                if (Objects.isNull(mapId) || Objects.isNull(markerCode)) {
                    throw new RuntimeException("动作参数异常！，请检查配置！");
                }
                //找到对应marker绑定的泊车点
                Marker marker = markerService.selectMarkerByMapIdAndMarkerCode(mapId, markerCode, false);
                if (Objects.isNull(marker)) {
                    //点位不存在 任务直接失败
                    String errorMsg = String.format(String.format("找不到对应marker：[%s]，无法查找关联的泊车点，请检查地图配置！", markerCode));
                    missionWork.setStatus(MISSION_WORK_STATUS_FAULT);
                    missionWork.setErrorCode(ExceptionInfoEnum.MISSION_WORK_NOT_PARK_BAND.getErrorCode());
                    String errorMessage = abnormalPromptService.getAbnormalMsgByCode(ExceptionInfoEnum.MISSION_WORK_NOT_PARK_BAND.getErrorCode());
                    if (StringUtils.isEmpty(errorMessage)) {
                        missionWork.setMessage(errorMsg);
                    } else {
                        missionWork.setMessage(errorMessage);
                    }
                    missionWorkService.updateStatus(missionWork, true, true);
                    throw new RuntimeException(errorMsg);
                }
                List<String> parkMarkerIds = marker.getBindParkMarkers();
                //如果点位有绑定泊车点 则修改配置 无绑定不操作
                if (!CollUtil.isEmpty(parkMarkerIds)) {
                    List<Marker> enableParkMarkers = markerService.selectEnableParkMarkersByMapId(mapId);
                    List<Marker> bandParMarkers = enableParkMarkers.stream().filter(m -> parkMarkerIds.contains(m.getCode())).collect(Collectors.toList());
                    if (bandParMarkers.isEmpty()) {
                        String errorMsg = String.format(String.format("找不到点位：[%s]关联的泊车点，请检查地图配置！", markerCode));
                        missionWork.setStatus(MISSION_WORK_STATUS_FAULT);
                        missionWork.setErrorCode(ExceptionInfoEnum.MISSION_WORK_NOT_PARK_BAND.getErrorCode());
                        String errorMessage = abnormalPromptService.getAbnormalMsgByCode(ExceptionInfoEnum.MISSION_WORK_NOT_PARK_BAND.getErrorCode());
                        if (StringUtils.isEmpty(errorMessage)) {
                            missionWork.setMessage(errorMsg);
                        } else {
                            missionWork.setMessage(errorMessage);
                        }
                        missionWorkService.updateStatus(missionWork, true, true);
                        throw new RuntimeException(errorMsg);
                    }

                    List<AGVBindMarkerVo> configs = new ArrayList<>();
                    bandParMarkers.forEach(t -> {
                        AGVBindMarkerVo vo = new AGVBindMarkerVo();
                        vo.setAgvMapId(mapId);
                        vo.setMarkerCode(t.getCode());
                        configs.add(vo);
                    });
                    setAgvParkConfig(workScheduler.getVehicleId(), configs);
                } else {
                    logger.warn("设定泊车点动作的点位：{}没有关联泊车点", markerCode);
                }
            }
            Map<String, Object> message = new HashMap<>();
            message.put("mission", mission);
            message.put("missionWork", missionWork);
            message.put("missionActions", missionActions);
            message.put("missionActionParameters", missionActionParameters);
            message.put("missionGlobalVariables", missionGlobalVariables);
            message.put("uniqueFlag", UUID.randomUUID().toString());

            MqttUtils.sendMqttMsg(MqttConstant.MISSION, workScheduler.getVehicleId(), message);
            logger.info("agvCode:[{}],event[发送作业指令]，content:[发送下发任务mq消息成功,missionWork:{}]", workScheduler.getVehicleId(), JSON.toJSONString(missionWork));
        } catch (Exception e) {
            // 异常后取消分配。
            logger.error("agvCode:[{}],event[发送作业指令]，content:[分配任务异常,missionWorkId:{}]", workScheduler.getVehicleId(), workScheduler.getWorkId(), e);
            workSchedulerService.updateCancel(workScheduler.getWorkId());
        }
    }

    private void setAgvParkConfig(String agvCode, List<AGVBindMarkerVo> configs) {
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (vehicle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }
        ThreadContext.put("ROUTINGKEY", agvCode);
        agv.setBindParkConfig(true);
        agv.setBindParkMarkers(JSONObject.toJSONString(configs));
        agvService.update(agv);
        vehicle.setBindParkConfig(true);
        vehicle.setAllowParkMarkerIds(agvService.getAGVBindMarkers(agv.getBindParkMarkers()));
        logger.debug("agvCode:[{}],event:[开启泊车点绑定配置],content:[{}]", agvCode, agv.toString());
        ThreadContext.remove("ROUTINGKEY");
    }

    private String getAgvParkConfig(String agvCode) {
        Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (vehicle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }
        return agv.getBindParkMarkers();
    }

    private String getParam(String key, List<MissionGlobalVariable> globalVariables, List<MissionActionParameter> params, MissionWork missionWork) {
        MissionActionParameter missionParam = params.stream().filter(missionActionParameter -> Objects.equals(missionActionParameter.getParameterKey(), key)).findFirst().orElse(null);
        if (Objects.nonNull(missionParam)) {
            //如果没有运行参数，则用固定参数
            if (Objects.nonNull(missionWork.getRuntimeParam())) {
                JSONObject js = JSON.parseObject(missionWork.getRuntimeParam());
                MissionGlobalVariable globalVariable = globalVariables.stream().filter(missionWorkGlobalVariable -> Objects.equals(missionWorkGlobalVariable.getVariableKey(), missionParam.getParameterValue())).findFirst().orElse(null);
                //没有运行参数，用固定参数
                if (Objects.nonNull(globalVariable)) {
                    String value = js.getString(globalVariable.getVariableKey());
                    return value;
                }
                return missionParam.getParameterValue();
            } else {
                return missionParam.getParameterValue();
            }
        }
        return null;
    }

    @Override
    public void cancelMissionWork(String missionWorkId, String vehicleId) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "stopMission");
        message.put("missionWorkId", missionWorkId);
        message.put("uniqueFlag", UUID.randomUUID().toString());
        MqttUtils.sendMqttMsg(MqttConstant.MISSION_WORK_COMMAND, vehicleId, message);
        logger.debug("agvCode:[{}],event:[停止作业]，content:[发送停止作业指令完成，message:{}]", vehicleId, JSON.toJSONString(message));
    }

    @Override
    public void pauseMissionWork(String missionWorkId, String vehicleId) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "pauseMission");
        message.put("missionWorkId", missionWorkId);
        message.put("uniqueFlag", UUID.randomUUID().toString());
        MqttUtils.sendMqttMsg(MqttConstant.MISSION_WORK_COMMAND, vehicleId, message);
        logger.debug("agvCode:[{}],event:[暂停作业]，content:[发送暂停作业指令完成，message:{}]", vehicleId, JSON.toJSONString(message));
    }

    @Override
    public void resumeMissionWork(String missionWorkId, String vehicleId) {
        //发送mq恢复任务
        Map<String, Object> message = new HashMap<>();
        message.put("type", "resumeMission");
        message.put("missionWorkId", missionWorkId);
        message.put("uniqueFlag", UUID.randomUUID().toString());
        MqttUtils.sendMqttMsg(MqttConstant.MISSION_WORK_COMMAND, vehicleId, message);
        logger.debug("agvCode:[{}],event:[恢复作业]，content:[发送恢复作业指令完成，message:{}]", vehicleId, JSON.toJSONString(message));
    }

    @Override
    public void continueMissionWork(String missionWorkId, String vehicleId) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "continueMission");
        message.put("missionWorkId", missionWorkId);
        message.put("uniqueFlag", UUID.randomUUID().toString());
        MqttUtils.sendMqttMsg(MqttConstant.MISSION_WORK_COMMAND, vehicleId, message);
        logger.debug("agvCode:[{}],event:[继续作业]，content:[发送继续作业指令完成，message:{}]", vehicleId, JSON.toJSONString(message));
    }

    @Override
    public void startCharge(ChargeScheduler chargeScheduler) {
        Map<String, Object> message = new HashMap<>();
        message.put("id", chargeScheduler.getId());
        message.put("command", "startCharge");
        Integer chargeMode = markerService.selectById(chargeScheduler.getChargePointMapName(), chargeScheduler.getChargeId(), false) == null ? 1 : 0;
        message.put("chargeMode", chargeMode);
        message.put("chargeMarkerId", chargeScheduler.getChargeId());
        message.put("retryCount", 5);
        message.put("retryInterval", 10);
        message.put("uniqueFlag", UUID.randomUUID().toString());
        MqttUtils.sendMqttMsg(MqttConstant.CHARGE_TOPIC, chargeScheduler.getVehicleId(), message);
        logger.debug("agvCode:[{}],event[发送充电指令]，content:[发送下发充电mq消息完成，message:{}]", chargeScheduler.getVehicleId(), JSON.toJSONString(message));
    }

    @Override
    public void cancelCharge(String vehicleId) {
        Map<String, Object> message = new HashMap<>();
        ChargeScheduler chargeScheduler = chargeSchedulerService.selectRunningByVehicle(vehicleId);
        if (chargeScheduler != null) {
            message.put("id", chargeScheduler.getId());
            message.put("command", "cancelCharge");
            message.put("uniqueFlag", UUID.randomUUID().toString());
            MqttUtils.sendMqttMsg(MqttConstant.CHARGE_TOPIC, vehicleId, message);
            logger.debug("agvCode:[{}],event[充电取消]，content:[发送取消充电mq消息完成，message:{}]", vehicleId, JSON.toJSONString(message));
        }
    }

    @Override
    public void cancelPathNavigation(String vehicleId) {
        Map<String, Object> message = new HashMap<>();
        MqttUtils.sendMqttMsg(MqttConstant.COMMAND, vehicleId, new CommandMessage(vehicleId, "cancelPathNavigation"));
        logger.debug("agvCode:[{}],event:[取消路径导航]，content:[发送取消路径导航指令完成，message:{}]", vehicleId, "cancelPathNavigation");
    }

    @Override
    public void startPark(ParkScheduler parkScheduler) {
        Map<String, Object> message = new HashMap<>();
        message.put("id", parkScheduler.getId());
        message.put("command", "startPark");
        message.put("parkCode", parkScheduler.getParkPointCode());//仅用于打印
        message.put("parkMarkerId", parkScheduler.getParkId());
        message.put("uniqueFlag", UUID.randomUUID().toString());
        MqttUtils.sendMqttMsg(MqttConstant.PARK_TOPIC, parkScheduler.getVehicleId(), message);
        logger.debug("agvCode:[{}],event[发送泊车指令]，content:[发送下发泊车mq消息完成，message:{}]", parkScheduler.getVehicleId(), JSON.toJSONString(message));
    }

    @Override
    public void cancelPark(String vehicleId) {
        Map<String, Object> message = new HashMap<>();
        ParkScheduler parkScheduler = parkSchedulerService.selectRunningByVehicle(vehicleId);
        if (parkScheduler != null) {
            message.put("id", parkScheduler.getId());
            message.put("command", "cancelPark");
            message.put("uniqueFlag", UUID.randomUUID().toString());
            MqttUtils.sendMqttMsg(MqttConstant.PARK_TOPIC, vehicleId, message);
            logger.debug("agvCode:[{}],event[取消泊车]，content:[发送取消泊车mq消息完成，message:{}]", vehicleId, JSON.toJSONString(message));
        }
    }

    @Override
    public void clearDockingError(String missionWorkId, String vehicleId) {
        Map<String, Object> message = new HashMap<>();
        message.put("type", "clearDockingError");
        message.put("missionWorkId", missionWorkId);
        message.put("uniqueFlag", UUID.randomUUID().toString());
        MqttUtils.sendMqttMsg(MqttConstant.MISSION_WORK_COMMAND, vehicleId, message);
        logger.debug("agvCode:[{}],event:[对接清错]，content:[发送对接清错指令完成，message:{}]", vehicleId, JSON.toJSONString(message));
    }

    @Override
    public void rePathPlan(String agvCode) {
        MqttUtils.sendMqttMsg(MqttConstant.COMMAND, agvCode, new CommandMessage(agvCode, "rePathPlan"));
        logger.debug("agvCode:[{}],event:[重新路径规划]，content:[发送重新路径规划指令完成，message:{}]", agvCode, "rePathPlan");
    }


}
