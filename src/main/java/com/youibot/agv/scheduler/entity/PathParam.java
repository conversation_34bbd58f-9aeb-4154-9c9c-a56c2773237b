package com.youibot.agv.scheduler.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>  E-mail:shis<PERSON><EMAIL>
 * @version CreateTime: 2019/10/15 15:49
 */
@Data
@ApiModel(value = "PathParam", description = "路径参数")
public class PathParam {

    @ApiModelProperty(value = "融合特征 1、开启 0、关闭",position = 20)
    private Integer features_requested;

    @ApiModelProperty(value = "避障 1、打开避障  2、关闭避障", position = 2)
    private Integer safety;

    @ApiModelProperty(value = "最大平移速度 >0 m/s", position = 11)
    private Double max_translation_speed;

    @ApiModelProperty(value = "最大旋转速度 rad/s >0", position = 12)
    private Double max_rotate_speed;

    private Double P;

    private Double D;

    @ApiModelProperty(value = "平移加（减）速度 m/s2 >0", position = 15)
    private Double translation_acc;

    @ApiModelProperty(value = "旋转加（减）速度 rad/s2 >0", position = 16)
    private Double rotate_acc;

    @ApiModelProperty(value = "雷达避障区域 0、默认区域")
    private Integer safety_scanner_region;//安全雷达区域切换，可设置为0，9,10,11  此设置为安全雷达避障，与上方点云避障无关

    @ApiModelProperty(value = "旋转避障区域，值1-16代表第1-16组区域，可以传空")
    private Integer rotation_obstacle_region;

    @ApiModelProperty(value = "创建时间", position = 17)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", position = 18)
    private Date updateTime;

    @JsonProperty("P")
    @ApiModelProperty(value = "平移加速比例控制系数 >0", position = 13)
    public Double getP() {
        return P;
    }

    @JsonProperty("D")
    @ApiModelProperty(value = "平移加速微分控制系数 >0", position = 14)
    public Double getD() {
        return D;
    }

    @ApiModelProperty(value = "拓展布尔",position = 19)
    private String extend_bit;

    @ApiModelProperty(value = "拓展字符串",position = 20)
    private String extend_string;

}
