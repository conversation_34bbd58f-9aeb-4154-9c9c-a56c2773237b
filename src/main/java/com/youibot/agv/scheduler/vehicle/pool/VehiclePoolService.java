package com.youibot.agv.scheduler.vehicle.pool;

import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.vo.FreeAgvAndNotFreeReasonVO;

import java.util.List;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-12 16:16
 */
public interface VehiclePoolService {

    /**
     * 获取所有的可以工作的机器人。
     * 空闲的，泊车中的，充电中的，符合条件的都可以作业。
     *
     * @return
     */
    List<Vehicle> getWaitWork();

    /**
     * 获取所有的可以工作的机器人。
     * 包括工作中的只有一个预分配任务的机器人
     * 不包括充电时长未满阈值的，以及正在进行校正充电的
     */
    List<Vehicle> getWaitWorkAndWorking(SchedulerConfig schedulerConfig);

    /**
     * 获取所有的可以充电的机器人。
     *
     * @return
     * @param 
     */
    List<Vehicle> getWaitCharge();

    /**
     * 获取所有需要泊车的机器人。
     *
     * @return
     * @param
     */
    List<Vehicle> getWaitPark();

    /**
     * 获取可驱赶机器人
     * @return
     */
    List<Vehicle> getCanDriveAway();

    /**
     * 更新池内机器人信息。
     *
     * @param vehicle
     */
    void updateVehicle(Vehicle vehicle);

    /**
     * 根据vehicleId 获取 vehicle.
     *
     * @param vehicleId
     * @return
     */
    Vehicle selectById(String vehicleId);

    List<Vehicle> selectAll();
    /**
     * 获取所有的可以工作的机器人和不可工作的机器人及其不可工作的原因。
     * 空闲的，泊车中的，充电中的，符合条件的都可以作业。
     *
     * @return
     */
    FreeAgvAndNotFreeReasonVO getWaitWorkAndNotFreeReason();
}
