package com.youibot.agv.scheduler.vehicle;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.youibot.agv.scheduler.engine.exception.AGVResultException;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.mqtt.bean.push.cmd.RelocationMessage;
import com.youibot.agv.scheduler.mqtt.bean.scribe.pathplan.PathPlanMessage;
import com.youibot.agv.scheduler.param.ErrorParam;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.util.ApplicationUtils;
import com.youibot.agv.scheduler.vehicle.entity.Colors;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.entity.ShapeInfo;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.youibot.agv.scheduler.constant.VehicleConstant.*;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 20:54
 */
@Data
public abstract class Vehicle {

    private final static Logger LOGGER = LoggerFactory.getLogger(Vehicle.class);

    protected String id;

    // 名称
    protected String name;

    //agv 地图id
    protected String agvMapId;

    protected String agvGroupId;

    protected String agvType;

    private String agvColor;

    /**
     * 路径规划请求队列。
     */
    private LinkedList<PathPlanMessage> pathPlanMessages = new LinkedList<>();

    private LinkedList<SidePath> sidePaths = new LinkedList<>();

    private List<Path> planedPaths = new LinkedList<>();
    private List<Path> executedPaths = new LinkedList<>();
    private List<Path> runningPaths = new LinkedList<>();

    //机器人当前执行的路径
    private volatile CopyOnWriteArrayList<String> currentRunningPaths = new CopyOnWriteArrayList<>();

    private Colors colors = new Colors();

    //连接时间
    protected Date connectedTime = new Date();

    //地图状态(自行更新)
    protected Integer mapStatus;// 0、未同步  1、已同步

    //地图指定状态(自行更新)
    protected Integer appointStatus;//0:未指定; 1:已指定

    //任务状态
    protected Integer workStatus = TASK_STATUS_FREE;

    //异常状态
    protected Integer abnormalStatus = ABNORMAL_STATUS_NO;

    //控制模式
    protected Integer controlMode;

    protected String navigationType;//导航类型 LASER:激光导航 QR_CODE:二维码导航 BLEND:混合导航

    protected Boolean initialized = false;

    // agv 设备列表信息
    protected ShapeInfo shapeInfo;

    // agv 状态信息
    protected DefaultVehicleStatus defaultVehicleStatus;

    protected boolean useElevator;

    //错误提示信息，目前只提示智能充电(归位)报错信息
    protected String errorMessage;

    protected boolean terminate = false;//是否已经调用terminate标记

    protected Integer status = DISABLE;//启用状态 0：未启用 1：启用

    private Integer onlineStatus = OUTLINE;//在线状态 0：离线 1：在线 2：断线

//    private volatile Integer allocateStatus = UNALLOCATE;//分配状态 0：未分配 1：已分配

    /**
     * 可执行状态,0:不可执行,1:可执行
     */
//    private volatile Integer isExecutable = 0;

//    private Integer workSequence;//工作状态下的任务优先级，无任务时为null,有值即有任务

    @JsonIgnore
    protected String laserData;//点云数据

    private Integer mustChargeBatteryValue;//智能充电必须充电电量阈值

    private String missionName;//执行的任务名称

    private String missionWorkId;//执行的作业ID

    private String lastMarkerId;//最后一次所在标记点ID

    /**
     * 上一次导航的目标点ID
     */
    private String lastNavigationAimMarkerId;

//    private boolean parkOrChargeScheduler = true;// true:自动泊车和充电。false:不自动泊车和充电。

    private boolean autoCharge = true; //true:自动充电，false:不自动充电

    private boolean autoPark = true; //true:自动泊车，false:不自动泊车

    private boolean autoAllocation = true; //true:任务自动分配, false: 任务不自动分配

    private Map<String, List<String>> allowParkMarkerIds;//所有绑定的泊车点 key为agvMapName value为 泊车点ID列表

    private boolean bindParkConfig;//绑定泊车点配置

    private Map<String, List<String>> allowChargeMarkerIds;//绑定的充电点

    private boolean bindChargeConfig;//绑定充电点配置

    private Integer isRePathPlan = 0;//是否重新路径规划,0否，1是
    private Long emc_jam_start_time;//路径导航急停的开始时间

    private List<ErrorParam> errorData = new ArrayList<>();//除任务外，异常集合

    private List<ErrorParam> missionErrorData = new ArrayList<>();//任务异常集合


    /**
     * 最后在风淋门外部通知开门的时间
     */
    private Long lastNotifyAirShowerDoorTimeOutside = 0L;

    // 仿真模式
    private boolean simulation = false;
    public DefaultVehicleStatus.PositionStatus getPositionStatus() {
        if (defaultVehicleStatus != null) {
            return defaultVehicleStatus.getPosition();
        }
        return null;
    }

    public String getCurrentMarkerId() {
        DefaultVehicleStatus.PositionStatus positionStatus = this.getPositionStatus();
        if (positionStatus == null) {
            return null;
        }
        return positionStatus.getPos_current_station();
    }

    public abstract Double getBatteryValue();

    public void initialize(Agv agv) {
        this.id = agv.getAgvCode();
        this.name = agv.getAgvName();
        this.agvType = agv.getAgvType();
        this.navigationType = agv.getNavigationType();
        this.shapeInfo = JSON.parseObject(agv.getShapeInfo(), ShapeInfo.class);
        this.status = agv.getStatus();
        this.onlineStatus = agv.getOnlineStatus();
        this.agvGroupId = agv.getAgvGroupId();
        this.agvColor = agv.getAgvColor();
        //当自动充电和泊车为null时，初始化小车的自动充电和泊车值为调度配置中的值
        SchedulerConfigService schedulerConfigService = (SchedulerConfigService) ApplicationUtils.getBean("schedulerConfigServiceImpl");
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        this.autoCharge = agv.getAutoCharge() == null || agv.getAutoCharge() == 2 ? schedulerConfig.getChargeSchedulerEnable() == 1 : agv.getAutoCharge() == 1;
        this.autoPark = agv.getAutoPark() == null || agv.getAutoPark() == 2 ? schedulerConfig.getParkSchedulerEnable() == 1 : agv.getAutoPark() == 1;
        this.bindChargeConfig = agv.isBindChargeConfig();
        this.bindParkConfig = agv.isBindParkConfig();
        AGVService agvService = (AGVService) ApplicationUtils.getBean("AGVServiceImpl");
        this.allowChargeMarkerIds = agvService.getAGVBindMarkers(agv.getBindChargeMarkers());
        this.allowParkMarkerIds = agvService.getAGVBindMarkers(agv.getBindParkMarkers());
    }

    /**
     * 调度系统不维护机器人的状态。是根据机器人的实际状态进行变化。
     * 需要一个当前正在分配的管理列表。把已分配的机器人放进去。当机器人的作业状态发生变更后。删除相关信息。
     * 并且在机器人分配过程中过滤掉已被分配的机器人。
     *
     * @param workScheduler
     */
    public abstract void startMissionWork(WorkScheduler workScheduler);

    public abstract void cancelMissionWork(String missionWorkId);

    public abstract void pauseMissionWork(String missionWorkId);

    public abstract void resumeMissionWork(String missionWorkId);

    public abstract void continueMissionWork(String missionWorkId);

    public abstract void clearDockingError(String missionWorkId);

    // 开始充电
    public abstract void startCharge(ChargeScheduler chargeScheduler);

    // 取消充电
    public abstract void cancelCharge();

    // 开始泊车
    public abstract void startPark(ParkScheduler parkScheduler);

    // 取消泊车
    public abstract void cancelPark();

    /**
     * 取消当前的路径导航，当不会取消任务。
     */
    public abstract void cancelPathNavigation();


    /**
     * 切换为手工控制模式
     *
     * @throws AGVResultException
     * @throws IOException
     */
    public abstract void switchManualMode() throws AGVResultException, IOException;

    /**
     * 切换到自动控制模式
     */
    public abstract void switchAutoMode() throws IOException, InterruptedException;

    public abstract void manualRelocation(RelocationMessage relocationMessage);

    public abstract void autoRelocation();

    /**
     * AGV一键恢复
     */
    public abstract void oneKeyResume();

    /**
     * AGV一键停止
     */
    public abstract void oneKeyStop();

    /**
     * AGV一键重置
     */
    public abstract void oneKeyReset();
    
    /**
     * AGV一键重置
     */
    public abstract void oneKeyResetLocation();

    /**
     * AGV重启
     *
     * @throws IOException
     */
    public abstract void agvRestart();

    /**
     * 指定地图
     *
     * @param agvMapId
     */
    public abstract void appointMap(String agvMapId);

    /**
     * 机器人重新规划路径。
     */
    public abstract void rePathPlan();

}
