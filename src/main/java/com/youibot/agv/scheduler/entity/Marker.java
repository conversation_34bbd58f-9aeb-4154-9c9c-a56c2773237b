package com.youibot.agv.scheduler.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.entity.Resource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.List;

import static com.youibot.agv.scheduler.constant.MapConstant.PATH_ALLOW_STOP_NAVIGATION;

/**
 * 标记点
 * 类名称：Marker
 * 创建时间：2019年4月2日 下午3:34:56
 */
@Data
@ApiModel(value = "Marker", description = "标记点")
public class Marker extends Resource {

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "编码", position = 1)
    private String code;

    @ApiModelProperty(value = "编码别名", position = 1)
    private String name;

    @ApiModelProperty(value = "x坐标", position = 3)
    private Double x;

    @ApiModelProperty(value = "y坐标", position = 4)
    private Double y;

    @ApiModelProperty(value = "地图名称", position = 6)
    private String agvMapName;

    @ApiModelProperty(value = "角度", position = 7)
    private Double angle;

    @ApiModelProperty(value = "类型 INITIAL_MARKER:初始点, CHARGING_MARKER:充电点, NAVIGATION_MARKER:导航点, WORK_MARKER:工作点, ADJUST_MARKER:调整点, ELEVATOR_MARKER:电梯点 , AVOID_MARKER:避让点", position = 10)
    private String type;

    public String getCovariance() {
        return covariance;
    }

    public void setCovariance(String covariance) {
        if (!StringUtils.isEmpty(covariance)) {
            JSONArray array = JSONObject.parseArray(covariance);
            this.covariance = array.toJSONString();
        }
    }

    @ApiModelProperty(value = "协方差矩阵", position = 12)
    private String covariance;

    @ApiModelProperty(value = "使用状态 ENABLE:启用  DISABLE:禁用", position = 13)
    private String usageStatus;

    @ApiModelProperty(value = "是否可泊车, 0:不可泊车，1：可泊车", position = 14)
    private Integer isPark;

    @ApiModelProperty(value = "是否可避让 1：可 0：不可,默认可避让", position = 14)
    private Integer isAvoid;

    @ApiModelProperty(value = "路网点类型, 0:交叉路网点，1：普通路网点,默认普通路网点", position = 14)
    private Integer networkMarkerType;

    @ApiModelProperty(value = "对节点", position = 14)
    private DockingPoint dockingPoint;

    @ApiModelProperty(value = "调整点数据", position = 15)
    private AdjustAction adjustAction;

    @ApiModelProperty(value = "关联的泊车点", position = 16)
    private List<String> bindParkMarkers;

    @ApiModelProperty(value = "是否可路径上停止路径导航  1、允许  2、禁止", position = 17)
    private Integer allowStopNavigation = PATH_ALLOW_STOP_NAVIGATION;

}
