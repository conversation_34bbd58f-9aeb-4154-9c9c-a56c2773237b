2025-03-13 16:00:47.379 [background-preinit] INFO  org.hibernate.validator.internal.util.Version 21 <clinit> - HV000001: Hibernate Validator 6.1.5.Final
2025-03-13 16:00:47.396 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 55 logStarting - Starting AdminApplication on DESKTOP-4CUGQPA with PID 11424 (E:\work\idea\wk2_fleet5\youisimulation-plus\youisimulation-admin\target\classes started by admin in E:\work\idea\wk2_fleet5)
2025-03-13 16:00:47.396 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 655 logStartupProfileInfo - The following profiles are active: dev,dev-agv
2025-03-13 16:00:50.918 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroConfig' of type [com.youibot.vehicle.scheduler.modules.security.config.ShiroConfig$$EnhancerBySpringCGLIB$$53c49a2c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.068 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.072 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusConfig' of type [com.youibot.vehicle.scheduler.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$a80c42e4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.081 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.087 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-join-com.github.yulichang.autoconfigure.MybatisPlusJoinProperties' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.090 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join properties config complete
2025-03-13 16:00:51.092 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.098 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjInterceptor' of type [com.github.yulichang.interceptor.MPJInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.105 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.108 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure$$EnhancerBySpringCGLIB$$84dd6cfe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.109 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure 56 dataSource - Init DruidDataSource
2025-03-13 16:00:51.172 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.174 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration' of type [com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.202 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'statFilter' of type [com.alibaba.druid.filter.stat.StatFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.892 [main] INFO  com.alibaba.druid.pool.DruidDataSource 1010 init - {dataSource-1} inited
2025-03-13 16:00:51.892 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'dataSource' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.899 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'myMetaObjectHandler' of type [com.youibot.vehicle.scheduler.common.config.MyMetaObjectHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:51.902 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join SqlInjector init
2025-03-13 16:00:51.905 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjSqlInjectorOnMiss' of type [com.github.yulichang.injector.MPJSqlInjector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.696 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.700 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.700 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.702 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [com.sun.proxy.$Proxy84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.705 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.706 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [com.sun.proxy.$Proxy85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.707 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.708 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [com.sun.proxy.$Proxy86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.709 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroServiceImpl' of type [com.youibot.vehicle.scheduler.modules.security.service.impl.ShiroServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.709 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'oauth2Realm' of type [com.youibot.vehicle.scheduler.modules.security.oauth2.Oauth2Realm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.727 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sessionManager' of type [com.youibot.vehicle.scheduler.common.config.CustomSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.743 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.805 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:52.819 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$60f50fb1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:53.011 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration$$EnhancerBySpringCGLIB$$c390606c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:53.020 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:53.024 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:53.027 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:53.038 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:53.089 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:53.099 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:00:53.164 [main] INFO  com.dtflys.forest.scanner.ClassPathClientScanner 153 processBeanDefinitions - [Forest] Created Forest Client Bean with name 'forestClient' and Proxy of 'com.youibot.vehicle.scheduler.modules.sim.helper.ForestClient' client interface
2025-03-13 16:00:53.579 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 108 initialize - Tomcat initialized with port(s): 6080 (http)
2025-03-13 16:00:53.592 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Initializing ProtocolHandler ["http-nio-6080"]
2025-03-13 16:00:53.592 [main] INFO  org.apache.catalina.core.StandardService 173 log - Starting service [Tomcat]
2025-03-13 16:00:53.593 [main] INFO  org.apache.catalina.core.StandardEngine 173 log - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-03-13 16:00:53.748 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring embedded WebApplicationContext
2025-03-13 16:00:53.749 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext 285 prepareWebApplicationContext - Root WebApplicationContext: initialization completed in 6303 ms
2025-03-13 16:00:55.221 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:00:57.926 [main] INFO  com.alibaba.arthas.spring.ArthasConfiguration 70 arthasAgent - Arthas agent start success.
2025-03-13 16:00:58.305 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 181 initialize - Initializing ExecutorService 'taskScheduler'
2025-03-13 16:00:58.476 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Starting ProtocolHandler ["http-nio-6080"]
2025-03-13 16:00:58.501 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 220 start - Tomcat started on port(s): 6080 (http) with context path ''
2025-03-13 16:00:58.957 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 61 logStarted - Started AdminApplication in 12.149 seconds (JVM running for 13.891)
2025-03-13 16:00:59.205 [main] INFO  org.reflections.Reflections 232 scan - Reflections took 43 ms to scan 1 urls, producing 5 keys and 15 values 
2025-03-13 16:01:04.404 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:01:24.425 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:01:33.935 [http-nio-6080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-13 16:01:33.936 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 525 initServletBean - Initializing Servlet 'dispatcherServlet'
2025-03-13 16:01:33.958 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 547 initServletBean - Completed initialization in 22 ms
2025-03-13 16:01:44.450 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:02:04.463 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:02:24.478 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:02:44.488 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:03:04.508 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:03:24.526 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:03:44.551 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:04:04.574 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:04:24.598 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:04:44.620 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:05:04.645 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:05:24.669 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:05:44.681 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:06:04.692 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:06:24.805 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:06:44.830 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:07:04.836 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:07:24.854 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:07:44.863 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:08:04.883 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:08:24.893 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:08:44.914 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:09:04.930 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:09:24.935 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:09:44.946 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:10:04.976 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:10:25.004 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:10:45.027 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:11:05.031 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:11:25.046 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:11:45.058 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:12:05.076 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:12:25.098 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:12:45.117 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:13:05.139 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:13:15.363 [Thread-17] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1265 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:13:15.364 [Thread-17] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1266 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:13:15.364 [Thread-17] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1267 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:13:15.365 [Thread-17] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1157 destroy - destroy
2025-03-13 16:13:15.368 [SpringContextShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 53 shutDownGracefully - Commencing graceful shutdown. Waiting for active requests to complete
2025-03-13 16:14:33.735 [background-preinit] INFO  org.hibernate.validator.internal.util.Version 21 <clinit> - HV000001: Hibernate Validator 6.1.5.Final
2025-03-13 16:14:33.743 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 55 logStarting - Starting AdminApplication on DESKTOP-4CUGQPA with PID 22116 (E:\work\idea\wk2_fleet5\youisimulation-plus\youisimulation-admin\target\classes started by admin in E:\work\idea\wk2_fleet5)
2025-03-13 16:14:33.744 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 655 logStartupProfileInfo - The following profiles are active: dev,dev-agv
2025-03-13 16:14:34.836 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroConfig' of type [com.youibot.vehicle.scheduler.modules.security.config.ShiroConfig$$EnhancerBySpringCGLIB$$2da8caef] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:34.960 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:34.964 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusConfig' of type [com.youibot.vehicle.scheduler.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$81f073a7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:34.975 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:34.981 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-join-com.github.yulichang.autoconfigure.MybatisPlusJoinProperties' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:34.985 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join properties config complete
2025-03-13 16:14:34.986 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:34.992 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjInterceptor' of type [com.github.yulichang.interceptor.MPJInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:34.999 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:35.002 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure$$EnhancerBySpringCGLIB$$5ec19dc1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:35.003 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure 56 dataSource - Init DruidDataSource
2025-03-13 16:14:35.054 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:35.057 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration' of type [com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:35.077 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'statFilter' of type [com.alibaba.druid.filter.stat.StatFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:35.686 [main] INFO  com.alibaba.druid.pool.DruidDataSource 1010 init - {dataSource-1} inited
2025-03-13 16:14:35.686 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'dataSource' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:35.694 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'myMetaObjectHandler' of type [com.youibot.vehicle.scheduler.common.config.MyMetaObjectHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:35.697 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join SqlInjector init
2025-03-13 16:14:35.699 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjSqlInjectorOnMiss' of type [com.github.yulichang.injector.MPJSqlInjector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.511 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.514 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.515 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.516 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [com.sun.proxy.$Proxy84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.518 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.519 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [com.sun.proxy.$Proxy85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.520 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.521 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [com.sun.proxy.$Proxy86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.522 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroServiceImpl' of type [com.youibot.vehicle.scheduler.modules.security.service.impl.ShiroServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.522 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'oauth2Realm' of type [com.youibot.vehicle.scheduler.modules.security.oauth2.Oauth2Realm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.528 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sessionManager' of type [com.youibot.vehicle.scheduler.common.config.CustomSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.540 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.590 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.602 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$3ad94074] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.748 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration$$EnhancerBySpringCGLIB$$9d74912f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.759 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.761 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.764 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.777 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.823 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.831 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:14:36.893 [main] INFO  com.dtflys.forest.scanner.ClassPathClientScanner 153 processBeanDefinitions - [Forest] Created Forest Client Bean with name 'forestClient' and Proxy of 'com.youibot.vehicle.scheduler.modules.sim.helper.ForestClient' client interface
2025-03-13 16:14:37.171 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 108 initialize - Tomcat initialized with port(s): 6080 (http)
2025-03-13 16:14:37.179 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Initializing ProtocolHandler ["http-nio-6080"]
2025-03-13 16:14:37.180 [main] INFO  org.apache.catalina.core.StandardService 173 log - Starting service [Tomcat]
2025-03-13 16:14:37.180 [main] INFO  org.apache.catalina.core.StandardEngine 173 log - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-03-13 16:14:37.272 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring embedded WebApplicationContext
2025-03-13 16:14:37.272 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext 285 prepareWebApplicationContext - Root WebApplicationContext: initialization completed in 3484 ms
2025-03-13 16:14:38.395 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:14:40.847 [main] INFO  com.alibaba.arthas.spring.ArthasConfiguration 70 arthasAgent - Arthas agent start success.
2025-03-13 16:14:41.062 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 181 initialize - Initializing ExecutorService 'taskScheduler'
2025-03-13 16:14:41.179 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Starting ProtocolHandler ["http-nio-6080"]
2025-03-13 16:14:41.192 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 220 start - Tomcat started on port(s): 6080 (http) with context path ''
2025-03-13 16:14:41.480 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 61 logStarted - Started AdminApplication in 8.198 seconds (JVM running for 9.046)
2025-03-13 16:14:41.664 [main] INFO  org.reflections.Reflections 232 scan - Reflections took 27 ms to scan 1 urls, producing 5 keys and 15 values 
2025-03-13 16:14:46.804 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:14:56.091 [http-nio-6080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-13 16:14:56.092 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 525 initServletBean - Initializing Servlet 'dispatcherServlet'
2025-03-13 16:14:56.133 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 547 initServletBean - Completed initialization in 41 ms
2025-03-13 16:15:06.812 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:15:24.288 [http-nio-6080-exec-13] INFO  com.dtflys.forest.config.ForestConfiguration 469 setBackend - [Forest] Http Backend: okhttp3
2025-03-13 16:15:24.357 [http-nio-6080-exec-13] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	GET http://127.0.0.1:8080/fleet/map/vehicleMaps/getMaplist HTTP
	Headers: 
		User-Agent: forest/1.5.36
2025-03-13 16:15:24.643 [http-nio-6080-exec-13] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 283ms
2025-03-13 16:15:26.820 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:15:46.836 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:16:06.848 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:16:26.867 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:16:46.888 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:17:06.910 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:17:26.931 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:17:46.939 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:18:06.945 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:18:20.136 [http-nio-6080-exec-27] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	GET http://127.0.0.1:8080/fleet/map/vehicleMaps/getMaplist HTTP
	Headers: 
		User-Agent: forest/1.5.36
2025-03-13 16:18:20.146 [http-nio-6080-exec-27] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 9ms
2025-03-13 16:18:26.958 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:18:46.968 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:19:06.996 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:19:27.001 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:19:47.006 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:20:07.033 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:20:27.056 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:20:47.072 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:21:07.131 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:21:27.141 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:21:47.162 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:22:07.174 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:22:27.195 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:22:47.235 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:23:07.258 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:23:27.286 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:23:47.300 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:24:07.314 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:24:27.335 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:24:47.349 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:25:07.367 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:25:27.428 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:25:47.530 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:26:07.551 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:26:27.564 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:26:47.581 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:26:52.654 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1265 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:26:52.655 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1266 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:26:52.656 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1267 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:26:52.656 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1157 destroy - destroy
2025-03-13 16:26:52.660 [SpringContextShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 53 shutDownGracefully - Commencing graceful shutdown. Waiting for active requests to complete
2025-03-13 16:26:53.050 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 78 doShutdown - Graceful shutdown complete
2025-03-13 16:26:53.099 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 218 shutdown - Shutting down ExecutorService 'taskScheduler'
2025-03-13 16:26:53.102 [SpringContextShutdownHook] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1154 destroy - destroy:isRunning,just_return
2025-03-13 16:26:53.103 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2003 close - {dataSource-1} closing ...
2025-03-13 16:26:53.106 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2075 close - {dataSource-1} closed
2025-03-13 16:27:47.899 [background-preinit] INFO  org.hibernate.validator.internal.util.Version 21 <clinit> - HV000001: Hibernate Validator 6.1.5.Final
2025-03-13 16:27:47.915 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 55 logStarting - Starting AdminApplication on DESKTOP-4CUGQPA with PID 28708 (E:\work\idea\wk2_fleet5\youisimulation-plus\youisimulation-admin\target\classes started by admin in E:\work\idea\wk2_fleet5)
2025-03-13 16:27:47.916 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 655 logStartupProfileInfo - The following profiles are active: dev,dev-agv
2025-03-13 16:27:49.195 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroConfig' of type [com.youibot.vehicle.scheduler.modules.security.config.ShiroConfig$$EnhancerBySpringCGLIB$$551e5033] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.354 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.359 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusConfig' of type [com.youibot.vehicle.scheduler.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$a965f8eb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.369 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.373 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-join-com.github.yulichang.autoconfigure.MybatisPlusJoinProperties' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.376 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join properties config complete
2025-03-13 16:27:49.377 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.382 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjInterceptor' of type [com.github.yulichang.interceptor.MPJInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.388 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.390 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure$$EnhancerBySpringCGLIB$$86372305] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.392 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure 56 dataSource - Init DruidDataSource
2025-03-13 16:27:49.453 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.455 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration' of type [com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:49.474 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'statFilter' of type [com.alibaba.druid.filter.stat.StatFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.065 [main] INFO  com.alibaba.druid.pool.DruidDataSource 1010 init - {dataSource-1} inited
2025-03-13 16:27:50.066 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'dataSource' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.072 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'myMetaObjectHandler' of type [com.youibot.vehicle.scheduler.common.config.MyMetaObjectHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.075 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join SqlInjector init
2025-03-13 16:27:50.077 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjSqlInjectorOnMiss' of type [com.github.yulichang.injector.MPJSqlInjector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.876 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.876 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.878 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [com.sun.proxy.$Proxy84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.879 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.880 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [com.sun.proxy.$Proxy85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.881 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.882 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [com.sun.proxy.$Proxy86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.883 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroServiceImpl' of type [com.youibot.vehicle.scheduler.modules.security.service.impl.ShiroServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.883 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'oauth2Realm' of type [com.youibot.vehicle.scheduler.modules.security.oauth2.Oauth2Realm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.891 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sessionManager' of type [com.youibot.vehicle.scheduler.common.config.CustomSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.903 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:50.985 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$624ec5b8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:51.130 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration$$EnhancerBySpringCGLIB$$c4ea1673] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:51.139 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:51.142 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:51.146 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:51.154 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:51.202 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:51.212 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:27:51.278 [main] INFO  com.dtflys.forest.scanner.ClassPathClientScanner 153 processBeanDefinitions - [Forest] Created Forest Client Bean with name 'forestClient' and Proxy of 'com.youibot.vehicle.scheduler.modules.sim.helper.ForestClient' client interface
2025-03-13 16:27:51.598 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 108 initialize - Tomcat initialized with port(s): 6080 (http)
2025-03-13 16:27:51.607 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Initializing ProtocolHandler ["http-nio-6080"]
2025-03-13 16:27:51.608 [main] INFO  org.apache.catalina.core.StandardService 173 log - Starting service [Tomcat]
2025-03-13 16:27:51.608 [main] INFO  org.apache.catalina.core.StandardEngine 173 log - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-03-13 16:27:51.705 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring embedded WebApplicationContext
2025-03-13 16:27:51.705 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext 285 prepareWebApplicationContext - Root WebApplicationContext: initialization completed in 3738 ms
2025-03-13 16:27:52.920 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:27:56.233 [main] INFO  com.alibaba.arthas.spring.ArthasConfiguration 70 arthasAgent - Arthas agent start success.
2025-03-13 16:27:56.499 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 181 initialize - Initializing ExecutorService 'taskScheduler'
2025-03-13 16:27:56.627 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Starting ProtocolHandler ["http-nio-6080"]
2025-03-13 16:27:56.643 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 220 start - Tomcat started on port(s): 6080 (http) with context path ''
2025-03-13 16:27:56.994 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 61 logStarted - Started AdminApplication in 9.588 seconds (JVM running for 10.622)
2025-03-13 16:27:57.192 [main] INFO  org.reflections.Reflections 232 scan - Reflections took 29 ms to scan 1 urls, producing 5 keys and 15 values 
2025-03-13 16:28:02.329 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:28:11.341 [http-nio-6080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-13 16:28:11.341 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 525 initServletBean - Initializing Servlet 'dispatcherServlet'
2025-03-13 16:28:11.371 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 547 initServletBean - Completed initialization in 30 ms
2025-03-13 16:28:15.900 [http-nio-6080-exec-2] INFO  com.dtflys.forest.config.ForestConfiguration 469 setBackend - [Forest] Http Backend: okhttp3
2025-03-13 16:28:15.962 [http-nio-6080-exec-2] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	GET http://127.0.0.1:8080/fleet/map/vehicleMaps/getMaplist HTTP
	Headers: 
		User-Agent: forest/1.5.36
2025-03-13 16:28:17.997 [http-nio-6080-exec-2] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: Failed to connect to /127.0.0.1:8080
2025-03-13 16:28:22.344 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:28:42.353 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:29:02.373 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:29:22.390 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:29:42.399 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:30:02.407 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:30:22.427 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:30:42.443 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:30:46.590 [http-nio-6080-exec-8] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	GET http://127.0.0.1:8080/fleet/map/vehicleMaps/getMaplist HTTP
	Headers: 
		User-Agent: forest/1.5.36
2025-03-13 16:30:46.627 [http-nio-6080-exec-8] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 29ms
2025-03-13 16:30:50.038 [http-nio-6080-exec-6] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	GET http://127.0.0.1:8080/fleet/map/vehicleMaps/getMaplist/testlk66?isDraft=false HTTP
	Headers: 
		User-Agent: forest/1.5.36
2025-03-13 16:30:50.057 [http-nio-6080-exec-6] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 17ms
2025-03-13 16:30:50.057 [http-nio-6080-exec-6] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response Content:
	{"code":0,"msg":"success","data":null}
2025-03-13 16:30:50.428 [simRobot-register-thread--thread-1] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	POST http://127.0.0.1:8080/fleet/sim/getAvailableMarkers HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: [{"first":"testlk66","second":1}]
2025-03-13 16:30:50.539 [simRobot-register-thread--thread-1] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 110ms
2025-03-13 16:30:50.539 [simRobot-register-thread--thread-1] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response Content:
	[{"first":"testlk66","second":[{"code":"testlk66_P_2","name":null,"angle":1.6,"type":"WorkMarker","vehicleMapCode":"testlk66","isPark":0,"isAvoid":1,"networkMarkerType":1,"dockingType":null,"dockingDirection":null,"markInfos":[{"locatingCode":"testlk66","x":15.669544481277098,"y":41.75826363969484}],"params":{"text1":null,"text2":null,"text3":null,"number1":null,"number2":null,"number3":null}}]}]
2025-03-13 16:30:50.565 [simRobot-register-thread--thread-1] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:1
2025-03-13 16:30:51.157 [netty-socket-client-thread-thread-1] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 100 run - vv:null mac:2A:42:F8:C4:6C:C1,连接服务器127.0.0.1:16646！
2025-03-13 16:30:51.321 [worker-1-1] INFO  c.y.v.s.m.client.execute.impl.VehicleEventExecute 106 handleRegister - vv:3 ,注册成功channelId:88c79306, data:{"currentDateTime":1741854651273,"instructList":[],"vehicleCode":"3"}
2025-03-13 16:30:57.012 [scheduling-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 218 shutdown - Shutting down ExecutorService
2025-03-13 16:30:57.013 [scheduling-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:30:57.451 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:31:12.452 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:31:27.463 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:31:42.467 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:31:57.476 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:32:12.490 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:32:20.146 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1265 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:32:20.150 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1266 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:32:20.150 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1267 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:32:20.151 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1157 destroy - destroy
2025-03-13 16:32:20.154 [SpringContextShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 53 shutDownGracefully - Commencing graceful shutdown. Waiting for active requests to complete
2025-03-13 16:32:20.161 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1203 persisitData - destroy_b:true
2025-03-13 16:32:20.366 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 78 doShutdown - Graceful shutdown complete
2025-03-13 16:32:20.421 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 218 shutdown - Shutting down ExecutorService 'taskScheduler'
2025-03-13 16:32:20.424 [SpringContextShutdownHook] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1154 destroy - destroy:isRunning,just_return
2025-03-13 16:32:20.426 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2003 close - {dataSource-1} closing ...
2025-03-13 16:32:20.429 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2075 close - {dataSource-1} closed
2025-03-13 16:35:04.511 [background-preinit] INFO  org.hibernate.validator.internal.util.Version 21 <clinit> - HV000001: Hibernate Validator 6.1.5.Final
2025-03-13 16:35:04.519 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 55 logStarting - Starting AdminApplication on DESKTOP-4CUGQPA with PID 12284 (E:\work\idea\wk2_fleet5\youisimulation-plus\youisimulation-admin\target\classes started by admin in E:\work\idea\wk2_fleet5)
2025-03-13 16:35:04.520 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 655 logStartupProfileInfo - The following profiles are active: dev,dev-agv
2025-03-13 16:35:05.640 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroConfig' of type [com.youibot.vehicle.scheduler.modules.security.config.ShiroConfig$$EnhancerBySpringCGLIB$$2da8caef] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.762 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.767 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusConfig' of type [com.youibot.vehicle.scheduler.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$81f073a7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.775 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.780 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-join-com.github.yulichang.autoconfigure.MybatisPlusJoinProperties' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.783 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join properties config complete
2025-03-13 16:35:05.784 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.789 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjInterceptor' of type [com.github.yulichang.interceptor.MPJInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.795 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.798 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure$$EnhancerBySpringCGLIB$$5ec19dc1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.799 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure 56 dataSource - Init DruidDataSource
2025-03-13 16:35:05.846 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.849 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration' of type [com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:05.877 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'statFilter' of type [com.alibaba.druid.filter.stat.StatFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:06.441 [main] INFO  com.alibaba.druid.pool.DruidDataSource 1010 init - {dataSource-1} inited
2025-03-13 16:35:06.442 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'dataSource' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:06.448 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'myMetaObjectHandler' of type [com.youibot.vehicle.scheduler.common.config.MyMetaObjectHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:06.450 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join SqlInjector init
2025-03-13 16:35:06.452 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjSqlInjectorOnMiss' of type [com.github.yulichang.injector.MPJSqlInjector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.222 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.226 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.227 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.229 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [com.sun.proxy.$Proxy84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.230 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.231 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [com.sun.proxy.$Proxy85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.232 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.233 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [com.sun.proxy.$Proxy86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.234 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroServiceImpl' of type [com.youibot.vehicle.scheduler.modules.security.service.impl.ShiroServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.234 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'oauth2Realm' of type [com.youibot.vehicle.scheduler.modules.security.oauth2.Oauth2Realm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.241 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sessionManager' of type [com.youibot.vehicle.scheduler.common.config.CustomSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.251 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.304 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.317 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$3ad94074] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.468 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration$$EnhancerBySpringCGLIB$$9d74912f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.477 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.479 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.483 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.492 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.539 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.549 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:35:07.616 [main] INFO  com.dtflys.forest.scanner.ClassPathClientScanner 153 processBeanDefinitions - [Forest] Created Forest Client Bean with name 'forestClient' and Proxy of 'com.youibot.vehicle.scheduler.modules.sim.helper.ForestClient' client interface
2025-03-13 16:35:07.900 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 108 initialize - Tomcat initialized with port(s): 6080 (http)
2025-03-13 16:35:07.908 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Initializing ProtocolHandler ["http-nio-6080"]
2025-03-13 16:35:07.909 [main] INFO  org.apache.catalina.core.StandardService 173 log - Starting service [Tomcat]
2025-03-13 16:35:07.909 [main] INFO  org.apache.catalina.core.StandardEngine 173 log - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-03-13 16:35:08.009 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring embedded WebApplicationContext
2025-03-13 16:35:08.009 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext 285 prepareWebApplicationContext - Root WebApplicationContext: initialization completed in 3444 ms
2025-03-13 16:35:09.091 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:35:11.362 [main] INFO  com.alibaba.arthas.spring.ArthasConfiguration 70 arthasAgent - Arthas agent start success.
2025-03-13 16:35:11.577 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 181 initialize - Initializing ExecutorService 'taskScheduler'
2025-03-13 16:35:11.704 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Starting ProtocolHandler ["http-nio-6080"]
2025-03-13 16:35:11.718 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 220 start - Tomcat started on port(s): 6080 (http) with context path ''
2025-03-13 16:35:12.030 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 61 logStarted - Started AdminApplication in 7.99 seconds (JVM running for 8.837)
2025-03-13 16:35:12.222 [main] INFO  org.reflections.Reflections 232 scan - Reflections took 26 ms to scan 1 urls, producing 5 keys and 15 values 
2025-03-13 16:35:12.605 [main] INFO  com.dtflys.forest.config.ForestConfiguration 469 setBackend - [Forest] Http Backend: okhttp3
2025-03-13 16:35:12.674 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	POST http://127.0.0.1:8080/fleet/sim/checkIfAvailable HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: {"agvCode":"3","codeList":["testlk66_P_3"]}
2025-03-13 16:35:12.713 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 35ms
2025-03-13 16:35:12.713 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response Content:
	false
2025-03-13 16:35:12.718 [main] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:35:12.720 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:1
2025-03-13 16:35:13.546 [netty-socket-client-thread-thread-1] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 100 run - vv:3 mac:2A:42:F8:C4:6C:C1,连接服务器127.0.0.1:16646！
2025-03-13 16:35:13.648 [worker-1-1] INFO  c.y.v.s.m.client.execute.impl.VehicleEventExecute 106 handleRegister - vv:3 ,注册成功channelId:c5311a95, data:{"currentDateTime":1741854913601,"instructList":[{"cancel":false,"code":"DockingCharge","end":true,"executing":false,"finished":false,"first":true,"instructId":"T20250313000001_N5","move":false,"onWork":false,"parameter":{"dockingType":"Vpoint","markerCode":"testlk66_P_3","batteryCharge":40.0,"chargeTime":20},"running":true,"seq":0,"status":"Running","taskId":"T20250313000001","timestamp":"2025-03-13T16:35:13.631","uniqueCode":"fb66739e-0934-40f8-93f0-93b0bc242dae","vehicleCode":"3"}],"vehicleCode":"3"}
2025-03-13 16:35:24.816 [http-nio-6080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-13 16:35:24.816 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 525 initServletBean - Initializing Servlet 'dispatcherServlet'
2025-03-13 16:35:24.837 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 547 initServletBean - Completed initialization in 21 ms
2025-03-13 16:35:27.733 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:35:42.747 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:35:57.762 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:36:12.031 [scheduling-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 218 shutdown - Shutting down ExecutorService
2025-03-13 16:36:12.031 [scheduling-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:36:12.774 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:36:27.785 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:36:42.790 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:36:56.519 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1265 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:36:56.520 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1266 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:36:56.521 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1267 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:36:56.524 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1157 destroy - destroy
2025-03-13 16:36:56.530 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1203 persisitData - destroy_b:true
2025-03-13 16:36:56.539 [SpringContextShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 53 shutDownGracefully - Commencing graceful shutdown. Waiting for active requests to complete
2025-03-13 16:36:56.809 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 78 doShutdown - Graceful shutdown complete
2025-03-13 16:36:56.861 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 218 shutdown - Shutting down ExecutorService 'taskScheduler'
2025-03-13 16:36:56.864 [SpringContextShutdownHook] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1154 destroy - destroy:isRunning,just_return
2025-03-13 16:36:56.867 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2003 close - {dataSource-1} closing ...
2025-03-13 16:36:56.871 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2075 close - {dataSource-1} closed
2025-03-13 16:39:22.680 [background-preinit] INFO  org.hibernate.validator.internal.util.Version 21 <clinit> - HV000001: Hibernate Validator 6.1.5.Final
2025-03-13 16:39:22.694 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 55 logStarting - Starting AdminApplication on DESKTOP-4CUGQPA with PID 29884 (E:\work\idea\wk2_fleet5\youisimulation-plus\youisimulation-admin\target\classes started by admin in E:\work\idea\wk2_fleet5)
2025-03-13 16:39:22.695 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 655 logStartupProfileInfo - The following profiles are active: dev,dev-agv
2025-03-13 16:39:23.814 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroConfig' of type [com.youibot.vehicle.scheduler.modules.security.config.ShiroConfig$$EnhancerBySpringCGLIB$$aa217888] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:23.963 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:23.967 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusConfig' of type [com.youibot.vehicle.scheduler.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$fe692140] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:23.974 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:23.979 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-join-com.github.yulichang.autoconfigure.MybatisPlusJoinProperties' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:23.981 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join properties config complete
2025-03-13 16:39:23.983 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:23.988 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjInterceptor' of type [com.github.yulichang.interceptor.MPJInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:23.994 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:23.996 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure$$EnhancerBySpringCGLIB$$db3a4b5a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:23.998 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure 56 dataSource - Init DruidDataSource
2025-03-13 16:39:24.048 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:24.050 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration' of type [com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:24.070 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'statFilter' of type [com.alibaba.druid.filter.stat.StatFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:24.651 [main] INFO  com.alibaba.druid.pool.DruidDataSource 1010 init - {dataSource-1} inited
2025-03-13 16:39:24.651 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'dataSource' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:24.658 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'myMetaObjectHandler' of type [com.youibot.vehicle.scheduler.common.config.MyMetaObjectHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:24.661 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join SqlInjector init
2025-03-13 16:39:24.663 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjSqlInjectorOnMiss' of type [com.github.yulichang.injector.MPJSqlInjector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.457 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.461 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.462 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.463 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [com.sun.proxy.$Proxy84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.464 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.466 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [com.sun.proxy.$Proxy85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.466 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.468 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [com.sun.proxy.$Proxy86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.468 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroServiceImpl' of type [com.youibot.vehicle.scheduler.modules.security.service.impl.ShiroServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.469 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'oauth2Realm' of type [com.youibot.vehicle.scheduler.modules.security.oauth2.Oauth2Realm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.476 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sessionManager' of type [com.youibot.vehicle.scheduler.common.config.CustomSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.489 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.549 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.562 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$b751ee0d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.722 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration$$EnhancerBySpringCGLIB$$19ed3ec8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.730 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.734 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.737 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.748 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.795 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.803 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:39:25.874 [main] INFO  com.dtflys.forest.scanner.ClassPathClientScanner 153 processBeanDefinitions - [Forest] Created Forest Client Bean with name 'forestClient' and Proxy of 'com.youibot.vehicle.scheduler.modules.sim.helper.ForestClient' client interface
2025-03-13 16:39:26.193 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 108 initialize - Tomcat initialized with port(s): 6080 (http)
2025-03-13 16:39:26.202 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Initializing ProtocolHandler ["http-nio-6080"]
2025-03-13 16:39:26.202 [main] INFO  org.apache.catalina.core.StandardService 173 log - Starting service [Tomcat]
2025-03-13 16:39:26.202 [main] INFO  org.apache.catalina.core.StandardEngine 173 log - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-03-13 16:39:26.301 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring embedded WebApplicationContext
2025-03-13 16:39:26.302 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext 285 prepareWebApplicationContext - Root WebApplicationContext: initialization completed in 3553 ms
2025-03-13 16:39:27.525 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:39:29.876 [main] INFO  com.alibaba.arthas.spring.ArthasConfiguration 70 arthasAgent - Arthas agent start success.
2025-03-13 16:39:30.145 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 181 initialize - Initializing ExecutorService 'taskScheduler'
2025-03-13 16:39:30.268 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Starting ProtocolHandler ["http-nio-6080"]
2025-03-13 16:39:30.282 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 220 start - Tomcat started on port(s): 6080 (http) with context path ''
2025-03-13 16:39:30.627 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 61 logStarted - Started AdminApplication in 8.456 seconds (JVM running for 9.477)
2025-03-13 16:39:30.824 [main] INFO  org.reflections.Reflections 232 scan - Reflections took 27 ms to scan 1 urls, producing 5 keys and 15 values 
2025-03-13 16:39:31.283 [main] INFO  com.dtflys.forest.config.ForestConfiguration 469 setBackend - [Forest] Http Backend: okhttp3
2025-03-13 16:39:31.375 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	POST http://127.0.0.1:8080/fleet/sim/checkIfAvailable HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: {"agvCode":"3","codeList":["testlk66_P_3"]}
2025-03-13 16:39:31.424 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 43ms
2025-03-13 16:39:31.425 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response Content:
	false
2025-03-13 16:39:31.432 [main] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:39:31.434 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:1
2025-03-13 16:39:31.966 [netty-socket-client-thread-thread-1] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 100 run - vv:3 mac:2A:42:F8:C4:6C:C1,连接服务器127.0.0.1:16646！
2025-03-13 16:39:32.099 [worker-1-1] INFO  c.y.v.s.m.client.execute.impl.VehicleEventExecute 106 handleRegister - vv:3 ,注册成功channelId:18a99b67, data:{"currentDateTime":1741855172055,"instructList":[],"vehicleCode":"3"}
2025-03-13 16:39:39.747 [http-nio-6080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-13 16:39:39.747 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 525 initServletBean - Initializing Servlet 'dispatcherServlet'
2025-03-13 16:39:39.775 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 547 initServletBean - Completed initialization in 28 ms
2025-03-13 16:39:46.445 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:40:01.454 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:40:16.464 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:40:30.641 [scheduling-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 218 shutdown - Shutting down ExecutorService
2025-03-13 16:40:30.641 [scheduling-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:40:31.474 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:40:46.487 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:41:01.494 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:41:16.510 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:41:28.937 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1265 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:41:28.938 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1266 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:41:28.938 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1267 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:41:28.938 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1157 destroy - destroy
2025-03-13 16:41:28.950 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1203 persisitData - destroy_b:true
2025-03-13 16:41:28.959 [SpringContextShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 53 shutDownGracefully - Commencing graceful shutdown. Waiting for active requests to complete
2025-03-13 16:41:29.183 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 78 doShutdown - Graceful shutdown complete
2025-03-13 16:41:29.234 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 218 shutdown - Shutting down ExecutorService 'taskScheduler'
2025-03-13 16:41:29.237 [SpringContextShutdownHook] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1154 destroy - destroy:isRunning,just_return
2025-03-13 16:41:29.239 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2003 close - {dataSource-1} closing ...
2025-03-13 16:41:29.242 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2075 close - {dataSource-1} closed
2025-03-13 16:42:06.526 [background-preinit] INFO  org.hibernate.validator.internal.util.Version 21 <clinit> - HV000001: Hibernate Validator 6.1.5.Final
2025-03-13 16:42:06.539 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 55 logStarting - Starting AdminApplication on DESKTOP-4CUGQPA with PID 21056 (E:\work\idea\wk2_fleet5\youisimulation-plus\youisimulation-admin\target\classes started by admin in E:\work\idea\wk2_fleet5)
2025-03-13 16:42:06.540 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 655 logStartupProfileInfo - The following profiles are active: dev,dev-agv
2025-03-13 16:42:07.515 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroConfig' of type [com.youibot.vehicle.scheduler.modules.security.config.ShiroConfig$$EnhancerBySpringCGLIB$$2da8caef] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.634 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.637 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusConfig' of type [com.youibot.vehicle.scheduler.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$81f073a7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.645 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.650 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-join-com.github.yulichang.autoconfigure.MybatisPlusJoinProperties' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.652 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join properties config complete
2025-03-13 16:42:07.654 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.659 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjInterceptor' of type [com.github.yulichang.interceptor.MPJInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.665 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.667 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure$$EnhancerBySpringCGLIB$$5ec19dc1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.668 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure 56 dataSource - Init DruidDataSource
2025-03-13 16:42:07.714 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.716 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration' of type [com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:07.735 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'statFilter' of type [com.alibaba.druid.filter.stat.StatFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:08.295 [main] INFO  com.alibaba.druid.pool.DruidDataSource 1010 init - {dataSource-1} inited
2025-03-13 16:42:08.296 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'dataSource' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:08.301 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'myMetaObjectHandler' of type [com.youibot.vehicle.scheduler.common.config.MyMetaObjectHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:08.304 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join SqlInjector init
2025-03-13 16:42:08.306 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjSqlInjectorOnMiss' of type [com.github.yulichang.injector.MPJSqlInjector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.049 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.052 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.052 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.054 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [com.sun.proxy.$Proxy84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.055 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.056 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [com.sun.proxy.$Proxy85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.057 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.058 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [com.sun.proxy.$Proxy86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.059 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroServiceImpl' of type [com.youibot.vehicle.scheduler.modules.security.service.impl.ShiroServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.059 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'oauth2Realm' of type [com.youibot.vehicle.scheduler.modules.security.oauth2.Oauth2Realm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.064 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sessionManager' of type [com.youibot.vehicle.scheduler.common.config.CustomSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.075 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.124 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.136 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$3ad94074] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.265 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration$$EnhancerBySpringCGLIB$$9d74912f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.274 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.276 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.279 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.297 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.339 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.347 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:42:09.411 [main] INFO  com.dtflys.forest.scanner.ClassPathClientScanner 153 processBeanDefinitions - [Forest] Created Forest Client Bean with name 'forestClient' and Proxy of 'com.youibot.vehicle.scheduler.modules.sim.helper.ForestClient' client interface
2025-03-13 16:42:09.669 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 108 initialize - Tomcat initialized with port(s): 6080 (http)
2025-03-13 16:42:09.676 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Initializing ProtocolHandler ["http-nio-6080"]
2025-03-13 16:42:09.677 [main] INFO  org.apache.catalina.core.StandardService 173 log - Starting service [Tomcat]
2025-03-13 16:42:09.677 [main] INFO  org.apache.catalina.core.StandardEngine 173 log - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-03-13 16:42:09.765 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring embedded WebApplicationContext
2025-03-13 16:42:09.765 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext 285 prepareWebApplicationContext - Root WebApplicationContext: initialization completed in 3181 ms
2025-03-13 16:42:10.820 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:42:13.360 [main] INFO  com.alibaba.arthas.spring.ArthasConfiguration 70 arthasAgent - Arthas agent start success.
2025-03-13 16:42:13.588 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 181 initialize - Initializing ExecutorService 'taskScheduler'
2025-03-13 16:42:13.699 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Starting ProtocolHandler ["http-nio-6080"]
2025-03-13 16:42:13.712 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 220 start - Tomcat started on port(s): 6080 (http) with context path ''
2025-03-13 16:42:14.072 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 61 logStarted - Started AdminApplication in 7.98 seconds (JVM running for 8.825)
2025-03-13 16:42:14.275 [http-nio-6080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-13 16:42:14.275 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 525 initServletBean - Initializing Servlet 'dispatcherServlet'
2025-03-13 16:42:14.320 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 547 initServletBean - Completed initialization in 44 ms
2025-03-13 16:42:14.373 [main] INFO  org.reflections.Reflections 232 scan - Reflections took 46 ms to scan 1 urls, producing 5 keys and 15 values 
2025-03-13 16:42:14.897 [main] INFO  com.dtflys.forest.config.ForestConfiguration 469 setBackend - [Forest] Http Backend: okhttp3
2025-03-13 16:42:14.964 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	POST http://127.0.0.1:8080/fleet/sim/checkIfAvailable HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: {"agvCode":"3","codeList":["testlk66_P_3"]}
2025-03-13 16:42:18.014 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:18.016 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 1
	POST http://127.0.0.1:8080/fleet/sim/checkIfAvailable HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: {"agvCode":"3","codeList":["testlk66_P_3"]}
2025-03-13 16:42:22.064 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:22.064 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 2
	POST http://127.0.0.1:8080/fleet/sim/checkIfAvailable HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: {"agvCode":"3","codeList":["testlk66_P_3"]}
2025-03-13 16:42:22.537 [http-nio-6080-exec-2] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1203 persisitData - destroy_b:true
2025-03-13 16:42:22.552 [ForkJoinPool.commonPool-worker-9] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	DELETE http://127.0.0.1:8080/fleet/vehicle//vehicles?force=true HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: ["3"]
2025-03-13 16:42:25.575 [ForkJoinPool.commonPool-worker-9] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:25.576 [ForkJoinPool.commonPool-worker-9] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 1
	DELETE http://127.0.0.1:8080/fleet/vehicle//vehicles?force=true HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: ["3"]
2025-03-13 16:42:28.094 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:28.095 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 3
	POST http://127.0.0.1:8080/fleet/sim/checkIfAvailable HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: {"agvCode":"3","codeList":["testlk66_P_3"]}
2025-03-13 16:42:29.604 [ForkJoinPool.commonPool-worker-9] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:29.605 [ForkJoinPool.commonPool-worker-9] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 2
	DELETE http://127.0.0.1:8080/fleet/vehicle//vehicles?force=true HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: ["3"]
2025-03-13 16:42:30.106 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: [Forest] retry count: 3, cause: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:30.111 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	POST http://127.0.0.1:8080/fleet/sim/getAvailableMarkers HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: [{"first":"testlk66","second":1}]
2025-03-13 16:42:33.137 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:33.137 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 1
	POST http://127.0.0.1:8080/fleet/sim/getAvailableMarkers HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: [{"first":"testlk66","second":1}]
2025-03-13 16:42:35.644 [ForkJoinPool.commonPool-worker-9] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:35.644 [ForkJoinPool.commonPool-worker-9] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 3
	DELETE http://127.0.0.1:8080/fleet/vehicle//vehicles?force=true HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: ["3"]
2025-03-13 16:42:37.168 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:37.168 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 2
	POST http://127.0.0.1:8080/fleet/sim/getAvailableMarkers HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: [{"first":"testlk66","second":1}]
2025-03-13 16:42:37.657 [ForkJoinPool.commonPool-worker-9] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: [Forest] retry count: 3, cause: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:43.189 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:43.189 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 3
	POST http://127.0.0.1:8080/fleet/sim/getAvailableMarkers HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: [{"first":"testlk66","second":1}]
2025-03-13 16:42:45.206 [main] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: [Network Error]: [Forest] retry count: 3, cause: Failed to connect to /127.0.0.1:8080
2025-03-13 16:42:45.214 [main] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:1
2025-03-13 16:42:45.217 [netty-socket-client-thread-thread-1] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 100 run - vv:3 mac:2A:42:F8:C4:6C:C1,连接服务器127.0.0.1:16646！
2025-03-13 16:42:50.225 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:43:10.241 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:43:30.246 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:43:30.369 [http-nio-6080-exec-8] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	GET http://10.0.60.13:8080/fleet/map/vehicleMaps/getMaplist HTTP
	Headers: 
		User-Agent: forest/1.5.36
2025-03-13 16:43:30.499 [http-nio-6080-exec-8] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 128ms
2025-03-13 16:43:50.271 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:44:10.282 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:44:30.294 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:44:50.308 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:45:10.327 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:45:30.339 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:45:50.346 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:46:10.365 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:46:30.389 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:46:50.406 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:47:10.425 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:47:30.440 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:47:50.452 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:48:10.470 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:48:30.482 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:48:50.495 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:49:10.508 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:49:30.518 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:49:50.536 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:50:10.553 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:50:30.585 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:50:50.604 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:51:10.622 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:51:30.662 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:51:50.675 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:52:10.680 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:52:30.692 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:52:50.695 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:53:10.709 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:53:30.731 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:53:50.752 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:54:10.753 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:54:30.775 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:54:51.025 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:55:11.037 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:55:31.052 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:55:51.064 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:56:00.128 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1265 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:56:00.128 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1266 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:56:00.129 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1267 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 16:56:00.129 [Thread-10] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1157 destroy - destroy
2025-03-13 16:56:00.133 [SpringContextShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 53 shutDownGracefully - Commencing graceful shutdown. Waiting for active requests to complete
2025-03-13 16:56:54.147 [background-preinit] INFO  org.hibernate.validator.internal.util.Version 21 <clinit> - HV000001: Hibernate Validator 6.1.5.Final
2025-03-13 16:56:54.152 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 55 logStarting - Starting AdminApplication on DESKTOP-4CUGQPA with PID 27384 (E:\work\idea\wk2_fleet5\youisimulation-plus\youisimulation-admin\target\classes started by admin in E:\work\idea\wk2_fleet5)
2025-03-13 16:56:54.152 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 655 logStartupProfileInfo - The following profiles are active: dev,dev-agv
2025-03-13 16:56:55.142 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroConfig' of type [com.youibot.vehicle.scheduler.modules.security.config.ShiroConfig$$EnhancerBySpringCGLIB$$2da8caef] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.270 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.273 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusConfig' of type [com.youibot.vehicle.scheduler.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$81f073a7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.281 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.286 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-join-com.github.yulichang.autoconfigure.MybatisPlusJoinProperties' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.288 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join properties config complete
2025-03-13 16:56:55.290 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.295 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjInterceptor' of type [com.github.yulichang.interceptor.MPJInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.303 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.305 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure$$EnhancerBySpringCGLIB$$5ec19dc1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.307 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure 56 dataSource - Init DruidDataSource
2025-03-13 16:56:55.355 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.358 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration' of type [com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.379 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'statFilter' of type [com.alibaba.druid.filter.stat.StatFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.963 [main] INFO  com.alibaba.druid.pool.DruidDataSource 1010 init - {dataSource-1} inited
2025-03-13 16:56:55.963 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'dataSource' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.970 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'myMetaObjectHandler' of type [com.youibot.vehicle.scheduler.common.config.MyMetaObjectHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:55.972 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join SqlInjector init
2025-03-13 16:56:55.974 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjSqlInjectorOnMiss' of type [com.github.yulichang.injector.MPJSqlInjector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.728 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.732 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.733 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.735 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [com.sun.proxy.$Proxy84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.736 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.737 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [com.sun.proxy.$Proxy85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.738 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.739 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [com.sun.proxy.$Proxy86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.740 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroServiceImpl' of type [com.youibot.vehicle.scheduler.modules.security.service.impl.ShiroServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.740 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'oauth2Realm' of type [com.youibot.vehicle.scheduler.modules.security.oauth2.Oauth2Realm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.747 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sessionManager' of type [com.youibot.vehicle.scheduler.common.config.CustomSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.759 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.809 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.822 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$3ad94074] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.963 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration$$EnhancerBySpringCGLIB$$9d74912f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.976 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.980 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:56.991 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:57.047 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:57.056 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 16:56:57.119 [main] INFO  com.dtflys.forest.scanner.ClassPathClientScanner 153 processBeanDefinitions - [Forest] Created Forest Client Bean with name 'forestClient' and Proxy of 'com.youibot.vehicle.scheduler.modules.sim.helper.ForestClient' client interface
2025-03-13 16:56:57.379 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 108 initialize - Tomcat initialized with port(s): 6080 (http)
2025-03-13 16:56:57.386 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Initializing ProtocolHandler ["http-nio-6080"]
2025-03-13 16:56:57.386 [main] INFO  org.apache.catalina.core.StandardService 173 log - Starting service [Tomcat]
2025-03-13 16:56:57.387 [main] INFO  org.apache.catalina.core.StandardEngine 173 log - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-03-13 16:56:57.480 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring embedded WebApplicationContext
2025-03-13 16:56:57.481 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext 285 prepareWebApplicationContext - Root WebApplicationContext: initialization completed in 3286 ms
2025-03-13 16:56:58.549 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:57:01.090 [main] INFO  com.alibaba.arthas.spring.ArthasConfiguration 70 arthasAgent - Arthas agent start success.
2025-03-13 16:57:01.327 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 181 initialize - Initializing ExecutorService 'taskScheduler'
2025-03-13 16:57:01.453 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Starting ProtocolHandler ["http-nio-6080"]
2025-03-13 16:57:01.467 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 220 start - Tomcat started on port(s): 6080 (http) with context path ''
2025-03-13 16:57:01.773 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 61 logStarted - Started AdminApplication in 8.076 seconds (JVM running for 8.914)
2025-03-13 16:57:01.957 [main] INFO  org.reflections.Reflections 232 scan - Reflections took 26 ms to scan 1 urls, producing 5 keys and 15 values 
2025-03-13 16:57:07.093 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:57:27.112 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:57:47.126 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:57:54.098 [http-nio-6080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-13 16:57:54.099 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 525 initServletBean - Initializing Servlet 'dispatcherServlet'
2025-03-13 16:57:54.126 [http-nio-6080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet 547 initServletBean - Completed initialization in 26 ms
2025-03-13 16:58:07.142 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:58:27.163 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:58:47.168 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:58:51.376 [http-nio-6080-exec-10] INFO  com.dtflys.forest.config.ForestConfiguration 469 setBackend - [Forest] Http Backend: okhttp3
2025-03-13 16:58:51.430 [http-nio-6080-exec-10] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	GET http://127.0.0.1:8080/fleet/map/vehicleMaps/getMaplist HTTP
	Headers: 
		User-Agent: forest/1.5.36
2025-03-13 16:58:51.463 [http-nio-6080-exec-10] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 30ms
2025-03-13 16:58:55.862 [http-nio-6080-exec-8] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	GET http://127.0.0.1:8080/fleet/map/vehicleMaps/getMaplist/AT100FLEETTEST2?isDraft=false HTTP
	Headers: 
		User-Agent: forest/1.5.36
2025-03-13 16:58:55.873 [http-nio-6080-exec-8] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 11ms
2025-03-13 16:58:55.874 [http-nio-6080-exec-8] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response Content:
	{"code":0,"msg":"success","data":null}
2025-03-13 16:58:56.168 [simRobot-register-thread--thread-1] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	POST http://127.0.0.1:8080/fleet/sim/getAvailableMarkers HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: [{"first":"AT100FLEETTEST2","second":1}]
2025-03-13 16:58:56.205 [simRobot-register-thread--thread-1] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 34ms
2025-03-13 16:58:56.205 [simRobot-register-thread--thread-1] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response Content:
	[{"first":"AT100FLEETTEST2","second":[{"code":"AT100FLEETTEST2_P_5","name":null,"angle":0.0,"type":"NavigationMarker","vehicleMapCode":"AT100FLEETTEST2","isPark":0,"isAvoid":1,"networkMarkerType":0,"dockingType":null,"dockingDirection":null,"markInfos":[{"locatingCode":"AT100FLEETTEST2","x":32.49845043169347,"y":32.3478}],"params":{"text1":null,"text2":null,"text3":null,"number1":null,"number2":null,"number3":null}}]}]
2025-03-13 16:58:56.221 [simRobot-register-thread--thread-1] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:1
2025-03-13 16:58:56.727 [netty-socket-client-thread-thread-1] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 100 run - vv:null mac:E2:06:E0:41:8D:E2,连接服务器127.0.0.1:16646！
2025-03-13 16:58:56.845 [worker-1-1] INFO  c.y.v.s.m.client.execute.impl.VehicleEventExecute 106 handleRegister - vv:4 ,注册成功channelId:1f9bda49, data:{"currentDateTime":1741856336810,"instructList":[],"vehicleCode":"4"}
2025-03-13 16:59:01.784 [scheduling-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 218 shutdown - Shutting down ExecutorService
2025-03-13 16:59:01.785 [scheduling-1] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 16:59:02.180 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:59:16.456 [http-nio-6080-exec-13] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1203 persisitData - destroy_b:true
2025-03-13 16:59:16.477 [ForkJoinPool.commonPool-worker-4] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	DELETE http://127.0.0.1:8080/fleet/vehicle//vehicles?force=true HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: ["4"]
2025-03-13 16:59:16.509 [ForkJoinPool.commonPool-worker-4] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 404, Time = 32ms
2025-03-13 16:59:17.514 [ForkJoinPool.commonPool-worker-4] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 1
	DELETE http://127.0.0.1:8080/fleet/vehicle//vehicles?force=true HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: ["4"]
2025-03-13 16:59:17.532 [ForkJoinPool.commonPool-worker-4] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 404, Time = 18ms
2025-03-13 16:59:19.536 [ForkJoinPool.commonPool-worker-4] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 2
	DELETE http://127.0.0.1:8080/fleet/vehicle//vehicles?force=true HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: ["4"]
2025-03-13 16:59:19.555 [ForkJoinPool.commonPool-worker-4] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 404, Time = 19ms
2025-03-13 16:59:22.200 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 16:59:23.570 [ForkJoinPool.commonPool-worker-4] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	[Retry]: 3
	DELETE http://127.0.0.1:8080/fleet/vehicle//vehicles?force=true HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: ["4"]
2025-03-13 16:59:23.590 [ForkJoinPool.commonPool-worker-4] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 404, Time = 20ms
2025-03-13 16:59:42.212 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:00:02.222 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:00:22.238 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:00:42.241 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:01:02.257 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:01:22.272 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:01:42.394 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:02:02.399 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:02:22.417 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:02:42.422 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:03:02.427 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:03:22.446 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:03:42.527 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:04:02.538 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:04:22.557 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:04:42.572 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:04:43.926 [http-nio-6080-exec-15] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	GET http://127.0.0.1:8080/fleet/map/vehicleMaps/getMaplist HTTP
	Headers: 
		User-Agent: forest/1.5.36
2025-03-13 17:04:43.938 [http-nio-6080-exec-15] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 10ms
2025-03-13 17:04:48.884 [http-nio-6080-exec-21] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	GET http://127.0.0.1:8080/fleet/map/vehicleMaps/getMaplist/test88?isDraft=false HTTP
	Headers: 
		User-Agent: forest/1.5.36
2025-03-13 17:04:48.894 [http-nio-6080-exec-21] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 9ms
2025-03-13 17:04:48.895 [http-nio-6080-exec-21] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response Content:
	{"code":0,"msg":"success","data":null}
2025-03-13 17:04:48.957 [simRobot-register-thread--thread-2] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	POST http://127.0.0.1:8080/fleet/sim/getAvailableMarkers HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: [{"first":"test88","second":1}]
2025-03-13 17:04:48.989 [simRobot-register-thread--thread-2] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 31ms
2025-03-13 17:04:48.989 [simRobot-register-thread--thread-2] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response Content:
	[{"first":"test88","second":[]}]
2025-03-13 17:04:48.990 [simRobot-register-thread--thread-2] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:1
2025-03-13 17:04:48.996 [netty-socket-client-thread-thread-2] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 100 run - vv:null mac:B0:3A:D5:02:19:2D,连接服务器127.0.0.1:16646！
2025-03-13 17:04:49.134 [worker-1-2] INFO  c.y.v.s.m.client.execute.impl.VehicleEventExecute 106 handleRegister - vv:5 ,注册成功channelId:19131b12, data:{"currentDateTime":1741856689101,"instructList":[],"vehicleCode":"5"}
2025-03-13 17:04:57.585 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:05:12.188 [http-nio-6080-exec-25] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1203 persisitData - destroy_b:true
2025-03-13 17:05:12.200 [ForkJoinPool.commonPool-worker-9] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Request (okhttp3): 
	DELETE http://127.0.0.1:8080/fleet/vehicle//vehicles?force=true HTTP
	Headers: 
		User-Agent: forest/1.5.36
		Content-Type: application/json
	Body: ["5"]
2025-03-13 17:05:12.249 [ForkJoinPool.commonPool-worker-9] INFO  com.dtflys.forest.logging.DefaultLogHandler 21 info - [Forest] Response: Status = 200, Time = 49ms
2025-03-13 17:05:17.601 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:05:37.618 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:05:57.629 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:06:17.639 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:06:37.669 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:06:57.685 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:07:17.702 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:07:37.711 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:07:57.715 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:08:17.738 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:08:37.745 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:08:57.752 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:09:17.761 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:09:37.775 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:09:57.788 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:10:17.807 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:10:37.822 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:10:57.841 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:11:17.867 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:11:37.872 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:11:57.887 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:12:17.910 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:12:37.931 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:12:57.940 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:13:17.953 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:13:37.961 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:13:57.976 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:14:18.003 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:14:38.013 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:14:58.037 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:15:18.059 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:15:38.077 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:15:58.094 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:16:18.107 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:16:38.122 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:16:58.149 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:17:18.171 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:17:38.183 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:17:58.202 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:18:18.206 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:18:38.225 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:18:58.228 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:19:18.238 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:19:38.252 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:19:58.272 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:20:18.285 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:20:38.296 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:20:58.307 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:21:18.330 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:21:38.358 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:21:58.382 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:22:18.389 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:22:38.398 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:22:58.411 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:23:18.412 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:23:38.425 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:23:58.440 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:24:18.459 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:24:38.463 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:24:58.476 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:25:18.485 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:25:38.506 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:25:58.520 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:26:18.534 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:26:38.544 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:26:58.565 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:27:18.592 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:27:38.603 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:27:58.628 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:28:18.640 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:28:38.661 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:28:58.676 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:29:18.691 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:29:38.709 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:29:58.725 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:30:18.738 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:30:38.754 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:30:58.774 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:30:59.945 [Thread-9] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1265 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 17:30:59.946 [Thread-9] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1266 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 17:30:59.947 [Thread-9] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1267 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 17:30:59.947 [Thread-9] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1157 destroy - destroy
2025-03-13 17:30:59.952 [SpringContextShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 53 shutDownGracefully - Commencing graceful shutdown. Waiting for active requests to complete
2025-03-13 17:31:00.237 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 78 doShutdown - Graceful shutdown complete
2025-03-13 17:31:00.285 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 218 shutdown - Shutting down ExecutorService 'taskScheduler'
2025-03-13 17:31:00.289 [SpringContextShutdownHook] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1154 destroy - destroy:isRunning,just_return
2025-03-13 17:31:00.291 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2003 close - {dataSource-1} closing ...
2025-03-13 17:31:00.295 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2075 close - {dataSource-1} closed
2025-03-13 17:31:07.385 [background-preinit] INFO  org.hibernate.validator.internal.util.Version 21 <clinit> - HV000001: Hibernate Validator 6.1.5.Final
2025-03-13 17:31:07.399 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 55 logStarting - Starting AdminApplication on DESKTOP-4CUGQPA with PID 28748 (E:\work\idea\wk2_fleet5\youisimulation-plus\youisimulation-admin\target\classes started by admin in E:\work\idea\wk2_fleet5)
2025-03-13 17:31:07.400 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 655 logStartupProfileInfo - The following profiles are active: dev,dev-agv
2025-03-13 17:31:08.604 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroConfig' of type [com.youibot.vehicle.scheduler.modules.security.config.ShiroConfig$$EnhancerBySpringCGLIB$$8e1300d4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.754 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.757 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusConfig' of type [com.youibot.vehicle.scheduler.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$e25aa98c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.765 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatisPlusInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.769 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mybatis-plus-join-com.github.yulichang.autoconfigure.MybatisPlusJoinProperties' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.773 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join properties config complete
2025-03-13 17:31:08.774 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration' of type [com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.779 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjInterceptor' of type [com.github.yulichang.interceptor.MPJInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.785 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.787 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure$$EnhancerBySpringCGLIB$$bf2bd3a6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.789 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure 56 dataSource - Init DruidDataSource
2025-03-13 17:31:08.839 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.842 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration' of type [com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:08.864 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'statFilter' of type [com.alibaba.druid.filter.stat.StatFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:09.470 [main] INFO  com.alibaba.druid.pool.DruidDataSource 1010 init - {dataSource-1} inited
2025-03-13 17:31:09.470 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'dataSource' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:09.478 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'myMetaObjectHandler' of type [com.youibot.vehicle.scheduler.common.config.MyMetaObjectHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:09.481 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration 139 info - mybatis plus join SqlInjector init
2025-03-13 17:31:09.482 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'mpjSqlInjectorOnMiss' of type [com.github.yulichang.injector.MPJSqlInjector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.310 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.313 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.314 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.316 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysMenuDao' of type [com.sun.proxy.$Proxy84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.317 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.318 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserDao' of type [com.sun.proxy.$Proxy85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.320 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.320 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sysUserTokenDao' of type [com.sun.proxy.$Proxy86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.322 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'shiroServiceImpl' of type [com.youibot.vehicle.scheduler.modules.security.service.impl.ShiroServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.323 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'oauth2Realm' of type [com.youibot.vehicle.scheduler.modules.security.oauth2.Oauth2Realm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.330 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'sessionManager' of type [com.youibot.vehicle.scheduler.common.config.CustomSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.343 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.407 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.421 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$9b437659] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.600 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration$$EnhancerBySpringCGLIB$$fddec714] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.612 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.617 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.621 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.633 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.683 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.694 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker 335 postProcessAfterInitialization - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 17:31:10.773 [main] INFO  com.dtflys.forest.scanner.ClassPathClientScanner 153 processBeanDefinitions - [Forest] Created Forest Client Bean with name 'forestClient' and Proxy of 'com.youibot.vehicle.scheduler.modules.sim.helper.ForestClient' client interface
2025-03-13 17:31:11.104 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 108 initialize - Tomcat initialized with port(s): 6080 (http)
2025-03-13 17:31:11.113 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Initializing ProtocolHandler ["http-nio-6080"]
2025-03-13 17:31:11.113 [main] INFO  org.apache.catalina.core.StandardService 173 log - Starting service [Tomcat]
2025-03-13 17:31:11.113 [main] INFO  org.apache.catalina.core.StandardEngine 173 log - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-03-13 17:31:11.218 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] 173 log - Initializing Spring embedded WebApplicationContext
2025-03-13 17:31:11.219 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext 285 prepareWebApplicationContext - Root WebApplicationContext: initialization completed in 3765 ms
2025-03-13 17:31:12.532 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor 181 initialize - Initializing ExecutorService
2025-03-13 17:31:15.049 [main] INFO  com.alibaba.arthas.spring.ArthasConfiguration 70 arthasAgent - Arthas agent start success.
2025-03-13 17:31:15.376 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 181 initialize - Initializing ExecutorService 'taskScheduler'
2025-03-13 17:31:15.518 [main] INFO  org.apache.coyote.http11.Http11NioProtocol 173 log - Starting ProtocolHandler ["http-nio-6080"]
2025-03-13 17:31:15.540 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer 220 start - Tomcat started on port(s): 6080 (http) with context path ''
2025-03-13 17:31:15.911 [main] INFO  com.youibot.vehicle.scheduler.AdminApplication 61 logStarted - Started AdminApplication in 9.09 seconds (JVM running for 10.237)
2025-03-13 17:31:16.119 [main] INFO  org.reflections.Reflections 232 scan - Reflections took 28 ms to scan 1 urls, producing 5 keys and 15 values 
2025-03-13 17:31:21.287 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:31:41.295 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:32:01.314 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:32:21.328 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:32:41.342 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:33:01.358 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:33:21.377 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:33:41.387 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:34:01.409 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:34:21.421 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:34:41.431 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:35:01.440 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:35:21.465 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:35:41.484 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:36:01.488 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:36:21.511 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:36:41.527 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:37:01.551 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:37:21.566 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:37:41.587 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:38:01.604 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:38:21.610 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:38:41.632 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:39:01.647 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:39:21.675 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:39:41.693 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:40:01.705 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:40:21.717 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:40:41.733 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:41:01.738 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:41:21.755 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:41:41.760 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:42:01.780 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:42:21.799 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:42:41.822 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:43:01.838 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:43:21.855 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:43:41.859 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:44:01.871 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:44:21.884 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:44:41.887 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:45:01.913 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:45:21.932 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:45:41.935 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:46:01.942 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:46:21.962 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:46:41.983 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:47:02.004 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:47:22.011 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:47:42.024 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:48:02.046 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:48:22.057 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:48:42.083 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:49:02.104 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:49:22.113 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:49:42.124 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:50:02.140 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:50:22.163 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:50:42.179 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:51:02.205 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:51:22.228 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:51:42.239 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:52:02.249 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:52:22.257 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:52:42.270 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:53:02.278 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:53:22.293 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:53:42.316 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:54:02.338 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:54:22.356 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:54:42.364 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:55:02.381 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:55:22.395 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:55:42.413 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:56:02.433 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:56:22.449 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:56:42.452 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:57:02.469 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:57:22.479 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:57:42.492 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:58:02.503 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:58:22.522 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:58:42.534 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:59:02.560 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:59:22.569 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 17:59:42.580 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 18:00:02.586 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 18:00:22.615 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 18:00:42.626 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 18:01:02.643 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 18:01:22.653 [netty-socket-client-thread] INFO  c.y.v.scheduler.runnable.NettySocketClientThread 72 submit - netty_connect_submit_size:0
2025-03-13 18:01:31.293 [Thread-11] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1265 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 18:01:31.294 [Thread-11] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1266 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 18:01:31.296 [Thread-11] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1267 lambda$init2$28 - Shutdown_hook_triggered
2025-03-13 18:01:31.296 [Thread-11] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1157 destroy - destroy
2025-03-13 18:01:31.303 [SpringContextShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 53 shutDownGracefully - Commencing graceful shutdown. Waiting for active requests to complete
2025-03-13 18:01:31.676 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown 78 doShutdown - Graceful shutdown complete
2025-03-13 18:01:31.727 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler 218 shutdown - Shutting down ExecutorService 'taskScheduler'
2025-03-13 18:01:31.731 [SpringContextShutdownHook] INFO  c.y.v.s.m.sim.service.impl.SimRobotServiceImpl 1154 destroy - destroy:isRunning,just_return
2025-03-13 18:01:31.733 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2003 close - {dataSource-1} closing ...
2025-03-13 18:01:31.737 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource 2075 close - {dataSource-1} closed
