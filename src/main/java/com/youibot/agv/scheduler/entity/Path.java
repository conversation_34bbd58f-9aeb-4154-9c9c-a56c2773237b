package com.youibot.agv.scheduler.entity;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import static com.youibot.agv.scheduler.constant.MapConstant.PATH_ALLOW_STOP_NAVIGATION;

/**
 * <AUTHOR>  E-mail:<EMAIL>
 * @version CreateTime: 2019/10/17 11:23
 */
@Data
@ApiModel(value = "Path", description = "路径")
public class Path {

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "地图ID", position = 1)
    private String agvMapName;

    @ApiModelProperty(value = "开始标记点", position = 2)
    private String startMarkerId;

    @ApiModelProperty(value = "结束标记点", position = 3)
    private String endMarkerId;

    @ApiModelProperty(value = "开始点的控制点xy坐标", position = 4)
    private String startControl;

    @ApiModelProperty(value = "结束点的控制点xy坐标", position = 5)
    private String endControl;

    @ApiModelProperty(value = "长度", position = 6)
    private Double length;

    @ApiModelProperty(value = "线类型 1、直线 2、曲线（只要不为1,目前都默认为曲线）", position = 7)
    private Integer lineType;

    @ApiModelProperty(value = "方向 0、双向 1、正向 2、反向", position = 8)
    private Integer direction;

    @ApiModelProperty(value = "正向行驶时agv方向 1、正向 2、反向", position = 9)
    private Integer forwardAgvDirection;

    @ApiModelProperty(value = "反向行驶时agv方向 1、正向 2、反向", position = 10)
    private Integer reverseAgvDirection;

    @ApiModelProperty(value = "使用状态 ENABLE:启用  DISABLE:禁用", position = 11)
    private String usageStatus;

    @ApiModelProperty(value = "自动门ID", position = 12)
    private String autoDoorId;

    @ApiModelProperty(value = "自动门类型", position = 13)
    private String autoDoorType;

    @ApiModelProperty(value = "是否可路径上停止路径导航  1、允许  2、禁止", position = 16)
    private Integer allowStopNavigation = PATH_ALLOW_STOP_NAVIGATION;

    public void setStartControl(String startControl) {
        JSONObject jsonObject = JSONObject.parseObject(startControl);
        this.startControl = jsonObject.toJSONString();
    }

    public String getStartControl() {
        return this.startControl;
    }

    public void setEndControl(String endControl) {
        JSONObject jsonObject = JSONObject.parseObject(endControl);
        this.endControl = jsonObject.toJSONString();
    }

}
