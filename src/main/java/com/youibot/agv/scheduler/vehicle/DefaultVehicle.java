package com.youibot.agv.scheduler.vehicle;

import com.youibot.agv.scheduler.constant.MapConstant;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.mqtt.bean.push.cmd.RelocationMessage;
import com.youibot.agv.scheduler.mqtt.service.MapCommandService;
import com.youibot.agv.scheduler.mqtt.service.VehicleCommandService;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.WorkSchedulerService;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import static com.youibot.agv.scheduler.constant.VehicleConstant.*;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 21:07
 */
@Component
@Scope("prototype")
public class DefaultVehicle extends Vehicle {

    private final static Logger LOGGER = LoggerFactory.getLogger(DefaultVehicle.class);

    @Autowired
    public VehicleCommandService vehicleCommandService;

    @Autowired
    public MapCommandService mapCommandService;

    @Autowired
    public AGVMapService agvMapService;

    @Autowired
    public WorkSchedulerService workSchedulerService;

    @Override
    public void initialize(Agv agv) {
        super.initialize(agv);
        this.initialized = true;
    }

    // get battery value.
    public Double getBatteryValue() {
        if (this.getDefaultVehicleStatus() != null && this.getDefaultVehicleStatus().getBattery() != null) {
            return this.getDefaultVehicleStatus().getBattery().getBattery_value();
        }
        return null;
    }

    @Override
    public void pauseMissionWork(String missionWorkId) {
        try {
            vehicleCommandService.pauseMissionWork(missionWorkId, this.id);
        } catch (Exception e) {
            LOGGER.error("暂停指令失败, ", e);
            throw new ExecuteException(MessageUtils.getMessage("vehicle.pause_mission_fault") + " " + e.getMessage());
        }
    }

    @Override
    public void resumeMissionWork(String missionWorkId) {
        try {
            vehicleCommandService.resumeMissionWork(missionWorkId, this.id);
            //点击恢复以后将workScheduler的异常信息清理掉
            WorkScheduler workScheduler = workSchedulerService.selectPrepareAndRunningByVehicleId(this.id);
            workScheduler.setFaultMessage(null);
            workSchedulerService.update(workScheduler);
        } catch (Exception e) {
            LOGGER.error("恢复指令失败, ", e);
            throw new ExecuteException(MessageUtils.getMessage("vehicle.resume_mission_fault") + " " + e.getMessage());
        }
    }

    @Override
    public void continueMissionWork(String missionWorkId) {
        try {
            vehicleCommandService.continueMissionWork(missionWorkId, this.id);
        } catch (Exception e) {
            LOGGER.error("继续指令失败, ", e);
            throw new ExecuteException(MessageUtils.getMessage("vehicle.continue_mission_fault") + " " + e.getMessage());
        }
    }

    @Override
    public void clearDockingError(String missionWorkId) {
        try {
            vehicleCommandService.clearDockingError(missionWorkId, this.id);
        } catch (Exception e) {
            LOGGER.error("对接清错指令失败, ", e);
            throw new ExecuteException(MessageUtils.getMessage("vehicle.continue_mission_fault") + " " + e.getMessage());
        }
    }

    /**
     * 切换为自动模式
     * 1、地图正常
     * 2、录制状态未开始
     * 3、没有在执行手动发送的动作（页面发送的自由导航等）
     */
    @Override
    public void switchAutoMode() {

        if (MapConstant.MAP_STATUS_NOT_APPOINT.equals(super.appointStatus)) {
            //throw new ExecuteException(MessageUtils.getMessage("vehicle.map_not_sync_or_map_syncing"));
            throw new ExecuteException(MessageUtils.getMessage("vehicle.map_not_appoint"));
        }
        if (AUTO_CONTROL_MODE.equals(this.getControlMode())) {
            throw new YOUIFleetException(MessageUtils.getMessage("vehicle.current_control_model_is_auto"));
        }
        //super.controlMode = AUTO_CONTROL_MODE;//自动控制模式
        vehicleCommandService.switchAutoMode(this.id);
    }

    @Override
    public void switchManualMode() {
        /**
         * 检测AGV是否在执行任务且未出现异常
         */
        /*if (!TASK_STATUS_FREE.equals(workStatus) && ABNORMAL_STATUS_NO.equals(abnormalStatus)) {
            throw new ExecuteException(MessageUtils.getMessage("http.agv_execute_task_now"));
        }*/
        if (MANUAL_CONTROL_MODE.equals(this.getControlMode())) {
            throw new YOUIFleetException(MessageUtils.getMessage("vehicle.current_control_model_is_manual"));
        }
        //super.controlMode = MANUAL_CONTROL_MODE;//手动控制模式
        vehicleCommandService.switchManualMode(this.id);
    }

//    @Override
//    public synchronized void terminate() {
//        try {
//            if (this.terminate) {
//                return;
//            }
//            LOGGER.debug("defaultVehicle terminate.");
//            //添加在线日志的结束时间
//            agvLogService.updateEndTimeByAgvCodeAndType(this.id, AGV_LOG_TYPE_ON_LINE);
//            // 清理自己出default vehicle pool.
//            vehiclePool.detachVehicle(this.id);
//            LOGGER.debug("detach vehicle from pool.");
//            terminate = true;
//        } catch (Exception e) {
//            LOGGER.error("vehicle terminate error, ", e);
//        }
//    }

    /**
     * 恢复机器人异常
     * 1、充电异常   恢复失败
     * 2、归位异常   恢复失败
     * 3、任务异常（暂停和异常）   尝试恢复（清除异常+停止任务）
     */
    @Override
    public void oneKeyResume() {
        if (ABNORMAL_STATUS_DOCKING.equals(abnormalStatus)) {
            throw new ExecuteException("对接异常，请先对接清错");
        }

        if (ABNORMAL_STATUS_NO.equals(abnormalStatus)) {//判断有无异常
            throw new ExecuteException(MessageUtils.getMessage("vehicle.not_exception"));
        }
        //发送一键恢复mq,并将任务状态改为恢复中
        vehicleCommandService.oneKeyResume(this.id);
    }

    /**
     * 一键停止
     * 1、充电 --> 停止充电动作，置任务状态为空闲，控制模式切换为手工模式；
     * 2、归位 --> 停止归位动作，置任务状态为空闲，控制模式切换为手工模式；
     * 3、工作 --> 停止绑定的工作，置任务状态为空闲，控制模式切换为手工模式；
     */
    @Override
    public void oneKeyStop() {
        if (ABNORMAL_STATUS_DOCKING.equals(abnormalStatus)) {
            throw new ExecuteException("对接异常，请先对接清错");
        }

        //1.异常状态下不允许停止
        if (!ABNORMAL_STATUS_NO.equals(this.abnormalStatus)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.status_is_abnormal"));
        }
        if (TASK_STATUS_FREE.equals(workStatus)) {//空闲状态
            throw new ExecuteException(MessageUtils.getMessage("http.no_mission_can_stop"));
        }
        //发送一键停止mq,并将任务状态改为停止中
        vehicleCommandService.oneKeyStop(this.id);
    }

    /**
     * 一键重置
     * 1、充电异常 --> 清理充电异常，置任务状态为空闲，控制模式切换为手工模式；
     * 2、归位异常 --> 清理归位异常，置任务状态为空闲，控制模式切换为手工模式；
     * 3、工作异常 --> 清理工作异常，置任务状态为空闲，置对应工作的状态为停止，控制模式切换为手工模式；
     */
    @Override
    public void oneKeyReset() {
        if (ABNORMAL_STATUS_DOCKING.equals(abnormalStatus)) {
            throw new ExecuteException("对接异常，请先对接清错");
        }
        //发送一键停止mq,并将任务状态改为停止中
        vehicleCommandService.oneKeyReset(this.id);
    }

    @Override
	public void oneKeyResetLocation() {
        if (ABNORMAL_STATUS_DOCKING.equals(abnormalStatus)) {
            throw new ExecuteException("对接异常，请先对接清错");
        }

        //发送一键停止mq,并将任务状态改为停止中
        vehicleCommandService.oneKeyResetLocation(this.id);
		
	}
    
    @Override
    public void agvRestart() {
        vehicleCommandService.agvRestart(this.getId());
    }

    @Override
    public void startMissionWork(WorkScheduler workScheduler) {
        vehicleCommandService.startMissionWork(workScheduler);
    }

    @Override
    public void cancelMissionWork(String missionWorkId) {
        vehicleCommandService.cancelMissionWork(missionWorkId, this.getId());
    }

    @Override
    public void startCharge(ChargeScheduler chargeScheduler) {
        vehicleCommandService.startCharge(chargeScheduler);
    }

    @Override
    public void cancelCharge() {
        vehicleCommandService.cancelCharge(this.getId());
    }

    @Override
    public void startPark(ParkScheduler parkScheduler) {
        vehicleCommandService.startPark(parkScheduler);
    }

    @Override
    public void cancelPark() {
        vehicleCommandService.cancelPark(this.getId());
    }

    @Override
    public void cancelPathNavigation() {
        vehicleCommandService.cancelPathNavigation(this.getId());
    }

    /**
     * 重定位指令: 手动重定位
     *
     * @param relocationMessage
     * @return
     */
    @Override
    public void manualRelocation(RelocationMessage relocationMessage) {
        try {
            if (!MANUAL_CONTROL_MODE.equals(controlMode)) {
                throw new ExecuteException(MessageUtils.getMessage("vehicle.mode_is_not_manual"));
            }
            //手动重定位 mq
            if (StringUtils.isEmpty(this.agvMapId)) {
                throw new ExecuteException(MessageUtils.getMessage("vehicle.map_not_appoint"));
            }
            relocationMessage.setAgvMapId(this.agvMapId);
            vehicleCommandService.manualRelocation(relocationMessage);
        } catch (Exception e) {
            LOGGER.error("agv relocation error,", e);
            throw e;
        }
    }

    @Override
    public void autoRelocation() {
        if (!MANUAL_CONTROL_MODE.equals(controlMode)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.mode_is_not_manual"));
        }
        if (StringUtils.isEmpty(agvMapId)) {
            throw new ExecuteException(MessageUtils.getMessage("vehicle.map_not_appoint"));
        }
        LOGGER.debug("激光地图自动重定位, id : " + id);
        //自动重定位 mq
        vehicleCommandService.autoRelocation(this.id, "autoRelocation", agvMapId);
    }

    @Override
    public void appointMap(String agvMapId) {
        // 仿真系统需要先同步地图再指定
        if(isSimulation()){
            agvMapService.syncMap(this.id, Arrays.asList(agvMapService.selectById(agvMapId)));
        }
        mapCommandService.appointCurrentMap(this.id, agvMapId);
    }


    /**
     * 机器人重新进行路径规划。
     */
    @Override
    public void rePathPlan() {
        vehicleCommandService.rePathPlan(this.getId());
    }

	
}
