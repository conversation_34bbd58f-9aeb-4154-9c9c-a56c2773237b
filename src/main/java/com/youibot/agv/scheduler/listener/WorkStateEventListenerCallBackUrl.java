package com.youibot.agv.scheduler.listener;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.listener.event.MissionWorkStateEvent;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpClientService;
import com.youibot.agv.scheduler.scheduleplan.httpClient.HttpResult;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月23日 上午11:20:11
 */
@Component
public class WorkStateEventListenerCallBackUrl {
    private static final Logger logger = LoggerFactory.getLogger(WorkStateEventListenerCallBackUrl.class);

    @Autowired
    private HttpClientService httpClientService;

    @Async
    @EventListener
    public void reCallbackUrl(MissionWorkStateEvent event) {
        if (event.getSource() != null && event.getSource() instanceof MissionWork) {
            MissionWork missionWork = (MissionWork) event.getSource();
            if (StrUtil.isBlankIfStr(missionWork.getCallbackUrl())) {
                return;
            }
            ThreadUtil.execute(() -> {
                for (int i = 1; i <= 5; i++) {
                    try {
                        if (StrUtil.isNotBlank(missionWork.getAgvCode())) {
                            ThreadContext.put("ROUTINGKEY", missionWork.getAgvCode());
                        }

                        Map<String, Object> param = new HashMap<>();
                        String runtimeParam = missionWork.getRuntimeParam();
                        if (!StringUtils.isEmpty(runtimeParam)) {
                            Map map = JSONObject.parseObject(runtimeParam, Map.class);
                            param.putAll(map);
                        }
                        param.put("missionWorkId", missionWork.getId());
                        param.put("schedulePlanId", missionWork.getSchedulePlanId());
                        param.put("agvCode", missionWork.getAgvCode());
                        param.put("status", missionWork.getStatus());
                        param.put("errorCode", missionWork.getErrorCode());
                        param.put("errorMessage", missionWork.getMessage());
                        HttpResult httpResult = httpClientService.doPost(missionWork.getCallbackUrl(), param);

                        HashMap map = new HashMap();
                        map.put("missionWork", missionWork);
                        map.put("param", param);
                        map.put("response", httpResult);
                        logger.debug("agvCode:[{}],event:[作业状态变更接口回调],回调相关数据：[{}]", missionWork.getAgvCode(), JSONObject.toJSONString(map));
                        if (httpResult.getCode() == 200) {
                            logger.debug("agvCode:[{}],event:[作业状态变更接口回调]，任务数据回调成功，不再重试", missionWork.getAgvCode());
                            return;
                        }
                    } catch (Exception e) {
                        logger.error("agvCode:[{}],event:[作业状态变更接口回调异常],作业数据：[{}], 失败次数:[{}], 异常原因：[{}]", missionWork.getAgvCode(), JSONObject.toJSONString(missionWork), i, e);
                    } finally {
                        if (StrUtil.isNotBlank(missionWork.getAgvCode())) {
                            ThreadContext.remove("ROUTINGKEY");
                        }
                    }
                }
            });
        }
    }

//    @EventListener
//    public void reCallbackUrl(MissionWorkActionStateEvent event) {
//        if (event.getSource() != null && event.getSource() instanceof MissionWork) {
//            MissionWorkAction missionWorkAction = (MissionWorkAction) event.getSource();
//            if (LOGGER.isDebugEnabled()) {
//                LOGGER.debug(missionWorkAction.toString());
//            }
//            MissionWork missionWork = missionWorkService.selectById(missionWorkAction.getMissionWorkId());
//            try {
//                Map<String, Object> param = new HashMap<>();
//                param.put("missionWorkId", missionWork.getMissionId());
//                param.put("schedulePlanId", missionWork.getSchedulePlanId());
//                param.put("agvId", missionWork.getAgvId());
//                param.put("status", missionWork.getStatus());
//                param.put("missionWorkActionId", missionWorkAction.getId());
//                param.put("missionWorkActionType", missionWorkAction.getActionType());
//                param.put("missionActionId", missionWorkAction.getMissionActionId());
//                param.put("missionActionStatus", missionWorkAction.getStatus());
//                httpClientService.doPut(missionWork.getCallbackUrl(), param);
//            } catch (IOException e) {
//                LOGGER.error("Mission work update status ,call back url error. url:" + missionWork.getCallbackUrl());
//            }
//        }
//    }
}
