package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerScope;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.MarkerAllocationService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.VehicleConflictInfo;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.service.ConflictCheck;
import com.youibot.agv.scheduler.mqtt.bean.scribe.pathplan.PathPlanMessage;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.mqtt.bean.scribe.pathplan.PathPlanMessage.TYPE_AVOID;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 8/27/21 6:01 PM
 * @Description 冲突解决管理。主要是解决机器人在路径规划的过程是识别机器人的双向冲突和环形冲突。
 * 让机器人进行避让的功能。
 * 用一个线程去完成。目的是期望不要影响路径的下发速度。
 */
@Component
public class ConflictManager {
    private final static Logger logger = LoggerFactory.getLogger(ConflictManager.class);

    public final static Logger conflictCheckLog = LoggerFactory.getLogger("conflictCheck");

    /**
     * 由于区域资源的规则较为复杂，且还有预申请的机制，因此冲突检测难以覆盖所有情况
     * 因此这里维护无法申请资源的小车以及造成它无法申请的其它小车的关系
     * 在冲突检测中进行判断，只要相互影响，即是冲突
     * key是无法申请资源的小车A，value是造成小车A无法申请资源的其它小车
     */
    public static Map<String, List<String>> conflictAgv = new ConcurrentHashMap<>();

    @Value("${PATH_PLAN.CONFLICT_HANDLE_RULE}")
    private Integer conflictHandleRule;

    @Value("${PATH_PLAN.OPEN_OBSTRUCT_DETOUR}")
    private boolean obstructDetour;

    @Autowired
    private ConflictCheck conflictCheck;
    @Autowired
    private CheckAndSendPathService checkAndSendPathService;
    @Autowired
    private VehiclePoolService vehiclePoolService;
    @Autowired
    private MarkerAllocationService markerAllocationService;
    @Autowired
    private FullLocationService fullLocationService;

    @Async
    public void start() {
        Thread.currentThread().setName("ConflictManagerThread");
        while (true) {
            try {
                Thread.sleep(1000);
                if (conflictHandleRule == 1) {
                    //智能避让
                    /**
                     * 识别对象冲突
                     */
                    List<List<VehicleConflictInfo>> oppositeConflicts = conflictCheck.getVehicleOppositeConflicts();
                    /**
                     * 处理对象冲突
                     */
                    if (!CollectionUtils.isEmpty(oppositeConflicts)) {
                        for (List<VehicleConflictInfo> vehicleConflictInfos : oppositeConflicts) {
                            this.processConflictIntelligent("对象冲突", vehicleConflictInfos);
                        }
                    }
                    /**
                     * 识别动态区域资源与其它资源形成的环形冲突 两车及以上
                     */
                    List<List<VehicleConflictInfo>> ringAreaConflicts = conflictCheck.getVehicleRingAreaConflicts();
                    if (!CollectionUtils.isEmpty(ringAreaConflicts)) {
                        for (List<VehicleConflictInfo> vehicleConflictInfos : ringAreaConflicts) {
                            this.processConflictIntelligent("环形冲突", vehicleConflictInfos);
                        }
                    }
                }else{
                    //避让点避让
                    /**
                     * 识别并处理对向冲突。
                     */
                    List<List<String>> oppositeConflicts = conflictCheck.getOppositeConflicts();
                    if (!CollectionUtils.isEmpty(oppositeConflicts)) {
                        for (List<String> vehicleIds : oppositeConflicts) {
                            this.processConflict(vehicleIds);
                        }
                    }
                    /**
                     * 识别并处理环型冲突。
                     */
                    List<List<String>> ringConflicts = conflictCheck.getRingConflicts();
                    if (!CollectionUtils.isEmpty(ringConflicts)) {
                        for (List<String> vehicleIds : ringConflicts) {
                            this.processConflict(vehicleIds);
                        }
                    }
                }
                /**
                 * 空闲驱赶
                 */
                conflictCheck.vehicleBlockDrive();
                /**
                 * 识别并处理阻挡冲突。
                 */
                if (obstructDetour) {
                    List<Pair<String,List<String>>> obstructVehicleIds = conflictCheck.getObstructConflicts();
                    if (!CollectionUtils.isEmpty(obstructVehicleIds)) {
                        obstructVehicleIds.forEach(this::processObStructConflicts);
                    }
                }
            } catch (Exception e) {
                logger.error("process vehicle conflict error.", e);
            }
        }
    }

    /**
     * 开始处理冲突，如果机器人到达目标点存在冲突。
     * 1：获取机器人最近的空闲的避让点。进行评分对比。
     * 2：取消机器人当前的路径规划。
     * 3：发送两个路径，一个规划到避让点。一个规划到目标点。
     *
     * @param vehicleIds
     */
    private void processConflict(List<String> vehicleIds) {
        if (CollectionUtils.isEmpty(vehicleIds)) {
            return;
        }
        /**
         * 检测一下这个避让是否已经开始，如果已经开始，则不再进行冲突检测。
         */
        for (String vehicleId : vehicleIds) {
            ThreadContext.put("ROUTINGKEY", vehicleId);
            Vehicle vehicle = vehiclePoolService.selectById(vehicleId);
            PathPlanMessage pathPlanMessage = vehicle.getPathPlanMessages().peekFirst();
            if (pathPlanMessage != null && !StringUtils.isEmpty(pathPlanMessage.getType()) && TYPE_AVOID.equals(pathPlanMessage.getType())) {
                logger.info("agvCode:[{}],event:[冲突处理],content:[机器人已经开始避让,pathPlanMessage:{}冲突机器人集合:{}]", vehicleId, pathPlanMessage.toString(), vehicleIds);
                return;
            }
            ThreadContext.remove("ROUTINGKEY");
        }
        /**
         *获得离空闲避让点最近的机器人。
         */
        MarkerScope markerScope = null;
        Vehicle avoidVehicle = null;
        for (String vehicleId : vehicleIds) {
            ThreadContext.put("ROUTINGKEY", vehicleId);
            Vehicle vehicle = vehiclePoolService.selectById(vehicleId);
            if (vehicle != null) {
                MarkerScope tempScope = markerAllocationService.getAvoidMarkerFromEnable(vehicle);
                if (tempScope == null) {
                    logger.warn("机器人无可用的避让点。vehicleCode：" + vehicle.getId());
                    ConflictManager.conflictCheckLog.debug("【避让分配】 无可用的避让点, 冲突机器人:{}", vehicleId);
                    continue;
                }
                if (tempScope.getScope() != null) {
                    if (markerScope == null) {
                        markerScope = tempScope;
                        avoidVehicle = vehicle;
                    } else if (Double.compare(markerScope.getScope(), tempScope.getScope()) < 0) {
                        markerScope = tempScope;
                        avoidVehicle = vehicle;
                    }
                }
            }
            ThreadContext.remove("ROUTINGKEY");
        }
        /**
         * 如果机器人已经在避让点。则退出。不再次进行重新避让。
         */
        if (markerScope != null && avoidVehicle != null) {
            VehicleLocation vehicleLocation = fullLocationService.getVehicleLocation(avoidVehicle.getId());
            if (vehicleLocation != null && vehicleLocation.getMarker() != null) {
                if ("AVOID_MARKER".equals(vehicleLocation.getMarker().getType())) {
                    return;
                }
            }
        }

        /**
         * 如果没有可用避让点, 撤销去避让点的导航
         */
        if (null == markerScope) {
            for (String vehicleId : vehicleIds) {
                ThreadContext.put("ROUTINGKEY", vehicleId);
                Map<String, String> agvCodeLock = checkAndSendPathService.getAgvCodeLock();
                agvCodeLock.computeIfAbsent(vehicleId, k -> k);
                synchronized (agvCodeLock.get(vehicleId)) {
                    Vehicle vehicle = vehiclePoolService.selectById(vehicleId);
                    PathPlanMessage pathPlanMessage = vehicle.getPathPlanMessages().peekFirst();
                    if (pathPlanMessage != null && !StringUtils.isEmpty(pathPlanMessage.getType()) && TYPE_AVOID.equals(pathPlanMessage.getType())) {
                        logger.info("agvCode:[{}],event:[冲突处理],content:[机器人已经开始避让,pathPlanMessage:{}冲突机器人集合:{}]", vehicleId, pathPlanMessage.toString(), vehicleIds);
                        continue;
                    }

                    pathPlanMessage = checkAndSendPathService.getPathPlanMessage(vehicleId);
                    if (pathPlanMessage != null && TYPE_AVOID.equals(pathPlanMessage.getType())) {
                        //当机器人避让过程中，没有正在执行路径，有规划路径时，说明规划的避让车不合理，则需要取消当前的避让，重新识别避让。如果取消过，不能重复取消。
                        if (CollectionUtils.isEmpty(vehicle.getRunningPaths()) && !CollectionUtils.isEmpty(vehicle.getPlanedPaths())) {
                            if (null == pathPlanMessage.getIsCancel() || pathPlanMessage.getIsCancel() == 0) {
                                checkAndSendPathService.getPathPlanMessage(vehicleId).setIsCancel(1);
                                vehicle.cancelPathNavigation();
                            } else {
                                logger.info("agvCode:[{}],event:[冲突处理],content:[避让正在取消中，无需重复取消]", vehicleId);
                            }
                            continue;
                        }
                    }
                }
                ThreadContext.remove("ROUTINGKEY");
            }
            return;
        }

        /**
         * 取消机器人的规划，让机器人重新规划避让路径上。再规划到目标点。
         * PATH_PLAN_MISSION_WORK_ID：意思是因为路径规划导致的清理。逻辑不是很合理。需要进行修改。
         * 1：获取机器人当前的目标点。
         * 2：取消当前的路径规划。
         * 3：下发路径规划请求到避让目标点。
         * 4：下发路径规划请求到避让目标点。
         */
        ThreadContext.put("ROUTINGKEY", avoidVehicle.getId());
        logger.info("agvCode:[{}],event:[避让分配],content:[当前为避让车，避让点:{}]", avoidVehicle.getId(), markerScope.getMarker().getCode());
        /**
         * 如果pathPlanMessage是避让类型的话。则忽略掉。并获取车辆的下一条路径并设计为目标点。
         */
        PathPlanMessage pathPlanMessage = checkAndSendPathService.getPathPlanMessage(avoidVehicle.getId());

        String agvCode = avoidVehicle.getId();
        Map<String, String> agvCodeLock = checkAndSendPathService.getAgvCodeLock();
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {

            //当前路径规划请求的目标点，如果在需要处理的地图上，暂时不处理
            if (pathPlanMessage != null && (PathPlanManger.containsAniMarker(pathPlanMessage.getAimMarkerId()) || PathPlanManger.containsVehicle(avoidVehicle))) {
                logger.debug("ConflictManager: agv:[{}],event:[冲突检测],content:[处于地图发布流程中，暂时跳过],ppm:{}", avoidVehicle.getId(), avoidVehicle.getPathPlanMessages());
                return;
            }

            if (pathPlanMessage == null || TYPE_AVOID.equals(pathPlanMessage.getType())) {
                pathPlanMessage = avoidVehicle.getPathPlanMessages().pollFirst();
            }
            if (pathPlanMessage == null || (pathPlanMessage.getIsCancel() != null && pathPlanMessage.getIsCancel() == 1)) {
                logger.error("获取车辆的目标路径, 或者路径规划已取消, pathPlanMessage:{}", pathPlanMessage);
                return;
            }
            /**
             * 路径必须按顺序进行添加。否则会导致机器人先执行旧的路径。必须把机器人的其他路径添加到当前执行的路径后。
             * oldPathPlanMessage理论上会存在避让路径。只会存在目标路径。
             */
            LinkedList<PathPlanMessage> oldPathPlanMessage = new LinkedList<>();
            oldPathPlanMessage.addAll(avoidVehicle.getPathPlanMessages());
            avoidVehicle.getPathPlanMessages().clear();
            /**
             * 添加避让路径规划请求到机器人。
             */
            PathPlanMessage avoidPathPlanMessage = new PathPlanMessage();
            avoidPathPlanMessage.setAgvCode(pathPlanMessage.getAgvCode());
            avoidPathPlanMessage.setMissionWorkId(pathPlanMessage.getMissionWorkId());
            avoidPathPlanMessage.setMissionWorkActionId(pathPlanMessage.getMissionWorkActionId());
            avoidPathPlanMessage.setAimMarkerId(markerScope.getMarker().getId());
            avoidPathPlanMessage.setTime(System.currentTimeMillis());
            avoidPathPlanMessage.setType(TYPE_AVOID);
            avoidPathPlanMessage.setAvoidList(vehicleIds);
            avoidVehicle.getPathPlanMessages().addLast(avoidPathPlanMessage);
            /**
             * 添加目标路径规划请求到机器人。
             */
            PathPlanMessage newPathPlanMessage = new PathPlanMessage();
            newPathPlanMessage.setAgvCode(pathPlanMessage.getAgvCode());
            newPathPlanMessage.setMissionWorkId(pathPlanMessage.getMissionWorkId());
            newPathPlanMessage.setMissionWorkActionId(pathPlanMessage.getMissionWorkActionId());
            newPathPlanMessage.setAimMarkerId(pathPlanMessage.getAimMarkerId());
            newPathPlanMessage.setTime(System.currentTimeMillis());
            newPathPlanMessage.setType("AVOID_NEXT");
            avoidVehicle.getPathPlanMessages().addLast(newPathPlanMessage);
            /**
             * 添加机器人的之前的路径规划请求到机器人。
             */
            if (!CollectionUtils.isEmpty(oldPathPlanMessage)) {
                avoidVehicle.getPathPlanMessages().addAll(oldPathPlanMessage);
            }
            /**
             * 取消机器人当前的目标规划。
             */
            logger.debug("PathPlanManager: agv:{},ppm:{}", avoidVehicle.getId(), avoidVehicle.getPathPlanMessages());
            pathPlanMessage.setIsCancel(1);
            avoidVehicle.cancelPathNavigation();
            logger.info("agvCode:[{}],event:[避让分配],content:[避让分配完成]", avoidVehicle.getId());
            ThreadContext.remove("ROUTINGKEY");
            ConflictManager.conflictCheckLog.debug("【避让分配】 机器人: {} 分配避让完成, 避让点位: {},  avoidPathPlanMessage:{}", agvCode, MapGraphUtil.getMarkerByMarkerId(avoidPathPlanMessage.getAimMarkerId()).getCode(), avoidPathPlanMessage);
        }
    }

    /**
     * 处理冲突
     * 第一次遍历冲突机器人组：判断冲突是否已经在处理中
     * 第二次遍历冲突机器人组：处理冲突
     *
     * @param vehicleConflictInfos
     */
    private void processConflictIntelligent(String type, List<VehicleConflictInfo> vehicleConflictInfos) {
        if (CollectionUtils.isEmpty(vehicleConflictInfos)) {
            return;
        }
        List<String> conflictVehicleIds = vehicleConflictInfos.stream().map(VehicleConflictInfo::getVehicleCode).collect(Collectors.toList());

        //避让路径不可走的AgvCode,以及不可避让的点位
        String unableAvoidAgvCode = null;
        Integer avoidRoundCount = 0;
        /**
         * 第一次遍历，判断避让处理是否已经开始，不需要重复分配处理
         */
        for (VehicleConflictInfo vehicleConflictInfo : vehicleConflictInfos) {
            Vehicle vehicle = vehiclePoolService.selectById(vehicleConflictInfo.getVehicleCode());
            PathPlanMessage pathPlanMessage = checkAndSendPathService.getPathPlanMessage(vehicleConflictInfo.getVehicleCode());
            if (pathPlanMessage != null && pathPlanMessage.getType() != null && TYPE_AVOID.equals(pathPlanMessage.getType()) &&
                    pathPlanMessage.getAvoidList().size() == vehicleConflictInfos.size() && pathPlanMessage.getAvoidList().containsAll(conflictVehicleIds)) {
                logger.debug("agvCode:[{}],event:[避让分配],content:[机器人已经开始避让, 冲突机器人为:{}]", vehicle.getId(), conflictVehicleIds);
                conflictCheckLog.debug("【避让分配】 机器人 {} 已经开始避让, 冲突机器人为:{}", vehicleConflictInfo.getVehicleCode(), conflictVehicleIds);
                Integer isCancel = Optional.ofNullable(pathPlanMessage.getIsCancel()).orElse(0);
                Date pathPlanStartTime = Optional.ofNullable(pathPlanMessage.getStartTime()).orElse(new Date());
                if (CollectionUtils.isEmpty(vehicle.getRunningPaths()) && !CollectionUtils.isEmpty(vehicle.getPlanedPaths()) && isCancel == 0 && (System.currentTimeMillis() - pathPlanStartTime.getTime()) > 10000) {
                    logger.debug("agvCode:[{}],event:[避让分配],content:[机器人已经开始避让, 但规划路径不可行走, 撤销该去避让点的导航, 冲突机器人为:{}]", vehicle.getId(), conflictVehicleIds);
                    conflictCheckLog.debug("【避让分配】 机器人 {} 已经开始避让, 但规划路径不可行走, 撤销该去避让点的导航, 冲突机器人为:{}", vehicleConflictInfo.getVehicleCode(), conflictVehicleIds);
                    pathPlanMessage.setIsCancel(1);
                    vehicle.cancelPathNavigation();
                    unableAvoidAgvCode = vehicleConflictInfo.getVehicleCode();
                    avoidRoundCount = Optional.ofNullable(pathPlanMessage.getAvoidRoundCount()).orElse(0);
                    avoidRoundCount++;
                    break;
                }
                return;
            }
            pathPlanMessage = vehicle.getPathPlanMessages().peekFirst();
            if (pathPlanMessage != null && pathPlanMessage.getType() != null && TYPE_AVOID.equals(pathPlanMessage.getType()) &&
                    pathPlanMessage.getAvoidList().size() == vehicleConflictInfos.size() && pathPlanMessage.getAvoidList().containsAll(conflictVehicleIds)) {
                logger.debug("agvCode:[{}],event:[避让分配],content:[机器人已经开始避让, 冲突机器人为:{}]", vehicle.getId(), conflictVehicleIds);
                conflictCheckLog.debug("【避让分配】 机器人 {} 已经开始避让，冲突机器人为:{}", vehicleConflictInfo.getVehicleCode(), conflictVehicleIds);
                return;
            }
        }

        if (unableAvoidAgvCode != null) {
            //这里防止避让路径没取消完导致下面的排序出现问题
            int count = 0;
            while (true) {
                try {
                    TimeUnit.MILLISECONDS.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                if (++count > 100) {
                    break;
                }
                PathPlanMessage pathPlanMessage = checkAndSendPathService.getPathPlanMessage(unableAvoidAgvCode);
                if (Objects.nonNull(pathPlanMessage) && !Objects.equals(pathPlanMessage.getType(), TYPE_AVOID)) {
                    break;
                }
            }
        }

        /**
         * 第二次遍历，分配避让
         */
        List<MarkerScope> list = new ArrayList<>();
        for (VehicleConflictInfo vehicleConflictInfo : vehicleConflictInfos) {
            Vehicle vehicle = vehiclePoolService.selectById(vehicleConflictInfo.getVehicleCode());
            if (vehicle != null) {
                List<MarkerScope> tempScopes = markerAllocationService.getAvoidMarkerFromEnable(vehicleConflictInfo, conflictVehicleIds);
                if (tempScopes == null) {
                    continue;
                }
                MarkerScope markerScope = tempScopes.get(0);
                markerScope.setVehicleId(vehicle.getId());
                list.add(markerScope);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            String conflictVehicleIdsStr = Optional.ofNullable(conflictVehicleIds).map(data -> data.stream().collect(Collectors.joining(","))).orElse("");
            conflictVehicleIds.forEach(vehicleId -> {
                logger.debug("agvCode:[{}],event:[避让分配],content:[无可用的避让点, 冲突机器人:{}]", vehicleId, conflictVehicleIdsStr);
                ConflictManager.conflictCheckLog.debug("【避让分配】 无可用的避让点, 冲突机器人:{}", conflictVehicleIdsStr);
            });
            return;
        }
        //按照避让评分排序
        list.sort(Comparator.comparing(MarkerScope::getScope));
        Collections.reverse(list);
        MarkerScope markerScope = list.get(0);
        Vehicle avoidVehicle = vehiclePoolService.selectById(markerScope.getVehicleId());
        String agvCode = avoidVehicle.getId();

        Map<String, String> agvCodeLock = checkAndSendPathService.getAgvCodeLock();
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {
            PathPlanMessage pathPlanMessage = checkAndSendPathService.getPathPlanMessage(agvCode);
            if (pathPlanMessage == null) {
                logger.debug("agvCode:[{}],event:[避让分配],content:[机器人无正在执行的路径导航]", agvCode);
                return;
            }
            Integer isCancel = Optional.ofNullable(pathPlanMessage.getIsCancel()).orElse(0);
            if (isCancel == 1) {
                logger.debug("agvCode:[{}],event:[避让分配],content:[机器人正在取消路径规划]", agvCode);
                return;
            }
            /**
             * 路径必须按顺序进行添加。否则会导致机器人先执行旧的路径。必须把机器人的其他路径添加到当前执行的路径后。
             * oldPathPlanMessage理论上会存在避让路径。只会存在目标路径。
             */
            LinkedList<PathPlanMessage> oldPathPlanMessage = new LinkedList<>();
            oldPathPlanMessage.addAll(avoidVehicle.getPathPlanMessages());
            avoidVehicle.getPathPlanMessages().clear();
            /**
             * 添加避让路径规划请求到机器人。
             */
            PathPlanMessage avoidPathPlanMessage = new PathPlanMessage();
            avoidPathPlanMessage.setAgvCode(pathPlanMessage.getAgvCode());
            avoidPathPlanMessage.setMissionWorkId(pathPlanMessage.getMissionWorkId());
            avoidPathPlanMessage.setMissionWorkActionId(pathPlanMessage.getMissionWorkActionId());
            avoidPathPlanMessage.setAimMarkerId(markerScope.getMarker().getId());
            avoidPathPlanMessage.setTime(System.currentTimeMillis());
            avoidPathPlanMessage.setType(TYPE_AVOID);
            avoidPathPlanMessage.setAvoidList(conflictVehicleIds);
            avoidPathPlanMessage.setAvoidRoundCount(avoidRoundCount);
            avoidPathPlanMessage.setNotPassMarkerCodes(markerScope.getNotPassMarkerCodes());
            avoidVehicle.getPathPlanMessages().addLast(avoidPathPlanMessage);
            /**
             * 如果当前正在执行的路径导航不是去避让的, 添加该路径规划请求到机器人
             * 如果当前是泊车任务的话，就不用再去泊车，直接停在避让点就好了
             */
            if (!TYPE_AVOID.equals(pathPlanMessage.getType()) && !"SMART_WAIT".equals(pathPlanMessage.getMissionWorkId())) {
                PathPlanMessage newPathPlanMessage = new PathPlanMessage();
                newPathPlanMessage.setAgvCode(pathPlanMessage.getAgvCode());
                newPathPlanMessage.setMissionWorkId(pathPlanMessage.getMissionWorkId());
                newPathPlanMessage.setMissionWorkActionId(pathPlanMessage.getMissionWorkActionId());
                newPathPlanMessage.setAimMarkerId(pathPlanMessage.getAimMarkerId());
                newPathPlanMessage.setTime(System.currentTimeMillis());
                newPathPlanMessage.setType("AVOID_NEXT");
                avoidVehicle.getPathPlanMessages().addLast(newPathPlanMessage);
            }
            /**
             * 添加机器人的之前的路径规划请求到机器人。
             */
            if (!CollectionUtils.isEmpty(oldPathPlanMessage)) {
                avoidVehicle.getPathPlanMessages().addAll(oldPathPlanMessage);
            }
            /**
             * 取消机器人当前的目标规划。
             */
            pathPlanMessage.setIsCancel(1);
            avoidVehicle.cancelPathNavigation();
            logger.debug("agvCode:[{}],event:[避让分配],content:[避让分配完成], avoidPathPlanMessage:{}", agvCode, avoidPathPlanMessage);
            ConflictManager.conflictCheckLog.debug("【避让分配】 机器人: {} 分配避让完成, 避让点位: {},  avoidPathPlanMessage:{}", agvCode, MapGraphUtil.getMarkerByMarkerId(avoidPathPlanMessage.getAimMarkerId()).getCode(), avoidPathPlanMessage);
        }
        logger.debug("当前批次处理的是：{}：冲突的机器人：{}",type, conflictVehicleIds);
    }

    /**
     * 处理阻挡冲突
     *
     * @param pair
     */
    private void processObStructConflicts(Pair<String,List<String>> pair) {
        String vehicleId = pair.first();
        List<String> notPassMarkerCodes = pair.second();

        ThreadContext.put("ROUTINGKEY", vehicleId);
        Vehicle vehicle = vehiclePoolService.selectById(vehicleId);
        PathPlanMessage pathPlanMessage = checkAndSendPathService.getPathPlanMessage(vehicleId);

        String agvCode = vehicle.getId();
        Map<String, String> agvCodeLock = checkAndSendPathService.getAgvCodeLock();
        agvCodeLock.computeIfAbsent(agvCode, k -> k);
        synchronized (agvCodeLock.get(agvCode)) {

            //当前路径规划请求的目标点，如果在需要处理的地图上，暂时不处理
            if (pathPlanMessage != null && (PathPlanManger.containsAniMarker(pathPlanMessage.getAimMarkerId()) || PathPlanManger.containsVehicle(vehicle))) {
                logger.debug("ConflictManager: agv:[{}],event:[冲突检测],content:[处于地图发布流程中，暂时跳过],ppm:{}", vehicle.getId(), vehicle.getPathPlanMessages());
                return;
            }

            if (vehicle == null || pathPlanMessage == null || (pathPlanMessage.getIsCancel() != null && pathPlanMessage.getIsCancel() == 1)) {
                logger.warn("处理路径规划阻挡冲突时, 获取机器人或者路径导航数据, 或者路径导航已取消为空！vehicleId:{}, pathPlanMessage:{}", vehicleId, pathPlanMessage);
                return;
            }
            if (!StringUtils.isEmpty(pathPlanMessage.getType()) && TYPE_AVOID.equals(pathPlanMessage.getType())) {
                return;//机器人去避让点途中被阻挡, 暂不处理
            }
            /**
             * 路径必须按顺序进行添加。否则会导致机器人先执行旧的路径。必须把机器人的其他路径添加到当前执行的路径后。
             * oldPathPlanMessage理论上会存在避让路径。只会存在目标路径。
             */
            LinkedList<PathPlanMessage> pathPlanMessages = vehicle.getPathPlanMessages();
            if (!CollectionUtils.isEmpty(pathPlanMessages)) {
                logger.warn("vehicle存在路径规划数据, 等待下一次冲突识别, vehicleId:{}", vehicleId);
                return;
            }
            vehicle.getPathPlanMessages().clear();
            /**
             * 添加目标路径规划请求到机器人。
             */
            PathPlanMessage newPathPlanMessage = new PathPlanMessage();
            newPathPlanMessage.setAgvCode(pathPlanMessage.getAgvCode());
            newPathPlanMessage.setType(pathPlanMessage.getType());
            newPathPlanMessage.setMissionWorkId(pathPlanMessage.getMissionWorkId());
            newPathPlanMessage.setMissionWorkActionId(pathPlanMessage.getMissionWorkActionId());
            newPathPlanMessage.setAimMarkerId(pathPlanMessage.getAimMarkerId());
            newPathPlanMessage.setTime(System.currentTimeMillis());
            newPathPlanMessage.setNotPassMarkerCodes(notPassMarkerCodes);
            logger.debug("PathPlanManager: agv:{},ppm:{}", vehicle.getId(), vehicle.getPathPlanMessages());
            vehicle.getPathPlanMessages().addLast(newPathPlanMessage);

            /**
             * 取消机器人当前的目标规划。
             */
            logger.debug("PathPlanManager: agv:{},ppm:{}", vehicle.getId(), vehicle.getPathPlanMessages());
            pathPlanMessage.setIsCancel(1);
            vehicle.cancelPathNavigation();
            PathPlanMessage pathPlanMessage3 = checkAndSendPathService.getPathPlanMessage(vehicle.getId());
            if (null != vehicle.getPathPlanMessages()) {
                logger.debug("vehicle path plan message:{}, agvCode:{}", vehicle.getPathPlanMessages().toString(), vehicleId);
            }
            if (null != pathPlanMessage3) {
                logger.debug("end avoid path plan message:{}, agvCode:{}" + pathPlanMessage3.toString(), vehicleId);
            }
            ThreadContext.remove("ROUTINGKEY");
        }
    }

}
