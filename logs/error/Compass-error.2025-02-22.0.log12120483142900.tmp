2025-02-22 14:02:26.190 [main] ERROR com.alibaba.druid.pool.DruidDataSource 902 init - init datasource error, url: *************************************************************************************************************************************************
java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:197)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1570)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1636)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:898)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1920)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1862)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:884)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1336)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1176)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1404)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:453)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:239)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at com.youibot.vehicle.scheduler.AdminApplication.main(AdminApplication.java:26)
2025-02-22 14:02:26.195 [main] ERROR com.alibaba.druid.pool.DruidDataSource 946 init - {dataSource-1} init error
java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:197)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1570)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1636)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:898)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1920)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1862)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:884)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1336)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1176)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1404)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:453)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:239)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at com.youibot.vehicle.scheduler.AdminApplication.main(AdminApplication.java:26)
2025-02-22 14:02:26.213 [Druid-ConnectionPool-Create-37878764] ERROR com.alibaba.druid.pool.DruidDataSource 2552 run - create connection SQLException, url: *************************************************************************************************************************************************, errorCode 1049, state 42000
java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:197)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1570)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1636)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2550)
2025-02-22 14:02:26.225 [Druid-ConnectionPool-Create-37878764] ERROR com.alibaba.druid.pool.DruidDataSource 2552 run - create connection SQLException, url: *************************************************************************************************************************************************, errorCode 1049, state 42000
java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:197)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1570)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1636)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2550)
2025-02-22 14:02:26.226 [main] ERROR org.springframework.boot.SpringApplication 837 reportFailure - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'initSystem': Unsatisfied dependency expressed through field 'nettySocketClient'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'nettySocketClient': Unsatisfied dependency expressed through field 'vehicleEventService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'vehicleEventServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemConfigService': Unsatisfied dependency expressed through field 'baseDao'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemConfigDao' defined in file [E:\work\idea\wk2_fleet5\youicompass-plus\youicompass-admin\target\classes\com\youibot\vehicle\scheduler\modules\config\dao\SystemConfigDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at com.youibot.vehicle.scheduler.AdminApplication.main(AdminApplication.java:26)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'nettySocketClient': Unsatisfied dependency expressed through field 'vehicleEventService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'vehicleEventServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemConfigService': Unsatisfied dependency expressed through field 'baseDao'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemConfigDao' defined in file [E:\work\idea\wk2_fleet5\youicompass-plus\youicompass-admin\target\classes\com\youibot\vehicle\scheduler\modules\config\dao\SystemConfigDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'vehicleEventServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemConfigService': Unsatisfied dependency expressed through field 'baseDao'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemConfigDao' defined in file [E:\work\idea\wk2_fleet5\youicompass-plus\youicompass-admin\target\classes\com\youibot\vehicle\scheduler\modules\config\dao\SystemConfigDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemConfigService': Unsatisfied dependency expressed through field 'baseDao'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemConfigDao' defined in file [E:\work\idea\wk2_fleet5\youicompass-plus\youicompass-admin\target\classes\com\youibot\vehicle\scheduler\modules\config\dao\SystemConfigDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:453)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:239)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	... 44 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemConfigDao' defined in file [E:\work\idea\wk2_fleet5\youicompass-plus\youicompass-admin\target\classes\com\youibot\vehicle\scheduler\modules\config\dao\SystemConfigDao.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1524)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1404)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 60 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:797)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:538)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1336)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1176)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1509)
	... 71 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:884)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	... 84 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Unknown database 'youibot_youicompass'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:197)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1570)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1636)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:898)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1920)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1862)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	... 95 common frames omitted
2025-02-22 14:04:55.608 [main] ERROR org.springframework.boot.SpringApplication 837 reportFailure - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'shelfCommunicationHandler' defined in file [E:\work\idea\wk2_fleet5\youicompass-plus\youicompass-admin\target\classes\com\youibot\vehicle\scheduler\modules\shelf\ShelfCommunicationHandler.class]: Initialization of bean failed; nested exception is java.lang.IllegalStateException: Need to invoke method 'locationChange' declared on target class 'ShelfCommunicationHandler', but not found in any interface(s) of the exposed proxy type. Either pull the method up to an interface or switch to CGLIB proxies by enforcing proxy-target-class mode in your configuration.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at com.youibot.vehicle.scheduler.AdminApplication.main(AdminApplication.java:26)
Caused by: java.lang.IllegalStateException: Need to invoke method 'locationChange' declared on target class 'ShelfCommunicationHandler', but not found in any interface(s) of the exposed proxy type. Either pull the method up to an interface or switch to CGLIB proxies by enforcing proxy-target-class mode in your configuration.
	at org.springframework.core.MethodIntrospector.selectInvocableMethod(MethodIntrospector.java:132)
	at org.springframework.aop.support.AopUtils.selectInvocableMethod(AopUtils.java:135)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.createRunnable(ScheduledAnnotationBeanPostProcessor.java:526)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.processScheduled(ScheduledAnnotationBeanPostProcessor.java:393)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.lambda$null$1(ScheduledAnnotationBeanPostProcessor.java:374)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.lambda$postProcessAfterInitialization$2(ScheduledAnnotationBeanPostProcessor.java:374)
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684)
	at org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor.postProcessAfterInitialization(ScheduledAnnotationBeanPostProcessor.java:373)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:430)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1798)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	... 16 common frames omitted
2025-02-22 14:05:16.622 [Thread-32] ERROR c.y.v.s.modules.shelf.ShelfCommunicationHandler 638 startAsync - shelf_connect_khshelf: host=*************, port=1000, portOpen=false
2025-02-22 14:05:16.623 [Thread-32] ERROR c.y.v.s.modules.shelf.ShelfCommunicationHandler 654 startAsync - shelf_connect_khshelf: host=*************, port=1000, portOpen=false
2025-02-22 14:05:46.402 [nioEventLoopGroup-4-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:05:47.672 [Thread-10] ERROR c.y.v.s.m.v.thread.ModbusSyncVehicleStatusThread 49 run - shelf_connect_screen_ip_port_not_open
2025-02-22 14:05:49.466 [nioEventLoopGroup-5-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:05:52.547 [nioEventLoopGroup-6-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:05:55.610 [nioEventLoopGroup-7-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:05:58.691 [nioEventLoopGroup-8-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:01.768 [nioEventLoopGroup-9-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:02.192 [Thread-32] ERROR c.y.v.s.modules.shelf.ShelfCommunicationHandler 630 startAsync - shelf_connect_khshelf: host=*************, port=1000, portOpen=false
2025-02-22 14:06:02.192 [Thread-32] ERROR c.y.v.s.modules.shelf.ShelfCommunicationHandler 646 startAsync - shelf_connect_khshelf: host=*************, port=1000, portOpen=false
2025-02-22 14:06:03.733 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:06:03.734 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:06:03.779 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:06:03.780 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:06:04.833 [nioEventLoopGroup-10-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:07.900 [nioEventLoopGroup-11-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:10.979 [nioEventLoopGroup-12-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:14.045 [nioEventLoopGroup-13-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:17.110 [nioEventLoopGroup-14-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:20.175 [nioEventLoopGroup-15-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:23.247 [nioEventLoopGroup-16-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:24.836 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:06:24.911 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:06:26.314 [nioEventLoopGroup-17-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:29.392 [nioEventLoopGroup-18-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:29.843 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:06:29.935 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:06:32.456 [nioEventLoopGroup-19-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:35.519 [nioEventLoopGroup-20-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:38.582 [nioEventLoopGroup-21-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:41.641 [nioEventLoopGroup-22-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:44.705 [nioEventLoopGroup-23-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:45.946 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:06:46.066 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:06:47.773 [nioEventLoopGroup-24-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:50.837 [nioEventLoopGroup-25-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:53.900 [nioEventLoopGroup-26-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:06:55.955 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:06:56.091 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:06:56.981 [nioEventLoopGroup-27-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:00.044 [nioEventLoopGroup-28-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:03.110 [nioEventLoopGroup-29-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:06.188 [nioEventLoopGroup-30-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:07.048 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:07:07.213 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:07:09.253 [nioEventLoopGroup-31-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:12.318 [nioEventLoopGroup-32-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:15.382 [nioEventLoopGroup-33-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:18.446 [nioEventLoopGroup-34-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:21.508 [nioEventLoopGroup-35-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:22.068 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:07:22.249 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:07:24.562 [nioEventLoopGroup-36-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:27.629 [nioEventLoopGroup-37-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:28.158 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:07:28.355 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:07:30.710 [nioEventLoopGroup-38-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:33.789 [nioEventLoopGroup-39-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:36.852 [nioEventLoopGroup-40-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:39.903 [nioEventLoopGroup-41-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:42.968 [nioEventLoopGroup-42-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:46.036 [nioEventLoopGroup-43-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:48.166 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:07:48.407 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:07:49.101 [nioEventLoopGroup-44-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:49.251 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:07:49.493 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:07:52.150 [nioEventLoopGroup-45-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:55.212 [nioEventLoopGroup-46-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:07:58.267 [nioEventLoopGroup-47-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:01.347 [nioEventLoopGroup-48-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:04.409 [nioEventLoopGroup-49-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:07.461 [nioEventLoopGroup-50-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:10.361 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:08:10.527 [nioEventLoopGroup-51-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:10.632 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:08:13.592 [nioEventLoopGroup-52-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:14.271 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:08:14.559 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:08:16.657 [nioEventLoopGroup-53-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:19.709 [nioEventLoopGroup-54-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:22.771 [nioEventLoopGroup-55-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:25.832 [nioEventLoopGroup-56-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:28.895 [nioEventLoopGroup-57-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:31.466 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:08:31.781 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:08:31.962 [nioEventLoopGroup-58-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:35.017 [nioEventLoopGroup-59-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:38.070 [nioEventLoopGroup-60-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:40.368 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:08:40.715 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:08:41.138 [nioEventLoopGroup-61-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:44.201 [nioEventLoopGroup-62-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:47.264 [nioEventLoopGroup-63-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:50.332 [nioEventLoopGroup-64-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:52.563 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:08:52.924 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:08:53.392 [nioEventLoopGroup-65-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:08:56.441 [nioEventLoopGroup-66-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:24:52.149 [nioEventLoopGroup-4-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:24:53.371 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:24:53.460 [Thread-9] ERROR c.y.v.s.m.v.thread.ModbusSyncVehicleStatusThread 49 run - shelf_connect_screen_ip_port_not_open
2025-02-22 14:24:54.881 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:24:55.213 [nioEventLoopGroup-5-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:24:56.391 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:24:57.901 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:24:58.294 [nioEventLoopGroup-6-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:24:59.411 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:25:00.920 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:25:01.357 [nioEventLoopGroup-7-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:25:02.427 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:25:03.933 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:25:04.430 [nioEventLoopGroup-8-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:25:05.442 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:25:06.950 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:25:07.509 [nioEventLoopGroup-9-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:25:08.173 [Thread-31] ERROR c.y.v.s.modules.shelf.ShelfCommunicationHandler 642 startAsync - shelf_connect_khshelf: host=*************, port=1000, portOpen=false
2025-02-22 14:25:08.173 [Thread-31] ERROR c.y.v.s.modules.shelf.ShelfCommunicationHandler 658 startAsync - shelf_connect_khshelf: host=*************, port=1000, portOpen=false
2025-02-22 14:25:08.460 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:25:09.545 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:25:09.545 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:25:09.606 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:25:09.606 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:25:09.967 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:25:10.571 [nioEventLoopGroup-10-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:25:11.477 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:25:12.986 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler 95 handleError - Unexpected error occurred in scheduled task
java.lang.NullPointerException: null
	at com.youibot.vehicle.scheduler.modules.shelf.ShelfCommunicationHandler.locationChange(ShelfCommunicationHandler.java:582)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:205)
	at com.sun.proxy.$Proxy128.locationChange(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor74.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
2025-02-22 14:25:13.650 [nioEventLoopGroup-11-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:01.482 [nioEventLoopGroup-4-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:02.853 [Thread-10] ERROR c.y.v.s.m.v.thread.ModbusSyncVehicleStatusThread 49 run - shelf_connect_screen_ip_port_not_open
2025-02-22 14:26:04.546 [nioEventLoopGroup-5-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:07.609 [nioEventLoopGroup-6-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:10.688 [nioEventLoopGroup-7-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:13.764 [nioEventLoopGroup-8-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:16.842 [nioEventLoopGroup-9-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:17.544 [Thread-32] ERROR c.y.v.s.modules.shelf.ShelfCommunicationHandler 642 startAsync - shelf_connect_khshelf: host=*************, port=1000, portOpen=false
2025-02-22 14:26:17.544 [Thread-32] ERROR c.y.v.s.modules.shelf.ShelfCommunicationHandler 658 startAsync - shelf_connect_khshelf: host=*************, port=1000, portOpen=false
2025-02-22 14:26:18.925 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:26:18.940 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:26:18.985 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:26:18.987 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:26:19.905 [nioEventLoopGroup-10-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:22.997 [nioEventLoopGroup-11-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:26.063 [nioEventLoopGroup-12-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:29.114 [nioEventLoopGroup-13-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:32.171 [nioEventLoopGroup-14-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:35.236 [nioEventLoopGroup-15-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:38.299 [nioEventLoopGroup-16-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:40.037 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:26:40.127 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:26:41.365 [nioEventLoopGroup-17-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:44.428 [nioEventLoopGroup-18-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:45.033 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:26:45.139 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:26:47.495 [nioEventLoopGroup-19-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:50.560 [nioEventLoopGroup-20-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:53.610 [nioEventLoopGroup-21-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:56.673 [nioEventLoopGroup-22-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:26:59.736 [nioEventLoopGroup-23-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:01.123 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:27:01.259 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:27:02.797 [nioEventLoopGroup-24-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:05.877 [nioEventLoopGroup-25-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:08.957 [nioEventLoopGroup-26-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:11.149 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:27:11.315 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:27:12.024 [nioEventLoopGroup-27-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:15.072 [nioEventLoopGroup-28-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:18.136 [nioEventLoopGroup-29-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:21.216 [nioEventLoopGroup-30-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:22.227 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:27:22.410 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:27:24.281 [nioEventLoopGroup-31-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:27.353 [nioEventLoopGroup-32-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:30.411 [nioEventLoopGroup-33-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:33.468 [nioEventLoopGroup-34-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:36.533 [nioEventLoopGroup-35-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:37.272 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:27:37.483 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:27:39.600 [nioEventLoopGroup-36-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:42.664 [nioEventLoopGroup-37-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:43.328 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:27:43.570 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:27:45.729 [nioEventLoopGroup-38-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:48.793 [nioEventLoopGroup-39-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:51.855 [nioEventLoopGroup-40-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:54.900 [nioEventLoopGroup-41-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:27:57.952 [nioEventLoopGroup-42-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:01.060 [nioEventLoopGroup-43-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:03.402 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:28:03.659 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:28:04.126 [nioEventLoopGroup-44-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:04.442 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:28:04.730 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:28:07.192 [nioEventLoopGroup-45-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:10.254 [nioEventLoopGroup-46-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:13.317 [nioEventLoopGroup-47-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:16.385 [nioEventLoopGroup-48-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:19.445 [nioEventLoopGroup-49-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:22.509 [nioEventLoopGroup-50-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:25.556 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:28:25.572 [nioEventLoopGroup-51-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:25.889 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:28:28.640 [nioEventLoopGroup-52-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:29.514 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:28:29.816 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:28:31.730 [nioEventLoopGroup-53-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:34.794 [nioEventLoopGroup-54-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:37.854 [nioEventLoopGroup-55-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:40.920 [nioEventLoopGroup-56-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:43.982 [nioEventLoopGroup-57-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:46.674 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:28:47.036 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:28:47.051 [nioEventLoopGroup-58-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:50.117 [nioEventLoopGroup-59-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:53.212 [nioEventLoopGroup-60-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:55.613 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:28:55.960 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:28:56.276 [nioEventLoopGroup-61-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:28:59.345 [nioEventLoopGroup-62-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:02.408 [nioEventLoopGroup-63-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:05.462 [nioEventLoopGroup-64-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:07.759 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:29:08.167 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:29:08.530 [nioEventLoopGroup-65-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:11.595 [nioEventLoopGroup-66-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:14.651 [nioEventLoopGroup-67-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:17.715 [nioEventLoopGroup-68-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:20.776 [nioEventLoopGroup-69-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:21.727 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:29:22.119 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:29:23.835 [nioEventLoopGroup-70-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:26.901 [nioEventLoopGroup-71-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:28.862 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:29:29.311 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:29:29.961 [nioEventLoopGroup-72-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:33.008 [nioEventLoopGroup-73-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:36.079 [nioEventLoopGroup-74-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:39.151 [nioEventLoopGroup-75-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:42.215 [nioEventLoopGroup-76-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:45.265 [nioEventLoopGroup-77-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:47.850 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:29:48.288 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:29:48.318 [nioEventLoopGroup-78-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:49.950 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:29:50.464 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:29:51.372 [nioEventLoopGroup-79-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:54.436 [nioEventLoopGroup-80-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:29:57.514 [nioEventLoopGroup-81-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:00.564 [nioEventLoopGroup-82-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:03.615 [nioEventLoopGroup-83-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:06.680 [nioEventLoopGroup-84-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:09.743 [nioEventLoopGroup-85-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:11.057 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:30:11.617 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:30:12.796 [nioEventLoopGroup-86-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:13.943 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:30:14.426 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:30:15.858 [nioEventLoopGroup-87-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:18.914 [nioEventLoopGroup-88-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:21.988 [nioEventLoopGroup-89-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:25.053 [nioEventLoopGroup-90-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:28.129 [nioEventLoopGroup-91-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:31.178 [nioEventLoopGroup-92-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:32.156 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:30:32.762 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:30:34.242 [nioEventLoopGroup-93-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:37.381 [nioEventLoopGroup-94-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:40.066 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:30:40.444 [nioEventLoopGroup-95-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:40.594 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:30:43.519 [nioEventLoopGroup-96-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:46.596 [nioEventLoopGroup-97-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:49.942 [nioEventLoopGroup-98-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:53.009 [nioEventLoopGroup-99-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:53.267 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:30:53.907 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:30:56.180 [nioEventLoopGroup-100-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:30:59.693 [nioEventLoopGroup-101-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:02.758 [nioEventLoopGroup-102-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:06.339 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:31:06.415 [nioEventLoopGroup-103-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:06.777 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:31:09.840 [nioEventLoopGroup-104-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:12.917 [nioEventLoopGroup-105-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:14.484 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:31:15.316 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:31:15.980 [nioEventLoopGroup-106-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:19.057 [nioEventLoopGroup-107-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:22.122 [nioEventLoopGroup-108-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:25.185 [nioEventLoopGroup-109-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:28.246 [nioEventLoopGroup-110-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:31.305 [nioEventLoopGroup-111-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:32.437 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:31:32.935 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:31:34.366 [nioEventLoopGroup-112-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:35.589 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:31:36.451 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:31:37.429 [nioEventLoopGroup-113-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:40.493 [nioEventLoopGroup-114-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:43.557 [nioEventLoopGroup-115-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:46.619 [nioEventLoopGroup-116-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:49.679 [nioEventLoopGroup-117-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:52.740 [nioEventLoopGroup-118-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:55.804 [nioEventLoopGroup-119-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:56.679 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:31:57.599 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:31:58.549 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:31:58.864 [nioEventLoopGroup-120-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:31:59.091 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:32:01.928 [nioEventLoopGroup-121-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:04.978 [nioEventLoopGroup-122-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:08.038 [nioEventLoopGroup-123-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:11.100 [nioEventLoopGroup-124-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:14.164 [nioEventLoopGroup-125-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:17.216 [nioEventLoopGroup-126-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:17.786 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:32:18.739 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:32:20.253 [nioEventLoopGroup-127-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:23.281 [nioEventLoopGroup-128-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:24.650 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:32:25.254 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:32:26.339 [nioEventLoopGroup-129-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:29.403 [nioEventLoopGroup-130-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:32.468 [nioEventLoopGroup-131-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:35.531 [nioEventLoopGroup-132-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:38.593 [nioEventLoopGroup-133-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:38.898 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:32:39.878 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:32:41.645 [nioEventLoopGroup-134-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:44.704 [nioEventLoopGroup-135-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:47.749 [nioEventLoopGroup-136-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:50.766 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:32:50.811 [nioEventLoopGroup-137-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:51.415 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:32:53.874 [nioEventLoopGroup-138-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:32:56.935 [nioEventLoopGroup-139-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:00.000 [nioEventLoopGroup-140-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:00.000 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:33:01.027 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:33:03.064 [nioEventLoopGroup-141-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:06.122 [nioEventLoopGroup-142-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:09.185 [nioEventLoopGroup-143-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:12.246 [nioEventLoopGroup-144-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:15.311 [nioEventLoopGroup-145-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:16.870 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:33:17.579 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:33:18.380 [nioEventLoopGroup-146-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:21.101 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:33:21.464 [nioEventLoopGroup-147-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:22.173 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:33:24.543 [nioEventLoopGroup-148-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:27.607 [nioEventLoopGroup-149-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:30.656 [nioEventLoopGroup-150-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:33.720 [nioEventLoopGroup-151-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:36.786 [nioEventLoopGroup-152-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:39.849 [nioEventLoopGroup-153-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:42.187 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:33:42.913 [nioEventLoopGroup-154-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:42.973 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:33:43.320 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:33:43.742 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:33:45.978 [nioEventLoopGroup-155-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:49.037 [nioEventLoopGroup-156-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:52.103 [nioEventLoopGroup-157-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:55.157 [nioEventLoopGroup-158-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:33:58.217 [nioEventLoopGroup-159-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:01.274 [nioEventLoopGroup-160-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:03.294 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:34:04.335 [nioEventLoopGroup-161-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:04.471 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:34:07.397 [nioEventLoopGroup-162-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:09.075 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:34:09.890 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:34:10.449 [nioEventLoopGroup-163-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:13.514 [nioEventLoopGroup-164-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:16.576 [nioEventLoopGroup-165-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:19.643 [nioEventLoopGroup-166-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:22.707 [nioEventLoopGroup-167-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:24.385 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:34:25.625 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:34:25.775 [nioEventLoopGroup-168-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:28.843 [nioEventLoopGroup-169-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:31.905 [nioEventLoopGroup-170-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:34.967 [nioEventLoopGroup-171-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:35.178 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:34:36.052 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:34:38.030 [nioEventLoopGroup-172-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:41.099 [nioEventLoopGroup-173-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:44.153 [nioEventLoopGroup-174-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:45.509 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:34:46.787 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:34:47.210 [nioEventLoopGroup-175-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:50.274 [nioEventLoopGroup-176-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:53.343 [nioEventLoopGroup-177-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:56.405 [nioEventLoopGroup-178-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:34:59.470 [nioEventLoopGroup-179-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:01.299 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:35:02.190 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:35:02.539 [nioEventLoopGroup-180-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:05.614 [nioEventLoopGroup-181-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:06.594 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:35:07.942 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:35:08.662 [nioEventLoopGroup-182-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:11.727 [nioEventLoopGroup-183-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:14.778 [nioEventLoopGroup-184-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:17.839 [nioEventLoopGroup-185-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:20.885 [nioEventLoopGroup-186-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:23.961 [nioEventLoopGroup-187-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:27.012 [nioEventLoopGroup-188-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:27.406 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:35:27.696 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:35:28.343 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:35:29.072 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:35:30.083 [nioEventLoopGroup-189-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:33.145 [nioEventLoopGroup-190-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:36.198 [nioEventLoopGroup-191-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:39.262 [nioEventLoopGroup-192-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:42.342 [nioEventLoopGroup-193-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:45.407 [nioEventLoopGroup-194-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:48.482 [nioEventLoopGroup-195-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:48.799 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:35:50.221 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:35:51.564 [nioEventLoopGroup-196-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:53.516 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:35:54.526 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:35:54.631 [nioEventLoopGroup-197-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:35:57.695 [nioEventLoopGroup-198-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:00.755 [nioEventLoopGroup-199-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:03.818 [nioEventLoopGroup-200-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:06.869 [nioEventLoopGroup-201-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:09.903 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:36:09.934 [nioEventLoopGroup-202-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:11.368 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:36:12.979 [nioEventLoopGroup-203-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:16.035 [nioEventLoopGroup-204-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:19.096 [nioEventLoopGroup-205-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:19.624 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:36:20.681 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:36:22.160 [nioEventLoopGroup-206-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:25.224 [nioEventLoopGroup-207-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:28.285 [nioEventLoopGroup-208-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:30.998 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:36:31.345 [nioEventLoopGroup-209-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:32.507 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:36:34.404 [nioEventLoopGroup-210-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:37.481 [nioEventLoopGroup-211-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:40.529 [nioEventLoopGroup-212-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:43.603 [nioEventLoopGroup-213-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:45.732 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:36:46.667 [nioEventLoopGroup-214-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:46.832 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:36:49.727 [nioEventLoopGroup-215-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:52.093 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:36:52.788 [nioEventLoopGroup-216-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:53.649 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:36:55.854 [nioEventLoopGroup-217-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:36:58.934 [nioEventLoopGroup-218-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:02.010 [nioEventLoopGroup-219-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:05.087 [nioEventLoopGroup-220-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:08.148 [nioEventLoopGroup-221-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:11.191 [nioEventLoopGroup-222-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:11.870 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:37:12.986 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:37:13.212 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:37:14.251 [nioEventLoopGroup-223-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:14.810 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:37:17.328 [nioEventLoopGroup-224-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:20.390 [nioEventLoopGroup-225-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:23.452 [nioEventLoopGroup-226-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:26.516 [nioEventLoopGroup-227-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:29.579 [nioEventLoopGroup-228-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:32.639 [nioEventLoopGroup-229-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:34.314 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:37:35.716 [nioEventLoopGroup-230-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:35.973 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:37:37.980 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:37:38.793 [nioEventLoopGroup-231-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:39.140 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:37:41.867 [nioEventLoopGroup-232-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:44.930 [nioEventLoopGroup-233-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:47.993 [nioEventLoopGroup-234-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:51.054 [nioEventLoopGroup-235-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:54.132 [nioEventLoopGroup-236-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:37:55.415 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:37:57.120 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:37:57.195 [nioEventLoopGroup-237-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:00.271 [nioEventLoopGroup-238-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:03.333 [nioEventLoopGroup-239-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:04.089 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:38:05.294 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:38:06.396 [nioEventLoopGroup-240-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:09.460 [nioEventLoopGroup-241-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:12.522 [nioEventLoopGroup-242-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:15.582 [nioEventLoopGroup-243-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:16.503 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:38:18.267 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:38:18.646 [nioEventLoopGroup-244-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:21.705 [nioEventLoopGroup-245-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:24.779 [nioEventLoopGroup-246-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:27.853 [nioEventLoopGroup-247-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:30.205 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:38:30.914 [nioEventLoopGroup-248-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:31.443 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:38:33.976 [nioEventLoopGroup-249-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:37.037 [nioEventLoopGroup-250-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:37.595 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:38:39.405 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:38:40.085 [nioEventLoopGroup-251-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:43.146 [nioEventLoopGroup-252-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:46.222 [nioEventLoopGroup-253-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:49.281 [nioEventLoopGroup-254-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:52.343 [nioEventLoopGroup-255-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:55.406 [nioEventLoopGroup-256-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:56.296 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:38:57.594 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:38:58.468 [nioEventLoopGroup-257-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:38:58.694 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:39:00.553 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:39:01.529 [nioEventLoopGroup-258-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:04.591 [nioEventLoopGroup-259-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:07.654 [nioEventLoopGroup-260-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:10.711 [nioEventLoopGroup-261-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:13.770 [nioEventLoopGroup-262-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:16.829 [nioEventLoopGroup-263-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:19.799 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:39:19.889 [nioEventLoopGroup-264-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:21.713 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:39:22.407 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:39:22.950 [nioEventLoopGroup-265-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:23.734 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:39:26.012 [nioEventLoopGroup-266-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:29.088 [nioEventLoopGroup-267-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:32.146 [nioEventLoopGroup-268-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:35.208 [nioEventLoopGroup-269-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:38.271 [nioEventLoopGroup-270-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:40.894 [default-status-thread] ERROR c.y.v.s.m.status.thread.DefaultStatusUpdateThread 69 run - 获取AGV总状态出错。
2025-02-22 14:39:41.332 [nioEventLoopGroup-271-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:42.868 [default-heartbeat-thread] ERROR c.y.v.s.modules.heartbeat.DefaultHeartbeatThread 63 run - 心跳检测出错, 
java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.create(AGVSocketClient.java:72)
	at com.youibot.vehicle.scheduler.common.engine.manager.socket.AGVSocketClient.createAGVClient(AGVSocketClient.java:61)
	at com.youibot.vehicle.scheduler.modules.heartbeat.DefaultHeartbeatThread.run(DefaultHeartbeatThread.java:52)
2025-02-22 14:39:44.377 [nioEventLoopGroup-272-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:47.450 [nioEventLoopGroup-273-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:48.507 [mos-status-update-thread] ERROR c.y.v.s.m.status.thread.MosStatusUpdateThread 73 run - 获取MOS上集成状态异常：Connection timed out: connect
2025-02-22 14:39:49.880 [mos-storage-info-update-thread] ERROR c.y.v.s.m.status.thread.MosStorageInfoUpdateThread 92 run - 获取MOS储位信息异常：Connection timed out: connect
2025-02-22 14:39:50.513 [nioEventLoopGroup-274-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:53.575 [nioEventLoopGroup-275-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:56.640 [nioEventLoopGroup-276-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
2025-02-22 14:39:59.701 [nioEventLoopGroup-277-1] ERROR c.y.v.scheduler.modules.netty.NettySocketClient 104 lambda$doConnect$0 - 连接服务器127.0.0.1失败！
