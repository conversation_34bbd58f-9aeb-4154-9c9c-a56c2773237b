package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019-05-13 14:25
 */
public class VehicleConstant {

    //任务状态
    public static final Integer TASK_STATUS_FREE = 1;//空闲
    public static final Integer TASK_STATUS_WORK = 2;//任务
    public static final Integer TASK_STATUS_CHARGE = 3;//充电
    public static final Integer TASK_STATUS_PARK = 4;//归位

    //异常状态
    public static final Integer ABNORMAL_STATUS_NO = 1;//无异常
    public static final Integer ABNORMAL_STATUS_WORK = 2;//工作异常
    public static final Integer ABNORMAL_STATUS_CHARGING = 3;//充电异常
    public static final Integer ABNORMAL_STATUS_HOMING = 4;//归位异常
    public static final Integer ABNORMAL_STATUS_DOCKING = 5;//对接异常

    //控制模式
    public static final Integer AUTO_CONTROL_MODE = 1;//自动模式
    public static final Integer MANUAL_CONTROL_MODE = 2;//手动模式

    //AGV统计
    public static final Integer ALL = 0;//所有
    public static final Integer DAY = 1;//天
    public static final Integer WEEK = 7;//周
    public static final Integer MONTH = 30;//月

    //AGV日志类型
    public static final Integer AGV_LOG_TYPE_ON_LINE = 1;//在线
    public static final Integer AGV_LOG_TYPE_WORKING = 2;//工作
    public static final Integer AGV_LOG_TYPE_ERROR = 3;//异常

    public static final Integer RED_LIGHT = 0;//红灯
    public static final Integer GREEN_LIGHT = 1;//绿灯

    //地图指定状态
    public static final Integer APPOINT = 0;//地图指定中
    public static final Integer APPOINT_SUCCESS = 1;//地图指定成功
    public static final Integer APPOINT_FAIL = 2;//地图指定失败

    //定位状态
    /**
     * 根据compass上传的置信度定位情况，定位成功
     */
    public static final Double LOCATION_SUCCESS = 1.0;
    /**
     * 根据compass上次的置信度定位情况，定位失败
     */
    public static final Double LOCATION_FAIL = 0.0;

    //是否可中断智能任务  0:不可中断、1:可中断
    //public static final Integer NOT_INTERRUPT = 0;//不可中断
    //public static final Integer INTERRUPT = 1;//可中断

    //agv 启用/禁用状态
    public static final Integer DISABLE = 0;//禁用
    public static final Integer ENABLE = 1;//启用

    //agv 上线状态
    public static final Integer OUTLINE = 0;//离线
    public static final Integer ONLINE = 1;//在线
    public static final Integer OFFLINE = 2;//断线

}
