package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.*;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleScope;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.LocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.VehicleScopeService;
import com.youibot.agv.scheduler.engine.pathplan.util.LogExceptionStackUtil;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.param.*;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Api(tags = "通过markId 获取路径顺序")
@RequestMapping("/api/v3/bestWayWitchMarkId")
@RestController
public class GetBestWayWithMarkIdController {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetBestWayWithMarkIdController.class);

    @Autowired
    private VehicleScopeService vehicleScopeService;
    @Autowired
    private VehiclePoolService vehiclePoolService;
    @Autowired
    private FullLocationService fullLocationService;
    @Autowired
    private MarkerService markerService;

    private TSPGeneticAlgorithm tsp = new TSPGeneticAlgorithm();
    private TSPAntColonyAlgorithm ant = new TSPAntColonyAlgorithm();
    private TSPTMS1GeneticAlgorithm tspTms1 = new TSPTMS1GeneticAlgorithm();
    private TSPTMS2GeneticAlgorithm tspTms2 = new TSPTMS2GeneticAlgorithm();
    private TSPTMS3GeneticAlgorithm tspTms3 = new TSPTMS3GeneticAlgorithm();
    private TSPTMS4GeneticAlgorithm tspTms4 = new TSPTMS4GeneticAlgorithm();
    private TSPTMS5FloorAlgorithm tspTms5 = new TSPTMS5FloorAlgorithm();
    private TMSMarkerIdsSequenceAlgorithm tmsSequence1 = new TMSMarkerIdsSequenceAlgorithm();


    @ApiOperation("通过站点获取站点路径优先级,有设置起始点")
    @PostMapping("/getBestWay")
    public Map<String, Integer> getBestWay(@RequestBody LinkedHashSet<String> set) {

        LOGGER.info("set:{}", JSON.toJSONString(set));
        List<String> list = new LinkedList<>(set);
        Map<String, Integer> result = Maps.newHashMap();
        if (list.size() == 1) {
            result.put(list.get(0), 0);
        } else {
            String[] path = getWay(list, true);
            //起始站点
            result.put(list.get(0), 0);
            for (int i = 0; i < path.length; i++) {
                result.put(path[i], i + 1);
            }
        }
        return result;
    }


    @ApiOperation("通过站点获取站点路径优先级,不设置起始点")
    @PostMapping("/getBestWayNoStart")
    public Map<String, Integer> getBestWayNoStart(@RequestBody Set<String> set) {
        LOGGER.info("set:{}", JSON.toJSONString(set));
        List<String> list = new LinkedList<>(set);
        Map<String, Integer> result = Maps.newHashMap();
        if (list.size() == 1) {
            result.put(list.get(0), 0);
        } else {
            String[] path = getWay(list, false);
            for (int i = 0; i < path.length; i++) {
                result.put(path[i], i);
            }
        }
        return result;
    }


    @ApiOperation("上下料路径规划一起完成,同时给一组上料点,一组下料点,一起给出上下料顺序")
    @PostMapping("/getLoadUnloadMaterialSequence1")
    public Map<String, Integer> getLoadUnloadMaterialSequence1(@RequestBody TMSMaterial tmsMaterial) {
        List<String> loadMaterial = tmsMaterial.getLoadMaterial();
        List<String> unloadMaterial = tmsMaterial.getUnloadMaterial();
        tspTms1.GAInit(loadMaterial, unloadMaterial);
        String[] path = tspTms1.run();
        Map<String, Integer> map = new ConcurrentHashMap<>();
        for (int i = 0; i < path.length; i++) {
            map.put(path[i], i);
        }
        return map;
    }


    @ApiOperation("同时给出一组上料点,下料点, 按顺序给出依次给出 上-下-上-下 这种顺序")
    @PostMapping("/getLoadUnloadMaterialSequence2")
    public Map<Pair<String, String>, Integer> getLoadUnloadMaterialSequence2(@RequestBody TMSMaterial tmsMaterial) {
        List<String> loadMaterial = tmsMaterial.getLoadMaterial();
        List<String> unloadMaterial = tmsMaterial.getUnloadMaterial();
        tspTms2.GAInit(loadMaterial, unloadMaterial);
        List<Pair<String, String>> path = tspTms2.run();
        Map<Pair<String, String>, Integer> map = new ConcurrentHashMap<>();
        for (int i = 0; i < path.size(); i++) {
            map.put(path.get(i), i);
        }
        return map;
    }


    @ApiOperation("同时给出两组缓冲区标记点列表,从两组标记点列表中各取一个，返回距离最近的两个点")
    @PostMapping("/getLoadUnloadMaterialSequence3")
    public Pair<String, String> getLoadUnloadMaterialSequence3(@RequestBody TMSMaterial tmsMaterial) {
        if (tmsMaterial == null) {
            throw new ExecuteException("错误的参数");
        }
        List<String> loadMaterial = tmsMaterial.getLoadMaterial();
        List<String> unloadMaterial = tmsMaterial.getUnloadMaterial();
        tspTms3.GAInit(loadMaterial, unloadMaterial);
        return tspTms3.run();
    }


    @ApiOperation("给出一组标记点和一个目标点,从该组标记点中取一个，返回距离最近的那个导航点")
    @PostMapping("/getLoadUnloadMaterialSequence4")
    public String getLoadUnloadMaterialSequence4(@RequestBody TMSMaterial4 tmsMaterial4) {
        if (tmsMaterial4 == null) {
            throw new ExecuteException("错误的参数");
        }
        List<String> loadMaterial = tmsMaterial4.getLoadMaterialList();
        String unloadMaterial = tmsMaterial4.getUnloadMaterial();
        tspTms4.GAInit(loadMaterial, unloadMaterial);
        return tspTms4.run();
    }


    @ApiOperation("提升机跨楼层运输,选择最短路径")
    @PostMapping("/getLoadUnloadMaterialSequence5")
    public Pair<String, String> getMarkerIdsSequence1(@RequestBody TMSFloor tmsFloor) {
        if (tmsFloor == null) {
            throw new ExecuteException("错误的参数");
        }
        String startMarkerId = tmsFloor.getStartMarkerId();
        String endMarkerId = tmsFloor.getEndMarkerId();
        List<Pair<String, String>> floorMarkerPairList = tmsFloor.getFloorMarkerPairList();

        Marker startMarker = MapGraphUtil.getMarkerByMarkerId(startMarkerId);
        Marker endMarker = MapGraphUtil.getMarkerByMarkerId(endMarkerId);
        if (startMarker == null || endMarker == null) {
            throw new ExecuteException("点未启用或不存在");
        }
        for (Pair<String, String> stringStringPair : floorMarkerPairList) {
            Marker floorMarkerPairFirst = MapGraphUtil.getMarkerByMarkerId(stringStringPair.first());
            Marker floorMarkerPairSecond = MapGraphUtil.getMarkerByMarkerId(stringStringPair.second());
            if (floorMarkerPairFirst == null || floorMarkerPairSecond == null) {
                throw new ExecuteException("点未启用或不存在");
            }
            if (!startMarker.getAgvMapName().equals(floorMarkerPairFirst.getAgvMapName())) {
                throw new ExecuteException("起始点与提升机起始点不在同一张地图上");
            }
            if (!endMarker.getAgvMapName().equals(floorMarkerPairSecond.getAgvMapName())) {
                throw new ExecuteException("终止点点与提升机终止点不在同一张地图上");
            }
        }
        tspTms5.GAInit(startMarkerId, floorMarkerPairList, endMarkerId);
        return tspTms5.run();
    }


    @ApiOperation("给出一个agvCode和一组的markerId, 根据markerId距离agvCode的距离进行排序，距离小的markerId在前")
    @PostMapping("/getMarkerIdsSequence")
    public Map<String, Integer> getMarkerIdsSequence(@RequestBody TMSMarkerIdSequence tmsMarkerIdSequence) {
        String agvCode = tmsMarkerIdSequence.getAgvCode();
        List<String> markerIds = tmsMarkerIdSequence.getMarkerIds();
//        VehicleLocation vehicleLocation = locationService.getVehicleLocation(agvCode);
        VehicleLocation vehicleLocation = fullLocationService.getVehicleLocation(agvCode);
        String agvLocation = null;
        if (vehicleLocation.getMarker() != null) {
            agvLocation = vehicleLocation.getMarker().getId();
        } else if (!CollectionUtils.isEmpty(vehicleLocation.getSidePaths())) {
            agvLocation = vehicleLocation.getSidePaths().get(0).getEndMarkerId();
        }
        if (agvLocation == null) {
            throw new ExecuteException("该机器人已经脱轨，请检查机器人状态");
        }

        tmsSequence1.init(agvLocation, markerIds);

        List<String> sortMarkerIds = tmsSequence1.run();
        Map<String, Integer> map = new ConcurrentHashMap<>();
        for (int i = 0; i < sortMarkerIds.size(); i++) {
            map.put(sortMarkerIds.get(i), i);
        }
        return map;
    }

    @LogOperation
    @ApiOperation("给出一个初始点markerId和一组的markerIds, 进行排序，距离初始点近的markerId在前")
    @PostMapping("/getMoreMarkerIdsSequence")
    public Map<String, Integer> getMoreMarkerIdsSequence(@RequestBody TMSSequence tmsSequence) {
        String startMarkId = tmsSequence.getStartMarkerId();
        List<String> markerIds = tmsSequence.getMarkerIds();
        LOGGER.debug("start sort =====> start:{},seq:{}", startMarkId, markerIds);
        Map<String, Integer> map = new ConcurrentHashMap<>();
        if (markerIds.size() == 1) {
            map.put(markerIds.get(0), 0);
        } else {
//            try {
//                tmsSequence1.init(startMarkId, markerIds);
//            } catch (Exception e) {
//                LogExceptionStackUtil.LogExceptionStack(e);
//            }
//            List<String> sortMarkerIds = tmsSequence1.run();
//            for (int i = 0; i < sortMarkerIds.size(); i++) {
//                map.put(sortMarkerIds.get(i), i);
//            }
            String[] path = getBestWay(startMarkId, markerIds);
            for (int i = 0; i < path.length; i++) {
                map.put(path[i], i);
            }
        }
        LOGGER.debug("end sort =====> start:{},result:{}", startMarkId, map);
        return map;
    }

    /**
     * @param startMarkerId 起点
     * @param seqList       不包括起点
     * @return
     */
    private String[] getBestWay(String startMarkerId, List<String> seqList) {
        String[] remain = seqList.toArray(new String[0]);
        ant.GAInit(startMarkerId, remain);
        String[] result = new String[0];
        try {
            result = ant.run();
        } catch (Exception e) {
            LOGGER.error("process position message error. {}", LogExceptionStackUtil.LogExceptionStack(e));
        }
        return result;
    }


    @ApiOperation("给一个markerId,根据这个markerId距离机器人列表的距离进行排序，距离小机器人的在前")
    @GetMapping("/getAgvCodesSequence/{markerId}")
    public Map<String, Integer> getAgvCodesSequence(@PathVariable("markerId") String markerId) {
        if (markerId == null) {
            throw new ExecuteException("错误的参数");
        }
        if (MapGraphUtil.getMarkerByMarkerId(markerId) == null) {
            throw new ExecuteException("错误的参数, 该参数不存在");
        }
        Map<String, Integer> map = new LinkedHashMap<>();
        List<Vehicle> vehicles = vehiclePoolService.getWaitWork();
        if (CollectionUtils.isEmpty(vehicles)) {
            return map;
        }
        // vehicles 评分后排序。
        List<VehicleScope> vehicleScopes = new ArrayList<>();
        for (Vehicle vehicle : vehicles) {
            Double distanceScope = vehicleScopeService.scopeByDistance(markerId, vehicle);
            if (distanceScope != null) {
                vehicleScopes.add(new VehicleScope(vehicle, distanceScope));
            }
        }
        //评分排序，降序
        vehicleScopes.sort(new Comparator<VehicleScope>() {
            @Override
            public int compare(VehicleScope o1, VehicleScope o2) {
                return o2.getScope().compareTo(o1.getScope());
            }
        });
        for (int i = 0; i < vehicleScopes.size(); i++) {
            map.put(vehicleScopes.get(i).getVehicle().getId(), i);
        }
        return map;
    }


    /**
     * @param list
     * @param fromStartPoint true从起始点开始 false不设置固定起始点
     * @return
     */
    private String[] getWay(List<String> list, boolean fromStartPoint) {
        String start = null;
        if (fromStartPoint) {
            //有起始点
            start = list.get(0);
            LOGGER.info("start:{}", start);
            list.remove(0);
        }
        String[] remain = list.toArray(new String[0]);
//        tsp.GAInit(start, remain);
//        return tsp.run();
        ant.GAInit(start, remain);
        String[] result = new String[0];
        try {
            result = ant.run();
        } catch (Exception e) {
            LOGGER.error("process position message error. {}", LogExceptionStackUtil.LogExceptionStack(e));
        }
        return result;
    }
}
