package com.youibot.agv.scheduler.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * 区域
 * 类名称：EventArea
 * 创建时间：2019年4月2日 下午3:39:02
 */
@Data
@ApiModel(value = "MapArea", description = "地图区域")
public class MapArea {

    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "select uuid()")
    @ApiModelProperty(value = "ID", position = 0)
    private String id;

    @ApiModelProperty(value = "区域类型 单机区域:SINGLE_AGV_AREA 通道区域：CHANNEL_AREA 禁止取消任务区域：NO_CANCEL_TASK_AREA 交管区域：TrafficArea", position = 1)
    private String areaTypeId;

    @ApiModelProperty(value = "名称", position = 2)
    private String name;

    @ApiModelProperty(value = "备注", position = 3)
    private String remark;

    @ApiModelProperty(value = "区域坐标列表", position = 4)
    private String polygon;

    @ApiModelProperty(value = "地图的ID", position = 5)
    private String agvMapId;

    @ApiModelProperty(value = "创建时间", position = 6)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间", position = 7)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Transient
    private List<Marker> areaMarkers;

    public void setPolygon(String polygon) {
        JSONArray jsonArray = JSONObject.parseArray(polygon);
        this.polygon = jsonArray.toJSONString();
    }

    public String getPolygon() {
        return this.polygon;
    }

}
