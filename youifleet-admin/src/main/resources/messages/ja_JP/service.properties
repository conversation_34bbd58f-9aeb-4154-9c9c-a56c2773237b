system.missing.parameter=パラメータが足りない
system.data.type.error=データタイプエラー
system.code.format.error=コード[%s]書式エラー
system.name.format.error=名称[%s]書式エラー
system.code.duplicate.error=[%s]値が重複しました
system.code.is.empty.error=[%s]値が未入力
system.operate.timeout=操作タイムアウト
system.directory.is.empty=現在のディレクトリ[%s]にはファイルがありません
system.directory.is.not.exists=現在のディレクトリ[%s]が存在していません
system.account.is.not.exists=アカウントは存在していません
system.account.is.already.exists=アカウントは既に存在しています
system.account.passwd.is.error=アカウントとパスワードが一致しません
system.account.old.passwd.is.error=旧パスワードが正しくありません
system.account.is.disable=アカウントは利用停止されています
system.account.has.no.permission=このアカウントにはアクセス権がありません。管理者にお問い合わせください
system.menu.config.is.error=上位メニューを自身に設定することはできません
system.menu.delete.error=先にサブメニューまたはボタンを削除してください
system.account.permission.deny=ユーザー権限が不足
system.account.token.invalid=ログインしていない又は利用停止になっています
system.db.record.duplicate.error=データベースにこのレコードは既に存在しています
system.no.avaliable.marker=ポイントセット導入使用できません
system.no.avaliable.vehicle=ロボットセット導入使用できません
system.version.is.dismatch=現在のシステムバージョンが一致しません
license.certificate.failure=ライセンスが不明なエラーが発生しました
license.certificate.not.uploaded=ライセンスがアップロードされていません!
license.certificate.validate.failed=アップロードされたライセンスデータに誤りがあります
license.certificate.expired=ライセンスの有効期限が切れています
excel.export.error=Excelファイルのエクスポートが異常
excel.import.error=Excelファイルが解析異常
excel.import.code.empty=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：コードが未入力
excel.import.name.empty=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：名称が未入力
excel.import.type.empty=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：タイプが未入力
excel.import.row.empty=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：行数が未入力
excel.import.colum.empty=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：列数が未入力
excel.import.layer.empty=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：層数が未入力
excel.import.workHeight.empty=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：作業高さが未入力
excel.import.code.exists=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：コード[{1}]が既に存在しています
excel.import.barcode.exists=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：バンコード[{1}]が既に存在しています
excel.import.usage.status.error=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：有効状態エラー又は現在のシステム言語と一致していません
excel.import.occupy.status.error=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：占用状態エラー又は現在のシステム言語と一致していません
excel.import.notice.level.error=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：通知レベルエラー又は現在のシステム言語と一致していません
excel.import.event.type.error=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：イベントタイプエラー又は現在のシステム言語と一致していません
excel.import.code.repeat=エクスポートが失敗, Excel表で重複しているコードが存在しています[{0}]
excel.import.barcode.repeat=エクスポートが失敗, Excel表で重複しているバンコードが存在しています[{0}]
excel.import.data.convert.fail=エクスポートが失敗, 第[{0}]行データ検証失敗、原因は：第[{1}]列数据書式エラー
excel.import.format.error=表の書式が正しくありません。テンプレートをエクスポートしてから再インポートしてください
excel.import.name.format.error=インポートされたファイル形式が正しくありません
language.empty=この言語がサポートしません
language.inuse=この言語現在使用しています
language.upload.file.error=アップロードされたファイル形式が正しくありません
language.upload.missing.info=言語パッケージにinfoファイルが含まれていません
language.upload.missing.service=言語パッケージにserviceファイルが含まれていません。
language.upload.missing.web=言語パッケージにwebファイルが含まれていません。
language.code.duplicate=コード[{0}]が既に存在しています
language.code.nonstandard=コード[{0}]国際化の要件を満たしていません
vehicle.batchOperation.result={0}台ロボット操作成功, {1}台ロボット操作失敗
vehicle.operation.fail=ロボット操作[%s]失敗、[%s]
vehicle.connect.fail=channelが接続していません、又は接続が中断されました
vehicle.request.timeout=リクエストタイムアウト、%s
vehicle.is.not.login.error=ロボットがまだ登録していません,先に登録してください！
vehicle.network.error=ロボット操作失敗、ロボット[%s]まだ接続していません
vehicle.code.duplicate=コード[{0}]が既に存在しています、再入力していください
vehicle.name.duplicate=名称[{0}]が既に存在しています、再入力していください
vehicle.name.pattern=現在値[{0}]はアルファベット、数字、またはアンダースコアで構成する必要があります
vehicle.network.anomaly=通信失敗, ロボットの接続状態を確認してくだ
vehicle.navigation.cancel=ロボット経路ナビゲーションがキャンセルされました
vehicle.locationMapCode.empty=地図が同期していません
vehicle.out.of.trace.error=ロボットが軌道を外れました！
vehicle.aim.marker.unreachable.error=目標ポイントが到着できません!
vehicle.controlStatus.repair.error=机器人处于检修模式，不允许进行控制模式切换（待翻译）
vehicle.empty=ロボットが存在していません
vehicle.type.empty=ロボットタイプ[%s]が存在していません
vehicle.type.bind.duplicate=ロボットタイプ[%s]バインドが重複しています
vehicle.type.excel.head.code=コード
vehicle.type.excel.head.name=名称
vehicle.type.excel.head.rotatable=回転を許可します
vehicle.group.excel.head.code=コード
vehicle.group.excel.head.name=名称
vehicle.wait.reason.marker.occupied=前方のポイント[{0}]が他のロボットに占用しています
vehicle.wait.reason.marker.inControlArea=前方のポイント[{0}]は封鎖区域内です
vehicle.wait.reason.noParkArea.occupied=前方駐車禁止エリアが他のロボットに占用しています
vehicle.wait.reason.auto.door.closed=前方自動ドーアが開いていません
vehicle.wait.reason.airshower.door.closed=前方エアシャワードアが開いていません
vehicle.wait.reason.elevator.door.closed=前方エレベーターが開いていません
vehicle.wait.reason.marker.inForbiddenArea=前方ポイント[{0}]は立入禁止区域内です
warehouse.code.empty=倉庫コードが未入力
warehouse.material.type.absent=材料コード[{0}]が存在していません
warehouse.code.duplicate=倉庫コード[{0}]が既に存在しています、再入力してください
warehouse.barcode.duplicate=バンコード[{0}]が既に存在しています、再入力してください
warehouse.start.need.less.than.end=開始の行/列/層は、終了の行/列/層より小さくなってください
warehouse.height.number.consistent=層数と作業高さデータの数は一致している必要があります
warehouse.material.type.error=物料タイプのデータ取得に失敗しました
warehouse.empty.or.disable=保管エリアが存在していません、又は無効化になりました
warehouse.status.lock=保管エリアがロックされました
warehouse.status.store=この保管エリアは保管状態です
warehouse.barcode.inuse=このバンコードは既に他の保管エリアに使用されています
warehouse.area.code.duplicate=倉庫エリアコード[{0}]が既に存在しています、再入力してください
warehouse.type.code.duplicate=タイプコード[{0}]が既に存在しています、再入力してください
warehouse.area.excel.head.code=コード
warehouse.area.excel.head.name=名称
warehouse.type.excel.head.code=コード
warehouse.type.excel.head.name=名称
warehouse.excel.head.code=コード
warehouse.excel.head.type.code=タイプコード
warehouse.excel.head.area.code=倉庫エリアコード
warehouse.excel.head.row=行数
warehouse.excel.head.colum=列数
warehouse.excel.head.layer=層数
warehouse.excel.head.work.height=作業高さ
warehouse.excel.head.work.marker=作業ポイント
warehouse.excel.head.occupy.status=占用状態
warehouse.excel.head.barcode=バンコード
warehouse.excel.head.usage.code=有効状態
warehouse.excel.head.param1=拡張プロパティ1
warehouse.excel.head.param2=拡張プロパティ2
warehouse.excel.head.param3=拡張プロパティ3
warehouse.excel.head.param4=拡張プロパティ4
warehouse.excel.head.param5=拡張プロパティ5
warehouse.excel.head.status.updatedate=在庫更新時間
warehouse.usage.status.enable=有効化
warehouse.usage.status.disable=無効化
warehouse.occupy.status.lock=ロック
warehouse.occupy.status.store=保管
warehouse.occupy.status.free=待機
statistics.cannot.gt.today=選択した日付は本日を超えることはできません
statistics.cannot.lt.one.year.ago=選択した日付は1年前の日付を指定することはできません
statistics.start.cannot.gt.end=開始時間は終了時間を超えることはできません
statistics.name.avgHandleTime=平均処理時間
statistics.name.number=数
statistics.name.other=その他
statistics.name.no.map=地図無し
statistics.name.low=低い
statistics.name.lower=やや低い
statistics.name.medium=中
statistics.name.higher=やや高い
statistics.name.high=高い
statistics.name.busy=ビジー
statistics.name.free=待機
statistics.name.abnormal=異常
statistics.name.charge=充電
statistics.name.park=駐車
statistics.name.work=作業
statistics.name.disconnect=未接続
statistics.name.wait=待ち
statistics.name.running=実行
statistics.name.total.task=タスク全体
statistics.name.create=新規作成
statistics.name.finished=完成
statistics.name.cancel=キャンセル
statistics.name.avgExecuteTime=平均実行時間
statistics.name.avgAllocateTime=平均実行時間
statistics.name.actual.rate=実稼働率
statistics.name.theory.rate=理論稼働率
statistics.name.cpu.rate.total=総使用率
statistics.name.cpu.rate.java=JAVA
statistics.name.cpu.rate.mysql=MySQL
statistics.name.cpu.rate.other=その他
statistics.name.memo.rate.total=総メモリ
statistics.name.memo.rate.java=JAVA
statistics.name.memo.rate.mysql=MySQL
statistics.name.memo.rate.other=その他
statistics.name.disk.total=総メモリ
statistics.name.disk.used=使用済み容量
statistics.name.disk.free=空き容量
statistics.name.mysql.select=Select
statistics.name.mysql.delete=Delete
statistics.name.mysql.update=Update
statistics.name.mysql.insert=Insert
statistics.name.mysql.times.select=Select
statistics.name.mysql.times.delete=Delete
statistics.name.mysql.times.update=Update
statistics.name.mysql.times.insert=Insert
statistics.unit.number=個
statistics.unit.tai=台
statistics.unit.time=回
statistics.unit.second=秒
statistics.unit.minute=分
statistics.unit.hour=時
statistics.unit.day=日
charge.station.not.exist.error=充電杭は存在しません
charge.station.connect.fail=充電杭の接続に失敗しました
charge.station.request.timeout=充電杭の要求がタイムアウトしました。ネットワーク接続を確認してください
charge.station.reset.error=充電杭リセット異常
charge.station.break_discharge.error=充電杭終端放電異常
charge.station.bind.marker.delete.error=该充电桩与充电点有绑定，请先解除绑定关系(待翻译)
charge.station.duplicate.bind.marker.error=充电桩[%s]已经绑定了点位[%s]，请重新选择(待翻译)
config.unit.day=日
config.unit.meter=メートル
config.unit.second=秒
config.value.range=設定されたデータ範囲
config.company.name=深圳YouibotRobotics株式会社
config.title.systemVersion=バージョン番号
config.remark.systemVersion=バージョン番号
config.title.ownCompany=著作権所有
config.remark.ownCompany=著作権所有
config.title.licenseCompanyName=ライセンス情報 - 会社名
config.remark.licenseCompanyName=ライセンス情報 - 会社名
config.title.licenseValidTimeRange=ライセンス情報 - 有効期限
config.remark.licenseValidTimeRange=ライセンス情報 - 有効期限
config.title.userOptLogExpireTime=操作ログ
config.remark.userOptLogExpireTime=ユーザー操作ログ保存期間
config.title.interfaceLogExpireTime=インターフェースログ
config.remark.interfaceLogExpireTime=インターフェースログ保存期間
config.title.runningLogExpireTime=実行ログ
config.remark.runningLogExpireTime=実行ログ保存期間
config.title.notificationExpireTime=メッセージ
config.remark.notificationExpireTime=メッセージ保存期間
config.title.businessDataExpireTime=業務データ
config.remark.businessDataExpireTime=業務の運用データ保存期間（タスクリストなどの業務データを含む）
config.title.reportDataExpireTime=レポートデータ
config.remark.reportDataExpireTime=集計アーカイブ後のレポートデータ保存期間
config.title.markerSpacingCheck=ポイント間隔
config.remark.markerSpacingCheck=ポイント間隔判定を有効化
config.title.markerSpacing=ポイント間隔
config.remark.markerSpacing=ポイント間隔（mm）
config.title.markerAndPathSpacingCheck=ポイントから経路までの間隔
config.remark.markerAndPathSpacingCheck=ポイントから経路までの間隔判定を有効化
config.title.markerAndPathSpacing=ポイントから経路までの間隔
config.remark.markerAndPathSpacing=ポイントから経路までの間隔（mm）
config.title.blockCheckEnable=障害物回避経路再計画
config.remark.blockCheckEnable=AMRが障害物を検出された場合、スケジューリングシステムが経路再計画、障害物がない経路を探します
config.title.blockCheckInterval=障害物回避経路再計画
config.remark.blockCheckInterval=障害物回避のトリガー時間
config.title.removeBlockInterval=障害物回避経路再計画
config.remark.removeBlockInterval=障害物リセット時間
config.title.abnormalVehicleRunPolicy=故障のAMR遭遇
config.remark.abnormalVehicleRunPolicy=前方に故障またはオフラインのAMRがある場合、AMRの実行戦略を設定します
config.title.freeVehicleRunPolicy=待機のAMR遭遇
config.remark.freeVehicleRunPolicy=前方に待機のAMRがある場合、AMRの実行戦略を設定します
config.title.workVehicleRunPolicy=ビジーのAMR遭遇
config.remark.workVehicleRunPolicy=前方にビジーのAMRがある場合、AMRの実行戦略を設定します
config.title.avoidMarkerTypes=回避ポイントのタイプ
config.remark.avoidMarkerTypes=複数AMRの経路が衝突した場合、AMRが回避できるポイントのタイプを設定します
config.title.pathApplyLength=配信経路距離
config.remark.pathApplyLength=スケジューリングシステムがAMRに配信する経路の長さであり、ネットワーク環境が悪い場合は、この値を増加させることができます
config.title.autoReleaseResource=オフライン時のリソースリリース
config.remark.autoReleaseResource=AMRが一定時間ネットから接続を中断された場合、スケジューリングシステムはそのAMRが占有していた位置とエリアを解放します
config.title.disconnectionTime=オフライン時に資源解放
config.remark.disconnectionTime=オフライン時間
config.title.occupyResourceRange=AMRバーチャル半径
config.remark.occupyResourceRange=AMRがポイントまたは経路上にいない場合、AMRの中心を円の中心とし、この値を半径として、その円に含まれるすべてのポイントを占有します
config.title.trackRadius=軌道半径
config.remark.trackRadius=AMRが最も近いポイントまたは経路との距離がこの値を超えた場合、システムはAMRが軌道を外れたと判定します
config.title.channelAvoidance=通路回避
config.remark.channelAvoidance=通路回避を有効にすると、対向するAMRが通路外で自動的に回避することが可能になります
config.title.autoDoorAdvanceLength=自動ドアの事前呼び出し
config.remark.autoDoorAdvanceLength=AMRが自動ドア前のポイントとの距離がこの値を下回った場合にドア開放を呼び出します。この値が0の場合、事前呼び出しは行われません
config.title.showerDoorAdvanceLength=エアシャワードアの事前呼び出し
config.remark.showerDoorAdvanceLength=AMRがエアシャワードア前のポイントとの距離がこの値を下回った場合にドア開放を呼び出します。この値が0の場合、事前呼び出しは行われません
config.title.elevatorAdvanceLength=エレベーターの事前呼び出し
config.remark.elevatorAdvanceLength=AMRがエレベーター前のポイントとの距離がこの値を下回った場合にドア開放を呼び出します。この値が0の場合、事前呼び出しは行われません
config.title.highPerformanceMode=高性能モード
config.remark.highPerformanceMode=大規模なAMRのスケジューリングを行う場合、高性能モードを有効にすることでスケジューリング効率を向上させることができます
config.title.highBattery=バッテリー 残量が多い（%）
config.remark.highBattery=バッテリー 残量が多い（%）
config.title.lowBattery=バッテリー 残量が少ない（%）
config.remark.lowBattery=バッテリー 残量が少ない（%）
config.title.chargeTaskTypeId=新規タスク
config.remark.chargeTaskTypeId=新規タスク
config.title.autoCharge=状態
config.remark.autoCharge=状態
config.title.parkTaskTypeId=新規タスク
config.remark.parkTaskTypeId=新規タスク
config.title.autoPark=状態
config.remark.autoPark=状態
config.title.pushCycle=プッシュインターバル
config.remark.pushCycle=この時間範囲内で1回だけプッシュを行われます
config.title.vehicleStatusPushUrl=AMR状態インターフェースアドレス
config.remark.vehicleStatusPushUrl=AMRの状態が変化した際、その情報を指定されたインターフェースアドレスにプッシュします
config.title.noticePushUrl=メッセージプッシュアドレス
config.remark.noticePushUrl=エラーメッセージが発生又は消えた際，異常データ情報を指定されたインターフェースアドレスにプッシュします
config.title.pdaVersion=pda最新バージョン
config.remark.pdaVersion=pda最新バージョン
config.title.vehicleStatusPushInterval=モニタリングでのAMR状態プッシュ間隔(ms)
config.remark.vehicleStatusPushInterval=モニタリングでのAMR状態プッシュ間隔(ms)
config.title.noticePushInterval=モニタリングメッセージプッシュ間隔(ms)
config.remark.noticePushInterval=モニタリングメッセージプッシュ間隔(ms)
config.title.mapElementPushInterval=モニタリングの地図要素状態変更プッシュ間隔(ms)
config.remark.mapElementPushInterval=モニタリングの地図要素状態変更プッシュ間隔(ms)
config.title.thirdSystemTrafficAreaReqUrl=交通管理エリアリソースリクエストアドレス
config.remark.thirdSystemTrafficAreaReqUrl=Fleetがクライアントとして動作する場合、サーバー側に交管エリアリソースをリクエストし、リクエストインターフェースアドレスを設定します
config.title.driveFreeVehicleFreeTime=待機時間
config.remark.driveFreeVehicleFreeTime=待機状態のロボットを追い出す際、ロボットは指定された待機時間に達している必要があります
config.property.is.exist.error=システム属性が既に存在しています！
config.property.type.is.duplicate.error=システムタイプ分類重複！
config.title.globalPauseExecutingArmScriptIsStop=全场暂停（待翻译）
config.remark.globalPauseExecutingArmScriptIsStop=全场暂停时正在执行机械臂动作的机器人（待翻译）
config.title.noticePushLanguageType=消息语言配置（待翻译）
config.remark.noticePushLanguageType=定义错误类型的消息推送时的语言（待翻译）
config.title.exceptionNotifyTime=通知时间(待翻译)
config.remark.exceptionNotifyTime=机器人触发急停，分配/申请不到点位、区城资源达到设定时间时发起更高等级通知(待翻译)
notice.missing.notice.config=システムにこのエラーコードの設定情報が不足しています
notice.level.common=普通
notice.level.warning=警告
notice.level.error=エラー
notice.record.status.not.close=未クロス
notice.record.status.closed=クロス済み
notice.config.excel.head.code=コード
notice.config.excel.head.level=レベル
notice.config.excel.head.source=出所
notice.config.excel.head.invalidTime=間隔時間
notice.config.excel.head.desc=説明
notice.config.excel.head.solution=対策
notice.record.excel.head.code=コード
notice.record.excel.head.level=レベル
notice.record.excel.head.source=出所
notice.record.excel.head.desc=説明
notice.record.excel.head.solution=対策
notice.record.excel.head.status=状態
notice.record.excel.head.vehicle=ロボット
notice.record.excel.head.task=タスクID
notice.record.excel.head.device=デバイスID
notice.record.excel.head.map=地図
notice.record.excel.head.create.date=作成時間
notice.record.excel.head.update.date=更新時間
notice.record.excel.head.close.date=閉じる時間
notice.record.http.request.param=異常通知状態変更HTTPプッシュ, リクエストパラメータ
notice.record.http.response.param=異常通知状態変更HTTPプッシュ, レスポンスパラメータ
notice.description.100001=base心拍数タイムアウト
notice.solution.100001=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.100002=コアボードの起動がタイムアウトしました
notice.solution.100002=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.100020=状態ランプの通信障害
notice.solution.100020=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100021=信号ランプの通信障害
notice.solution.100021=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100030=スポットレーザー1の通信タイムアウト
notice.solution.100030=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100031=スポットレーザー1故障
notice.solution.100031=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100032=スポットレーザー2通信タイムアウト
notice.solution.100032=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100033=スポットレーザー2故障
notice.solution.100033=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100034=スポットレーザー3通信タイムアウト
notice.solution.100034=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100035=スポットレーザー3故障
notice.solution.100035=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100036=スポットレーザー4通信タイムアウト
notice.solution.100036=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100037=スポットレーザー4故障
notice.solution.100037=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100050=安全レーダー1通信タイムアウト
notice.solution.100050=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100051=安全レーダー1故障
notice.solution.100051=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100052=安全レーダー2通信タイムアウト
notice.solution.100052=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100053=安全レーダー2故障
notice.solution.100053=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100054=安全レーダー3通信タイムアウト
notice.solution.100054=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100055=安全レーダー3故障
notice.solution.100055=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100056=安全レーダー4通信タイムアウト
notice.solution.100056=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100057=安全レーダー4故障
notice.solution.100057=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100100=RE拡張ボード0通信タイムアウト
notice.solution.100100=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100101=RE拡張ボード1通信タイムアウト
notice.solution.100101=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100102=RE拡張ボード2通信タイムアウト
notice.solution.100102=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100103=RE拡張ボード3通信タイムアウト
notice.solution.100103=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100104=RE拡張ボード4通信タイムアウト
notice.solution.100104=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100105=RE拡張ボード5通信タイムアウト
notice.solution.100105=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100106=RE拡張ボード6通信タイムアウト
notice.solution.100106=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100107=RE拡張ボード7通信タイムアウト
notice.solution.100107=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100108=RE拡張ボード8通信タイムアウト
notice.solution.100108=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100109=RE拡張ボード9通信タイムアウト
notice.solution.100109=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100110=RE拡張ボード10通信タイムアウト
notice.solution.100110=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100111=RE拡張ボード11通信タイムアウト
notice.solution.100111=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100112=RE拡張ボード12通信タイムアウト
notice.solution.100112=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100113=RE拡張ボード13通信タイムアウト
notice.solution.100113=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100114=RE拡張ボード14通信タイムアウト
notice.solution.100114=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.100115=RE拡張ボード15通信タイムアウト
notice.solution.100115=配線が緩んでいる可能性があります，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください
notice.description.105000=左側のドライブCAN通信障害
notice.solution.105000=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105001=左側のドライブCanノード起動タイムアウト
notice.solution.105001=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105002=左側のドライブ電源起動タイムアウト
notice.solution.105002=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105003=左側のドライブPDO構成ファイルがありません
notice.solution.105003=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105009=左側のドライブ未定義エラー
notice.solution.105009=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105010=左側のドライブエンコーダの故障
notice.solution.105010=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105011=左側のドライブの電圧が高すぎる
notice.solution.105011=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105012=左側のドライブの電圧が低すぎる
notice.solution.105012=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105013=左側のドライブ過電流
notice.solution.105013=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105014=左側のドライブの温度が高すぎる
notice.solution.105014=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105015=左側のドライブ実行誤差が大きすぎる
notice.solution.105015=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105016=左側のドライブ論理電圧異常
notice.solution.105016=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105017=左側のドライブモーター故障
notice.solution.105017=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105018=左側のドライブ故障
notice.solution.105018=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105019=左側のドライブのシステムデータエラー
notice.solution.105019=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105020=左側のドライブソフトウェア動作エラー
notice.solution.105020=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105021=左側のドライブモーター設定エラー
notice.solution.105021=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105022=左側のドライブ正方向リミットエラー
notice.solution.105022=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105023=左側のドライブ逆方向リミットエラー
notice.solution.105023=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105024=左側のドライブ超速アラーム
notice.solution.105024=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105025=左側のドライブ過負荷
notice.solution.105025=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105026=左側のドライブCAN BUSバス異常
notice.solution.105026=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105027=左側のドライブOpenCanパラメータエラー
notice.solution.105027=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105028=左側のドライブOpenCan通信異常
notice.solution.105028=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105029=左側のドライブブレーキ異常
notice.solution.105029=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105030=左侧ドライバの異常が停止しました
notice.solution.105030=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105031=左側のドライブ相電圧異常
notice.solution.105031=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105100=右側のドライブCAN通信障害
notice.solution.105100=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105101=右側のドライブCanノード起動タイムアウト
notice.solution.105101=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105102=右側のドライブ電源起動タイムアウト
notice.solution.105102=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105103=右側のドライブPDO構成ファイルがありません
notice.solution.105103=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105109=右側のドライブ未定義エラー
notice.solution.105109=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105110=右側のドライブエンコーダの故障
notice.solution.105110=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105111=右側のドライブの電圧が高すぎる
notice.solution.105111=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105112=右側のドライブの電圧が低すぎる
notice.solution.105112=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105113=右側のドライブ過電流
notice.solution.105113=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105114=右側のドライブの温度が高すぎる
notice.solution.105114=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105115=右側のドライブ実行誤差が大きすぎる
notice.solution.105115=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105116=右側のドライブ論理電圧異常
notice.solution.105116=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105117=右側のドライブモーター故障
notice.solution.105117=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105118=右側のドライブ故障
notice.solution.105118=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105119=右側のドライブのシステムデータエラー
notice.solution.105119=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105120=右側のドライブソフトウェア動作エラー
notice.solution.105120=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105121=右側のドライブモーター設定エラー
notice.solution.105121=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105122=右側のドライブ正方向リミットエラー
notice.solution.105122=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105123=右側のドライブ逆方向リミットエラー
notice.solution.105123=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105124=右側のドライブ超速アラーム
notice.solution.105124=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105125=右側のドライブ過負荷
notice.solution.105125=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105126=右側のドライブCAN BUSバス異常
notice.solution.105126=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105127=右側のドライブOpenCanパラメータエラー
notice.solution.105127=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105128=右側のドライブOpenCan通信異常
notice.solution.105128=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105129=右側のドライブブレーキ異常
notice.solution.105129=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105130=右侧ドライバの異常が停止しました
notice.solution.105130=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105131=右側のドライブ相電圧異常
notice.solution.105131=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105200=リフトドライブCAN通信障害
notice.solution.105200=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105201=リフトドライブ Canノード起動タイムアウト
notice.solution.105201=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105202=リフトドライブ原点検索タイムアウト
notice.solution.105202=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105203=リフトドライブ電源起動タイムアウト
notice.solution.105203=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105204=リフトドライブPDO配置パラメータが不足しています
notice.solution.105204=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105209=リフトドライブ未定義エラー
notice.solution.105209=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105210=リフトドライブエンコーダの故障
notice.solution.105210=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105211=リフトドライブの電圧が高すぎる
notice.solution.105211=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105212=リフトドライブの電圧が低すぎる
notice.solution.105212=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105213=リフトドライブ過電流
notice.solution.105213=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105214=リフトドライブの温度が高すぎる
notice.solution.105214=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105215=リフトドライブ実行誤差が大きすぎる
notice.solution.105215=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105216=リフトドライブ論理電圧異常
notice.solution.105216=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105217=リフトドライブモーター故障
notice.solution.105217=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105218=リフトドライブ故障
notice.solution.105218=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105219=リフトドライブのシステムデータエラー
notice.solution.105219=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105220=リフトドライブソフトウェア動作エラー
notice.solution.105220=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105221=リフトドライブモーター設定エラー
notice.solution.105221=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105222=リフトドライブ正方向リミットエラー
notice.solution.105222=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105223=リフトドライブ逆方向リミットエラー
notice.solution.105223=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105224=リフトドライブ超速アラーム
notice.solution.105224=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105225=リフトドライブ過負荷
notice.solution.105225=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105226=リフトドライブCAN BUSバス異常
notice.solution.105226=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105227=リフトドライブOpenCanパラメータエラー
notice.solution.105227=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105228=リフトドライブOpenCan通信異常
notice.solution.105228=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105229=升降ドライバの異常が停止しました
notice.solution.105229=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105230=リフトドライブ相電圧異常
notice.solution.105230=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105300=ロータリードライブCAN通信障害
notice.solution.105300=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105301=ロータリードライブ Canノード起動タイムアウト
notice.solution.105301=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105302=ロータリードライブ原点検索タイムアウト
notice.solution.105302=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105303=ロータリードライブ電源起動タイムアウト
notice.solution.105303=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105304=ロータリードライブPDO配置パラメータが不足しています
notice.solution.105304=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105309=ロータリードライブ未定義エラー
notice.solution.105309=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105310=ロータリードライブエンコーダの故障
notice.solution.105310=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105311=ロータリードライブの電圧が高すぎる
notice.solution.105311=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105312=ロータリードライブの電圧が低すぎる
notice.solution.105312=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105313=ロータリードライブ過電流
notice.solution.105313=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105314=ロータリードライブの温度が高すぎる
notice.solution.105314=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105315=ロータリードライブ実行誤差が大きすぎる
notice.solution.105315=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105316=ロータリードライブ論理電圧異常
notice.solution.105316=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105317=ロータリードライブモーター故障
notice.solution.105317=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105318=ロータリードライブ故障
notice.solution.105318=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105319=ロータリードライブのシステムデータエラー
notice.solution.105319=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105320=ロータリードライブソフトウェア動作エラー
notice.solution.105320=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105321=ロータリードライブモーター設定エラー
notice.solution.105321=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105322=ロータリードライブ正方向リミットエラー
notice.solution.105322=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105323=ロータリードライブ逆方向リミットエラー
notice.solution.105323=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105324=ロータリードライブ超速アラーム
notice.solution.105324=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105325=ロータリードライブ過負荷
notice.solution.105325=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105326=ロータリードライブCAN BUSバス異常
notice.solution.105326=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105327=ロータリードライブOpenCanパラメータエラー
notice.solution.105327=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105328=ロータリードライブOpenCan通信異常
notice.solution.105328=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105329=旋转ドライバの異常が停止しました
notice.solution.105329=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105330=ロータリードライブ相電圧異常
notice.solution.105330=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105500=BMS 通信異常
notice.solution.105500=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105501=BMS未定義エラー
notice.solution.105501=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105502=充電過電流異常
notice.solution.105502=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105503=放電過電流異常
notice.solution.105503=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105504=セル低電圧
notice.solution.105504=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105505=セル過電圧
notice.solution.105505=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105506=総体低電圧
notice.solution.105506=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105507=総体過電圧
notice.solution.105507=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105508=電圧差が充電許容電圧差上限を超えた
notice.solution.105508=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105509=セル間電圧差が上限を超えた
notice.solution.105509=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105510=セル温度が充電温度上限を超えた
notice.solution.105510=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105511=セル温度が放電温度上限を超えた
notice.solution.105511=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105512=セル温度が充電温度上限を超えた
notice.solution.105512=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105513=セル温度差が充電温度上限を超えた
notice.solution.105513=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105514=セル温度が充放電温度下限を下回った
notice.solution.105514=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105515=MOSFET温度が充電温度上限を超えた
notice.solution.105515=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105516=MOSFET温差超过放单温差上限
notice.solution.105516=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105551=bms異常（保護やサンプリング故障を含む……）
notice.solution.105551=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105552=BMSパック異常(過電圧、過電流、高低温障害などの情報を含む)
notice.solution.105552=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105610=PDO1 過電流
notice.solution.105610=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105611=PDO2 過電流
notice.solution.105611=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105612=PDO3 過電流
notice.solution.105612=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105613=PDO4 過電流
notice.solution.105613=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105614=PDO5 過電流
notice.solution.105614=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105615=PDO6 過電流
notice.solution.105615=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105616=PDO7 過電流
notice.solution.105616=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105617=総PDO電流大きすぎる
notice.solution.105617=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105700=四輪操舵，左側ステアリングモーター未定義エラー
notice.solution.105700=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105701=四輪操舵，左側ステアリングモーターソフトウェアエラー
notice.solution.105701=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105702=四輪操舵，左側ステアリングモーター過電圧
notice.solution.105702=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105703=四輪操舵，左側ステアリングモーター低電圧
notice.solution.105703=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105704=四輪操舵，左側ステアリングモーター起動エラー
notice.solution.105704=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105705=四輪操舵，左側ステアリングモーター過電流
notice.solution.105705=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105706=四輪操舵，左側ステアリングモーターエンコーダーエラー
notice.solution.105706=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105707=四輪操舵，左側ステアリングモーターの温度が高すぎる
notice.solution.105707=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105708=四輪操舵，左側ステアリングモーター基板が高温になりすぎています
notice.solution.105708=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105709=四輪操舵，左側ステアリングモーター通信タイムアウト
notice.solution.105709=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105800=四輪操舵，右側ステアリングモーター未定義エラー
notice.solution.105800=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105801=四輪操舵，右側ステアリングモーターソフトウェアエラー
notice.solution.105801=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105802=四輪操舵，右側ステアリングモーター過電圧
notice.solution.105802=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105803=四輪操舵，右側ステアリングモーター低電圧
notice.solution.105803=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105804=四輪操舵，右側ステアリングモーター起動エラー
notice.solution.105804=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105805=四輪操舵，右側ステアリングモーター過電流
notice.solution.105805=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105806=四輪操舵，右側ステアリングモーターエンコーダーエラー
notice.solution.105806=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105807=四輪操舵，右側ステアリングモーターの温度が高すぎる
notice.solution.105807=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105808=四輪操舵，右側ステアリングモーター基板が高温になりすぎています
notice.solution.105808=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105809=四輪操舵，右側ステアリングモーター通信タイムアウト
notice.solution.105809=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105900=舵輪、前輪走行ドライブCAN通信障害
notice.solution.105900=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105901=舵輪、前輪走行ドライブCanノード起動タイムアウト
notice.solution.105901=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105902=舵輪、前輪走行ドライブ電源起動タイムアウト
notice.solution.105902=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105903=舵輪、前輪走行ドライブPDO配置パラメータが不足しています
notice.solution.105903=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105909=舵輪、前輪走行ドライブ未定義エラー
notice.solution.105909=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105910=舵輪、前輪走行ドライブエンコーダの故障
notice.solution.105910=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105911=舵輪、前輪走行ドライブ電圧が高すぎる
notice.solution.105911=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105912=舵輪、前輪走行ドライブ電圧が低すぎる
notice.solution.105912=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105913=舵輪、前輪走行ドライブ過電流
notice.solution.105913=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105914=舵輪、前輪走行モーターの温度が高すぎる
notice.solution.105914=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105915=舵輪、前輪走行モーター実行誤差が大きすぎる
notice.solution.105915=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105916=舵輪、前輪走行モーター論理電圧異常
notice.solution.105916=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105917=舵輪、前輪行走モーター故障
notice.solution.105917=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105918=舵輪、前輪走行ドライブ故障
notice.solution.105918=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105919=舵輪、前輪走行ドライブシステムデータエラー
notice.solution.105919=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105920=舵輪、前輪走行ドライブソフトウェア動作エラー
notice.solution.105920=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105921=舵輪、前輪行轮モーター設定エラー
notice.solution.105921=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105922=舵輪、前輪走行モーター超速アラーム
notice.solution.105922=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105923=舵輪、前輪走行モーター過負荷
notice.solution.105923=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105924=舵輪、前輪走行ドライブCAN BUSバス異常
notice.solution.105924=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105925=舵輪、前輪走行ドライブOpenCanパラメータエラー
notice.solution.105925=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105926==舵輪、前輪走行ドライブOpenCan通信異常
notice.solution.105926=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105927=舵輪、前輪ブレーキ異常
notice.solution.105927=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105928=舵輪、前輪行走ドライバの異常が停止しました
notice.solution.105928=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.105929=舵輪、前輪走行モーター相電圧異常
notice.solution.105929=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106000=舵輪、前輪ステアリングドライブCAN通信障害
notice.solution.106000=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106001=舵輪、前輪ステアリングドライブCanノード起動タイムアウト
notice.solution.106001=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106002=舵輪、前輪ステアリングドライブ電源起動タイムアウト
notice.solution.106002=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106003=舵輪、前輪ステアリングドライブPDO配置パラメータが不足しています
notice.solution.106003=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106004=舵輪、前輪ステアリングドライブ原点検索タイムアウト
notice.solution.106004=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106009=舵輪、前輪ステアリングドライブ未定義エラー
notice.solution.106009=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106010=舵輪、前輪ステアリングドライブエンコーダの故障
notice.solution.106010=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106011=舵輪、前輪ステアリングドライブ電圧が高すぎる
notice.solution.106011=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106012=舵輪、前輪ステアリングドライブ電圧が低すぎる
notice.solution.106012=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106013=舵輪、前輪ステアリングドライブ過電流
notice.solution.106013=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106014=舵輪、前輪ステアリングモーターの温度が高すぎる
notice.solution.106014=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106015=舵輪、前輪ステアリングモーター実行誤差が大きすぎる
notice.solution.106015=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106016=舵輪、前輪ステアリングモーター論理電圧異常
notice.solution.106016=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106017=舵輪、前輪ステアリングモーター故障
notice.solution.106017=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106018=舵輪、前輪ステアリングドライブ故障
notice.solution.106018=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106019=舵輪、前輪ステアリングドライブシステムデータエラー
notice.solution.106019=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106020=舵輪、前輪ステアリングドライブソフトウェア動作エラー
notice.solution.106020=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106021=舵輪、前輪ステアリングモーター設定エラー
notice.solution.106021=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106022=舵輪、前輪ステアリングドライブ正方向リミットエラー
notice.solution.106022=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106023=舵輪、前輪ステアリングドライブ逆方向リミットエラー
notice.solution.106023=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106024=舵輪、前輪ステアリングモーター超速アラーム
notice.solution.106024=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106025=舵輪、前輪ステアリングモーター過負荷
notice.solution.106025=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106026=舵輪、前輪ステアリングドライブCAN BUS总线故障
notice.solution.106026=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106027=舵輪、前輪ステアリングドライブOpenCanパラメータエラー
notice.solution.106027=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106028=舵輪、前輪ステアリングドライブOpenCan通信異常
notice.solution.106028=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106029=舵輪、前輪ステアリングドライバの異常が停止しました
notice.solution.106029=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106030=舵輪、前輪ステアリングモーター相電圧異常
notice.solution.106030=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106100=舵輪，后轮走行ドライブCAN通信障害
notice.solution.106100=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106101=舵輪，后轮走行ドライブCanノード起動タイムアウト
notice.solution.106101=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106102=舵輪，后轮走行ドライブ電源起動タイムアウト
notice.solution.106102=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106103=舵輪，后轮走行ドライブPDO配置パラメータが不足しています
notice.solution.106103=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106109=舵輪，后轮走行ドライブ未定義エラー
notice.solution.106109=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106110=舵輪，后轮走行ドライブエンコーダの故障
notice.solution.106110=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106111=舵輪，后轮走行ドライブ電圧が高すぎる
notice.solution.106111=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106112=舵輪，后轮走行ドライブ電圧が低すぎる
notice.solution.106112=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106113=舵輪，后轮走行ドライブ過電流
notice.solution.106113=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106114=舵輪，后轮走行モーターの温度が高すぎる
notice.solution.106114=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106115=舵輪，后轮走行モーター実行誤差が大きすぎる
notice.solution.106115=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106116=舵輪，后轮走行モーター論理電圧異常
notice.solution.106116=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106117=舵輪，后轮行走モーター故障
notice.solution.106117=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106118=舵輪，后轮走行ドライブ故障
notice.solution.106118=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106119=舵輪，后轮走行ドライブシステムデータエラー
notice.solution.106119=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106120=舵輪，后轮走行ドライブソフトウェア動作エラー
notice.solution.106120=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106121=舵輪，后轮行轮モーター設定エラー
notice.solution.106121=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106122=舵輪，后轮走行モーター超速アラーム
notice.solution.106122=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106123=舵輪，后轮走行モーター過負荷
notice.solution.106123=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106124=舵輪，后轮走行ドライブCAN BUSバス異常
notice.solution.106124=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106125=舵輪，后轮走行ドライブOpenCanパラメータエラー
notice.solution.106125=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106126=舵輪，后轮走行ドライブOpenCan通信異常
notice.solution.106126=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106127=舵輪，后轮ブレーキ異常
notice.solution.106127=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106128=舵輪，后轮行走異常停止
notice.solution.106128=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106129=舵輪，后轮走行モーター相電圧異常
notice.solution.106129=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106200=舵輪，后轮ステアリングドライブCAN通信障害
notice.solution.106200=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106201=舵輪，后轮ステアリングドライブCanノード起動タイムアウト
notice.solution.106201=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106202=舵輪，后轮ステアリングドライブ電源起動タイムアウト
notice.solution.106202=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106203=舵輪，后轮ステアリングドライブPDO配置パラメータが不足しています
notice.solution.106203=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106204=舵輪，后轮ステアリングドライブ原点検索タイムアウト
notice.solution.106204=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106209=舵輪，后轮ステアリングドライブ未定義エラー
notice.solution.106209=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106210=舵輪，后轮ステアリングドライブエンコーダの故障
notice.solution.106210=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106211=舵輪，后轮ステアリングドライブ電圧が高すぎる
notice.solution.106211=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106212=舵輪，后轮ステアリングドライブ電圧が低すぎる
notice.solution.106212=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106213=舵輪，后轮ステアリングドライブ過電流
notice.solution.106213=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106214=舵輪，后轮ステアリングモーターの温度が高すぎる
notice.solution.106214=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106215=舵輪，后轮ステアリングモーター実行誤差が大きすぎる
notice.solution.106215=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106216=舵輪，后轮ステアリングモーター論理電圧異常
notice.solution.106216=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106217=舵輪，后轮ステアリングモーター故障
notice.solution.106217=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106218=舵輪，后轮ステアリングドライブ故障
notice.solution.106218=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106219=舵輪，后轮ステアリングドライブシステムデータエラー
notice.solution.106219=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106220=舵輪，后轮ステアリングドライブソフトウェア動作エラー
notice.solution.106220=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106221=舵輪，后轮ステアリングモーター設定エラー
notice.solution.106221=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106222=舵輪，后轮ステアリングドライブ正方向リミットエラー
notice.solution.106222=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106223=舵輪，后轮ステアリングドライブ逆方向リミットエラー
notice.solution.106223=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106224=舵輪，后轮ステアリングモーター超速アラーム
notice.solution.106224=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106225=舵輪，后轮ステアリングモーター過負荷
notice.solution.106225=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106226=舵輪，后轮ステアリングドライブCAN BUSバス異常
notice.solution.106226=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106227=舵輪，后轮ステアリングドライブOpenCanパラメータエラー
notice.solution.106227=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106228=舵輪，后轮ステアリングドライブOpenCan通信異常
notice.solution.106228=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106229=舵輪，后轮ステアリングドライバの異常が停止しました
notice.solution.106229=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.106230=舵輪，后轮ステアリングモーター相電圧異常
notice.solution.106230=再起動による復旧が必要です。復旧できない場合、または異常が複数回発生する場合は、アフターサービスにお問い合わせください
notice.description.110001=左側のドライブ-異常
notice.solution.110001=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110002=左側のドライブ-異常
notice.solution.110002=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110003=左側のドライブ-異常
notice.solution.110003=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110004=左側のドライブ-異常
notice.solution.110004=ドライバの異常：シャットダウンし、1時間静置してから再度ご利用ください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110005=左側のドライブ-異常
notice.solution.110005=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110006=左側のドライブ-異常
notice.solution.110006=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110007=左側のドライブ-異常
notice.solution.110007=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110008=左側のドライブ-異常
notice.solution.110008=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110009=左側のドライブ-異常
notice.solution.110009=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110010=左側のドライブ-異常
notice.solution.110010=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110011=左側のドライブ-異常
notice.solution.110011=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110012=左側のドライブ-異常
notice.solution.110012=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110013=左側のドライブ-異常
notice.solution.110013=ドライバの異常：シャットダウンし、1時間静置してから再度ご利用ください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110014=左側のドライブ-異常
notice.solution.110014=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110015=左側のドライブ-異常
notice.solution.110015=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110016=左側のドライブ-異常
notice.solution.110016=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110017=左側のドライブ-異常
notice.solution.110017=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110018=左側のドライブ-異常
notice.solution.110018=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110019=左側のドライブ-異常
notice.solution.110019=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110020=左側のドライブ-異常
notice.solution.110020=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110021=左側のドライブ-異常
notice.solution.110021=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110022=左側のドライブ-異常
notice.solution.110022=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110023=左側のドライブ-異常
notice.solution.110023=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110024=左側のドライブ-異常
notice.solution.110024=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110025=左側のドライブ-異常
notice.solution.110025=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110026=左側のドライブ-異常
notice.solution.110026=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110027=左側のドライブ-異常
notice.solution.110027=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110028=左側のドライブ-異常
notice.solution.110028=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110029=左側のドライブ-異常
notice.solution.110029=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110030=左側のドライブ-異常
notice.solution.110030=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110031=左側のドライブ-異常
notice.solution.110031=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110032=右側のドライブ-異常
notice.solution.110032=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110033=右側のドライブ-異常
notice.solution.110033=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110034=右側のドライブ-異常
notice.solution.110034=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110035=右側のドライブ-異常
notice.solution.110035=ドライバの異常：シャットダウンし、1時間静置してから再度ご利用ください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110036=右側のドライブ-異常
notice.solution.110036=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110037=右側のドライブ-異常
notice.solution.110037=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110038=右側のドライブ-異常
notice.solution.110038=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110039=右側のドライブ-異常
notice.solution.110039=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110040=右側のドライブ-異常
notice.solution.110040=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110041=右側のドライブ-異常
notice.solution.110041=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110042=右側のドライブ-異常
notice.solution.110042=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110043=右側のドライブ-異常
notice.solution.110043=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110044=右側のドライブ-異常
notice.solution.110044=ドライバの異常：シャットダウンし、1時間静置してから再度ご利用ください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110045=右側のドライブ-異常
notice.solution.110045=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110046=右側のドライブ-異常
notice.solution.110046=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110047=右側のドライブ-異常
notice.solution.110047=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110048=右側のドライブ-異常
notice.solution.110048=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110049=右側のドライブ-異常
notice.solution.110049=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110050=右側のドライブ-異常
notice.solution.110050=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110051=右側のドライブ-異常
notice.solution.110051=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110052=右側のドライブ-異常
notice.solution.110052=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110053=右側のドライブ-異常
notice.solution.110053=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110054=右側のドライブ-異常
notice.solution.110054=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110055=右側のドライブ-異常
notice.solution.110055=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110056=右側のドライブ-異常
notice.solution.110056=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110057=右側のドライブ-異常
notice.solution.110057=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110058=右側のドライブ-異常
notice.solution.110058=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110059=右側のドライブ-異常
notice.solution.110059=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110060=右側のドライブ-異常
notice.solution.110060=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110061=右側のドライブ-異常
notice.solution.110061=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110062=右側のドライブ-異常
notice.solution.110062=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110063=リフトドライブ-異常
notice.solution.110063=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110064=リフトドライブ-異常
notice.solution.110064=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110065=リフトドライブ-異常
notice.solution.110065=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110066=リフトドライブ-異常
notice.solution.110066=ドライバの異常：シャットダウンし、1時間静置してから再度ご利用ください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110067=リフトドライブ-異常
notice.solution.110067=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110068=リフトドライブ-異常
notice.solution.110068=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110069=リフトドライブ-異常
notice.solution.110069=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110070=リフトドライブ-異常
notice.solution.110070=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110071=リフトドライブ-異常
notice.solution.110071=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110072=リフトドライブ-異常
notice.solution.110072=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110073=リフトドライブ-異常
notice.solution.110073=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110074=リフトドライブ-異常
notice.solution.110074=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110075=リフトドライブ-異常
notice.solution.110075=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110076=リフトドライブ-異常
notice.solution.110076=ドライバの異常：シャットダウンし、1時間静置してから再度ご利用ください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110077=リフトドライブ-異常
notice.solution.110077=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110078=リフトドライブ-異常
notice.solution.110078=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110079=リフトドライブ-異常
notice.solution.110079=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110080=リフトドライブ-異常
notice.solution.110080=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110081=リフトドライブ-異常
notice.solution.110081=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110082=リフトドライブ-異常
notice.solution.110082=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110083=リフトドライブ-異常
notice.solution.110083=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110084=リフトドライブ-異常
notice.solution.110084=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110085=リフトドライブ-異常
notice.solution.110085=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110086=リフトドライブ-異常
notice.solution.110086=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110087=リフトドライブ-異常
notice.solution.110087=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110088=リフトドライブ-異常
notice.solution.110088=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110089=リフトドライブ-異常
notice.solution.110089=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110090=リフトドライブ-異常
notice.solution.110090=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110091=ロータリードライブ-異常
notice.solution.110091=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110092=ロータリードライブ-異常
notice.solution.110092=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110093=ロータリードライブ-異常
notice.solution.110093=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110094=ロータリードライブ-異常
notice.solution.110094=ドライバの異常：シャットダウンし、1時間静置してから再度ご利用ください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110095=ロータリードライブ-異常
notice.solution.110095=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110096=ロータリードライブ-異常
notice.solution.110096=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110097=ロータリードライブ-異常
notice.solution.110097=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110098=ロータリードライブ-異常
notice.solution.110098=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110099=ロータリードライブ-異常
notice.solution.110099=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110100=ロータリードライブ-異常
notice.solution.110100=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110101=ロータリードライブ-異常
notice.solution.110101=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110102=ロータリードライブ-異常
notice.solution.110102=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110103=ロータリードライブ-異常
notice.solution.110103=ドライバの異常：シャットダウンし、1時間静置してから再度ご利用ください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110104=ロータリードライブ-異常
notice.solution.110104=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110105=ロータリードライブ-異常
notice.solution.110105=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110106=ロータリードライブ-異常
notice.solution.110106=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110107=ロータリードライブ-異常
notice.solution.110107=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110108=ロータリードライブ-異常
notice.solution.110108=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110109=ロータリードライブ-異常
notice.solution.110109=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110110=ロータリードライブ-異常
notice.solution.110110=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110111=ロータリードライブ-異常
notice.solution.110111=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110112=ロータリードライブ-異常
notice.solution.110112=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110113=ロータリードライブ-異常
notice.solution.110113=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110114=ロータリードライブ-異常
notice.solution.110114=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110115=ロータリードライブ-異常
notice.solution.110115=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110116=ロータリードライブ-異常
notice.solution.110116=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110117=ロータリードライブ-異常
notice.solution.110117=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110118=ロータリードライブ-異常
notice.solution.110118=ドライバの異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.110200=モーター初期化-異常
notice.solution.110200=昇降モーターメッセージ送信が失敗した、走行モーターに適切なパラメータが設定されていないか、接続されていない可能性があります。再起動しても問題が解決しない場合は、アフターサービスにお問い合わせください。
notice.description.110201=モーター初期化-異常
notice.solution.110201=リフトモーターメッセージ送信が失敗した，走行モーターに適切なパラメータが設定されていないか、接続されていない可能性があります。再起動しても問題が解決しない場合は、アフターサービスにお問い合わせください。。
notice.description.110202=モーター初期化-異常
notice.solution.110202=インサートモーターメッセージ送信が失敗した，走行モーターに適切なパラメータが設定されていないか、接続されていない可能性があります。再起動しても問題が解決しない場合は、アフターサービスにお問い合わせください。。
notice.description.110203=モーター初期化-異常
notice.solution.110203=回転モーターメッセージ送信が失敗した，走行モーターに適切なパラメータが設定されていないか、接続されていない可能性があります。再起動しても問題が解決しない場合は、アフターサービスにお問い合わせください。。
notice.description.110204=モーター初期化-異常
notice.solution.110204=グリッパーモーターメッセージ送信が失敗した，走行モーターに適切なパラメータが設定されていないか、接続されていない可能性があります。再起動しても問題が解決しない場合は、アフターサービスにお問い合わせください。。
notice.description.110205=モーター初期化-異常
notice.solution.110205=森創リフトモーターメッセージ送信が失敗した，走行モーターに適切なパラメータが設定されていないか、接続されていない可能性があります。再起動しても問題が解決しない場合は、アフターサービスにお問い合わせください。。
notice.description.110206=モーター初期化-異常
notice.solution.110206=森創回転モーターメッセージ送信が失敗した，走行モーターに適切なパラメータが設定されていないか、接続されていない可能性があります。再起動しても問題が解決しない場合は、アフターサービスにお問い合わせください。。
notice.description.110207=モーター初期化-異常
notice.solution.110207=SRモーターメッセージ送信が失敗した，走行モーターに適切なパラメータが設定されていないか、接続されていない可能性があります。再起動しても問題が解決しない場合は、アフターサービスにお問い合わせください。。
notice.description.110300=モーター制御-異常
notice.solution.110300=回転指令が最大範囲を超えた：-180~180。エラーをクリアした後に、再度指令を下すことで解決できます。
notice.description.110301=モーター制御-異常
notice.solution.110301=回転指令の速度が最大範囲を超えた。エラーをクリアした後に、再度指令を下すことで解決できます。速度最大は8rpm。
notice.description.110302=モーター制御-異常
notice.solution.110302=リフト指令が最大範囲を超えた。エラーをクリアした後に、再度指令を下すことで解決できます。合理な範囲内何度も試しても成功しない場合、アフターサービスにお問い合わせてください。
notice.description.110303=モーター制御-異常
notice.solution.110303=リフト指令の速度が最大範囲を超えた。エラーをクリアした後に、再度指令を下すことで解決できます。最大速度は10mm/s。
notice.description.110400=モーター実行-異常
notice.solution.110400=緊急停止をトリガして解除する、AMRを再起動して状態を確認してください。もし問題が解決しない場合は、アフターサービスにお問い合わせください
notice.description.110401=モーター実行-異常
notice.solution.110401=緊急停止をトリガして解除する、AMRを再起動して状態を確認してください。もし問題が解決しない場合は、アフターサービスにお問い合わせください
notice.description.110402=モーター実行-異常
notice.solution.110402=緊急停止をトリガして解除する、AMRを再起動して状態を確認してください。もし問題が解決しない場合は、アフターサービスにお問い合わせください
notice.description.110403=モーター実行-異常
notice.solution.110403=緊急停止をトリガして解除する、AMRを再起動して状態を確認してください。もし問題が解決しない場合は、アフターサービスにお問い合わせください
notice.description.113001=Canカード-異常
notice.solution.113001=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113002=Canカード-異常
notice.solution.113002=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113003=Canカード-異常
notice.solution.113003=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113004=Canカード-異常
notice.solution.113004=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113005=Canカード-異常
notice.solution.113005=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113006=Canカード-異常
notice.solution.113006=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113007=Canカード-異常
notice.solution.113007=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113008=Canカード-異常
notice.solution.113008=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113009=Canカード-異常
notice.solution.113009=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113010=Canカード-異常
notice.solution.113010=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113011=Canカード-異常
notice.solution.113011=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113012=Canカード-異常
notice.solution.113012=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113013=Canカード-異常
notice.solution.113013=Canカード異常：再起動して問題が解決するか確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.113015=Canカード-異常
notice.solution.113015=Canカード異常：CANカードが接続されていないか、接続ケーブルが断線している可能性があります、再起動しても解決しない場合は，アフターサービスに連絡してください。
notice.description.113016=Canカード-異常
notice.solution.113016=Canカード異常：CANカードデバイス異常の可能性があります，再起動しても解決しない場合は，アフターサービスに連絡してください。
notice.description.113017=Canカード-異常
notice.solution.113017=Canカード異常：CANカードデバイス異常の可能性があります，再起動しても解決しない場合は，アフターサービスに連絡してください。
notice.description.114000=IMU-異常
notice.solution.114000=シリアルポートの番号を確認してください。
notice.description.114001=IMU-異常
notice.solution.114001=シリアルポートの番号を確認してください。
notice.description.114002=IMU-異常
notice.solution.114002=シリアルポートの接続状態を確認してください。
notice.description.114003=IMU-異常
notice.solution.114003=シリアルポートの接続状態及び干渉状況を確認してください。
notice.description.114004=IMU-異常
notice.solution.114004=シリアルポートの接続状態及び干渉状況を確認してください。
notice.description.120001=充電-異常
notice.solution.120001=バッテリー通信状況が悪い，又は一時通信失敗。再実行しても解決しない場合は、アフターサービスに連絡してください。
notice.description.120002=充電-異常
notice.solution.120002=再起動又は再実行しても解決しない場合、アフターサービスに連絡してください。
notice.description.120003=充電-異常
notice.solution.120003=再起動又は再実行しても解決しない場合、アフターサービスに連絡してください。
notice.description.120004=充電-異常
notice.solution.120004=再起動又は再実行しても解決しない場合、アフターサービスに連絡してください。
notice.description.120005=充電-異常
notice.solution.120005=環境の影響によって、情報の取得が失敗する可能性があります。環境を調整し、干渉のない良好な状態にしてから、もう一度タスクを試みてください，調整後再実行しても解決しない場合アフターサービスに連絡してください。
notice.description.120006=充電-異常
notice.solution.120006=環境の影響によって、情報の取得が失敗する可能性があります。環境を調整し、干渉のない良好な状態にしてから、もう一度タスクを試みてください，調整後再実行しても解決しない場合アフターサービスに連絡してください。
notice.description.120007=充電-異常
notice.solution.120007=充電ステーションとのドッキング状態を確認してください，ドッキングのパラメータを調整してください。充電ステーションは自動モードに切り替えたを確保してください。
notice.description.120008=充電-異常
notice.solution.120008=充満率を89%に下げてください、注：デフォルトは97%。もし調整後まだ問題が解決できない場合，アフターサービスに連絡してください。
notice.description.120100=bms-異常
notice.solution.120100=シリアルポートが正常かどうかを確認してください
notice.description.120101=bms-異常
notice.solution.120101=データの読み取り値を確認してください，バッテリー通信プロトコルを確認してください。
notice.description.120102=bms-異常
notice.solution.120102=データの読み取り値を確認してください，バッテリー通信プロトコルを確認してください。
notice.description.120103=bms-異常
notice.solution.120103=シリアルポートが正常かどうかを確認してください
notice.description.120104=bms-異常
notice.solution.120104=シリアルポートが正常かどうかを確認してください
notice.description.120106=bms-異常
notice.solution.120106=シリアルポートのデータ干渉をチェックしてください
notice.description.120107=bms-異常
notice.solution.120107=バッテリーの数を確認してください
notice.description.120108=bms-異常
notice.solution.120108=二重バッテリーの電圧をチェックしてください
notice.description.121001=音频-異常
notice.solution.121001=指定された音声ファイル名がAGV内に存在することを確認し、音声ファイル名の末尾に拡張子（.mp3など）を付けないように注意します。また、音声ファイルがmp3形式であることを確認してください。
notice.description.123004=Socket-異常
notice.solution.123004=APIポート番号とインターフェース番号が正しいかどうかを再確認してください
notice.description.123005=Socket-異常
notice.solution.123005=再起動して問題が解決するか確認してください，復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.123006=Socket-異常
notice.solution.123006=設定情報を取得できません，アフターサービスに連絡してください
notice.description.127001=ナビゲーション実行に異常
notice.solution.127001=ナビゲーション実行に異常：現在のタスクを停止する必要があります，アフターサービスに連絡してください。
notice.description.127002=ナビゲーション実行に異常
notice.solution.127002=ナビゲーション実行に異常：現在のタスクを停止する必要があります，AMRが脱線しているかどうかを判断し、脱線している場合はAMRを経路上に移動させてください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.127003=ナビゲーション実行に異常
notice.solution.127003=ナビゲーション実行に異常：現在のタスクを停止する必要があります，人工で定位データの存在を判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.127004=ナビゲーション実行に異常
notice.solution.127004=ナビゲーション実行に異常：現在のタスクを停止する必要があります，人工で棚の下のマーカーが正常に認識されているか判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.127005=ナビゲーション実行に異常
notice.solution.127005=ナビゲーション実行に異常：現在のタスクを停止する必要があります，レーダーやPCLに関連するエラーが存在するかどうかを判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.127006=ナビゲーション実行に異常
notice.solution.127006=ナビゲーション実行に異常：現在のタスクを停止する必要があります，モーターに関するのエラーが存在するかどうかを判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.127007=ナビゲーション実行に異常
notice.solution.127007=ナビゲーション実行に異常：現在のタスクを停止する必要があります，定位に関連するエラーが存在するか判断してください。もし存在しない場合は、レーザー定位の際にレーダーに関連するエラーがあるかを判断し、QRコード定位についてはQRコードセンサーの通信異常があるかを確認してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.127008=ナビゲーション実行に異常
notice.solution.127008=ナビゲーション実行に異常：現在のタスクを停止する必要があります，レーザーラダーのデータが正常かどうかを判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.127009=ナビゲーション実行に異常
notice.solution.127009=ナビゲーション実行に異常：現在のタスクを停止する必要があります，現在の経路周辺の人工的な特徴が遮られているかどうかを判断し、もし遮られている場合は遮りを避けてください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.127010=ナビゲーション実行に異常
notice.solution.127010=ナビゲーション実行に異常：現在のタスクを停止する必要があります，現在のレーダーローカライゼーションデータが正常かどうかを判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.127011=ナビゲーション実行に異常111
notice.solution.127011=ナビゲーション実行に異常：現在のタスクを停止する必要があります，現在のレーダーローカライゼーションデータが正常かどうかを判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。222
notice.description.127012=曲線の角度変化範囲が大きすぎます
notice.solution.127012=経路の曲がり具合を減らし、より滑らかな経路にしてください
notice.description.128001=ドッキング実行異常
notice.solution.128001=ドッキング実行異常：エラー状態を回復必要があります，現在のAMRが接続作業を実行中か、または以前に接続指示を実行した後、接続から離脱していないかを判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.128002=ドッキング実行異常
notice.solution.128002=ドッキング実行異常：エラー状態を回復必要があります，現在指定されたドッキング目標が合理的かどうかを判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.128003=ドッキング実行異常
notice.solution.128003=ドッキング実行異常：エラー状態を回復必要があります，特徴検出モジュールの実行状態を判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.128004=ドッキング実行異常
notice.solution.128004=ドッキング実行異常：エラー状態を回復必要があります，特徴検出モジュールの実行状態を判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.128005=ドッキング実行異常
notice.solution.128005=ドッキング実行異常：エラー状態を回復必要があります，特徴検出モジュールの実行状態を判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.128006=ドッキング実行異常
notice.solution.128006=ドッキング実行異常：エラー状態を回復必要があります，アフターサービスに連絡してください。
notice.description.128007=ドッキング実行異常
notice.solution.128007=ドッキング実行異常：エラー状態を回復必要があります，特徴検出モジュールの実行状態を判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.128008=ドッキング実行異常
notice.solution.128008=エラー状態を回復必要があります，定位に関連するエラーが存在するか判断してください。存在しない場合は、レーダーに関連するエラーがあるかどうかを判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.128009=ドッキング実行異常
notice.solution.128009=ドッキング実行異常：エラーステータスをクリアし、特徴検出の実行状態を判断してください。もし復旧できない場合や異常が何度も発生する場合は、アフターサービスに連絡してください
notice.description.128010=ドッキング実行異常
notice.solution.128010=QRコードの棚のアライメントに異常があります：エラー状態を回復必要があります，QRコードの棚に対してアライメントプロセス中に再度アライメントを実行したかどうかを判断してください
notice.description.128011=棚のQRコードアライメントに異常があります
notice.solution.128011=QRコードの棚のアライメントに異常があります：QRコードが照射されているか確認し、照射されている場合はQRコード認識ノードが有効になっているか、またノードのパラメータが正しく設定されているかを確認してください
notice.description.128012=棚のQRコードアライメントに異常があります
notice.solution.128012=側面アライメントが異常：エラー状態を回復必要があります，側面に対してアライメントプロセス中に再度アライメントを実行したかどうかを判断してください
notice.description.128100=側面アライメントが異常
notice.solution.128100=側面アライメントが異常：エラー状態を回復必要があります，側面に対してアライメントプロセス中に再度アライメントを実行したかどうかを判断
notice.description.128101=側面アライメントが異常
notice.solution.128101=側面接続センサーと機台の距離が範囲内にあるか確認してください
notice.description.130001=ローカライゼーション異常
notice.solution.130001=再度ローカライゼーションをしてください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.133001=特徴検出実行異常
notice.solution.133001=特徴検出実行に異常があります：人工でAMRが接続範囲内にあるか、特徴がAMRのレーダーの高さと一致しているかを判断してください。復元できません、または異常が複数回発生する場合、アフターサービスに連絡してください。
notice.description.133002=特徴検出実行異常
notice.solution.133002=特徴検出実行に異常があります：人工で類似の特徴があるかどうかを判断し、AMRの接続距離や方向を調整してください。もし復旧できない場合や異常が何度も発生する場合、アフターサービスに連絡してください。
notice.description.133003=特徴検出実行異常
notice.solution.133003=特徴検出実行に異常があります：レーザーレーダーに関するエラーがあるかどうかを判断してください。もし復旧できない場合や異常が何度も発生する場合、アフターサービスに連絡してください。
notice.description.133004=特徴検出実行異常
notice.solution.133004=特徴検出実行に異常があります：ローカライゼーションに関するエラーがあるかどうかを判断してください。もし復旧できない場合や異常が何度も発生する場合、アフターサービスに連絡してください。
notice.description.135001=マッピング実行異常
notice.solution.135001=マッピングの実行に異常があります：レーザーレーダーに関するエラーがあるかどうかを判断してください。もし復旧できない場合や異常が何度も発生する場合、アフターサービスに連絡してください。
notice.description.135002=マッピング実行異常
notice.solution.135002=マッピングの実行に異常があります：人工で描画プロセスがループを形成しているかどうかを判断してください。もしループが全く形成されていない場合、アフターサービスに連絡してください。
notice.description.140000=スクリプト異常
notice.solution.140000=スクリプト異常：socketやLua指令のパラメータ（例えば、長さやタイプなど）を確認してください。
notice.description.140001=スクリプト異常
notice.solution.140001=スクリプト異常：指定制御のスクリプトが存在しない，スクリプトの名称又はIDを確認してください。
notice.description.140002=スクリプト異常
notice.solution.140002=スクリプト異常：スクリプトは実行中，ユーザーの制御指令を実行しません。
notice.description.140003=スクリプト異常
notice.solution.140003=スクリプト異常：スクリプトのサブスレッドが正常に終了していない場合、これは致命的なバグです。開発者に連絡してください
notice.description.140004=スクリプト異常
notice.solution.140004=スクリプト異常：スクリプトの起動がタイムアウトしました。スクリプトの保存経路が正しいかどうかを確認してください。問題がない場合は、開発者に連絡してください。
notice.description.140005=スクリプト異常
notice.solution.140005=スクリプト異常：スクリプトの停止がタイムアウトしました。スクリプトがすでに終了しているかどうかを確認してください。問題がない場合は、開発者に連絡してください。
notice.description.140006=スクリプト異常
notice.solution.140006=スクリプト異常：送信された制御指令が存在しません。JSON指令のcommandを確認してください。
notice.description.140007=スクリプト異常
notice.solution.140007=スクリプト異常：指定されたスクリプト変数のアドレスが間違っています。送信されたスクリプト変数のアドレスが指定範囲【0，31】内にあるか確認してください。
notice.description.140008=スクリプト異常
notice.solution.140008=スクリプト異常：開発者に連絡してください。
notice.description.140009=スクリプト異常
notice.solution.140009=スクリプト異常：luaスクリプトにmain関数が存在するかどうかを確認してください
notice.description.140010=スクリプト異常
notice.solution.140010=スクリプト異常：luaスクリプトにexception関数が存在するかどうかを確認してください
notice.description.140011=スクリプト異常
notice.solution.140011=スクリプト異常：luaスクリプトにcancel関数が存在するかどうかを確認してください
notice.description.140012=スクリプト異常
notice.solution.140012=スクリプト異常：この状況では、設定ファイルを確認してください必要があります
notice.description.140013=スクリプト異常
notice.solution.140013=スクリプト異常：ユーザーが送信したJSONに問題があります。以下のJSONデータを確認し、開発者に連絡してください
notice.description.140014=スクリプト異常
notice.solution.140014=スクリプト異常：ユーザーが送信したJSONに問題があります。以下のJSONデータを確認し、開発者に連絡してください
notice.description.140015=スクリプト異常
notice.solution.140015=スクリプト異常：インターフェース要求の変数タイプを確認してください。
notice.description.140016=スクリプト異常
notice.solution.140016=スクリプト異常： 1.目標点が存在するか確認してください； 2.現在位置から目標点への経路があるか確認してください； 3.AMRの定位が無効になっていないか確認してください、問題はどっちでもないばあい、開発者に連絡してください。
notice.description.140017=スクリプト異常
notice.solution.140017=スクリプト異常： 1.目標点が存在するか確認してください； 2.現在位置から目標点への経路があるか確認してください； 3.AMRの定位が無効になっていないか確認してください、問題はどっちでもないばあい、開発者に連絡してください。
notice.description.140018=スクリプト異常
notice.solution.140018=スクリプト異常：compassから送信されたフィールドに問題があります，開発者に連絡してください。
notice.description.140019=スクリプト異常
notice.solution.140019=スクリプト異常：compassのsocketサービス接続中断。
notice.description.140020=スクリプト異常
notice.solution.140020=スクリプト異常：直線運動エラー，開発者に連絡してください。
notice.description.140021=スクリプト異常
notice.solution.140021=スクリプト異常：環境が接続条件を満たしているかどうかを確認してください，開発者に連絡してください。
notice.description.140022=スクリプト異常
notice.solution.140022=スクリプト異常：アルゴリズムノードが異常になっているかどうかを確認してください，開発者に連絡してください。
notice.description.140023=スクリプト異常
notice.solution.140023=スクリプト異常：スクリプトのフォーマットが正しいかどうかを確認してください，開発者に連絡してください。
notice.description.140024=スクリプト異常
notice.solution.140024=スクリプト異常：スクリプト実行中に異常を検出しました。，開発者に連絡してください。
notice.description.140025=スクリプト異常
notice.solution.140025=スクリプト異常：スクリプトナビゲーション実行異常，開発者に連絡してください。
notice.description.140026=スクリプト異常
notice.solution.140026=スクリプト異常：call新しいスクリプト異常，開発者に連絡してください。
notice.description.140027=スクリプト異常
notice.solution.140027=スクリプト異常：スクリプトの拡張子を“.lua”に正しく編集してください。
notice.description.140028=スクリプト異常
notice.solution.140028=スクリプト異常：未知異常，開発者に連絡してください。
notice.description.145000=スクリプト異常
notice.solution.145000=スクリプト異常：ロボットアームの接続がタイムアウト，ロボットアームが通電かどうかを確認してください、ネットケーブルが正常かどうかを確認してください。
notice.description.145001=スクリプト異常
notice.solution.145001=スクリプト異常：ロボットアームが接続していない，ロボットアームが通電かどうかを確認してください、ネットケーブルが正常かどうかを確認してください。
notice.description.145002=スクリプト異常
notice.solution.145002=スクリプト異常：開発者に連絡してください。
notice.description.145003=スクリプト異常
notice.solution.145003=スクリプト異常：ロボットアームに入力された制御パラメータが間違っています。プロトコルに従ってパラメータを確認してください。
notice.description.145004=スクリプト異常
notice.solution.145004=スクリプト異常：ロボットアームからのメッセージが間違っています，開発者に連絡してください。
notice.description.145005=スクリプト異常
notice.solution.145005=スクリプト異常：ロボットアームの制御命令が間違っています，プロトコルに従ってパラメータを確認してください。
notice.description.145006=スクリプト異常
notice.solution.145006=スクリプト異常：開発者に連絡してください。
notice.description.145007=スクリプト異常
notice.solution.145007=スクリプト異常：開発者に連絡してください。
notice.description.146000=スクリプト異常
notice.solution.146000=スクリプト異常：開発者に連絡してください。
notice.description.146001=スクリプト異常
notice.solution.146001=スクリプト異常：開発者に連絡してください。
notice.description.147001=音光システム-異常
notice.solution.147001=再起動しても問題が解決できない場合，アフターサービスに連絡してください
notice.description.147002=スポートレーダー-異常
notice.solution.147002=再起動しても問題が解決できない場合，アフターサービスに連絡してください
notice.description.147004=側面光電接続-異常
notice.solution.147004=ネットIPアドレスを確認してください。
notice.description.150000=レーザーレーダー異常
notice.solution.150000=ネットワークポートとIPアドレスを確認してください。
notice.description.150002=レーザーレーダー異常
notice.solution.150002=設定頻度が合理的な範囲内にあるか確認してください。
notice.description.150003=レーザーレーダー異常
notice.solution.150003=設定サンプリングレートが合理的な範囲内にあるか確認してください。
notice.description.150004=レーザーレーダー異常
notice.solution.150004=ネットワークが正常かどうか確認してください。
notice.description.150005=レーザーレーダー異常
notice.solution.150005=ネットワークが正常かどうか確認してください。
notice.description.150100=PLCクライアント異常
notice.solution.150100=ネットワークのIPアドレスとポート番号を確認してください。
notice.description.150101=PLCクライアント異常
notice.solution.150101=ネットワークのIPアドレスとポート番号を確認してください。
notice.description.150102=PLCクライアント異常
notice.solution.150102=ネットワークが正常かどうか確認してください。
notice.description.150103=PLCクライアント異常
notice.solution.150103=ネットワークが正常かどうか確認してください。
notice.description.150104=PLCクライアント異常
notice.solution.150104=緊急停止がトリガーされているか確認してください。
notice.description.150151=安全PLCクライアント異常
notice.solution.150151=ネットワークのIPアドレスとポート番号を確認してください。
notice.description.150152=安全PLCクライアント異常
notice.solution.150152=ネットワークが正常かどうか確認してください。
notice.description.150153=安全PLCクライアント異常
notice.solution.150153=ネットワークが正常かどうか確認してください。
notice.description.150154=安全PLCクライアント異常
notice.solution.150154=緊急停止がトリガーされているか確認してください。
notice.description.150155=安全PLCクライアント異常
notice.solution.150155=エンコーダの警報を確認してください。
notice.description.150300=QRコード異常
notice.solution.150300=シリアルポートの番号を確認してください。
notice.description.150301=QRコード異常
notice.solution.150301=シリアルポートの接続状態を確認してください。
notice.description.150302=QRコード異常
notice.solution.150302=シリアルポートの接続状態を確認してください。
notice.description.150303=QRコード異常
notice.solution.150303=シリアルポートの接続状態及び干渉状況を確認してください。
notice.description.150304=QRコード異常
notice.solution.150304=シリアルポートの接続状態及び干渉状況を確認してください。
notice.description.150310=QRコード異常
notice.solution.150310=カメラの接続を確認してください
notice.description.150311=QRコード異常
notice.solution.150311=カメラの接続を確認してください
notice.description.150312=QRコード異常
notice.solution.150312=カメラの接続を確認してください
notice.description.150313=QRコード異常
notice.solution.150313=カメラの接続を確認してください
notice.description.150400=3Dカメラ異常
notice.solution.150400=カメラの接続を確認してください
notice.description.150401=3Dカメラ異常
notice.solution.150401=カメラの配置を確認してください。
notice.description.150500=超音波センサ異常
notice.solution.150500=超音波センサ接続状態を確認してください。
notice.description.150501=超音波センサ異常
notice.solution.150501=超音波センサの配置が正常かどうかを確認してください。
notice.description.170001=インテグレーター異常
notice.solution.170001=インテグレーター異常：開発者に連絡してください。
notice.description.170002=インテグレーター異常
notice.solution.170002=インテグレーター警告：インテグレーターマニュアルの操作によってリセットしてください。
notice.description.170003=インテグレーター異常
notice.solution.170003=インテグレーター異常：ドライバーをリセットして故障をクリアするか、電源を切って再起動してください。
notice.description.170004=インテグレーター異常
notice.solution.170004=インテグレーター異常：ドライバーをリセットして故障をクリアするか、電源を切って再起動してください。
notice.description.170005=インテグレーター異常
notice.solution.170005=インテグレーター異常：ドライバーをリセットして故障をクリアするか、電源を切って再起動してください。
notice.description.170006=インテグレーター異常
notice.solution.170006=インテグレーター異常：ドライバーをリセットして故障をクリアするか、電源を切って再起動してください。
notice.description.170007=インテグレーター異常
notice.solution.170007=インテグレーター異常：ドライバーをリセットして故障をクリアするか、電源を切って再起動してください。
notice.description.170008=インテグレーター異常
notice.solution.170008=インテグレーター異常：緊急停止ボタンを解放すると回復します
notice.description.171001=Hikvision雲台インテグレーター異常
notice.solution.171001=Hikvision雲台異常: 1.ネットワークケーブルが緩んでいないか確認してください 2.ネットワーク通信が正常か確認してください
notice.description.171002=ソナーセンサー（インテグレーター）異常
notice.solution.171002=ソナーセンサー異常:デバイスの接続が正常かどうかを確認してください
notice.description.171003=落下防止センサー異常
notice.solution.171003=落下防止センサーに異常があります：デバイスの接続を確認してください
notice.description.171004=落下防止センサー異常
notice.solution.171004=落下防止センサーに異常があります：デバイスの接続を確認してください
notice.description.171005=落下防止センサー異常
notice.solution.171005=落下防止センサーに異常があります：デバイスの接続を確認してください
notice.description.171006=落下防止センサー異常
notice.solution.171006=落下防止センサーに異常があります：デバイスの接続を確認してください
notice.description.171007=超音波センサー（インテグレーター）異常
notice.solution.171007=超音波センサー異常:デバイスの接続を確認してください
notice.description.171008=超音波センサー（インテグレーター）異常
notice.solution.171008=超音波センサー異常:デバイスの接続を確認してください
notice.description.171009=超音波センサー（インテグレーター）異常
notice.solution.171009=超音波センサー異常:デバイスの接続を確認してください
notice.description.171010=超音波センサー（インテグレーター）異常
notice.solution.171010=超音波センサー異常:デバイスの接続を確認してください
notice.description.200017=Pilotに発送された指令がさがせません
notice.solution.200017=技術サポートに連絡してください
notice.description.200018=指令実行失敗
notice.solution.200018=技術サポートに連絡してください
notice.description.200101=緊急停止ボタン
notice.solution.200101=AMRの状態を確認してください
notice.description.200102=安全デバイス緊急停止
notice.solution.200102=AMRの状態を確認してください
notice.description.200103=冲突緊急停止
notice.solution.200103=AMRの状態を確認してください
notice.description.200104=経路ナビゲーション緊急停止
notice.solution.200104=AMRの状態を確認してください
notice.description.200105=AMRリフト緊急停止
notice.solution.200105=AMRリフト緊急停止
notice.description.200106=AMRリフトエラー
notice.solution.200106=AMRリフトエラー
notice.description.200107=AMRローラ緊急停止
notice.solution.200107=AMRローラ緊急停止
notice.description.200108=AMRローラエラー
notice.solution.200108=AMRローラエラー
notice.description.200109=AMR一時停止
notice.solution.200109=AMR一時停止
notice.description.200110=AMRマナーモード
notice.solution.200110=AMRマナーモード
notice.description.200111=AMR未ローカライゼーション
notice.solution.200111=AMR未ローカライゼーション
notice.description.200112=AMR制御モードはハードウェアノブによる切り替えしています！
notice.solution.200112=AMR制御モードはハードウェアノブによる切り替えしています！
notice.description.200113=ロボットアーム準備中
notice.solution.200113=ロボットアーム状態確認
notice.description.200114=システム内部エラー
notice.solution.200114=ロボットアーム状態確認
notice.description.200120=[未翻译]
notice.solution.200120=[未翻译]
notice.description.300001=システム内部エラー
notice.solution.300001=技術サポートに連絡してください
notice.description.300002=统计モジュールプログラム異常
notice.solution.300002=技術サポートに連絡してください
notice.description.300003=地図モジュールプログラム異常
notice.solution.300003=技術サポートに連絡してください
notice.description.300004=AMRモジュールプログラム異常
notice.solution.300004=技術サポートに連絡してください
notice.description.300005=タスクモジュールプログラム異常
notice.solution.300005=技術サポートに連絡してください
notice.description.300006=交通管理モジュールプログラム異常
notice.solution.300006=技術サポートに連絡してください
notice.description.300007=イベントモジュールプログラム異常
notice.solution.300007=技術サポートに連絡してください
notice.description.300008=エアシャワードアモジュール異常
notice.solution.300008=技術サポートに連絡してください
notice.description.300009=自動ドアモジュール異常
notice.solution.300009=技術サポートに連絡してください
notice.description.300010=エレベーターモジュール異常
notice.solution.300010=技術サポートに連絡してください
notice.description.300011=呼び出しボックスモジュール異常
notice.solution.300011=技術サポートに連絡してください
notice.description.300012=異常
notice.solution.300012=技術サポートに連絡してください
notice.description.300013=CPU利用率高い過ぎ
notice.solution.300013=技術サポートに連絡してください
notice.description.300014=メモリ資源占用率高い
notice.solution.300014=技術サポートに連絡してください
notice.description.300015=ハードディスクのリソース使用率が高すぎます
notice.solution.300015=技術サポートに連絡してください
notice.description.300016=充电桩状态模块程序异常（待翻译）
notice.solution.300016=技術サポートに連絡してください
notice.description.300101=AMR占用ポイント失敗
notice.solution.300101=AMRが現在いるポイントは他のAMRに占有されています。AMRを他の経路上に移動させてください
notice.description.300102=AMR占用エリア失敗
notice.solution.300102=AMRが現在いる単機エリアは他のAMRに占有されています。AMRを他の経路上に移動させてください
notice.description.300103=AMR占用エレベーター失敗
notice.solution.300103=AMRが現在いるエレベーターは他のAMRに占有されています。AMRを他の経路上に移動させてください
notice.description.300104=AMRが接続と中断しました
notice.solution.300104=1：AMRとサーバーのネットワーク接続が正常か確認してください； 2：AMRの電源が入っているか確認してください； 3：AMRに設定されたシステムIPおよびポートが正しいか確認してください；
notice.description.300105=AMRが軌道から外しました
notice.solution.300105=AMRのローカライゼーション状態を確認してください，ローカライゼーションが問題ない場合，AMRを他の経路上に移動させてください
notice.description.300106=AMRマナーモード
notice.solution.300106=AMR現在はマナーモードです，自動モードに切り替えてください
notice.description.300107=机器人检修控制模式（待翻译）
notice.solution.300107=机器人处于检修控制模式，请切换成自动控制模式（待翻译）
notice.description.300201=自動ドア接続失敗
notice.solution.300201=1：デバイスとサーバーのネットワーク接続が正常か確認してください； 2：デバイスの電源が入っているか確認してください； 3：地図編成画面でデバイスのIPおよびポート番号の設定が正しいか確認してください；
notice.description.300202=エアシャワードア接続失敗
notice.solution.300202=1：デバイスとサーバーのネットワーク接続が正常か確認してください； 2：デバイスの電源が入っているか確認してください； 3：地図編成画面でデバイスのIPおよびポート番号の設定が正しいか確認してください；
notice.description.300203=エレベーター接続失敗
notice.solution.300203=1：デバイスとサーバーのネットワーク接続が正常か確認してください； 2：デバイスの電源が入っているか確認してください； 3：地図編成画面でデバイスのIPおよびポート番号の設定が正しいか確認してください；
notice.description.300204=自動ドア指令を読み取り失敗
notice.solution.300204=1：デバイスとサーバーのネットワーク接続が正常か確認してください； 2：デバイスの電源が入っているか確認してください； 3：地図編成画面でデバイスの読み取りアドレス設定が正しいか確認してください；
notice.description.300205=エアシャワードア指令を読み取り失敗
notice.solution.300205=1：デバイスとサーバーのネットワーク接続が正常か確認してください； 2：デバイスの電源が入っているか確認してください； 3：地図編成画面でデバイスの読み取りアドレス設定が正しいか確認してください；
notice.description.300206=エレベーター指令を読み取り失敗
notice.solution.300206=1：デバイスとサーバーのネットワーク接続が正常か確認してください； 2：デバイスの電源が入っているか確認してください； 3：地図編成画面でデバイスの読み取りアドレス設定が正しいか確認してください；
notice.description.300207=自動ドア指令を書き込み失敗
notice.solution.300207=1：デバイスとサーバーのネットワーク接続が正常か確認してください； 2：デバイスの電源が入っているか確認してください； 3：地図編成画面でデバイスの読み取りアドレス設定が正しいか確認してください；
notice.description.300208=エアシャワードア指令を書き込み失敗
notice.solution.300208=1：デバイスとサーバーのネットワーク接続が正常か確認してください； 2：デバイスの電源が入っているか確認してください； 3：地図編成画面でデバイスの読み取りアドレス設定が正しいか確認してください；
notice.description.300209=エレベーター指令を書き込み失敗
notice.solution.300209=1：デバイスとサーバーのネットワーク接続が正常か確認してください； 2：デバイスの電源が入っているか確認してください； 3：地図編成画面でデバイスの読み取りアドレス設定が正しいか確認してください；
notice.description.300210=自動ドアが経路にバインドされていません
notice.solution.300210=地図編集画面を開いて，自動ドアに経路をバインドされてください
notice.description.300211=エアシャワードアが経路にバインドされていません
notice.solution.300211=地図編集画面を開いて，エアシャワードアに経路をバインドされてください
notice.description.300212=エレベーター未にポイントをバインドされてください
notice.solution.300212=地図編集画面を開いて，エレベーターにポイントをバインドされてください
notice.description.300213=呼び出しボックスがスケジューリングシステムに接続していない
notice.solution.300213=1：呼び出しボックスとサーバーのネットワーク接続が正常か確認してください； 2：呼び出しボックスの電源が入っているか確認してください； 3：呼び出しボックス設定ツールを使用して、呼び出しボックスのサーバーアドレス設定が正しいか確認してください；
notice.description.300214=呼び出しボックスがシステムに配置していない
notice.solution.300214=呼び出しボックスのタスクフローのイベントタイプを配置してください
notice.description.300215=呼び出しボックスに関連のタスクフローが公開していない
notice.solution.300215=呼び出しボックスにバインドのタスクフローを公開してください
notice.description.300216=呼び出しボックス番号が重複しています
notice.solution.300216=複数の呼び出しボックスが同じ呼び出しボックスIDに設定されています，呼び出しボックス設定ツールを使用して、呼び出しボックスを再設定してください
notice.description.300217=呼び出しボックスソフトウェア異常
notice.solution.300217=呼び出しボックス設定ツールを使用して呼び出しボックスに接続してください，呼び出しボックスのプログラムの問題を確認してください
notice.description.300218=呼び出しボックスハードウェア異常
notice.solution.300218=呼び出しボックス設定ツールを使用して呼び出しボックスに接続してください，呼び出しボックスのハードウェアの問題を確認してください
notice.description.300219=呼び出しボックス配置異常
notice.solution.300219=呼び出しボックス設定ツールを使用して呼び出しボックスに接続してください，呼び出しボックスの配置の問題を確認してください
notice.description.300220=呼び出しボックスバッテリー残量が少ない
notice.solution.300220=呼び出しボックスに手動充電をしてください
notice.description.300222=充电桩不存在（待翻译）
notice.solution.300222=请在设备管理界面检查充电桩是否存在（待翻译）
notice.description.300223=充电桩不可用（待翻译）
notice.solution.300223=请在设备管理界面检查充电桩状态（待翻译）
notice.description.300224=充电桩已断线（待翻译）
notice.solution.300224=请检查设备和服务器的网络连接是否正常（待翻译）
notice.description.300225=充电桩状态异常（待翻译）
notice.solution.300225=请在设备管理界面检查充电桩状态（待翻译）
notice.description.300226=充电桩放电中（待翻译）
notice.solution.300226=请在设备管理界面检查充电桩状态（待翻译）
notice.description.300227=充电点未绑定充电桩（待翻译）
notice.solution.300227=请在地图编排界面检查充电点配置（待翻译）
notice.description.300301=タスク異常
notice.solution.300301=技術サポートに連絡してください
notice.description.300302=タスクノード異常
notice.solution.300302=技術サポートに連絡してください
notice.description.300303=ロボットタイプにロボットがいない
notice.solution.300303=AMRをロボットタイプにバインドしてください
notice.description.300304=ロボット組にロボットがいない
notice.solution.300304=AMRをロボット組にバインドしてください
notice.description.300305=AMRが存在しない
notice.solution.300305=タスク詳細ポップアップとAMRリスト画面を開き、タスク新規作成で存在しないAMRが使用されていないか確認してください。
notice.description.300306=地図でAMRが存在しない
notice.solution.300306=この地図で使い可能なAMRを登録してください
notice.description.300307=このポイントが存在しない
notice.solution.300307=タスク詳細ポップアップと地図リスト画面を開き、タスク作成で存在しないポイントが使用されていないか確認してください。
notice.description.300308=目標ポイントに到達できない
notice.solution.300308=1：AMRの現在地と目標地点が同じ地図上にあるか確認してください； 2：AMRの現在地と目標地点の間に利用可能な経路があるか確認してください；
notice.description.300309=不正なAMRを制御しています
notice.solution.300309=1：このノードで制御するAMRが指定されていない場合、このノードの前に「動的にAMRを割り当てる」または「特定のAMRを割り当てる」ノードを1つだけ実行する必要があります； 2：このノードで制御するAMRが指定されている場合、このノードで指定されたAMRは、「動的にAMRを割り当てる」または「特定のAMRを割り当てる」ノードの出力パラメータである必要があります；
notice.description.300310=ARM指令発送失敗
notice.solution.300310=AMRとネットの接続を確認してください；
notice.description.300311=AMRが指令を受け取れません
notice.solution.300311=1：今AMRは自動モードになるかどうかを確認してください； 2：緊急停止状態を確認してください； 3：AMRの接続状態を確認してください； 4：AMRの異常状態を確認してください；
notice.description.300312=AMRは指令を実行失敗
notice.solution.300312=技術サポートに連絡してください
notice.description.300313=使えるポイントはありません
notice.solution.300313=1：このノードで地図が指定されていますが、その地図上にポイントが見つかりません； 2：このノードでポイントタイプが指定されていますが、ポイントが見つかりません；
notice.description.300314=データ形式の変換に失敗しました
notice.solution.300314=タスク詳細画面でノードパラメータの入力値を確認してください
notice.description.300315=PLCと接続できません
notice.solution.300315=1：PLCとサーバーのネットワークの接続状態を確認してください； 2：PLCの電源を確認してください； 3：ノードに設定されたPLCのアドレスとポートが正しいかどうか確認してください；
notice.description.300316=レジスタの値が範囲外です
notice.solution.300316=タスク詳細を開き、タスク作成時に範囲外のレジスタ値が使用されていないか確認してください
notice.description.300317=ポイントの属性がありません
notice.solution.300317=地図編集を開き、そのポイントの属性を確認してください
notice.description.300318=保管場所が存在しない又は有効化されていません
notice.solution.300318=保管場所ページを開き、タスク作成時に存在しないか未有効化の保管場所が使用されていないか確認してください
notice.description.300319=保管エリアが存在していません
notice.solution.300319=保管場所ページを開き、タスク作成時に存在しないか未有効化の保管エリアが使用されていないか確認してください
notice.description.300320=保管エリアタイプが存在していません
notice.solution.300320=保管場所ページを開き、タスク作成時に存在しないか未有効化の保管エリアタイプが使用されていないか確認してください
notice.description.300321=使用可能な保管場所がありません
notice.solution.300321=保管場所ページを開き, タスク選択したの保管場所を確認してください
notice.description.300322=バンコードが存在していません
notice.solution.300322=保管場所ページを開き, タスク作成した時のバンコードを確認してください
notice.description.300323=ポイントの隣接ポイントが唯一ではありません
notice.solution.300323=タスク作成時に選択されたポイントに隣接ポイントがないか、または複数の隣接ポイントが存在しないか確認してください
notice.description.300324=バンコードが既に存在しています
notice.solution.300324=保管場所ページを開き, 同じバンコードを使っているのかを確認してください
notice.description.300325=保管場所が占用されました
notice.solution.300325=保管場所ページを開き, この保管場所の占用状態を確認してください
notice.description.300326=Httpリクエスト異常
notice.solution.300326=1：HTTPリクエストアドレスの設定が正しいかどうか確認してください； 2：デバイスとサーバーのネットワーク接続が正常か確認してください；
notice.description.300327=この番号に複数の条件に一致するポイントが存在します
notice.solution.300327=タスク作成時に選択されたポイントが複数存在しないか確認してください
notice.description.300328=条件に一致する保管場所が見つかりません
notice.solution.300328=保管場所ページを開き, タスク選択したの保管場所を確認してください
notice.description.300329=立入禁止エリアが存在していません
notice.solution.300329=タスクフローの詳細ページを開き、対応するノードに既存の立入禁止エリアを入力してください
notice.description.300330=このエリアに操作は禁止
notice.solution.300330=タスクフローの詳細ページを開き、対応するノードに操作可能なエリアコードを入力してください
notice.description.300331=机器人充电停靠偏离角度过大（待翻译）
notice.solution.300331=请打开地图编辑页面，调整对应点位的偏移角度（待翻译）
notice.description.300332=作业任务不能停止（待翻译）
notice.solution.300332=请检查机器人执行的任务是否为作业任务（待翻译）
notice.description.300401=AMRのナビゲーションが衝突し、使用可能な回避ポイントがありません
notice.solution.300401=技術サポートに連絡してください
notice.description.300501=地図ファイルのディスクへの書き込みに失敗しました
notice.solution.300501=該当ディスクディレクトリのアクセス権限を再設定してください
notice.description.300502=ディスクから地図ファイルの読み取りに失敗しました
notice.solution.300502=該当ディスクディレクトリのアクセス権限を再設定してください
notice.description.570001=机械臂通用失敗
notice.solution.570001=MOSの開発者に連絡してください
notice.description.570002=机械臂接口参数错误
notice.solution.570002=MOSの開発者に連絡してください
notice.description.570003=未兼容的指令接口
notice.solution.570003=MOSの開発者に連絡してください
notice.description.570004=ロボット接続失敗
notice.solution.570004=MOSの開発者に連絡してください
notice.description.570005=ロボットアームsocket通讯消息收发異常
notice.solution.570005=MOSの開発者に連絡してください
notice.description.570006=Socket断开连接
notice.solution.570006=MOSの開発者に連絡してください
notice.description.570007=创建请求失敗
notice.solution.570007=MOSの開発者に連絡してください
notice.description.570008=请求相关的内部变量出错
notice.solution.570008=MOSの開発者に連絡してください
notice.description.570009=请求超时
notice.solution.570009=MOSの開発者に連絡してください
notice.description.570010=发送リクエスト情報失敗
notice.solution.570010=MOSの開発者に連絡してください
notice.description.570011=响应信息为空
notice.solution.570011=MOSの開発者に連絡してください
notice.description.570012=响应信息header不符
notice.solution.570012=MOSの開発者に連絡してください
notice.description.570013=解析响应失敗
notice.solution.570013=MOSの開発者に連絡してください
notice.description.570014=正解出错
notice.solution.570014=终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务。
notice.description.570015=逆解出错
notice.solution.570015=终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务。
notice.description.570016=工具标定出错
notice.solution.570016=MOSの開発者に連絡してください
notice.description.570017=工具标定参数有错
notice.solution.570017=MOSの開発者に連絡してください
notice.description.570018=坐标系标定失敗
notice.solution.570018=MOSの開発者に連絡してください
notice.description.570019=基坐标系转ユーザー座标失敗
notice.solution.570019=MOSの開発者に連絡してください
notice.description.570020=ユーザー坐标系转基座标失敗
notice.solution.570020=MOSの開発者に連絡してください
notice.description.570021=机器人上电失敗
notice.solution.570021=MOSの開発者に連絡してください
notice.description.570022=机器人断电失敗
notice.solution.570022=MOSの開発者に連絡してください
notice.description.570023=机器人使能失敗
notice.solution.570023=MOSの開発者に連絡してください
notice.description.570024=机器人下使能失敗
notice.solution.570024=MOSの開発者に連絡してください
notice.description.570025=机器人复位失敗
notice.solution.570025=再次点击复位按钮进行复位，如果还失敗，请取出示教器，清除报错。
notice.description.570026=机器人暂停失敗
notice.solution.570026=MOSの開発者に連絡してください
notice.description.570027=机器人停止失敗
notice.solution.570027=拍下急停按钮，取下机械臂抓手上的物料，上电，使能，手动将机械臂回到原点后，恢复任务。
notice.description.570028=机器人状態获取失敗
notice.solution.570028=终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务。
notice.description.570029=机器人编码器状態同步失敗
notice.solution.570029=MOSの開発者に連絡してください
notice.description.570030=机器人模式不正确
notice.solution.570030=MOSの開発者に連絡してください
notice.description.570031=机器人JOG运动失敗
notice.solution.570031=MOSの開発者に連絡してください
notice.description.570032=机器人拖动示教设置失敗
notice.solution.570032=MOSの開発者に連絡してください
notice.description.570033=机器人速度设置失敗
notice.solution.570033=MOSの開発者に連絡してください
notice.description.570034=机器人路点清除失敗
notice.solution.570034=MOSの開発者に連絡してください
notice.description.570035=机器人当前坐标系获取失敗
notice.solution.570035=MOSの開発者に連絡してください
notice.description.570036=机器人坐标系设置失敗
notice.solution.570036=MOSの開発者に連絡してください
notice.description.570037=机器人重量重心设置失敗
notice.solution.570037=MOSの開発者に連絡してください
notice.description.570038=机器人IO设置失敗
notice.solution.570038=MOSの開発者に連絡してください
notice.description.570039=机器人TCP设置失敗
notice.solution.570039=MOSの開発者に連絡してください
notice.description.570040=机器人TCP获取失敗
notice.solution.570040=MOSの開発者に連絡してください
notice.description.570041=move指令阻塞等待超时
notice.solution.570041=终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，检查超时原因。1.如因为路径点轨迹融合半径太大，导致运动不到位，可适当将对应步骤的速度降低。然后单机测试运行该任务，如果不会再报超时，即可恢复任务2.如因为触发防护性停止导致机械臂暂停时间超过1分钟，可以直接恢复任务。
notice.description.570042=运动相关的内部变量出错
notice.solution.570042=MOSの開発者に連絡してください
notice.description.570043=运动请求失敗
notice.solution.570043=MOSの開発者に連絡してください
notice.description.570044=生成运动请求失敗
notice.solution.570044=MOSの開発者に連絡してください
notice.description.570045=运动被イベント中断
notice.solution.570045=终止当前任务，取下机械臂抓手上的物料，点击复位按钮进行复位，如果无法复位，请使用示教器清除错误。清错后重新上电、使能，手动将机械臂回到原点后，恢复任务。
notice.description.570046=运动相关的路点容器的长度不符合规定
notice.solution.570046=MOSの開発者に連絡してください
notice.description.570047=服务器响应返回错误
notice.solution.570047=MOSの開発者に連絡してください
notice.description.570048=真实机械臂不存在
notice.solution.570048=MOSの開発者に連絡してください
notice.description.570049=调用缓停接口失敗
notice.solution.570049=MOSの開発者に連絡してください
notice.description.570050=调用急停接口失敗
notice.solution.570050=MOSの開発者に連絡してください
notice.description.570051=调用暂停接口失敗
notice.solution.570051=MOSの開発者に連絡してください
notice.description.570052=调用继续接口失敗
notice.solution.570052=MOSの開発者に連絡してください
notice.description.570053=运动被条件中断
notice.solution.570053=终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，检查失效条件。如果是非传感器自身原图导致的条件失效，请恢复任务。如果是传感器问题，请联系售后人员解决。
notice.description.570054=运动被手动中断
notice.solution.570054=MOSの開発者に連絡してください
notice.description.571001=关节运动属性配置错误
notice.solution.571001=MOSの開発者に連絡してください
notice.description.571002=直线运动属性配置错误
notice.solution.571002=MOSの開発者に連絡してください
notice.description.571003=轨迹运动属性配置错误
notice.solution.571003=MOSの開発者に連絡してください
notice.description.571004=无效的运动属性配置
notice.solution.571004=MOSの開発者に連絡してください
notice.description.571005=等待机器人停止
notice.solution.571005=MOSの開発者に連絡してください
notice.description.571006=超出关节运动范围
notice.solution.571006=MOSの開発者に連絡してください
notice.description.571007=请正确设置MODEP第一个路点
notice.solution.571007=MOSの開発者に連絡してください
notice.description.571008=传送带跟踪配置错误
notice.solution.571008=MOSの開発者に連絡してください
notice.description.571009=传送带轨迹类型错误
notice.solution.571009=MOSの開発者に連絡してください
notice.description.571010=相对坐标变换逆解失敗
notice.solution.571010=MOSの開発者に連絡してください
notice.description.571011=示教模式发生碰撞
notice.solution.571011=MOSの開発者に連絡してください
notice.description.571012=运动属性配置错误
notice.solution.571012=MOSの開発者に連絡してください
notice.description.571101=轨迹異常
notice.solution.571101=MOSの開発者に連絡してください
notice.description.571102=轨迹规划错误
notice.solution.571102=MOSの開発者に連絡してください
notice.description.571103=二型在线轨迹规划失敗
notice.solution.571103=MOSの開発者に連絡してください
notice.description.571104=逆解失敗
notice.solution.571104=MOSの開発者に連絡してください
notice.description.571105=动力学限制保护
notice.solution.571105=MOSの開発者に連絡してください
notice.description.571106=传送带跟踪失敗
notice.solution.571106=MOSの開発者に連絡してください
notice.description.571107=超出传送带工作范围
notice.solution.571107=MOSの開発者に連絡してください
notice.description.571108=关节超出范围
notice.solution.571108=终止当前任务，取下机械臂抓手上的物料。检查MOS错误日志，查看是哪个点导致的关节超限，修改该点位后，将机械臂回到原点，重新开始任务。
notice.description.571109=关节超速
notice.solution.571109=MOSの開発者に連絡してください
notice.description.571110=离线轨迹规划失敗
notice.solution.571110=MOSの開発者に連絡してください
notice.description.571200=控制器異常，逆解失敗
notice.solution.571200=MOSの開発者に連絡してください
notice.description.571201=控制器異常，状態異常
notice.solution.571201=MOSの開発者に連絡してください
notice.description.571300=运动进入到stop阶段
notice.solution.571300=MOSの開発者に連絡してください
notice.description.571401=机械臂未定义的失敗
notice.solution.571401=MOSの開発者に連絡してください
notice.description.571501=机械臂ListenNode 未启动
notice.solution.571501=MOSの開発者に連絡してください
notice.description.572100=PLCクライアント異常
notice.solution.572100=MOSの開発者に連絡してください
notice.description.572101=PLCクライアント異常
notice.solution.572101=MOSの開発者に連絡してください
notice.description.572102=PLCクライアント異常
notice.solution.572102=MOSの開発者に連絡してください
notice.description.572103=PLCクライアント異常
notice.solution.572103=MOSの開発者に連絡してください
notice.description.572104=PLCクライアント異常
notice.solution.572104=MOSの開発者に連絡してください
notice.description.572105=PLCクライアント異常
notice.solution.572105=MOSの開発者に連絡してください
notice.description.572106=PLCクライアント異常
notice.solution.572106=MOSの開発者に連絡してください
notice.description.572107=PLCクライアント異常
notice.solution.572107=MOSの開発者に連絡してください
notice.description.572108=PLCクライアント異常
notice.solution.572108=MOSの開発者に連絡してください
notice.description.572109=PLCクライアント異常
notice.solution.572109=MOSの開発者に連絡してください
notice.description.572110=PLCクライアント異常
notice.solution.572110=MOSの開発者に連絡してください
notice.description.572111=PLCクライアント異常
notice.solution.572111=MOSの開発者に連絡してください
notice.description.572112=PLCクライアント異常
notice.solution.572112=MOSの開発者に連絡してください
notice.description.572113=PLCクライアント異常
notice.solution.572113=MOSの開発者に連絡してください
notice.description.572114=PLCクライアント異常
notice.solution.572114=MOSの開発者に連絡してください
notice.description.572115=PLCクライアント異常
notice.solution.572115=MOSの開発者に連絡してください
notice.description.572116=PLCクライアント異常
notice.solution.572116=MOSの開発者に連絡してください
notice.description.572117=PLCクライアント異常
notice.solution.572117=MOSの開発者に連絡してください
notice.description.572118=PLCクライアント異常
notice.solution.572118=MOSの開発者に連絡してください
notice.description.572119=PLCクライアント異常
notice.solution.572119=MOSの開発者に連絡してください
notice.description.572120=PLCクライアント異常
notice.solution.572120=MOSの開発者に連絡してください
notice.description.572121=PLCクライアント異常
notice.solution.572121=MOSの開発者に連絡してください
notice.description.572122=PLCクライアント異常
notice.solution.572122=MOSの開発者に連絡してください
notice.description.572123=PLCクライアント異常
notice.solution.572123=MOSの開発者に連絡してください
notice.description.572124=PLCクライアント異常
notice.solution.572124=MOSの開発者に連絡してください
notice.description.572125=PLCクライアント異常
notice.solution.572125=MOSの開発者に連絡してください
notice.description.572126=PLCクライアント異常
notice.solution.572126=MOSの開発者に連絡してください
notice.description.572127=PLCクライアント異常
notice.solution.572127=MOSの開発者に連絡してください
notice.description.572128=PLCクライアント異常
notice.solution.572128=MOSの開発者に連絡してください
notice.description.572129=PLCクライアント異常
notice.solution.572129=MOSの開発者に連絡してください
notice.description.572130=PLCクライアント異常
notice.solution.572130=MOSの開発者に連絡してください
notice.description.572131=PLCクライアント異常
notice.solution.572131=MOSの開発者に連絡してください
notice.description.572132=PLCクライアント異常
notice.solution.572132=MOSの開発者に連絡してください
notice.description.572133=PLCクライアント異常
notice.solution.572133=MOSの開発者に連絡してください
notice.description.572134=PLCクライアント異常
notice.solution.572134=MOSの開発者に連絡してください
notice.description.572135=PLCクライアント異常
notice.solution.572135=MOSの開発者に連絡してください
notice.description.572136=PLCクライアント異常
notice.solution.572136=MOSの開発者に連絡してください
notice.description.572137=PLCクライアント異常
notice.solution.572137=MOSの開発者に連絡してください
notice.description.572138=PLCクライアント異常
notice.solution.572138=MOSの開発者に連絡してください
notice.description.572139=PLCクライアント異常
notice.solution.572139=MOSの開発者に連絡してください
notice.description.572150=硬件控制器未初始化
notice.solution.572150=MOSの開発者に連絡してください
notice.description.572200=PLC硬件異常
notice.solution.572200=MOSの開発者に連絡してください
notice.description.572201=PLC硬件異常
notice.solution.572201=MOSの開発者に連絡してください
notice.description.572202=PLC硬件異常
notice.solution.572202=MOSの開発者に連絡してください
notice.description.572203=PLC硬件異常
notice.solution.572203=MOSの開発者に連絡してください
notice.description.572204=PLC硬件異常
notice.solution.572204=MOSの開発者に連絡してください
notice.description.572205=PLC硬件異常
notice.solution.572205=MOSの開発者に連絡してください
notice.description.572206=PLC硬件異常
notice.solution.572206=MOSの開発者に連絡してください
notice.description.572207=PLC硬件異常
notice.solution.572207=MOSの開発者に連絡してください
notice.description.572208=PLC硬件異常
notice.solution.572208=MOSの開発者に連絡してください
notice.description.572209=PLC硬件異常
notice.solution.572209=MOSの開発者に連絡してください
notice.description.572210=PLC硬件異常
notice.solution.572210=MOSの開発者に連絡してください
notice.description.572211=PLC硬件異常
notice.solution.572211=MOSの開発者に連絡してください
notice.description.572212=PLC硬件異常
notice.solution.572212=MOSの開発者に連絡してください
notice.description.572213=PLC硬件異常
notice.solution.572213=MOSの開発者に連絡してください
notice.description.572214=PLC硬件異常
notice.solution.572214=MOSの開発者に連絡してください
notice.description.572215=PLC硬件異常
notice.solution.572215=MOSの開発者に連絡してください
notice.description.572216=PLC硬件異常
notice.solution.572216=MOSの開発者に連絡してください
notice.description.572217=PLC硬件異常
notice.solution.572217=MOSの開発者に連絡してください
notice.description.572218=PLC硬件異常
notice.solution.572218=MOSの開発者に連絡してください
notice.description.572219=PLC硬件異常
notice.solution.572219=MOSの開発者に連絡してください
notice.description.572220=PLC硬件異常
notice.solution.572220=MOSの開発者に連絡してください
notice.description.572221=PLC硬件異常
notice.solution.572221=MOSの開発者に連絡してください
notice.description.572222=PLC硬件異常
notice.solution.572222=MOSの開発者に連絡してください
notice.description.572223=PLC硬件異常
notice.solution.572223=MOSの開発者に連絡してください
notice.description.572224=PLC硬件異常
notice.solution.572224=MOSの開発者に連絡してください
notice.description.572225=PLC硬件異常
notice.solution.572225=MOSの開発者に連絡してください
notice.description.572226=PLC硬件異常
notice.solution.572226=MOSの開発者に連絡してください
notice.description.572227=PLC硬件異常
notice.solution.572227=MOSの開発者に連絡してください
notice.description.572228=PLC硬件異常
notice.solution.572228=MOSの開発者に連絡してください
notice.description.572229=PLC硬件異常
notice.solution.572229=MOSの開発者に連絡してください
notice.description.572230=PLC硬件異常
notice.solution.572230=MOSの開発者に連絡してください
notice.description.572231=PLC硬件異常
notice.solution.572231=MOSの開発者に連絡してください
notice.description.572232=PLC硬件異常
notice.solution.572232=MOSの開発者に連絡してください
notice.description.572233=PLC硬件異常
notice.solution.572233=MOSの開発者に連絡してください
notice.description.572234=PLC硬件異常
notice.solution.572234=MOSの開発者に連絡してください
notice.description.572235=PLC硬件異常
notice.solution.572235=MOSの開発者に連絡してください
notice.description.572236=PLC硬件異常
notice.solution.572236=MOSの開発者に連絡してください
notice.description.572237=PLC硬件異常
notice.solution.572237=MOSの開発者に連絡してください
notice.description.572238=PLC硬件異常
notice.solution.572238=MOSの開発者に連絡してください
notice.description.572239=PLC硬件異常
notice.solution.572239=MOSの開発者に連絡してください
notice.description.572240=PLC硬件異常
notice.solution.572240=MOSの開発者に連絡してください
notice.description.572241=PLC硬件異常
notice.solution.572241=MOSの開発者に連絡してください
notice.description.572242=PLC硬件異常
notice.solution.572242=MOSの開発者に連絡してください
notice.description.572243=PLC硬件異常
notice.solution.572243=MOSの開発者に連絡してください
notice.description.572244=PLC硬件異常
notice.solution.572244=MOSの開発者に連絡してください
notice.description.572245=PLC硬件異常
notice.solution.572245=MOSの開発者に連絡してください
notice.description.572246=PLC硬件異常
notice.solution.572246=MOSの開発者に連絡してください
notice.description.572247=PLC硬件異常
notice.solution.572247=MOSの開発者に連絡してください
notice.description.572248=PLC硬件異常
notice.solution.572248=MOSの開発者に連絡してください
notice.description.572249=PLC硬件異常
notice.solution.572249=MOSの開発者に連絡してください
notice.description.572250=PLC硬件異常
notice.solution.572250=MOSの開発者に連絡してください
notice.description.572251=PLC硬件異常
notice.solution.572251=MOSの開発者に連絡してください
notice.description.572252=PLC硬件異常
notice.solution.572252=MOSの開発者に連絡してください
notice.description.572253=PLC硬件異常
notice.solution.572253=MOSの開発者に連絡してください
notice.description.572254=PLC硬件異常
notice.solution.572254=MOSの開発者に連絡してください
notice.description.572255=PLC硬件異常
notice.solution.572255=MOSの開発者に連絡してください
notice.description.572256=PLC硬件異常
notice.solution.572256=MOSの開発者に連絡してください
notice.description.572257=PLC硬件異常
notice.solution.572257=MOSの開発者に連絡してください
notice.description.572258=PLC硬件異常
notice.solution.572258=MOSの開発者に連絡してください
notice.description.572259=PLC硬件異常
notice.solution.572259=MOSの開発者に連絡してください
notice.description.572260=PLC硬件異常
notice.solution.572260=MOSの開発者に連絡してください
notice.description.572261=PLC硬件異常
notice.solution.572261=MOSの開発者に連絡してください
notice.description.572262=PLC硬件異常
notice.solution.572262=MOSの開発者に連絡してください
notice.description.572263=PLC硬件異常
notice.solution.572263=MOSの開発者に連絡してください
notice.description.572264=PLC硬件異常
notice.solution.572264=MOSの開発者に連絡してください
notice.description.572265=PLC硬件異常
notice.solution.572265=MOSの開発者に連絡してください
notice.description.572266=PLC硬件異常
notice.solution.572266=MOSの開発者に連絡してください
notice.description.572267=PLC硬件異常
notice.solution.572267=MOSの開発者に連絡してください
notice.description.572268=PLC硬件異常
notice.solution.572268=MOSの開発者に連絡してください
notice.description.572269=PLC硬件異常
notice.solution.572269=MOSの開発者に連絡してください
notice.description.572270=PLC硬件異常
notice.solution.572270=MOSの開発者に連絡してください
notice.description.572271=PLC硬件異常
notice.solution.572271=MOSの開発者に連絡してください
notice.description.572272=PLC硬件異常
notice.solution.572272=MOSの開発者に連絡してください
notice.description.572273=PLC硬件異常
notice.solution.572273=MOSの開発者に連絡してください
notice.description.572274=PLC硬件異常
notice.solution.572274=MOSの開発者に連絡してください
notice.description.572275=PLC硬件異常
notice.solution.572275=MOSの開発者に連絡してください
notice.description.572276=PLC硬件異常
notice.solution.572276=MOSの開発者に連絡してください
notice.description.572277=PLC硬件異常
notice.solution.572277=MOSの開発者に連絡してください
notice.description.572278=PLC硬件異常
notice.solution.572278=MOSの開発者に連絡してください
notice.description.572279=PLC硬件異常
notice.solution.572279=MOSの開発者に連絡してください
notice.description.572280=PLC硬件異常
notice.solution.572280=MOSの開発者に連絡してください
notice.description.572281=PLC硬件異常
notice.solution.572281=MOSの開発者に連絡してください
notice.description.572282=PLC硬件異常
notice.solution.572282=MOSの開発者に連絡してください
notice.description.572283=PLC硬件異常
notice.solution.572283=MOSの開発者に連絡してください
notice.description.572284=PLC硬件異常
notice.solution.572284=MOSの開発者に連絡してください
notice.description.572285=PLC硬件異常
notice.solution.572285=MOSの開発者に連絡してください
notice.description.572286=PLC硬件異常
notice.solution.572286=MOSの開発者に連絡してください
notice.description.572287=PLC硬件異常
notice.solution.572287=MOSの開発者に連絡してください
notice.description.572288=PLC硬件異常
notice.solution.572288=MOSの開発者に連絡してください
notice.description.572289=PLC硬件異常
notice.solution.572289=MOSの開発者に連絡してください
notice.description.572290=PLC硬件異常
notice.solution.572290=MOSの開発者に連絡してください
notice.description.572291=PLC硬件異常
notice.solution.572291=MOSの開発者に連絡してください
notice.description.572292=PLC硬件異常
notice.solution.572292=MOSの開発者に連絡してください
notice.description.572293=PLC硬件異常
notice.solution.572293=MOSの開発者に連絡してください
notice.description.572294=PLC硬件異常
notice.solution.572294=MOSの開発者に連絡してください
notice.description.572295=PLC硬件異常
notice.solution.572295=MOSの開発者に連絡してください
notice.description.572296=PLC硬件異常
notice.solution.572296=MOSの開発者に連絡してください
notice.description.572297=PLC硬件異常
notice.solution.572297=MOSの開発者に連絡してください
notice.description.572298=PLC硬件異常
notice.solution.572298=MOSの開発者に連絡してください
notice.description.572299=PLC硬件異常
notice.solution.572299=MOSの開発者に連絡してください
notice.description.572300=PLC硬件異常
notice.solution.572300=MOSの開発者に連絡してください
notice.description.572301=PLC硬件異常
notice.solution.572301=MOSの開発者に連絡してください
notice.description.572302=PLC硬件異常
notice.solution.572302=MOSの開発者に連絡してください
notice.description.572303=PLC硬件異常
notice.solution.572303=MOSの開発者に連絡してください
notice.description.572304=PLC硬件異常
notice.solution.572304=MOSの開発者に連絡してください
notice.description.572305=PLC硬件異常
notice.solution.572305=MOSの開発者に連絡してください
notice.description.572306=PLC硬件異常
notice.solution.572306=MOSの開発者に連絡してください
notice.description.572307=PLC硬件異常
notice.solution.572307=MOSの開発者に連絡してください
notice.description.572308=PLC硬件異常
notice.solution.572308=MOSの開発者に連絡してください
notice.description.572309=PLC硬件異常
notice.solution.572309=MOSの開発者に連絡してください
notice.description.572310=PLC硬件異常
notice.solution.572310=MOSの開発者に連絡してください
notice.description.572311=PLC硬件異常
notice.solution.572311=MOSの開発者に連絡してください
notice.description.572312=PLC硬件異常
notice.solution.572312=MOSの開発者に連絡してください
notice.description.572313=PLC硬件異常
notice.solution.572313=MOSの開発者に連絡してください
notice.description.572314=PLC硬件異常
notice.solution.572314=MOSの開発者に連絡してください
notice.description.572315=PLC硬件異常
notice.solution.572315=MOSの開発者に連絡してください
notice.description.572316=PLC硬件異常
notice.solution.572316=MOSの開発者に連絡してください
notice.description.572317=PLC硬件異常
notice.solution.572317=MOSの開発者に連絡してください
notice.description.572318=PLC硬件異常
notice.solution.572318=MOSの開発者に連絡してください
notice.description.572319=PLC硬件異常
notice.solution.572319=MOSの開発者に連絡してください
notice.description.572320=PLC硬件異常
notice.solution.572320=MOSの開発者に連絡してください
notice.description.572321=PLC硬件異常
notice.solution.572321=MOSの開発者に連絡してください
notice.description.572322=PLC硬件異常
notice.solution.572322=MOSの開発者に連絡してください
notice.description.572323=PLC硬件異常
notice.solution.572323=MOSの開発者に連絡してください
notice.description.572324=PLC硬件異常
notice.solution.572324=MOSの開発者に連絡してください
notice.description.572325=PLC硬件異常
notice.solution.572325=MOSの開発者に連絡してください
notice.description.572326=PLC硬件異常
notice.solution.572326=MOSの開発者に連絡してください
notice.description.572327=PLC硬件異常
notice.solution.572327=MOSの開発者に連絡してください
notice.description.572328=PLC硬件異常
notice.solution.572328=MOSの開発者に連絡してください
notice.description.572329=PLC硬件異常
notice.solution.572329=MOSの開発者に連絡してください
notice.description.572330=PLC硬件異常
notice.solution.572330=MOSの開発者に連絡してください
notice.description.572331=PLC硬件異常
notice.solution.572331=MOSの開発者に連絡してください
notice.description.572332=PLC硬件異常
notice.solution.572332=MOSの開発者に連絡してください
notice.description.572333=PLC硬件異常
notice.solution.572333=MOSの開発者に連絡してください
notice.description.572334=PLC硬件異常
notice.solution.572334=MOSの開発者に連絡してください
notice.description.572335=PLC硬件異常
notice.solution.572335=MOSの開発者に連絡してください
notice.description.572336=PLC硬件異常
notice.solution.572336=MOSの開発者に連絡してください
notice.description.572337=PLC硬件異常
notice.solution.572337=MOSの開発者に連絡してください
notice.description.572338=PLC硬件異常
notice.solution.572338=MOSの開発者に連絡してください
notice.description.572339=PLC硬件異常
notice.solution.572339=MOSの開発者に連絡してください
notice.description.572340=PLC硬件異常
notice.solution.572340=MOSの開発者に連絡してください
notice.description.572341=PLC硬件異常
notice.solution.572341=MOSの開発者に連絡してください
notice.description.572342=PLC硬件異常
notice.solution.572342=MOSの開発者に連絡してください
notice.description.572343=PLC硬件異常
notice.solution.572343=MOSの開発者に連絡してください
notice.description.572344=PLC硬件異常
notice.solution.572344=MOSの開発者に連絡してください
notice.description.572345=PLC硬件異常
notice.solution.572345=MOSの開発者に連絡してください
notice.description.572346=PLC硬件異常
notice.solution.572346=MOSの開発者に連絡してください
notice.description.572347=PLC硬件異常
notice.solution.572347=MOSの開発者に連絡してください
notice.description.572348=PLC硬件異常
notice.solution.572348=MOSの開発者に連絡してください
notice.description.572349=PLC硬件異常
notice.solution.572349=MOSの開発者に連絡してください
notice.description.572350=PLC硬件異常
notice.solution.572350=MOSの開発者に連絡してください
notice.description.572351=PLC硬件異常
notice.solution.572351=MOSの開発者に連絡してください
notice.description.572352=PLC硬件異常
notice.solution.572352=MOSの開発者に連絡してください
notice.description.572353=PLC硬件異常
notice.solution.572353=MOSの開発者に連絡してください
notice.description.572354=PLC硬件異常
notice.solution.572354=MOSの開発者に連絡してください
notice.description.572355=PLC硬件異常
notice.solution.572355=MOSの開発者に連絡してください
notice.description.572356=PLC硬件異常
notice.solution.572356=MOSの開発者に連絡してください
notice.description.572357=PLC硬件異常
notice.solution.572357=MOSの開発者に連絡してください
notice.description.572358=PLC硬件異常
notice.solution.572358=MOSの開発者に連絡してください
notice.description.572359=PLC硬件異常
notice.solution.572359=MOSの開発者に連絡してください
notice.description.572360=PLC硬件異常
notice.solution.572360=MOSの開発者に連絡してください
notice.description.572361=PLC硬件異常
notice.solution.572361=MOSの開発者に連絡してください
notice.description.572362=PLC硬件異常
notice.solution.572362=MOSの開発者に連絡してください
notice.description.572363=PLC硬件異常
notice.solution.572363=MOSの開発者に連絡してください
notice.description.572364=PLC硬件異常
notice.solution.572364=MOSの開発者に連絡してください
notice.description.572365=PLC硬件異常
notice.solution.572365=MOSの開発者に連絡してください
notice.description.572366=PLC硬件異常
notice.solution.572366=MOSの開発者に連絡してください
notice.description.572367=PLC硬件異常
notice.solution.572367=MOSの開発者に連絡してください
notice.description.572368=PLC硬件異常
notice.solution.572368=MOSの開発者に連絡してください
notice.description.572369=PLC硬件異常
notice.solution.572369=MOSの開発者に連絡してください
notice.description.572370=PLC硬件異常
notice.solution.572370=MOSの開発者に連絡してください
notice.description.572371=PLC硬件異常
notice.solution.572371=MOSの開発者に連絡してください
notice.description.572372=PLC硬件異常
notice.solution.572372=MOSの開発者に連絡してください
notice.description.572373=PLC硬件異常
notice.solution.572373=MOSの開発者に連絡してください
notice.description.572374=PLC硬件異常
notice.solution.572374=MOSの開発者に連絡してください
notice.description.572375=PLC硬件異常
notice.solution.572375=MOSの開発者に連絡してください
notice.description.572376=PLC硬件異常
notice.solution.572376=MOSの開発者に連絡してください
notice.description.572377=PLC硬件異常
notice.solution.572377=MOSの開発者に連絡してください
notice.description.572378=PLC硬件異常
notice.solution.572378=MOSの開発者に連絡してください
notice.description.572379=PLC硬件異常
notice.solution.572379=MOSの開発者に連絡してください
notice.description.572380=PLC硬件異常
notice.solution.572380=MOSの開発者に連絡してください
notice.description.572381=PLC硬件異常
notice.solution.572381=MOSの開発者に連絡してください
notice.description.572382=PLC硬件異常
notice.solution.572382=MOSの開発者に連絡してください
notice.description.572383=PLC硬件異常
notice.solution.572383=MOSの開発者に連絡してください
notice.description.572384=PLC硬件異常
notice.solution.572384=MOSの開発者に連絡してください
notice.description.572385=PLC硬件異常
notice.solution.572385=MOSの開発者に連絡してください
notice.description.572386=PLC硬件異常
notice.solution.572386=MOSの開発者に連絡してください
notice.description.572387=PLC硬件異常
notice.solution.572387=MOSの開発者に連絡してください
notice.description.572388=PLC硬件異常
notice.solution.572388=MOSの開発者に連絡してください
notice.description.572389=PLC硬件異常
notice.solution.572389=MOSの開発者に連絡してください
notice.description.572390=PLC硬件異常
notice.solution.572390=MOSの開発者に連絡してください
notice.description.572391=PLC硬件異常
notice.solution.572391=MOSの開発者に連絡してください
notice.description.572392=PLC硬件異常
notice.solution.572392=MOSの開発者に連絡してください
notice.description.572393=PLC硬件異常
notice.solution.572393=MOSの開発者に連絡してください
notice.description.572394=PLC硬件異常
notice.solution.572394=MOSの開発者に連絡してください
notice.description.572395=PLC硬件異常
notice.solution.572395=MOSの開発者に連絡してください
notice.description.572396=PLC硬件異常
notice.solution.572396=MOSの開発者に連絡してください
notice.description.572397=PLC硬件異常
notice.solution.572397=MOSの開発者に連絡してください
notice.description.572398=PLC硬件異常
notice.solution.572398=MOSの開発者に連絡してください
notice.description.572399=PLC硬件異常
notice.solution.572399=MOSの開発者に連絡してください
notice.description.573100=E84 消息等待超时
notice.solution.573100=MOSの開発者に連絡してください
notice.description.573101=E84不支持的操作类型
notice.solution.573101=MOSの開発者に連絡してください
notice.description.573102=E84不支持的机器人上下料状態
notice.solution.573102=MOSの開発者に連絡してください
notice.description.573103=mqtt_client函数调用出错
notice.solution.573103=MOSの開発者に連絡してください
notice.description.573104=mqtt获取数据超时
notice.solution.573104=MOSの開発者に連絡してください
notice.description.573105=mqtt发送数据出错
notice.solution.573105=MOSの開発者に連絡してください
notice.description.573106=mqtt接收数据出错
notice.solution.573106=MOSの開発者に連絡してください
notice.description.573107=mqtt接收到故障信息
notice.solution.573107=MOSの開発者に連絡してください
notice.description.573108=雷达区域切换失敗
notice.solution.573108=MOSの開発者に連絡してください
notice.description.574100=夹爪打开失敗
notice.solution.574100=MOSの開発者に連絡してください
notice.description.574101=夹爪关闭失敗
notice.solution.574101=MOSの開発者に連絡してください
notice.description.574102=夹爪复位失敗
notice.solution.574102=MOSの開発者に連絡してください
notice.description.574103=夹爪485协议接收失敗
notice.solution.574103=MOSの開発者に連絡してください
notice.description.574200=距离传感器检测超时
notice.solution.574200=MOSの開発者に連絡してください
notice.description.574201=抓手有无料检测传感器检测超时
notice.solution.574201=MOSの開発者に連絡してください
notice.description.574300=相机未连接
notice.solution.574300=MOSの開発者に連絡してください
notice.description.574301=相机内部错误
notice.solution.574301=MOSの開発者に連絡してください
notice.description.574302=超时
notice.solution.574302=MOSの開発者に連絡してください
notice.description.574303=相机未知命令
notice.solution.574303=MOSの開発者に連絡してください
notice.description.574304=索引超出范围
notice.solution.574304=MOSの開発者に連絡してください
notice.description.574305=自变量太少
notice.solution.574305=MOSの開発者に連絡してください
notice.description.574306=无效自变量类型
notice.solution.574306=MOSの開発者に連絡してください
notice.description.574307=无效自变量
notice.solution.574307=MOSの開発者に連絡してください
notice.description.574308=不允许的命令
notice.solution.574308=MOSの開発者に連絡してください
notice.description.574309=不允许的组合
notice.solution.574309=MOSの開発者に連絡してください
notice.description.574310=相机忙
notice.solution.574310=MOSの開発者に連絡してください
notice.description.574311=未完全实施
notice.solution.574311=MOSの開発者に連絡してください
notice.description.574312=不支持
notice.solution.574312=MOSの開発者に連絡してください
notice.description.574313=結果字符串过⻓
notice.solution.574313=MOSの開発者に連絡してください
notice.description.574314=⽆效相机 ID
notice.solution.574314=MOSの開発者に連絡してください
notice.description.574315=⽆效相机特征 ID
notice.solution.574315=MOSの開発者に連絡してください
notice.description.574316=不同的配⽅名称
notice.solution.574316=MOSの開発者に連絡してください
notice.description.574317=不同版本
notice.solution.574317=MOSの開発者に連絡してください
notice.description.574318=没有标定
notice.solution.574318=MOSの開発者に連絡してください
notice.description.574319=标定失敗
notice.solution.574319=MOSの開発者に連絡してください
notice.description.574320=⽆效标定数据
notice.solution.574320=MOSの開発者に連絡してください
notice.description.574321=未达到给定的标定位置
notice.solution.574321=MOSの開発者に連絡してください
notice.description.574322=⽆启动命令
notice.solution.574322=MOSの開発者に連絡してください
notice.description.574323=特征未经过训练
notice.solution.574323=MOSの開発者に連絡してください
notice.description.574324=特征未找到
notice.solution.574324=MOSの開発者に連絡してください
notice.description.574325=特征未映射
notice.solution.574325=MOSの開発者に連絡してください
notice.description.574326=部件位置未经过训练
notice.solution.574326=MOSの開発者に連絡してください
notice.description.574327=机器⼈位置未经过训练
notice.solution.574327=MOSの開発者に連絡してください
notice.description.574328=⽆效部件 ID
notice.solution.574328=MOSの開発者に連絡してください
notice.description.574329=未定位此部件的所有特征
notice.solution.574329=MOSの開発者に連絡してください
notice.description.574330=部件⽆有效夹持纠正
notice.solution.574330=MOSの開発者に連絡してください
notice.description.574331=部件⽆有效夹持纠正
notice.solution.574331=MOSの開発者に連絡してください
notice.description.574350=相机读取socket错误
notice.solution.574350=MOSの開発者に連絡してください
notice.description.574351=相机响应信息header不符
notice.solution.574351=MOSの開発者に連絡してください
notice.description.574352=解析相机响应失敗
notice.solution.574352=MOSの開発者に連絡してください
notice.description.574360=相机内参标定失敗
notice.solution.574360=MOSの開発者に連絡してください
notice.description.574361=相机手眼标定失敗
notice.solution.574361=MOSの開発者に連絡してください
notice.description.574362=没有内参标定数据
notice.solution.574362=MOSの開発者に連絡してください
notice.description.574363=没有手眼标定数据
notice.solution.574363=MOSの開発者に連絡してください
notice.description.574364=相机数据获取失敗
notice.solution.574364=MOSの開発者に連絡してください
notice.description.574365=相机数据存储失敗
notice.solution.574365=MOSの開発者に連絡してください
notice.description.574366=特征点坐标获取失敗
notice.solution.574366=MOSの開発者に連絡してください
notice.description.574367=拍照计算的夹取位与示教夹取位之间偏差过大
notice.solution.574367=MOSの開発者に連絡してください
notice.description.574368=创建模板图像失敗
notice.solution.574368=MOSの開発者に連絡してください
notice.description.574369=获取算法参数失敗
notice.solution.574369=MOSの開発者に連絡してください
notice.description.574370=相机图像处理異常
notice.solution.574370=MOSの開発者に連絡してください
notice.description.574390=相机拍照失敗
notice.solution.574390=MOSの開発者に連絡してください
notice.description.574391=相机配方设置失敗
notice.solution.574391=MOSの開発者に連絡してください
notice.description.574392=相机触发参数获取失敗
notice.solution.574392=MOSの開発者に連絡してください
notice.description.574393=相机专案保存失敗
notice.solution.574393=MOSの開発者に連絡してください
notice.description.574394=相机未初始化
notice.solution.574394=MOSの開発者に連絡してください
notice.description.574400=光源设备串口打开失敗
notice.solution.574400=MOSの開発者に連絡してください
notice.description.574401=光源设备串口读写異常
notice.solution.574401=MOSの開発者に連絡してください
notice.description.574410=光源打开失敗
notice.solution.574410=MOSの開発者に連絡してください
notice.description.574411=光源关闭失敗
notice.solution.574411=MOSの開発者に連絡してください
notice.description.574412=光源亮度获取失敗
notice.solution.574412=MOSの開発者に連絡してください
notice.description.575100=无效的储位ID
notice.solution.575100=MOSの開発者に連絡してください
notice.description.575101=储位检测超时
notice.solution.575101=MOSの開発者に連絡してください
notice.description.575200=储位锁锁定超时
notice.solution.575200=MOSの開発者に連絡してください
notice.description.575201=储位锁解锁超时
notice.solution.575201=MOSの開発者に連絡してください
notice.description.575300=无物料信息检测sensor
notice.solution.575300=MOSの開発者に連絡してください
notice.description.575301=smart tag 传感器未连接
notice.solution.575301=MOSの開発者に連絡してください
notice.description.575302=smart tag 读取失敗
notice.solution.575302=MOSの開発者に連絡してください
notice.description.575303=smart tag 读取超时
notice.solution.575303=MOSの開発者に連絡してください
notice.description.575304=smart tag 数据无效
notice.solution.575304=MOSの開発者に連絡してください
notice.description.575401=RFID 传感器未连接
notice.solution.575401=MOSの開発者に連絡してください
notice.description.575402=RFID 读取失敗
notice.solution.575402=MOSの開発者に連絡してください
notice.description.575403=RFID 读取超时
notice.solution.575403=MOSの開発者に連絡してください
notice.description.575404=RFID 数据无效
notice.solution.575404=MOSの開発者に連絡してください
notice.description.575405=RFID 请求数据错误
notice.solution.575405=MOSの開発者に連絡してください
notice.description.575900=smart tag sensor 故障
notice.solution.575900=MOSの開発者に連絡してください
notice.description.575901=RFID sensor 故障
notice.solution.575901=MOSの開発者に連絡してください
notice.description.576100=升降柱超出运动范围
notice.solution.576100=MOSの開発者に連絡してください
notice.description.576101=无效的控制指令
notice.solution.576101=MOSの開発者に連絡してください
notice.description.576102=无效的多轴控制模式
notice.solution.576102=MOSの開発者に連絡してください
notice.description.576103=多轴设备未就绪，或出现異常
notice.solution.576103=MOSの開発者に連絡してください
notice.description.576104=Can卡未连接
notice.solution.576104=MOSの開発者に連絡してください
notice.description.576105=步科轴设备超出行程
notice.solution.576105=MOSの開発者に連絡してください
notice.description.576106=多轴设备无法获取当前位置
notice.solution.576106=MOSの開発者に連絡してください
notice.description.576107=多轴设备移动失敗
notice.solution.576107=MOSの開発者に連絡してください
notice.description.576108=多轴设备运动被条件中断
notice.solution.576108=MOSの開発者に連絡してください
notice.description.576109=多轴设备目标点位超出设置的限位
notice.solution.576109=MOSの開発者に連絡してください
notice.description.576110=多轴设备运动被手动中断
notice.solution.576110=MOSの開発者に連絡してください
notice.description.576111=单轴设备指令阻塞等待超时,超过600s未停止
notice.solution.576111=MOSの開発者に連絡してください
notice.description.576201=无效的多轴参数
notice.solution.576201=MOSの開発者に連絡してください
notice.description.576202=多轴管理初始化失敗
notice.solution.576202=MOSの開発者に連絡してください
notice.description.577100=云台任务JSON解析失敗
notice.solution.577100=MOSの開発者に連絡してください
notice.description.577101=云台执行动作类型错误
notice.solution.577101=MOSの開発者に連絡してください
notice.description.577102=云台通道错误
notice.solution.577102=MOSの開発者に連絡してください
notice.description.577103=云台通道类型错误
notice.solution.577103=MOSの開発者に連絡してください
notice.description.577104=云台相机名字错误
notice.solution.577104=MOSの開発者に連絡してください
notice.description.577105=云台执行拍照失敗
notice.solution.577105=MOSの開発者に連絡してください
notice.description.577106=云台执行录像失敗
notice.solution.577106=MOSの開発者に連絡してください
notice.description.577107=云台执行参数设置失敗
notice.solution.577107=MOSの開発者に連絡してください
notice.description.577108=云台执行普通测温失敗
notice.solution.577108=MOSの開発者に連絡してください
notice.description.577109=云台图像远程拷贝失敗
notice.solution.577109=MOSの開発者に連絡してください
notice.description.577110=云台获取SGC参数失敗，检查SGC下发的相机名是否对应
notice.solution.577110=MOSの開発者に連絡してください
notice.description.578100=传感器JSON解析失敗
notice.solution.578100=MOSの開発者に連絡してください
notice.description.578101=传感器名字不存在
notice.solution.578101=MOSの開発者に連絡してください
notice.description.578102=传入的G300M4的模式不对
notice.solution.578102=MOSの開発者に連絡してください
notice.description.578201=XSLAB声纹传感器初始化失敗
notice.solution.578201=MOSの開発者に連絡してください
notice.description.578202=G300M4局放传感器初始化失敗
notice.solution.578202=MOSの開発者に連絡してください
notice.description.578203=FS00802福申传感器初始化失敗
notice.solution.578203=MOSの開発者に連絡してください
notice.description.578204=接收G300M4数据失敗
notice.solution.578204=MOSの開発者に連絡してください
notice.description.578205=G300M4工作模式错误
notice.solution.578205=MOSの開発者に連絡してください
notice.description.578206=传感器初始化失敗或者运行错误，请检查配置
notice.solution.578206=MOSの開発者に連絡してください
notice.description.579000=系统未初始化
notice.solution.579000=MOSの開発者に連絡してください
notice.description.579100=取消任务失敗
notice.solution.579100=MOSの開発者に連絡してください
notice.description.579101=暂停任务失敗
notice.solution.579101=MOSの開発者に連絡してください
notice.description.579102=恢复任务失敗
notice.solution.579102=MOSの開発者に連絡してください
notice.description.579103=buffer解析错误
notice.solution.579103=MOSの開発者に連絡してください
notice.description.579104=未找到任务
notice.solution.579104=MOSの開発者に連絡してください
notice.description.579105=任务列表未更新
notice.solution.579105=MOSの開発者に連絡してください
notice.description.579106=存在未完成的任务
notice.solution.579106=MOSの開発者に連絡してください
notice.description.579107=任务被手动中断
notice.solution.579107=MOSの開発者に連絡してください
notice.description.579201=无效步骤类型
notice.solution.579201=MOSの開発者に連絡してください
notice.description.579202=未找到pose value
notice.solution.579202=MOSの開発者に連絡してください
notice.description.579203=未找到joint value
notice.solution.579203=MOSの開発者に連絡してください
notice.description.579204=未找到偏移量
notice.solution.579204=MOSの開発者に連絡してください
notice.description.579205=无效的feature ID
notice.solution.579205=MOSの開発者に連絡してください
notice.description.579206=无效的条件类型
notice.solution.579206=MOSの開発者に連絡してください
notice.description.579207=无效的条件参数
notice.solution.579207=MOSの開発者に連絡してください
notice.description.579208=动作列表获取失敗
notice.solution.579208=MOSの開発者に連絡してください
notice.description.579209=机械臂不在原点
notice.solution.579209=MOSの開発者に連絡してください
notice.description.579210=集成锁住,底盘正在运动
notice.solution.579210=MOSの開発者に連絡してください
notice.description.579211=Socket协议解析失敗
notice.solution.579211=MOSの開発者に連絡してください
notice.description.620001=充电机的输出电压高于预设值(待翻译)
notice.solution.620001=请检查电压设置(待翻译)
notice.description.620002=充电桩的温度高于预设值(待翻译)
notice.solution.620002=请检查刷块(待翻译)
notice.description.620004=充电桩的输入电压高于或低于输入电压范围(待翻译)
notice.solution.620004=请检查输入接线(待翻译)
notice.description.620005=充电桩的输出短路(待翻译)
notice.solution.620005=请检查输出端(待翻译)
notice.description.620006=充电桩的风扇故障(待翻译)
notice.solution.620006=请检查模块风扇(待翻译)
notice.description.620007=充电桩的输出电流高于预设值(待翻译)
notice.solution.620007=请检查电流设置(待翻译)
notice.description.620008=刷块温度过高(待翻译)(待翻译)
notice.solution.620008=请检查散热系统(待翻译)
notice.description.610001=发射端逆变电流超限(待翻译)
notice.solution.610001=将距离控制到合理范围及检查线圈(待翻译)
notice.description.610002=发射端逆变电流突变(待翻译)
notice.solution.610002=如果周期出现，送回返修(待翻译)
notice.description.610003=发射端无线通信掉线(待翻译)
notice.solution.610003=如果周期出现或者频繁出现则检查天线是否松动(待翻译)
notice.description.610004=发射端输入母线电压过高(待翻译)
notice.solution.610004=如果频繁出现，送回返修(待翻译)
notice.description.610005=发射端输入母线电压过低(待翻译)
notice.solution.610005=如果频繁出现，送回返修(待翻译)
notice.description.610006=发射端 FAULT 保护(待翻译)
notice.solution.610006=如果频繁出现，送回返修(待翻译)
notice.description.610007=发射端温度过高(待翻译)
notice.solution.610007=检查风扇是否正常转动(待翻译)
notice.description.610008=发射端外部命令停止使能(待翻译)
notice.solution.610008=无(待翻译)
notice.description.610009=发射端判断电压达到无电流(待翻译)
notice.solution.610009=无(待翻译)
notice.description.610010=发射端判定充满停机(待翻译)
notice.solution.610010=无(待翻译)
notice.description.610011=发射端电源模块故障(待翻译)
notice.solution.610011=如果频繁出现，送回返修(待翻译)
notice.description.610012=发射端耦合度测试不合格(待翻译)
notice.solution.610012=(待翻译)
notice.description.610013=发射端母线电流硬件保护(待翻译)
notice.solution.610013=如果频繁出现，送回返修(待翻译)
notice.description.610014=发射端母线电压硬件保护(待翻译)
notice.solution.610014=如果频繁出现，送回返修(待翻译)
notice.description.610015=发射端交流接触器异常停止(待翻译)
notice.solution.610015=(待翻译)
notice.description.610016=发射端线圈/导轨电流异常(待翻译)
notice.solution.610016=检查线圈间距离是否在规定范围内(待翻译)
notice.description.610017=TX导轨/线圈电流过流(待翻译)
notice.solution.610017=检查线圈间距离是否在规定范围内(待翻译)
notice.description.610018=发射端充电超时(待翻译)
notice.solution.610018=无(待翻译)
notice.description.610019=发射端FAULT保护1(待翻译)
notice.solution.610019=如果频繁出现，送回返修(待翻译)
notice.description.610020=发射端FAULT保护2(待翻译)
notice.solution.610020=如果频繁出现，送回返修(待翻译)
notice.description.610021=发射端FAULT保护3(待翻译)
notice.solution.610021=如果频繁出现，送回返修(待翻译)
notice.description.610022=发射端FAULT保护4(待翻译)
notice.solution.610022=如果频繁出现，送回返修(待翻译)
notice.description.610023=发射端FAULT保护5(待翻译)
notice.solution.610023=如果频繁出现，送回返修(待翻译)
notice.description.610024=发射端FAULT保护6(待翻译)
notice.solution.610024=如果频繁出现，送回返修(待翻译)
notice.description.610025=发射端温度保护1(待翻译)
notice.solution.610025=如果频繁出现，送回返修(待翻译)
notice.description.610026=发射端温度保护2(待翻译)
notice.solution.610026=如果频繁出现，送回返修(待翻译)
notice.description.610027=发射端温度保护3(待翻译)
notice.solution.610027=如果频繁出现，送回返修(待翻译)
notice.description.610028=发射端温度保护4(待翻译)
notice.solution.610028=如果频繁出现，送回返修(待翻译)
notice.description.610029=发射端温度保护5(待翻译)
notice.solution.610029=如果频繁出现，送回返修(待翻译)
notice.description.610030=发射端温度保护6(待翻译)
notice.solution.610030=如果频繁出现，送回返修(待翻译)
notice.description.610031=TX导轨/线圈电流电流过低(待翻译)
notice.solution.610031=无(待翻译)
notice.description.610032=调度系统命令停止(待翻译)
notice.solution.610032=请确认调度系统终止放电原因(待翻译)
notice.description.610101=接收端输出过压(待翻译)
notice.solution.610101=检查充电机接收端是否空载(待翻译)
notice.description.610102=接收端输出过流(待翻译)
notice.solution.610102=更换电池重试，排查电池问题，查看上位机设置值(待翻译)
notice.description.610103=接收端短路保护(待翻译)
notice.solution.610103=万用表测量输出两端是否短路(待翻译)
notice.description.610104=接收端判断充满停止(待翻译)
notice.solution.610104=无(待翻译)
notice.description.610105=接收端温度过高(待翻译)
notice.solution.610105=查看风扇是否正常转动(待翻译)
notice.description.610106=接收端输入电压过低(待翻译)
notice.solution.610106=如果频繁出现，送回返修(待翻译)
notice.description.610107=接收端外部命令停止(待翻译)
notice.solution.610107=无(待翻译)
notice.description.610108=接收端电池故障停止(待翻译)
notice.solution.610108=设置开启充电(待翻译)
notice.description.610109=接收端硬件输出过压(待翻译)
notice.solution.610109=检查是否突然断载(待翻译)
notice.description.610110=接收端硬件输出过流(待翻译)
notice.solution.610110=检查电池(待翻译)
notice.description.610111=接收端硬件短路保护(待翻译)
notice.solution.610111=万用表量取是否断路(待翻译)
notice.description.610112=接收端 BMS未使能(待翻译)
notice.solution.610112=设置开启充电(待翻译)
notice.description.610113=接收端风扇故障(待翻译)
notice.solution.610113=检查风扇(待翻译)
notice.description.300108=急停触发时间过长(待翻译)
notice.solution.300108=请检查机器人状态(待翻译)
notice.description.300402=避让计算时间过长(待翻译)
notice.solution.300402=请检查点位配置(待翻译)
notice.description.300109=分配充电点/泊车点时间过长(待翻译)
notice.solution.300109=请检查充电点/泊车点配置(待翻译)
notice.description.300110=申请点位/区域时间过长(待翻译)
notice.solution.300110=请检查点位/区域配置(待翻译)
notice.description.300403=避让次数异常(待翻译)
notice.solution.300403=请联系Fleet开发人员处理(待翻译)
log.export.interfaceLog.excelName=インターフェースログ
log.export.operationLog.excelName=操作ログ
log.operation.description.success=成功
log.operation.description.fail=失敗
log.third.system.operator=上流システム
log.controller.api.task.create=[API]タスク新規作成
log.controller.api.task.cancel=[API]タスクキャンセル
log.controller.api.task.overNode=[API]タスクノードをスキップ
log.controller.api.traffic.occupy=[API]交管エリアを申請
log.controller.api.traffic.release=[API]交管エリアを解除
log.controller.api.vehicle.operation=[API]AMRを操作する
log.controller.api.vehicle.globalPause=[API]全体を一時停止
log.controller.api.vehicle.globalResume=[API]全体を再開
log.controller.language.delete=言語を削除
log.controller.language.import=言語をインポート
log.controller.language.export=言語をエクスポート
log.controller.language.switch=言語を切り替える
log.controller.license.upload=ライセンスをアップロードする
log.controller.license.delete=ライセンスを削除する
log.controller.operationLog.delete=操作ログを削除する
log.controller.sysLog.delete=システムログを削除する
log.controller.airShowerDoor.insert=エアシャワードアを新規作成
log.controller.airShowerDoor.update=エアシャワードアを編集
log.controller.airShowerDoor.delete=エアシャワードアを削除
log.controller.airShowerDoor.open=エアシャワードアを開く
log.controller.airShowerDoor.close=エアシャワードアを閉じる
log.controller.autoDoor.insert=自動ドアを新規作成
log.controller.autoDoor.update=自動ドアを編集
log.controller.autoDoor.delete=自動ドアを削除
log.controller.autoDoor.open=自動ドアを開く
log.controller.autoDoor.close=自動ドアを閉じる
log.controller.elevator.insert=エレベーターを新規作成
log.controller.elevator.update=エレベーターを編集
log.controller.elevator.delete=エレベーターを削除
log.controller.elevator.import=エレベーターをインポート
log.controller.elevator.export=エレベーターをエクスポート
log.controller.elevator.open=エレベーターを開く
log.controller.elevator.close=エレベーターを閉じる
log.controller.mapArea.insert=エリアを新規作成
log.controller.mapArea.enable=エリアを有効化
log.controller.mapArea.disable=エリアを無効化
log.controller.mapArea.update=要素を編集
log.controller.mapArea.delete=要素を削除
log.controller.marker.insert=ポイントを新規作成
log.controller.marker.transcribe=ポイントを記録
log.controller.marker.update=要素を編集
log.controller.marker.delete=要素を削除
log.controller.path.insert=経路を作成
log.controller.path.update=要素を編集
log.controller.path.delete=要素を削除
log.controller.vehicleMap.insert=地図を作成
log.controller.vehicleMap.update=地図を編集
log.controller.vehicleMap.delete=地図を削除
log.controller.vehicleMap.batchDelete=批量地図を削除
log.controller.vehicleMap.deleteDraft=下書きデータを削除する
log.controller.vehicleMap.batchGenerateElement=ポイントと経路を一括生成する
log.controller.vehicleMap.batchUpdateElement=要素を編集
log.controller.vehicleMap.batchDeleteElement=要素を削除
log.controller.vehicleMap.import=地図をインポート
log.controller.vehicleMap.export=地図をエクスポート
log.controller.vehicleMap.copy=地図をコピー
log.controller.vehicleMap.pause=地図全体一時停止
log.controller.vehicleMap.recover=地図全体再開
log.controller.vehicleMap.publish=地図を公開
log.controller.vehicleMap.locatingMap.update=マッピング図を編集する
log.controller.vehicleMap.locatingMap.import=マッピング図をインポートする
log.controller.vehicleMap.locatingMap.changeDefault=デフォルトのマッピング図を切り替える
log.controller.vehicleMap.locatingMap.delete=マッピング図を削除する
log.controller.noticeConfig.insert=通知テンプレートを新規作成
log.controller.noticeConfig.update=通知テンプレートを編集する
log.controller.noticeConfig.delete=通知テンプレートを削除する
log.controller.noticeConfig.export=通知テンプレートをエクスポートする
log.controller.noticeConfig.import=通知テンプレートをインポートする
log.controller.noticeRecord.insert=異常記録を新規作成
log.controller.noticeRecord.update=異常記録を更新する
log.controller.noticeRecord.delete=異常記録を削除する
log.controller.noticeRecord.activation=通知を有効化する
log.controller.noticeRecord.ignore=通知を無視する
log.controller.noticeRecord.ignoreVehicle=AMRの通知を無視する
log.controller.noticeRecord.export=通知リストをエクスポートする
log.controller.pda.config=(PDA)バージョン情報を設定する
log.controller.pda.containerEnter=(PDA)コンテナが入場する
log.controller.pda.containerExit=(PDA)コンテナが出場する
log.controller.pda.execute=(PDA)タスク新規作成
log.controller.security.login=システムにログインする
log.controller.security.logout=システムからログアウトする
log.controller.sys.menu.insert=メニューを新規作成
log.controller.sys.menu.update=メニューを編集する
log.controller.sys.menu.delete=メニューを削除する
log.controller.sys.role.insert=キャラクターを新規作成
log.controller.sys.role.update=キャラクターを編集する
log.controller.sys.role.delete=キャラクターを削除する
log.controller.sys.property.batchUpdate=システム配置一括編集する
log.controller.sys.property.insert=システム配置を増加する
log.controller.sys.property.update=システム配置を編集する
log.controller.sys.property.delete=システム配置を削除する
log.controller.sys.user.password=パスワードを編集する
log.controller.sys.user.password.reset=パスワードをリセットする
log.controller.sys.user.insert=ユーザーを追加する
log.controller.sys.user.update=ユーザーを編集する
log.controller.sys.user.delete=ユーザーを削除する
log.controller.task.nodeConfig.insert=ノードタイプを新規作成
log.controller.task.nodeConfig.update=ノードタイプを編集する
log.controller.task.nodeConfig.delete=ノードタイプを削除する
log.controller.task.nodeConfig.batchCommon=ノードの一般設定
log.controller.task.nodeConfig.export=ノードタイプをエクスポートする
log.controller.task.nodeConfig.import=ノードタイプをインポートする
log.controller.task.insert=タスクを新規作成
log.controller.task.cancel=タスクをキャンセル
log.controller.task.delete=タスクを削除する
log.controller.task.skip=ノードをスキップ
log.controller.task.retry=ノードを再試行
log.controller.task.batchCancel=タスクを一括キャンセル
log.controller.task.cancelAll=ワンクリックでタスクをキャンセル
log.controller.task.export=記録をダウンロードする
log.controller.task.import=記録をアップロードする
log.controller.task.remark=タスク備考を追加する
log.controller.task.type.insert=タスクフローを新規作成
log.controller.task.type.update=タスクフローを編集する
log.controller.task.type.copy=タスクフローをコピーする
log.controller.task.type.delete=タスクフローを削除する
log.controller.task.type.enable=タスクフローを有効化する
log.controller.task.type.disable=タスクフローを無効
log.controller.task.type.export=タスクフローをエクスポートする
log.controller.task.type.import=タスクフローをインポートする
log.controller.vehicle.stop.open=一時停止を開始
log.controller.vehicle.stop.close=一時停止を終了
log.controller.vehicle.delete=AMRを削除する
log.controller.vehicle.restart=AMRを再起動
log.controller.vehicle.shutdown=AMRをシャットダウン
log.controller.vehicle.controls.manualMode=マナーモードに切り替えます
log.controller.vehicle.controls.autoMode=自動モードに切り替えます
log.controller.vehicle.scheduler.manualMode=手動スケジューリングモードに切り替え
log.controller.vehicle.scheduler.autoMode=自動スケジューリングモードに切り替え
log.controller.vehicle.update=AMR配置を編集する
log.controller.vehicle.updateBatch=AMR配置を一括編集する
log.controller.vehicle.updateGroupBatch=AMRグループを一括編集する
log.controller.vehicle.updateTypeBatch=AMRタイプを一括編集する
log.controller.vehicle.resource.clear=AMRを退場させる
log.controller.vehicle.reset=AMRをリセットする
log.controller.vehicle.dockingReset=ドッキングリセット
log.controller.vehicle.closeSoundLightAlarm=音響光学アラームをオフにする
log.controller.vehicle.charge=一般充電
log.controller.vehicle.group.insert=AMRグループを新規作成
log.controller.vehicle.group.update=AMRグループを編集する
log.controller.vehicle.group.delete=AMRグループを削除する
log.controller.vehicle.group.export=AMRグループをエクスポートする
log.controller.vehicle.group.import=AMRグループをインポートする
log.controller.vehicle.type.insert=AMRタイプを新規作成
log.controller.vehicle.type.update=AMRタイプを編集する
log.controller.vehicle.type.delete=AMRタイプを削除する
log.controller.vehicle.type.export=AMRタイプをエクスポートする
log.controller.vehicle.type.import=AMRタイプをインポートする
log.controller.vehicle.map.appoint=地図を指定します
log.controller.vehicle.map.relocation=AMRを再ローカライゼーション
log.controller.warehouse.area.insert=保管エリアを新規作成
log.controller.warehouse.area.update=保管エリアを編集する
log.controller.warehouse.area.delete=保管エリアを削除する
log.controller.warehouse.area.export=保管エリアをエクスポートする
log.controller.warehouse.area.import=保管エリアをインポートする
log.controller.warehouse.type.insert=保管エリアタイプを新規作成
log.controller.warehouse.type.update=保管エリアタイプを編集する
log.controller.warehouse.type.delete=保管エリアタイプを削除する
log.controller.warehouse.type.export=保管エリアタイプをエクスポートする
log.controller.warehouse.type.import=保管エリアタイプをインポートする
log.controller.warehouse.insert=保管エリアを新規作成
log.controller.warehouse.batchInsert=保管エリアを一括新規作成
log.controller.warehouse.update=保管エリアを編集する
log.controller.warehouse.delete=保管エリアを削除する
log.controller.warehouse.enable=保管エリアを有効化になる
log.controller.warehouse.disable=保管エリアを無効化になる
log.controller.warehouse.export=保管エリアをエクスポートする
log.controller.warehouse.import=保管エリアをインポートする
log.controller.event.insert=イベントを新規作成
log.controller.event.update=イベントを編集
log.controller.event.copy=イベントをコピー
log.controller.event.delete=イベントを削除
log.controller.event.enable=イベントを有効化
log.controller.event.disable=イベントを無効化
log.controller.event.export=イベントをエクスポート
log.controller.event.import=イベントをインポート
log.controller.charge.station.update=充電パイルの修正
log.controller.charge.station.delete=充電杭を削除するには
log.controller.charge.station.enable=充電杭を有効にする
log.controller.charge.station.disable=充電杭を無効にする
log.controller.charge.station.reset=チャージパイルリセット
log.controller.charge.station.stopCharge=充電パイル終端放電
log.operation.excel.head.operator=ユーザー
log.operation.excel.head.description=操作
log.operation.excel.head.success=結果
log.operation.excel.head.errorMsg=失敗の原因
log.operation.excel.head.wasteTime=応答時間
log.operation.excel.head.ip=クライアントのIPアドレス
log.operation.excel.head.paramsIn=リクエスト情報
log.operation.excel.head.paramsOut=レスポンス情報
log.operation.excel.head.operationTime=操作時間
log.interface.excel.head.description=詳細
log.interface.excel.head.success=結果
log.interface.excel.head.errorMsg=失敗の原因
log.interface.excel.head.wasteTime=応答時間
log.interface.excel.head.url=URL
log.interface.excel.head.paramsIn=リクエスト情報
log.interface.excel.head.paramsOut=レスポンス情報
log.interface.excel.head.operationTime=作成時間
log.system.module.task=タスク
log.system.module.task.allocation=AMRスケジューリング
log.system.module.charge.allocation=充電スケジューリング
log.system.module.park.allocation=駐車スケジューリング
log.system.module.resource.apply=交通資源
log.system.module.traffic.avoid=交通回避
log.system.module.vehicle=AMR
log.system.module.event=イベント
log.system.module.system=システム
log.system.module.autodoor=自動ドア
log.system.module.airshowerdoor=エアシャワードア
log.system.module.elevator=エレベーター
log.system.module.charge.station=じゅうでんぐい
log.system.type.Running=実行ログ
log.system.type.Warning=警告ログ
log.system.type.Error=エラーログ
log.system.system.start=スケジューリングシステムが起動成功
log.system.path.plan.is.unreachable=AMR移動ノード，AMRの経路計画が失敗しました，目標地点に到達できません
log.system.instruction.status.upload=AMRのコマンド状態のフィードバック
log.system.resource.elevator.apply.success=AMRがエレベーターのドア開け要求に成功しました
log.system.resource.elevator.ride.success=エレベーター到着，ドアが開きましら
log.system.resource.vehicle.clear.resource=AMRのネットワーク接続がタイムアウトし、AMRが占有しているリソースを自動で解放しました
log.system.vehicle.connect=AMRがスケジューリングシステムに接続しました
log.system.vehicle.disconnect=AMRがスケジューリングシステムから切断されました
log.system.vehicle.out.of.trace=軌道から外れました
log.system.vehicle.on.trace=軌道から外れていません
log.system.vehicle.pause.close=一時停止を終了
log.system.vehicle.pause.open=一時停止を開始
log.system.vehicle.close.stop=AMRは既に緊急停止状態から回復しました
log.system.vehicle.open.stop=AMRの緊急停止ボタンが押しされました
log.system.vehicle.manual.control=AMRはマナーモードに切り替えました
log.system.vehicle.auto.control=AMRは自動モードに切り替えました
log.system.vehicle.repair.control=机器人已被切换为检修控制模式（待翻译）
log.system.vehicle.work.status.work=作業中
log.system.vehicle.work.status.free=待機
log.system.vehicle.connect.status.connect=接続完了
log.system.vehicle.connect.status.disconnect=接続が切断しました
log.system.vehicle.control.status.manual=手動
log.system.vehicle.control.status.auto=自動
log.system.vehicle.control.status.repair=检修
log.system.vehicle.abnormal.status.abnormal=異常
log.system.vehicle.abnormal.status.normal=異常なし
log.system.vehicle.position.status.notLocated=ローカライゼーションていない
log.system.vehicle.position.status.located=ローカライゼーション完了
log.system.charge.scheduler.error=AMR自動充電スケジューリング異常
log.system.charge.create.task.success=AMR充電タスク作成成功，AMR現在バッテリー残量，充電ポイント
log.system.charge.create.task.fail=AMR充電タスク作成失敗，タスクフロー
log.system.charge.vehicle.disable=AMR自動充電機能が有効化していない
log.system.charge.battery.value.is.null=AMR現在バッテリー残量は空値
log.system.charge.no.usable.charge.marker=AMRが充電ポイントを取得できない，封鎖された充電ポイント，既に他のAMRが使用中の充電ポイント，充電ポイントが到達できない
log.system.charge.get.other.charge.marker=AMRバッテリー残量が少ない，AMRの充電ポイントを先取りする
log.system.park.scheduler.error=AMR駐車スケジューリング異常
log.system.park.vehicle.disable=AMR自動駐車機能が有効化していない
log.system.park.no.usable.park.marker=AMRが使える駐車ポイントがありません
log.system.park.create.task.success=AMR駐車タスク作成成功
log.system.park.create.task.fail=AMR駐車タスク作成失敗
log.system.traffic.marker.is.not.avaliable.error=AMRが回避ポイントに到達できない，経路計画再度行います
log.system.traffic.resource.conflict=複数のAMR間の経路競合，経路計画再度行います
log.system.traffic.detect.obstacle=AMRが障害物を検出しました，経路計画再度行います
log.system.traffic.detect.vehicle=AMRが前方に他のAMRの阻害を検出しました, 経路計画再度行います
log.system.traffic.detect.control.area=AMRが封鎖エリアに遭遇，経路計画再度行います
log.system.traffic.detect.map.publish=AMR経路計画再度行います，ユーザーが手動で地図を公開しました
log.system.traffic.detect.vehicle.error=経路ナビゲーションで前方にAMRの阻害を検出し、回避経路のエラーが発生しました
log.system.traffic.detect.vehicle.drive=AMRが前方の障害物として別のAMRを検出, AMRを移動して, 指定された位置まで誘導する
log.system.traffic.detect.vehicle.drive.error=AMRが前方の障害物を検出されました、誘導失敗
log.system.auto.door.thread.error=自動ドアプログラム異常
log.system.auto.door.connect.error=自動ドア接続が失敗
log.system.auto.door.no.bind.path.error=自動ドアが経路にバインドされていません
log.system.auto.door.read.error=自動ドア指令を読み取り失敗
log.system.auto.door.write.error=自動ドーア指令を書き込み失敗
log.system.auto.door.open.ok=自動ドーアが開いた
log.system.auto.door.close.ok=自動ドーアが閉じった
log.system.air.shower.thread.error=エアシャワードアプログラム異常
log.system.air.shower.no.bind.path.error=エアシャワードア関連する経路がまだ設定されていない
log.system.air.shower.connect.error=エアシャワードア接続失敗
log.system.air.shower.read.error=エアシャワードアコマンドの読み取りが失敗
log.system.air.shower.write.error=エアシャワードアコマンドの書き込みが失敗
log.system.air.shower.open.ok=エアシャワードア開いた
log.system.air.shower.close.ok=エアシャワードア閉じった
log.system.elevator.thread.error=エレベータープログラム異常
log.system.elevator.no.bind.path.error=エレベーター関連するポイントがまだ設定されていない
log.system.elevator.vehicle.leave=AMRがエレベーターから離れた、エレベーター使用完了
log.system.elevator.vehicle.apply.run=AMRがエレベーターの昇降を申請します
log.system.elevator.connect.error=エレベーター接続失敗
log.system.elevator.read.error=エレベーターコマンドの読み取りが失敗
log.system.elevator.write.error=エレベーターコマンドの書き込みが失敗
log.system.task.allocation.error=AMRタスクスケジューリング異常
log.system.task.allocation.cancel.old.task.success=タスクが中断されました, 新しいタスクがAMRに割り当てる
log.system.task.allocation.interrupt.current.task=タスクがAMRに割り当てる, AMR現在実行ているタスクを中断します
log.system.task.allocation.success=タスクを割り当てる完了
log.system.task.allocation.fail.vehicle.no.exist=指定されたロボットの割り当てに失敗しました，AMRが存在しません
log.system.task.allocation.fail.vehicle.state.error=指定されたロボットの割り当てに失敗しました，AMRの状態が一致しません。現在の状態は
log.system.task.allocation.fail.vehicle.locked=指定されたロボットの割り当てに失敗しました，AMR現在が他のタスクを実行中
log.system.task.start.run=タスク実行開始
log.system.task.run.error=タスク実行異常
log.system.task.run.finish=タスク実行完了
log.system.task.cancel.run=タスク実行をキャンセルします
log.system.task.status.change.callback.request.param=タスク状態変更HTTPコールバック、リクエストパラメータ
log.system.task.status.change.callback.response.param=タスク状態変更HTTPコールバック
log.system.node.start=ノード実行開始
log.system.node.end=ノード実行完了
log.system.node.cancel=ノード実行をキャンセルします
log.system.node.error=ノード実行異常
log.system.node.no.available.marker=ポイントの割り当てに失敗しました。利用可能なポイントがありません
log.system.node.start.send=ノード実行、AMRから指令を配布します
log.system.node.send.succeed=ノード実行、AMRから指令を配布しました
log.system.node.send.error=ノード実行、AMRから指令を配布失敗しました
log.system.node.run.succeed=ノード実行、AMRが指令を実行完了しました
log.system.node.run.error=ノード実行、AMRが指令を実行失敗しました
log.system.node.start.send.cancel=ノード実行、AMRから停止指令を配布します
log.system.node.send.cancel.succeed=ノード実行、AMRから停止指令を配布しました
log.system.node.send.cancel.error=ノード実行、AMRから停止指令を配布失敗しました
log.system.node.stop.succeed=ノード実行、AMRが停止指令を実行完了しました
log.system.node.stop.error=ノード実行、AMRが停止指令を実行失敗しました
log.system.node.cancel.timeout=ノード実行、AMR停止指令がタイムアウトし、システムが強制的にタスクを停止しました
log.system.node.button.release.succeed=解放ボタンノード，解放成功
log.system.node.button.release.error=解放ボタンノード異常，接続失敗
log.system.node.button.reset.succeed=解放ボタンノードリセット成功
log.system.node.button.reset.error=解放ボタンノードリセット異常，デバイスと接続失敗
log.system.node.vehicle.no.assign.error=ノード実行、AMRがロックされていません
log.system.node.vehicle.no.exist.error=ノード実行、AMRが存在していません
log.system.node.vehicle.disconnect.error=AMR移動ノード， AMRの状態確認に失敗しました, AMRとの接続が中断しました
log.system.node.vehicle.abnormal.error=AMR移動ノード， AMRの状態確認に失敗しました, AMRに異常が存在する
log.system.node.vehicle.state.error=ノード実行、AMR状態が一致しません
log.system.node.vehicle.move.state.change.re.pathplan=AMR移動ノード， AMRの状態が変化しました，経路再計画します
log.system.node.vehicle.move.send.instruction=AMR移動ノード， AMRから移動指令を配布します
log.system.node.vehicle.move.finish=AMR移動ノード， AMRが移動指令を完成しました
log.system.node.vehicle.move.error=AMR移動ノード， AMRが移動指令を実行失敗しました
log.system.node.vehicle.move.send.cancel=AMR移動ノード， AMRが移動指令をキャンセルしました
log.system.node.vehicle.move.send.cancel.fail=AMR移動ノード， AMRが移動指令をキャンセル失敗
log.system.node.vehicle.move.cancel.success=机AMR移動ノード， AMRが移動指令をキャンセル成功
log.system.node.vehicle.move.cancel.fail=AMR移動ノード， AMRが移動指令をキャンセル失敗
log.system.node.vehicle.move.no.position=AMR移動ノード， AMR位置データがない
log.system.node.vehicle.move.stay.tartget.marker=AMR移動ノード， AMR既に目標ポイントに到着
log.system.node.vehicle.move.pathplan.start=AMR移動ノード，AMRが経路計画開始
log.system.node.vehicle.move.pathplan.success=AMR移動ノード，AMRが経路計画成功
log.system.node.vehicle.move.pathplan.message=AMR移動ノード，経路計画データを生成します
log.system.node.vehicle.move.pathplan.cancel=AMR移動ノード，経路ナビゲーションがキャンセルを検知しました
log.system.node.vehicle.move.re.pathplan=AMR移動ノード，AMRが指定経路で走行しませんを検知しました，経路再計画します
log.system.node.vehicle.move.stop.button=AMR移動ノード，AMRの緊急停止ボタンが押しされました
log.system.node.set.variable.param.is.empty=変数ノードを設定する, 数値変数の設定に必要なパラメータが見つかりません
log.system.node.set.variable.param.change.error=変数ノードを設定する, パラメータ値：数値型ではなく、数値変数に変換できません
log.system.node.set.variable.param.source.is.unknown=変数ノードを設定する, パラメータ名：パラメータの出所が見つかりません
log.system.node.set.variable.param.is.change=変数ノードを設定する, 変数名を変更します
log.system.node.finish.request.param=タスク終了時のHTTPコールバックがリクエストを送信しました
log.system.node.finish.response.param=タスク終了時のHTTPコールバックがレスポンスを受信しました
log.system.node.http.request.param=HTTPリクエストノードを実行し、リクエストを送信します
log.system.node.http.response.param=HTTPリクエストノードを実行し、レスポンスを受信します
log.system.node.http.check.param=HTTP検証ノードで検証値が不足しています
log.system.node.http.check.path=HTTPパラメータの検証に失敗しました。対応するフィールドが見つかりません
log.system.node.http.check.fail=HTTPパラメータの検証に失敗しました
log.system.trigger.callbox.success=呼び出しボックスボタンイベントをトリガ成功，タスク新規作成成功
log.system.trigger.callbox.fail=呼び出しボックスボタンイベントをトリガ成功，タスク新規作成失敗
log.system.trigger.fix.success=呼び出しボックス定時イベントをトリガ成功，タスク新規作成成功
log.system.trigger.fix.fail=呼び出しボックス定時イベントをトリガ成功，タスク新規作成失敗
log.system.trigger.plc.success=レジスタイベントをトリガ成功，タスク新規作成成功
log.system.trigger.plc.fail=レジスタイベントをトリガ成功，タスク新規作成失敗
log.system.trigger.task.cancel.success=タスクキャンセルイベントをトリガ成功，タスク新規作成成功
log.system.trigger.task.cancel.fail=タスクキャンセルイベントをトリガ成功，タスク新規作成失敗
log.system.trigger.task.finish.success=タスク実行完了イベントをトリガ成功，タスク新規作成成功
log.system.trigger.task.finish.fail=タスク実行完了イベントをトリガ成功，タスク新規作成失敗
log.system.trigger.vehicle.abnormal.success=AMR異常イベントをトリガ成功，タスク新規作成成功
log.system.trigger.vehicle.abnormal.fail=AMR異常イベントをトリガ成功，タスク新規作成失敗
log.system.trigger.vehicle.plc.success=AMRレジスタイベントをトリガ成功，タスク新規作成成功
log.system.trigger.vehicle.plc.fail=AMRレジスタイベントをトリガ成功，タスク新規作成失敗
log.system.export.error=一回のログエクスポート数は10000行以下、もう一度お願いします
log.system.export.name=実行ログ
log.system.download.file.not.exist=指定なファイルが見つかりません、ダウンロードができません
log.system.download.file.error=ファイルダウンロード失敗
log.system.excel.head.module=カテゴリー
log.system.excel.head.type=レベル
log.system.excel.head.content=説明
log.system.excel.head.data=数据
log.system.excel.head.message=メッセージ
log.system.excel.head.vehicleCodes=AMR
log.system.excel.head.taskNos=タスク
log.system.excel.head.createDate=作成時間
log.system.excel.head.lastTime=最後の更新時間
log.system.charge.station.connect=充電杭の接続に成功
log.system.charge.station.disconnect=充電杭の切断
log.system.charge.station.operate=充電杭操作結果
log.system.charge.station.cancel.charge.task=由于充电被强制停止，节点取消执行[未翻译]
validation.id.require=IDが未入力です
validation.id.null=IDを空いてください
validation.pid.require=上級IDが未入力です
validation.sort.number=ソート値は0以上に設定してください
validation.sysparams.paramcode.require=パラメータコードが未入力です
validation.sysparams.paramvalue.require=パラメータが未入力です
validation.sysuser.username.require=ユーザーネームが未入力です
validation.sysuser.password.require=パスワードが未入力です
validation.sysuser.realname.require=名前が未入力です
validation.sysuser.email.require=メートが未入力です
validation.sysuser.email.error=メートの書式が違います
validation.sysuser.mobile.require=携帯番号が未入力です
validation.sysuser.superadmin.range=スーパ管理者の値の範囲は0～1です
validation.sysuser.status.range=状態の値の範囲は0～1です
validation.sysmenu.pid.require=上位メニューを選択してください
validation.sysmenu.name.require=メニュー名は空にできません
validation.sysmenu.type.range=メニュータイプの値の範囲は0～1です
validation.sysrole.name.require=ロール名は空にできません。
validation.schedule.status.range=状態の値の範囲は0～1です
validation.schedule.cron.require=cron式は空にできません
validation.schedule.bean.require=bean名は空にできません
validation.news.title.require=タイトルは空にできません
validation.news.content.require=内容は空にできません
validation.news.pubdate.require=公開日は空にできません
validation.map.marker.name=ポイント名の命名ルール：空、または英字または数字、アンダースコア、英字で始まり、長さは20文字以下
validation.map.marker.type.require=ポイントタイプは空にできません
validation.map.marker.code.require=ポイントコードは空にできません
validation.map.marker.type=ポイントタイプは以下のいずれかでなければなりません：ChargingMarker,NavigationMarker,WorkMarker
validation.map.marker.x.require=x座標は空にできません
validation.map.marker.y.require=y座標は空にできません
validation.map.path.type.require=経路タイプは空にできません
validation.map.path.type=経路タイプは以下のいずれかでなければなりません：Common（普通経路）、QR_Down（二次元コード連携経路）、Shelflegs（棚足連携）、Symbol_V（V型板連携）、Reflector（反射板連携）、LeaveDocking（ドッキング解除）、Pallet（パレット連携）
validation.map.path.startMarkerCode.require=開始マーカーポイントは空にできません
validation.map.path.endMarkerCode.require=終了マーカーポイントは空にできません
validation.map.path.weightRatio.require=経路の重みは正の数でなければなりません
validation.map.area.areaType.require=エリアタイプは空にできません。列挙値： 単機エリア:SingleAgvArea 表示エリア: ShowArea 封鎖エリア：ControlArea 通路エリア：ChannelArea 回転禁止エリア：NoRotatingArea 駐車禁止エリア：NoParkingArea
validation.map.area.areaType=エリアタイプは以下のいずれかでなければなりません：SingleAgvArea,ShowArea,ControlArea,ChannelArea,NoRotatingArea,NoParkingArea
validation.map.area.polygon.require=エリア座標リストは空にできません
validation.map.area.operateType.require=エリア構築方法は空にできません。列挙値：1、ポリゴンエリア方式による構築、2、矩形エリア方式による構築
validation.map.type.require=地図タイプは空にできません
validation.map.code.require=地図コードは空にできません
validation.map.name.require=地図名は空にできません
validation.map.originX.require=地図の中心x座標は空にできません
validation.map.originY.require=地図の中心y座標は空にできません
validation.map.resolution.require=地図の解像度は空にできません
validation.map.height.require=地図のピクセル高さは空にできません
validation.map.width.require=地図のピクセル幅は空にできません
validation.door.code.require=自動ドアコードは空にできません
validation.door.ip.require=自動ドアのIPは空にできません
validation.door.port.require=自動ドアのポートは空にできません
validation.door.openAddress.require=自動ドアの開放アドレスは空にできません
validation.door.openStatusAddress.require=自動ドアの開放状態アドレスは空にできません
validation.door.closeAddress.require=自動ドアの閉鎖アドレスは空にできません
validation.door.closeStatusAddress.require=自動ドアの閉鎖状態アドレスは空にできません
validation.door.pathCodes.require=自動ドア関連の経路コードは空にできません
validation.elevator.code.require=エレベーターコードは空にできません
validation.elevator.ip.require=エレベーターのIPは空にできません
validation.elevator.port.require=エレベーターのポートは空にできません
validation.elevator.controlAddress.require=エレベーター制御アドレスは空にできません
validation.elevator.destAddress.require=エレベーターの目的アドレスは空にできません
validation.elevator.openAddress.require=エレベーターの開放アドレスは空にできません
validation.elevator.readFunctionCode.require=エレベーターのModbus読取機能コードは空にできません
validation.elevator.writeFunctionCode.require=エレベーターのModbus書込機能コードは空にできません
validation.property.type.require=システムプロパティのタイプは空にできません
validation.property.category.require=システムプロパティのカテゴリは空にできません
validation.property.propertyKey.require=システムプロパティのキー値は空にできません
validation.property.valueType.require=システムプロパティの値タイプは空にできません
vehicleMap.airShowerDoor.not.exist.error=操作失敗、エアシャワードア[%s]が存在しません
vehicleMap.airShowerDoor.add.error=エアシャワードアを新規作成する異常、操作ログを確認してください。
vehicleMap.airShowerDoor.update.error=エアシャワードア[%s]を編集する異常、操作ログを確認してください
vehicleMap.airShowerDoor.delete.error=エアシャワードア[%s]を削除する異常、操作ログを確認してください
vehicleMap.airShowerDoor.bind.path.error=エアシャワードア[%s]の経路バインド異常、重複する経路がバインドされています
vehicleMap.autoDoor.not.exist.error=操作失敗、自動ドア[%s]が存在しません
vehicleMap.autoDoor.already.bind.other.device.error=操作失敗、経路[%s]は既に他のデバイスにバインドされています
vehicleMap.autoDoor.add.error=自動ドアを新規作成する異常、操作ログを確認してください
vehicleMap.autoDoor.update.error=自動ドア[%s]を編集する異常、操作ログを確認してください
vehicleMap.autoDoor.delete.error=自動ドア[%s]を削除する異常、操作ログを確認してください
vehicleMap.elevator.add.error=エレベーターを新規作成する異常、操作ログを確認してください
vehicleMap.elevator.update.error=エレベーター[%s]を編集する異常、操作ログを確認してください
vehicleMap.elevator.delete.error=エレベーター[%s]を削除する異常、操作ログを確認してください
vehicleMap.elevator.not.exist.error=操作失敗、エレベーター[%s]は既に存在します
vehicleMap.elevator.file.format.error=エレベーターファイルのフォーマットが正しくありません
vehicleMap.elevator.import.already.exist.error=エレベーターのインポート異常、エレベーター[%s]已存在
vehicleMap.elevator.import.error=エレベーターのインポート異常、原因：%s
vehicleMap.elevator.export.error=エレベーターのエクスポート異常、原因：%s
vehicleMap.elevator.publish.check.error=地図の公開チェック、関連するエレベーターが使用中です 強制的に公開しますか
vehicleMap.elevator.import.bind.map.error=エレベーター[%s]のエクスポートが失敗しました まずエレベーターがバインドされているすべての地図をインポートしてください
vehicleMap.elevator.bind.multi.marker.error=エレベーターバインド異常、エレベーターは同じ地図の複数のポイントにバインドできません
vehicleMap.mapArea.not.exist.error=操作失敗、エリア[%s]が存在しません
vehicleMap.mapArea.add.error=エリアを新規作成する異常、操作ログを確認してください
vehicleMap.mapArea.update.error=エリア[%s]を編集する異常、操作ログを確認してください
vehicleMap.mapArea.update.occupied.error=[未翻译]
vehicleMap.mapArea.delete.error=エリア[%s]を削除する異常、操作ログを確認してください
vehicleMap.marker.add.error=ポイントを新規作成する異常、操作ログを確認してください
vehicleMap.marker.update.error=ポイント[%s]を編集する異常、操作ログを確認してください
vehicleMap.marker.delete.error=ポイント[%s]を削除する異常、操作ログを確認してください
vehicleMap.marker.not.exist.error=操作失敗、ポイント[%s]が存在しません
vehicleMap.marker.already.bind.other.device.error=操作失敗、ポイント[%s]は既に他のデバイスにバインドされています
vehicleMap.marker.spacing.error=ポイント間の間隔が設定値[%s]未満です
vehicleMap.path.bind.marker.no.exist.error=操作失敗、経路にバインドされたポイント[%s]が存在しません
vehicleMap.path.already.bind.device.error=操作失敗、経路[%s]は既にデバイスにバインドされています
vehicleMap.path.already.exist.error=操作失敗、経路[%s]は既に存在します
vehicleMap.path.not.exist.error=操作失敗、経路[%s]が存在しません
vehicleMap.path.add.error=経路を新規作成する異常、操作ログを確認してください
vehicleMap.path.update.error=経路[%s]を編集する異常、操作ログを確認してください
vehicleMap.path.delete.error=経路[%s]を編集する異常、操作ログを確認してください
vehicleMap.map.operating.duplicate.error=処理中です、同じ操作を繰り返さないでください
vehicleMap.map.not.exist.error=地図[%s]が存在しません、地図編集ページを終了してください
vehicleMap.map.file.format.error=インポートされたファイル[%s]のフォーマットが正しくありません
vehicleMap.map.add.error=地図を新規作成する異常、操作ログを確認してください
vehicleMap.map.update.error=地図[%s]を編集する異常、操作ログを確認してください
vehicleMap.map.delete.error=地図[%s]を削除する異常、操作ログを確認してください
vehicleMap.map.roadnet.update.error=地図[%s]の経路要素を編集する異常、操作ログを確認してください
vehicleMap.map.roadnet.delete.error=地図[%s]の経路要素を削除する異常、操作ログを確認してください
vehicleMap.map.draft.delete.error=地図[%s]の経路ドラフトを削除する異常、操作ログを確認してください
vehicleMap.map.is.not.publish.error=地図[%s]はまだ正式版がありません
vehicleMap.map.import.error=地図のインポート異常、原因：%s
vehicleMap.map.import.structure.error=インポートされた地図ファイルのフォーマットが正しくありません
vehicleMap.map.import.in.edit.page.error=地図編集ページでロケーション図をインポートしてください
vehicleMap.map.import.missing.info.error=インポートされた地図にinfoファイルがありません
vehicleMap.map.import.missing.png.error=インポートされた地図にpngファイルがありません
vehicleMap.map.import.missing.locating.error=インポートされた地図ファイルのフォーマットが正しくありません、経路ファイルにロケーション図情報がありません
vehicleMap.map.import.appoint.default.error=インポートされた地図ファイルのフォーマットが正しくありません、経路ファイルにデフォルトのロケーション図が指定されていません
vehicleMap.map.export.error=地図のエクスポート異常、原因：%s
vehicleMap.map.copy.error=地図のコピー異常、原因：%s
vehicleMap.map.reset.error=現在の地図[%s]には取り消し可能な操作がありません
vehicleMap.map.recover.error=現在の地図[%s]には復元可能な操作がありません
vehicleMap.map.global.recover.error=AMRの復元に失敗しました：%s
vehicleMap.map.publish.occupied.error=[未翻译]
vehicleMap.map.locatingmap.is.empty.error=地図[%s]のロケーション図データが空です!
vehicleMap.map.locatingmap.code.is.empty.error=地図[%s]のロケーション図コードが未入力です!
vehicleMap.map.locatingmap.not.exist.error=ロケーション図[%s]が存在しません、操作ログを確認してください
vehicleMap.map.locatingmap.update.error=ロケーション図[%s]を編集する異常、操作ログを確認してください
vehicleMap.map.locatingmap.default.error=[%s]のデフォルトロケーション図[%s]を削除することはできません
vehicleMap.map.locatingmap.import.structure.error=インポートされたロケーション図ファイルのディレクトリ構造が異常です
vehicleMap.map.locatingmap.import.file.is.empty.error=インポートされたロケーション図ファイルリストが空です
vehicleMap.map.locatingmap.import.file.is.missing.error=インポートされたロケーション図[%s]のファイルが見つかりません
vehicleMap.map.locatingmap.import.file.is.duplicate.error=该定位图%s已存在于地图%s中，请检查后再操作(待翻译)
vehicleMap.map.locatingmap.import.file.is.forbidden.error=该编码定位图%s不允许导入，请检查后再操作(待翻译)
vehicleMap.map.locatingmap.export.error=ロケーション図[%s]のエクスポート異常、原因：%s
device.connect.error=デバイス操作失敗、デバイス[%s]ネット接続していません
device.open.error=デバイス[%s]起動異常、操作ログを確認してください
device.close.error=デバイス[%s]シャットダウン異常、操作ログを確認してください
device.is.in.use.error=デバイス操作失敗、デバイス[%s]使用中
task.node.config.export.file.name=ノード設定
task.node.config.export.error=ノード設定のエクスポート異常、原因：%s
task.node.config.import.error=ノード設定のインポート異常、原因：%s
task.node.is.not.exist.error=タスクノード[%s]が存在しません
task.node.is.not.allow.retry.error=タスクノード[%s]はリトライが許可されていません
task.node.is.not.allow.skip.error=タスクノード[%s]はスキップが許可されていません
task.type.is.not.published.error=タスクの新規作成失敗、未公開のタスクフロー[%s]が使用されています
task.type.is.not.exist.error=タスクフロー[%s]が存在しません
task.type.export.error=タスクフローのエクスポート異常、原因：%s
task.type.import.error=タスクフローのインポート異常、原因：%s
task.type.node.is.empty.error=タスクの有効化失敗、タスクフロー[%s]に使用可能なノードがありません
task.type.enable.el.parse.error=有効化失敗、原因：%s
task.type.enable.param.is.null.error=未設定のパラメータがあるノード：[%s]
task.type.node.while.is.empty.error=空のループがあるノード：[%s]
task.type.prefix.name=タスクフロー
task.is.not.exist.error=タスク[%s]が存在しません
task.delete.running.error=実行中のタスクは削除できません
task.cancel.running.error=キャンセル失敗、このタスクはキャンセルが禁止されています
task.export.file.name=タスク
task.export.error=タスクのエクスポート異常、原因：%s
task.import.error=タスクのインポート異常、原因：%s
task.import.code.duplicate.error=タスク記録のアップロード失敗、重複するタスクコード[%s]
task.cancel.timeout.error=AMR指令のキャンセルタイムアウト、AMR[%s]を再起動して指令をクリアしてください
task.insert.vehicle.not.exist.error=タスクの新規作成失敗、入力されたAMR[%s]が存在しません
task.insert.marker.not.exist.error=タスクの新規作成失敗、入力されたポイント[%s]が存在しません
task.insert.marker.lock.error=ポイント[%s]は他のタスクに占有されています
task.insert.map.not.exist.error=タスクの新規作成失敗、入力された地図[%s]が存在しません
task.insert.dynamic.param.format.error=渡された動的パラメータ[%s]のフォーマット[%s]が正しくありません
task.excel.head.taskNo=番号
task.excel.head.externalTaskNo=外部コード
task.excel.head.name=名称
task.excel.head.status=状態
task.excel.head.priority=優先度
task.excel.head.vehicleCodes=AMR
task.excel.head.source=作成方法
task.excel.head.createDate=作成時間
task.excel.head.startTime=開始時間
task.excel.head.endTime=終了時間
task.excel.head.remark=備考
task.excel.head.callbackUrl=上流コールバックアドレス
task.event.not.exist.error=イベント[%s]が存在しません
task.event.bound.taskType.is.null.error=イベント[%s]に関連付けられたタスクフローがありません
task.event.running.duplicate.error=現在のイベントは重複を許可せず、実行中のタスクがあります
task.event.plc.condition.check.fail.error=レジスタトリガー条件の検証に失敗しました、確認してください
task.event.vehicle.condition.check.fail.error=AMRレジスタトリガー条件の検証に失敗しました、確認してください
task.event.fix.interval.time.error=間隔時間の設定が誤っています、確認してください
task.event.relate.task.contain.cancel.node.error=キャンセルタスクノードに関連付けられたタスク[%s]には、キャンセルタスクノードを含めることはできません
task.event.relate.task.create.error=キャンセルタスクノードに関連付けられたタスク[%s]の作成失敗、原因：[%s]
task.event.type.fixedTime=定時イベント
task.event.type.button=ボタンイベント
task.event.type.plc=レジスタイベント
task.event.type.vehiclePlc=AMRレジスタイベント
task.event.type.vehicleAbnormal=AMR異常イベント
task.event.type.taskCancel=タスクキャンセルイベント
task.event.type.taskFinished=タスク完了イベント
task.event.status.enable=有効化
task.event.status.disable=無効化
task.event.repeat.allow=許可
task.event.repeat.disallow=禁止
task.event.export.file.name=イベント
task.event.excel.head.code=イベントコード
task.event.excel.head.name=イベント名
task.event.excel.head.type=イベントタイプ
task.event.excel.head.isAllowRepeat=重複許可
task.event.excel.head.taskTypeId=タスクタイプ
task.event.excel.head.status=有効化状態
task.event.excel.head.param=イベントパラメータ
task.event.excel.head.taskParam=タスクパラメータ
task.node.name.Wait=待ち
task.node.name.DynamicAllocationVehicle=ランダムロボット割り当て
task.node.name.AllocationMarker=ランダムポイント割り当て
task.node.name.ReleaseVehicle=ロボットを解放する
task.node.name.VehicleRotation=ロボット回転
task.node.name.TrayRotation=パレット回転
task.node.name.TrayLifting=リフト機構を制御
task.node.name.ReadPlc=レジスタを読み込み
task.node.name.WritePlc=レジスタを書き込み
task.node.name.AssignAllocationVehicle=ロボットの指定割り当て
task.node.name.VehicleMove=AMR移動
task.node.name.GetMarkerAttribute=ポイントプロパティを取得
task.node.name.DockingCharge=充電ドッキング
task.node.name.ButtonRelease=ボタンで解放
task.node.name.ButtonReset=ボタンでリセット
task.node.name.Alarm=音声とランプのアラーム
task.node.name.ReadVehiclePlc=ロボットレジスタを読み込み
task.node.name.WriteVehiclePlc=ロボットレジスタを書き込み
task.node.name.Rotation=回転動作
task.node.name.RobotArmScript=ロボットアーム制御
task.node.name.CheckPlc=レジスタチェック
task.node.name.PlayAudio=音声を再生
task.node.name.SwitchSpeedArea=速度エリアを切り替え
task.node.name.SwitchDockingArea=ドッキングエリアを切り替え
task.node.name.TrayFollowControl=パレットモード切り替え
task.node.name.CheckVehiclePlc=ロボットレジスタをチェック
task.node.name.ForkArmLifting=フォークリフトの昇降
task.node.name.GetTaskAttribute=タスクプロパティを取得
task.node.name.FinishTask=タスク終了
task.node.name.HttpRequest=HTTPリクエスト
task.node.name.NobodyForkCharge=フォークリフト充電
task.node.name.DockingNavigation=ドッキング
task.node.name.SwitchScheduleMode=スケジューリングモードに切り替え
task.node.name.GetVehicleAttribute=ロボットプロパティを取得
task.node.name.AllocationWarehouse=保管場所の割り当て
task.node.name.UpdateWarehouse=保管場所更新
task.node.name.GetWarehouseAttribute=保管場所プロパティを取得
task.node.name.GetBarcodeAttribute=バーコードプロパティを取得
task.node.name.GetAdjacentMarker=隣接ポイントの取得
task.node.name.Ajust=微調整
task.node.name.LeaveDocking=ドッキング解除
task.node.name.StopAudio=音声生成を停止
task.node.name.ReadQrCode=QRコードを読み込み
task.node.name.CheckSensorObstacleStatus=フォークリフト荷物置き検出
task.node.name.SetVariable=変数設定
task.node.name.Notice=通知アラート
task.node.name.CancelTask=タスクキャンセル
task.node.name.ForbidCancelTask=タスクキャンセル禁止
task.node.name.OperateMapArea=地図操作エリア
task.node.name.HttpCheck=Httpチェック
task.node.name.JavaScript=Javaスクリプト
task.node.name.ReadPLCStr=レジスタ読み込み - 文字列
task.node.name.WritePLCStr=レジスタ書き込み-文字列
task.node.name.LockMarker=锁定点位(待翻译)
task.node.name.StopCharge=停止充电(待翻译)
task.node.notice.Wait=指定した時間が経過した後、次のノードを実行します
task.node.notice.DynamicAllocationVehicle=ランダムロボット割り当て、タスク占用成功した後ノードを実行する
task.node.notice.AllocationMarker=ランダムポイント割り当て
task.node.notice.ReleaseVehicle=ロボットを現在タスクの占用状態から解放する、解放されたロボットが他のタスクを取ることができます
task.node.notice.VehicleRotation=ロボットの回転を制御する
task.node.notice.TrayRotation=ロボットのパレット回転を制御する
task.node.notice.TrayLifting=リフト機構を制御する
task.node.notice.ReadPlc=リモートレジスタ読み込み
task.node.notice.WritePlc=外部のレジスタに書き込み
task.node.notice.AssignAllocationVehicle=タスクを指定されたロボットで取る、タスク占用成功した後ノードを実行する
task.node.notice.VehicleMove=ロボットのルートを計画し、目標位置まで制御します
task.node.notice.GetMarkerAttribute=ポイント属性を取って、他のノードに使用ため出力します
task.node.notice.DockingCharge=AMR充電ドッキングを制御する
task.node.notice.ButtonRelease=YOUIの呼び出しボックスを押された後、ノードを続いて実行する
task.node.notice.ButtonReset=呼び出しボックスをリセットできる、リセットされたボタンも再度トリガーできる
task.node.notice.Alarm=音声とランプのアラームを制御してアラームを行う
task.node.notice.ReadVehiclePlc=ロボット内部のレジスタを読み込み
task.node.notice.WriteVehiclePlc=ロボット内部のレジスタに書き込み
task.node.notice.Rotation=ロボットとパレットの連動回転を制御する
task.node.notice.RobotArmScript=Jsonスクリプトに通じて、ロボットアームの運動を制御する
task.node.notice.CheckPlc=レジスタチェック
task.node.notice.PlayAudio=音声を再生
task.node.notice.SwitchSpeedArea=速度エリアを切り替え
task.node.notice.SwitchDockingArea=ドッキングエリアを切り替え
task.node.notice.TrayFollowControl=パレットモード切り替え
task.node.notice.CheckVehiclePlc=ロボットレジスタをチェック
task.node.notice.ForkArmLifting=無人フォークリフトのフォークアームの昇降を制御します
task.node.notice.GetTaskAttribute=タスクプロパティを取得
task.node.notice.FinishTask=タスク終了
task.node.notice.HttpRequest=外部システムにリクエストを送信し、実行結果を待機します
task.node.notice.NobodyForkCharge=無人フォークリフトノード
task.node.notice.DockingNavigation=ドッキング
task.node.notice.SwitchScheduleMode=スケジューリングモードに切り替え
task.node.notice.GetVehicleAttribute=ロボットプロパティを取得
task.node.notice.AllocationWarehouse=保管場所の割り当て
task.node.notice.UpdateWarehouse=保管場所更新
task.node.notice.GetWarehouseAttribute=保管場所プロパティを取得
task.node.notice.GetBarcodeAttribute=バーコードプロパティを取得
task.node.notice.GetAdjacentMarker=隣接ポイントの取得
task.node.notice.Ajust=微調整
task.node.notice.LeaveDocking=ドッキング解除
task.node.notice.StopAudio=音声生成を停止する
task.node.notice.ReadQrCode=QRコードを読み込み
task.node.notice.CheckSensorObstacleStatus=フォークリフト先端センサーの障害物回避トリガーの状態。true: 障害物回避がトリガーされている；false: 障害物回避がトリガーされていない
task.node.notice.SetVariable=このノードが再設定できる変数の値
task.node.notice.Notice=通知アラート
task.node.notice.CancelTask=タスクキャンセルイベント
task.node.notice.ForbidCancelTask=タスクキャンセル禁止
task.node.notice.OperateMapArea=地図操作エリア
task.node.notice.HttpCheck=HTTP（POSTリクエスト）結果のチェック
task.node.notice.JavaScript=Javaスクリプトノード
task.node.notice.ReadPLCStr=PLCのASCIIを読みこんで、文字列に変換する
task.node.notice.WritePLCStr=PLCにASCIIコードを書き込み
task.node.notice.LockMarker=锁定点位(待翻译)
task.node.notice.StopCharge=停止充电(待翻译)
task.node.param.name.1718202092436357121.vehicleCode.In=ロボット
task.node.param.name.1718202092436357121.position.In=目的高さ
task.node.param.name.1718202092436357121.offsetHeight.In=高さオフセット
task.node.param.name.1718202092436357121.speed.In=速度
task.node.param.name.1738448135527288834.vehicleCode.In=ロボット
task.node.param.name.1738448135527288834.markerCode.In=充電ポイント
task.node.param.name.1738448135527288834.chargeTime.In=充電時間
task.node.param.name.1738448135527288834.batteryCharge.In=充電量
task.node.param.name.1738443040093851650.finishType.In=終了タイプ
task.node.param.name.1738443040093851650.noticeMsg.In=終了ヒント
task.node.param.name.1646764086215663617.vehicleCode.In=ロボット
task.node.param.name.1646764086215663617.vehicleAngle1.In=ロボット角度1
task.node.param.name.1646764086215663617.vehicleAngle2.In=ロボット角度2
task.node.param.name.1646764086215663617.trayRotationSpeed.In=パレット回転速度
task.node.param.name.1646764086215663617.trayAngle1.In=パレット角度1
task.node.param.name.1646764086215663617.trayAngle2.In=パレット角度2
task.node.param.name.1715183824889581570.vehicleCode.In=ロボット
task.node.param.name.1715183824889581570.ladarSwitch.In=速度エリア
task.node.param.name.1715184972354686978.vehicleCode.In=ロボット
task.node.param.name.1715184972354686978.obstacleArea.In=検出範囲
task.node.param.name.1738467719873515521.vehicleCode.In=ロボット
task.node.param.name.1738467719873515521.scheduleMode.In=スケジューリングモード
task.node.param.name.1715183168871075842.vehicleCode.In=ロボット
task.node.param.name.1715183168871075842.audioName.In=音声名称
task.node.param.name.1715183168871075842.audioVolume.In=音量
task.node.param.name.1715183168871075842.playCount.In=再生回数
task.node.param.name.1630863227598438401.markerCode.In=ポイント
task.node.param.name.1630863227598438401.vehicleGroupCode.In=ロボットグループ
task.node.param.name.1630863227598438401.vehicleTypeCode.In=ロボットタイプ
task.node.param.name.1630863227598438401.vehicleMapCodeList.In=地図
task.node.param.name.1630863227598438401.limitBattery.In=バッテリー残量
task.node.param.name.1630863227598438401.outVehicleCode.Out=ロボット
task.node.param.name.1630863227745239041.vehicleCode.In=ロボット
task.node.param.name.1630863227745239041.outVehicleCode.Out=ロボット
task.node.param.name.1630863227644575745.vehicleCode.In=ロボット
task.node.param.name.1630863227623604225.vehicleMapCodeList.In=地図
task.node.param.name.1630863227623604225.markerType.In=ポイントタイプ
task.node.param.name.1630863227623604225.outMarkerCode.Out=ポイントを選定
task.node.param.name.1630863227623604225.vehicleCode.In=ロボット
task.node.param.name.1738440272671100929.taskNo.Out=タスクコード
task.node.param.name.1738440272671100929.externalTaskNo.Out=外部タスクコード
task.node.param.name.1738440272671100929.priority.Out=優先順位
task.node.param.name.1630863227707490306.ip.In=IP
task.node.param.name.1630863227707490306.port.In=ポート
task.node.param.name.1630863227707490306.code.In=機能コード
task.node.param.name.1630863227707490306.slaveId.In=slaveId
task.node.param.name.1630863227707490306.address.In=読み込みアドレス
task.node.param.name.1630863227707490306.executeMode.In=実行方法
task.node.param.name.1630863227707490306.vehicleCode.In=ロボット
task.node.param.name.1630863227707490306.outValue.Out=出力値
task.node.param.name.1645676364679905282.vehicleCode.In=ロボット
task.node.param.name.1645676364679905282.code.In=機能コード
task.node.param.name.1645676364679905282.slaveId.In=slaveId
task.node.param.name.1645676364679905282.address.In=読み込みアドレス
task.node.param.name.1645676364679905282.outValue.Out=レジスタ値
task.node.param.name.1630863227724267521.ip.In=IP
task.node.param.name.1630863227724267521.port.In=ポート
task.node.param.name.1630863227724267521.code.In=機能コード
task.node.param.name.1630863227724267521.slaveId.In=slaveId
task.node.param.name.1630863227724267521.address.In=アドレスを入力
task.node.param.name.1630863227724267521.value.In=入力値
task.node.param.name.1630863227724267521.executeMode.In=実行方法
task.node.param.name.1630863227724267521.vehicleCode.In=ロボット
task.node.param.name.1645678201743114241.vehicleCode.In=ロボット
task.node.param.name.1645678201743114241.code.In=機能コード
task.node.param.name.1645678201743114241.slaveId.In=slaveId
task.node.param.name.1645678201743114241.address.In=アドレスを入力
task.node.param.name.1645678201743114241.value.In=入力値
task.node.param.name.1715188504537468930.vehicleCode.In=ロボット
task.node.param.name.1715188504537468930.code.In=機能コード
task.node.param.name.1715188504537468930.slaveId.In=slaveId
task.node.param.name.1715188504537468930.address.In=レジスタアドレス
task.node.param.name.1715188504537468930.successVal.In=通常退出
task.node.param.name.1715188504537468930.failVal.In=失敗退出
task.node.param.name.1715188504537468930.timeout.In=タイムアウト時間
task.node.param.name.1715188504537468930.outValue.Out=出力値
task.node.param.name.1641376178617024513.callBoxCode.In=呼び出しボックスコード
task.node.param.name.1641376178617024513.buttonCode.In=ボタンコード
task.node.param.name.1641376178617024513.timeout.In=タイムアウト解放（秒）
task.node.param.name.1641376553134817282.callBoxCode.In=呼び出しボックスコード
task.node.param.name.1641376553134817282.buttonCode.In=ボタンコード
task.node.param.name.1641377688272863233.ip.In=IP
task.node.param.name.1641377688272863233.port.In=ポート
task.node.param.name.1641377688272863233.type.In=アラームタイプ
task.node.param.name.1641377688272863233.time.In=続く時間（秒）
task.node.param.name.1742437277025456130.warehouseCode.In=保管場所
task.node.param.name.1742437277025456130.containerBarcode.In=コンテナ
task.node.param.name.1742437277025456130.dispatchPolicy.In=割り当てポリシー
task.node.param.name.1742437277025456130.occupyStatus.In=保管エリア状態
task.node.param.name.1742437277025456130.warehouseTypeCode.In=保管場所タイプ
task.node.param.name.1742437277025456130.warehouseAreaCode.In=保管エリア
task.node.param.name.1742437277025456130.extendParam1.In=拡張プロパティ1
task.node.param.name.1742437277025456130.extendParam2.In=拡張プロパティ2
task.node.param.name.1742437277025456130.extendParam3.In=拡張プロパティ3
task.node.param.name.1742437277025456130.extendParam4.In=拡張プロパティ4
task.node.param.name.1742437277025456130.extendParam5.In=拡張プロパティ5
task.node.param.name.1742437277025456130.warehouseCode.Out=保管場所コード
task.node.param.name.1742441148997193730.containerBarCode.In=コンテナバーコード
task.node.param.name.1742441148997193730.warehouseCode.Out=保管エリアコード
task.node.param.name.1742441148997193730.workMarkerCode.Out=作業ポイントコード
task.node.param.name.1742441148997193730.workHeight.Out=作業高さ
task.node.param.name.1742441148997193730.warehouseTypeCode.Out=保管場所タイプコード
task.node.param.name.1742441148997193730.warehouseAreaCode.Out=保管エリアコード
task.node.param.name.1742440444115046401.warehouseCode.In=保管場所
task.node.param.name.1742440444115046401.containerBarcode.Out=コンテナバーコード
task.node.param.name.1742440444115046401.workMarkerCode.Out=作業ポイントコード
task.node.param.name.1742440444115046401.workHeight.Out=作業高さ
task.node.param.name.1742440444115046401.warehouseTypeCode.Out=保管場所タイプコード
task.node.param.name.1742440444115046401.warehouseAreaCode.Out=保管エリアコード
task.node.param.name.1742441529047273474.markerCode.In=ポイント
task.node.param.name.1742441529047273474.markerCode.Out=隣接ポイントコード
task.node.param.name.1630863227652964354.vehicleCode.In=ロボット
task.node.param.name.1630863227652964354.angle.In=回転角度
task.node.param.name.1630863227652964354.rateType.In=回転タイプ
task.node.param.name.1630863227673935874.vehicleCode.In=ロボット
task.node.param.name.1630863227673935874.angle1.In=回転角度1
task.node.param.name.1630863227673935874.angle2.In=回転角度2
task.node.param.name.1630863227673935874.speed.In=回転速度
task.node.param.name.1630863227673935874.rateType.In=回転タイプ
task.node.param.name.1750822156822622210.vehicleCode.In=ロボット
task.node.param.name.1750822156822622210.offsetX.In=オフセット X
task.node.param.name.1750822156822622210.offsetY.In=オフセットY
task.node.param.name.1750822156822622210.offsetAngle.In=角度オフセット
task.node.param.name.1750822156822622210.rotationAngleRange.In=回転範囲
task.node.param.name.1750822156822622210.moveDistanceRange.In=移動範囲
task.node.param.name.1750822156822622210.obstacleRegion.In=検出エリア
task.node.param.name.1738450017482133505.vehicleCode.In=ロボット
task.node.param.name.1738450017482133505.dockingType.In=タイプ
task.node.param.name.1738450017482133505.startX.In=開始点 X 座標
task.node.param.name.1738450017482133505.startY.In=開始点 Y 座標
task.node.param.name.1738450017482133505.startAngle.In=開始点角度
task.node.param.name.1738450017482133505.endX.In=終点 X 座標
task.node.param.name.1738450017482133505.endY.In=終点 Y 座標
task.node.param.name.1738450017482133505.endAngle.In=終点角度
task.node.param.name.1738450017482133505.offsetX.In=オフセット X
task.node.param.name.1738450017482133505.offsetY.In=オフセット Y
task.node.param.name.1738450017482133505.offsetAngle.In=角度オフセット
task.node.param.name.1738450017482133505.workStationCode.In=作業位置コード
task.node.param.name.1738450017482133505.templateNo.In=テンプレート番号
task.node.param.name.1738450017482133505.cameraObstacle.In=カメラ検出
task.node.param.name.1738450017482133505.obstacleRegion.In=検出エリア
task.node.param.name.1751081370463637506.vehicleCode.In=ロボット
task.node.param.name.1738450017482133505.dockingDirection.In=ドッキング方向
task.node.param.name.1715186203621986306.vehicleCode.In=ロボット
task.node.param.name.1715186203621986306.type.In=天板フォロー
task.node.param.name.1630863227694907394.vehicleCode.In=ロボット
task.node.param.name.1630863227694907394.speed.In=昇降速度
task.node.param.name.1630863227694907394.targetTicks.In=昇降高さ
task.node.param.name.1751878094551769090.vehicleCode.In=ロボット
task.node.param.name.1751878094551769090.outValue.Out=QRコードデータ
task.node.param.name.1751825844022235138.vehicleCode.In=ロボット
task.node.param.name.1630863227787182081.markerCode.In=ポイント
task.node.param.name.1630863227787182081.markerName.In=カスタムリクエストコード
task.node.param.name.1630863227787182081.code.Out=ポイントコード
task.node.param.name.1630863227787182081.name.Out=カスタムリクエストポイントコード
task.node.param.name.1630863227787182081.type.Out=ポイントタイプ
task.node.param.name.1630863227787182081.isPark.Out=駐車可能
task.node.param.name.1630863227787182081.angle.Out=ポイント角度
task.node.param.name.1630863227787182081.chargeEnable.Out=是否允许工作时充电(待翻译)
task.node.param.name.1790203373283213314.oriValue.In=変数名
task.node.param.name.1790203373283213314.newValue.In=期待される変数値
task.node.param.name.1738444988633272322.url.In=Url アドレス
task.node.param.name.1738444988633272322.customParams.In=カスタム入力パラメータ
task.node.param.name.1738444988633272322.customValues.Out=カスタム出力パラメータ
task.node.param.name.1630863227757821953.vehicleCode.In=ロボット
task.node.param.name.1630863227757821953.markerCode.In=目標ポイント
task.node.param.name.1630863227757821953.dockingMove.In=ドッキングナビゲーション
task.node.param.name.1630863227757821953.accurate.In=精ローカライゼーション
task.node.param.name.1630863227757821953.fallPrevent.In=落下防止
task.node.param.name.1630863227757821953.safety3D.In=3D検出
task.node.param.name.1630863227757821953.featureFusion.In=ハイブリッド特徴
task.node.param.name.1630863227757821953.movingSpeed.In=移動速度
task.node.param.name.1630863227757821953.rotationSpeed.In=回転速度
task.node.param.name.1630863227757821953.moveObstacleRegion.In=直線移動検出エリア
task.node.param.name.1630863227757821953.rotationObstacleRegion.In=回転運動検出エリア
task.node.param.name.1630863227757821953.obstacleAvoidance.In=自律障害物回避
task.node.param.name.1630863227757821953.agvDirection.In=車体方向
task.node.param.name.1630863227757821953.extendParams.In=拡張プロパティ
task.node.param.name.1630863227577466882.time.In=待ち時間（秒）
task.node.param.name.1714957947186581506.ip.In=IP
task.node.param.name.1714957947186581506.port.In=ポート
task.node.param.name.1714957947186581506.code.In=機能コード
task.node.param.name.1714957947186581506.slaveId.In=slaveId
task.node.param.name.1714957947186581506.address.In=レジスタアドレス
task.node.param.name.1714957947186581506.successVal.In=通常退出
task.node.param.name.1714957947186581506.failVal.In=失敗退出
task.node.param.name.1714957947186581506.timeout.In=タイムアウト時間
task.node.param.name.1714957947186581506.executeMode.In=実行方法
task.node.param.name.1714957947186581506.vehicleCode.In=ロボット
task.node.param.name.1714957947186581506.outValue.Out=出力値
task.node.param.name.1788855369901137921.result_code.Out=リターンコード
task.node.param.name.1788855369901137921.error_message.Out=リターンメッセージ
task.node.param.name.1630863227787182081.extendParam1.Out=拡張プロパティ1
task.node.param.name.1630863227787182081.extendParam2.Out=拡張プロパティ2
task.node.param.name.1630863227787182081.extendParam3.Out=拡張プロパティ3
task.node.param.name.1630863227787182081.extendParam4.Out=拡張プロパティ4
task.node.param.name.1630863227787182081.extendParam5.Out=拡張プロパティ5
task.node.param.name.1742440444115046401.extendParam1.Out=拡張プロパティ1
task.node.param.name.1742440444115046401.extendParam2.Out=拡張プロパティ2
task.node.param.name.1742440444115046401.extendParam3.Out=拡張プロパティ3
task.node.param.name.1742440444115046401.extendParam4.Out=拡張プロパティ4
task.node.param.name.1742440444115046401.extendParam5.Out=拡張プロパティ5
task.node.param.name.1630863227799764993.vehicleCode.In=ロボット
task.node.param.name.1630863227799764993.markerCode.In=充電ポイント
task.node.param.name.1630863227799764993.chargeTime.In=充電時間（分）
task.node.param.name.1630863227799764993.batteryCharge.In=充電量
task.node.param.name.1630863227799764993.correctChargeCycle.In=充電校正周期（日）
task.node.param.name.1821375525306884097.message.In=異常メッセージ
task.node.param.name.1742439427101184002.warehouseCode.In=保管場所
task.node.param.name.1742439427101184002.occupyStatus.In=占用状態
task.node.param.name.1742439427101184002.containerBarcode.In=コンテナバーコード
task.node.param.name.1742439427101184002.extendParam1.In=拡張プロパティ1
task.node.param.name.1742439427101184002.extendParam2.In=拡張プロパティ2
task.node.param.name.1742439427101184002.extendParam3.In=拡張プロパティ3
task.node.param.name.1742439427101184002.extendParam4.In=拡張プロパティ4
task.node.param.name.1742439427101184002.extendParam5.In=拡張プロパティ5
task.node.param.name.1831609346757263362.taskTypeId.In=タスク実行
task.node.param.name.1831620038075908097.taskTypeId.In=任务类型
task.node.param.name.1831620038075908097.executionMode.In=执行模式
task.node.param.name.1831620038075908097.outValue.Out=任务编号
task.node.param.name.1750822156822622210.QR_dock_id.In=作業位置ID
task.node.param.name.1750822156822622210.dockingType.In=タイプ
task.node.param.name.1852196093673111553.url.In=URLアドレス
task.node.param.name.1852196093673111553.request.In=カスタムリクエストパラメータ
task.node.param.name.1852196093673111553.response.Out=出力値
task.node.param.name.1852196093673111553.checkParam.Out=パラメータチェック
task.node.param.name.1738470004770951170.vehicleCode.In=ロボット
task.node.param.name.1738470004770951170.vehicleCode.Out=ロボットコード
task.node.param.name.1738470004770951170.batteryValue.Out=バッテリー残量
task.node.param.name.1738470004770951170.vehicleTypeCode.Out=ロボットタイプ
task.node.param.name.1738470004770951170.vehicleGroupCode.Out=ロボット組
task.node.param.name.1738470004770951170.vehicleMapCode.Out=現在の地図
task.node.param.name.1738470004770951170.markerCode.Out=現在のポイント
task.node.param.name.1738470004770951170.storageInfoList.Out=保管場所データ
task.node.param.name.1654731794802663426.vehicleCode.In=ロボット
task.node.param.name.1654731794802663426.scriptData.In=制御スクリプト
task.node.param.name.1654731794802663426.HAND_VISION.Out=スキャン結果
task.node.param.name.1654731794802663426.OPERATE.Out=操作データ
task.node.param.name.1654731794802663426.STATUS.Out=実行結果
task.node.param.name.1856960932295598081.ip.In=IP
task.node.param.name.1856960932295598081.port.In=ポート
task.node.param.name.1856960932295598081.slaveId.In=slaveId
task.node.param.name.1856960932295598081.address.In=開始アドレス
task.node.param.name.1856960932295598081.value.In=入力値
task.node.param.name.1856959739322294274.ip.In=IP
task.node.param.name.1856959739322294274.port.In=ポート
task.node.param.name.1856959739322294274.slaveId.In=slaveId
task.node.param.name.1856959739322294274.address.In=開始アドレス
task.node.param.name.1856959739322294274.length.In=読み込み長さ
task.node.param.name.1856959739322294274.result.Out=出力値
task.node.param.name.1851551331579158530.mapAreaCode.In=エリアコード
task.node.param.name.1851551331579158530.operation.In=操作
task.node.param.name.1856960932295598081.executeMode.In=実行方法
task.node.param.name.1856960932295598081.vehicleCode.In=ロボット
task.node.param.name.1856959739322294274.executeMode.In=実行方法
task.node.param.name.1856959739322294274.vehicleCode.In=ロボット
task.node.param.name.1856959739322294274.code.In=功能码
task.node.param.name.1856960932295598081.code.In=機能コード
task.node.param.name.1856613384389165058.script.In=スクリプト
task.node.param.name.1856613384389165058.param1.In=パラメータ1
task.node.param.name.1856613384389165058.param2.In=パラメータ2
task.node.param.name.1856613384389165058.param3.In=パラメータ3
task.node.param.name.1856613384389165058.param4.In=パラメータ4
task.node.param.name.1856613384389165058.param5.In=パラメータ5
task.node.param.name.1856613384389165058.param1.Out=パラメータ1
task.node.param.name.1856613384389165058.param2.Out=パラメータ2
task.node.param.name.1856613384389165058.param3.Out=パラメータ3
task.node.param.name.1856613384389165058.param4.Out=パラメータ4
task.node.param.name.1856613384389165058.param5.Out=パラメータ5
task.node.param.name.1912414958361030657.markerCode.In=点位(待翻译)
task.node.param.name.1912414958361030657.vehicleCode.In=机器人(待翻译)
task.node.param.name.1912415217493520385.vehicleCode.In=机器人(待翻译)
task.node.param.notice.1718202092436357121.vehicleCode.In=[未翻译]
task.node.param.notice.1718202092436357121.position.In=単位はミリメートル
task.node.param.notice.1718202092436357121.offsetHeight.In=単位はミリメートル
task.node.param.notice.1718202092436357121.speed.In=単位はミリメートル／秒
task.node.param.notice.1738448135527288834.vehicleCode.In=[未翻译]
task.node.param.notice.1738448135527288834.markerCode.In=[未翻译]
task.node.param.notice.1738448135527288834.chargeTime.In=指定された充電時間（分）を超える場合、充電を中断してタスクを実行できます
task.node.param.notice.1738448135527288834.batteryCharge.In=指定された電力量を超えた場合、充電を中断してタスクを実行できます
task.node.param.notice.1738443040093851650.finishType.In=[未翻译]
task.node.param.notice.1738443040093851650.noticeMsg.In=タスク終了際，システムにヒントメッセージを送信する
task.node.param.notice.1646764086215663617.vehicleCode.In=値が空の場合、デフォルトでタスクを占有しているロボットを使用します
task.node.param.notice.1646764086215663617.vehicleAngle1.In=ロボット相対地図の角度
task.node.param.notice.1646764086215663617.vehicleAngle2.In=ロボット相対地図の角度
task.node.param.notice.1646764086215663617.trayRotationSpeed.In=[未翻译]
task.node.param.notice.1646764086215663617.trayAngle1.In=パレット相対地図の角度
task.node.param.notice.1646764086215663617.trayAngle2.In=パレット相対地図の角度
task.node.param.notice.1715183824889581570.vehicleCode.In=[未翻译]
task.node.param.notice.1715183824889581570.ladarSwitch.In=[未翻译]
task.node.param.notice.1715184972354686978.vehicleCode.In=[未翻译]
task.node.param.notice.1715184972354686978.obstacleArea.In=[未翻译]
task.node.param.notice.1738467719873515521.vehicleCode.In=[未翻译]
task.node.param.notice.1738467719873515521.scheduleMode.In=[未翻译]
task.node.param.notice.1715183168871075842.vehicleCode.In=[未翻译]
task.node.param.notice.1715183168871075842.audioName.In=[未翻译]
task.node.param.notice.1715183168871075842.audioVolume.In=[未翻译]
task.node.param.notice.1715183168871075842.playCount.In=[未翻译]
task.node.param.notice.1630863227598438401.markerCode.In=割り当てポイント付近のロボット。値が空の場合はランダムにロボットを割り当てます
task.node.param.notice.1630863227598438401.vehicleGroupCode.In=該当ロボットグループに属するロボットのみを割り当てます
task.node.param.notice.1630863227598438401.vehicleTypeCode.In=該当ロボットタイプに属するロボットのみを割り当てます
task.node.param.notice.1630863227598438401.vehicleMapCodeList.In=該当地図内のロボットのみを割り当てます
task.node.param.notice.1630863227598438401.limitBattery.In=指定されたバッテリー残量より高く、かつ充電設定のバッテリー残量を上回るロボットのみを割り当てます
task.node.param.notice.1630863227598438401.outVehicleCode.Out=[未翻译]
task.node.param.notice.1630863227745239041.vehicleCode.In=[未翻译]
task.node.param.notice.1630863227745239041.outVehicleCode.Out=[未翻译]
task.node.param.notice.1630863227644575745.vehicleCode.In=[未翻译]
task.node.param.notice.1630863227623604225.vehicleMapCodeList.In=該当地図内のポイントのみを割り当てます
task.node.param.notice.1630863227623604225.markerType.In=該当タイプのポイントのみを割り当てます
task.node.param.notice.1630863227623604225.outMarkerCode.Out=[未翻译]
task.node.param.notice.1630863227623604225.vehicleCode.In=[未翻译]
task.node.param.notice.1738440272671100929.taskNo.Out=タスク対応する唯一のコード
task.node.param.notice.1738440272671100929.externalTaskNo.Out=このタスク対応の外部タスクコード
task.node.param.notice.1738440272671100929.priority.Out=タスク実行するの優先順位
task.node.param.notice.1630863227707490306.ip.In=PLCのIPアドレス
task.node.param.notice.1630863227707490306.port.In=PLCのポートアドレス
task.node.param.notice.1630863227707490306.code.In=[未翻译]
task.node.param.notice.1630863227707490306.slaveId.In=[未翻译]
task.node.param.notice.1630863227707490306.address.In=レジスタの読み込みアドレス
task.node.param.notice.1630863227707490306.executeMode.In=[未翻译]
task.node.param.notice.1630863227707490306.vehicleCode.In=[未翻译]
task.node.param.notice.1630863227707490306.outValue.Out=[未翻译]
task.node.param.notice.1645676364679905282.vehicleCode.In=値が空の場合、デフォルトでタスクに割り当てられているロボットを使用します
task.node.param.notice.1645676364679905282.code.In=線圈レジスタの読み取り（01）、値の範囲：[0-1] 保持レジスタの読み取り（03）、値の範囲：[0-30000]
task.node.param.notice.1645676364679905282.slaveId.In=[未翻译]
task.node.param.notice.1645676364679905282.address.In=ロボットレジスタを読み込みのアドレス
task.node.param.notice.1645676364679905282.outValue.Out=[未翻译]
task.node.param.notice.1630863227724267521.ip.In=PLCのIPアドレス
task.node.param.notice.1630863227724267521.port.In=PLCのポート
task.node.param.notice.1630863227724267521.code.In=[未翻译]
task.node.param.notice.1630863227724267521.slaveId.In=[未翻译]
task.node.param.notice.1630863227724267521.address.In=レジスタのアドレスを入力する
task.node.param.notice.1630863227724267521.value.In=レジスタの値を入力する
task.node.param.notice.1630863227724267521.executeMode.In=[未翻译]
task.node.param.notice.1630863227724267521.vehicleCode.In=[未翻译]
task.node.param.notice.1645678201743114241.vehicleCode.In=値が空の場合、デフォルトでタスクに割り当てられているロボットを使用します
task.node.param.notice.1645678201743114241.code.In=単一のコイルレジスタへの書き込み（05），値の範囲[0-1] 単一のディスクリート入力レジスタへの書き込み（06），値の範囲[0-30000]
task.node.param.notice.1645678201743114241.slaveId.In=[未翻译]
task.node.param.notice.1645678201743114241.address.In=レジスタのアドレスを入力する
task.node.param.notice.1645678201743114241.value.In=レジスタの値を入力
task.node.param.notice.1715188504537468930.vehicleCode.In=[未翻译]
task.node.param.notice.1715188504537468930.code.In=[未翻译]
task.node.param.notice.1715188504537468930.slaveId.In=[未翻译]
task.node.param.notice.1715188504537468930.address.In=[未翻译]
task.node.param.notice.1715188504537468930.successVal.In=[未翻译]
task.node.param.notice.1715188504537468930.failVal.In=[未翻译]
task.node.param.notice.1715188504537468930.timeout.In=[未翻译]
task.node.param.notice.1715188504537468930.outValue.Out=[未翻译]
task.node.param.notice.1641376178617024513.callBoxCode.In=コールボックスの出荷番号は、コールボックス設定ツールで確認できます
task.node.param.notice.1641376178617024513.buttonCode.In=呼び出しボックスのボタンコード
task.node.param.notice.1641376178617024513.timeout.In=タイムアウトの時間を超えた後，ノードが自動的に完成させる
task.node.param.notice.1641376553134817282.callBoxCode.In=コールボックスの出荷番号は、コールボックス設定ツールで確認できます
task.node.param.notice.1641376553134817282.buttonCode.In=呼び出しボックスのボタンコード
task.node.param.notice.1641377688272863233.ip.In=音声およびランプアラームのIPアドレス
task.node.param.notice.1641377688272863233.port.In=音声およびランプアラームのポート
task.node.param.notice.1641377688272863233.type.In=[未翻译]
task.node.param.notice.1641377688272863233.time.In=アラームの続く時間
task.node.param.notice.1742437277025456130.warehouseCode.In=[未翻译]
task.node.param.notice.1742437277025456130.containerBarcode.In=[未翻译]
task.node.param.notice.1742437277025456130.dispatchPolicy.In=割り当てポリシー：ランダム割り当て（RANDOM）、先入れ先出し（FIFO）
task.node.param.notice.1742437277025456130.occupyStatus.In=[未翻译]
task.node.param.notice.1742437277025456130.warehouseTypeCode.In=[未翻译]
task.node.param.notice.1742437277025456130.warehouseAreaCode.In=[未翻译]
task.node.param.notice.1742437277025456130.extendParam1.In=[未翻译]
task.node.param.notice.1742437277025456130.extendParam2.In=[未翻译]
task.node.param.notice.1742437277025456130.extendParam3.In=[未翻译]
task.node.param.notice.1742437277025456130.extendParam4.In=[未翻译]
task.node.param.notice.1742437277025456130.extendParam5.In=[未翻译]
task.node.param.notice.1742437277025456130.warehouseCode.Out=[未翻译]
task.node.param.notice.1742441148997193730.containerBarCode.In=[未翻译]
task.node.param.notice.1742441148997193730.warehouseCode.Out=[未翻译]
task.node.param.notice.1742441148997193730.workMarkerCode.Out=[未翻译]
task.node.param.notice.1742441148997193730.workHeight.Out=[未翻译]
task.node.param.notice.1742441148997193730.warehouseTypeCode.Out=[未翻译]
task.node.param.notice.1742441148997193730.warehouseAreaCode.Out=[未翻译]
task.node.param.notice.1742440444115046401.warehouseCode.In=[未翻译]
task.node.param.notice.1742440444115046401.containerBarcode.Out=[未翻译]
task.node.param.notice.1742440444115046401.workMarkerCode.Out=[未翻译]
task.node.param.notice.1742440444115046401.workHeight.Out=[未翻译]
task.node.param.notice.1742440444115046401.warehouseTypeCode.Out=[未翻译]
task.node.param.notice.1742440444115046401.warehouseAreaCode.Out=[未翻译]
task.node.param.notice.1742441529047273474.markerCode.In=[未翻译]
task.node.param.notice.1742441529047273474.markerCode.Out=[未翻译]
task.node.param.notice.1630863227652964354.vehicleCode.In=値が空の場合、デフォルトでタスクに割り当てられているロボットを使用します
task.node.param.notice.1630863227652964354.angle.In=ロボット相対地図の角度
task.node.param.notice.1630863227652964354.rateType.In=[未翻译]
task.node.param.notice.1630863227673935874.vehicleCode.In=値が空の場合、デフォルトでタスクに割り当てられているロボットを使用します
task.node.param.notice.1630863227673935874.angle1.In=パレット相対地図の回転角度
task.node.param.notice.1630863227673935874.angle2.In=パレット相対地図の回転角度
task.node.param.notice.1630863227673935874.speed.In=[未翻译]
task.node.param.notice.1630863227673935874.rateType.In=[未翻译]
task.node.param.notice.1750822156822622210.vehicleCode.In=[未翻译]
task.node.param.notice.1750822156822622210.offsetX.In=単位はメートル
task.node.param.notice.1750822156822622210.offsetY.In=単位はメートル
task.node.param.notice.1750822156822622210.offsetAngle.In=[未翻译]
task.node.param.notice.1750822156822622210.rotationAngleRange.In=値は 0 の際AMRのデフォルト配置を使用します
task.node.param.notice.1750822156822622210.moveDistanceRange.In=値は 0 の際AMRのデフォルト配置を使用します
task.node.param.notice.1750822156822622210.obstacleRegion.In=[未翻译]
task.node.param.notice.1738450017482133505.vehicleCode.In=[未翻译]
task.node.param.notice.1738450017482133505.dockingType.In=[未翻译]
task.node.param.notice.1738450017482133505.startX.In=単位はメートル
task.node.param.notice.1738450017482133505.startY.In=単位はメートル
task.node.param.notice.1738450017482133505.startAngle.In=[未翻译]
task.node.param.notice.1738450017482133505.endX.In=単位はメートル
task.node.param.notice.1738450017482133505.endY.In=単位はメートル
task.node.param.notice.1738450017482133505.endAngle.In=[未翻译]
task.node.param.notice.1738450017482133505.offsetX.In=[未翻译]
task.node.param.notice.1738450017482133505.offsetY.In=[未翻译]
task.node.param.notice.1738450017482133505.offsetAngle.In=[未翻译]
task.node.param.notice.1738450017482133505.workStationCode.In=QRコードドッキングする際だけ使う
task.node.param.notice.1738450017482133505.templateNo.In=[未翻译]
task.node.param.notice.1738450017482133505.cameraObstacle.In=[未翻译]
task.node.param.notice.1738450017482133505.obstacleRegion.In=[未翻译]
task.node.param.notice.1751081370463637506.vehicleCode.In=[未翻译]
task.node.param.notice.1738450017482133505.dockingDirection.In=[未翻译]
task.node.param.notice.1715186203621986306.vehicleCode.In=[未翻译]
task.node.param.notice.1715186203621986306.type.In=１，ロボットに相対静止：パレットがAMRと一緒に動く２，地図に相対静止：AMRが動いてもパレットは動かない
task.node.param.notice.1630863227694907394.vehicleCode.In=値が空の場合、デフォルトでタスクに割り当てられているロボットを使用します
task.node.param.notice.1630863227694907394.speed.In=[未翻译]
task.node.param.notice.1630863227694907394.targetTicks.In=[未翻译]
task.node.param.notice.1751878094551769090.vehicleCode.In=[未翻译]
task.node.param.notice.1751878094551769090.outValue.Out=[未翻译]
task.node.param.notice.1751825844022235138.vehicleCode.In=[未翻译]
task.node.param.notice.1630863227787182081.markerCode.In=[未翻译]
task.node.param.notice.1630863227787182081.markerName.In=[未翻译]
task.node.param.notice.1630863227787182081.code.Out=ポイントに対応する唯一のコード
task.node.param.notice.1630863227787182081.name.Out=ユーザーでカスタムリクエストポイントコード，必要の場合入力する
task.node.param.notice.1630863227787182081.type.Out=例： ChargingMarker:充電ポイント, NavigationMarker:ナビゲーションポイント, WorkMarker:作業ポイント
task.node.param.notice.1630863227787182081.isPark.Out=例：true、false
task.node.param.notice.1630863227787182081.angle.Out=[未翻译]
task.node.param.notice.1630863227787182081.chargeEnable.Out=是否允许工作时充电(待翻译)
task.node.param.notice.1790203373283213314.oriValue.In=[未翻译]
task.node.param.notice.1790203373283213314.newValue.In=[未翻译]
task.node.param.notice.1738444988633272322.url.In=リクエストの Url アドレス
task.node.param.notice.1738444988633272322.customParams.In=[未翻译]
task.node.param.notice.1738444988633272322.customValues.Out=[未翻译]
task.node.param.notice.1630863227757821953.vehicleCode.In=値が空の場合、デフォルトでタスクに割り当てられているロボットを使用します
task.node.param.notice.1630863227757821953.markerCode.In=[未翻译]
task.node.param.notice.1630863227757821953.dockingMove.In=この配置を閉じると，ドッキングタイプの経路が全部無効化になります
task.node.param.notice.1630863227757821953.accurate.In=[未翻译]
task.node.param.notice.1630863227757821953.fallPrevent.In=[未翻译]
task.node.param.notice.1630863227757821953.safety3D.In=[未翻译]
task.node.param.notice.1630863227757821953.featureFusion.In=[未翻译]
task.node.param.notice.1630863227757821953.movingSpeed.In=[未翻译]
task.node.param.notice.1630863227757821953.rotationSpeed.In=[未翻译]
task.node.param.notice.1630863227757821953.moveObstacleRegion.In=[未翻译]
task.node.param.notice.1630863227757821953.rotationObstacleRegion.In=[未翻译]
task.node.param.notice.1630863227757821953.obstacleAvoidance.In=[未翻译]
task.node.param.notice.1630863227757821953.agvDirection.In=[未翻译]
task.node.param.notice.1630863227757821953.extendParams.In=[未翻译]
task.node.param.notice.1630863227577466882.time.In=[未翻译]
task.node.param.notice.1714957947186581506.ip.In=PLCのIPアドレス
task.node.param.notice.1714957947186581506.port.In=PLCのポートアドレス
task.node.param.notice.1714957947186581506.code.In=[未翻译]
task.node.param.notice.1714957947186581506.slaveId.In=[未翻译]
task.node.param.notice.1714957947186581506.address.In=読み込みのレジスタアドレス
task.node.param.notice.1714957947186581506.successVal.In=[未翻译]
task.node.param.notice.1714957947186581506.failVal.In=[未翻译]
task.node.param.notice.1714957947186581506.timeout.In=[未翻译]
task.node.param.notice.1714957947186581506.executeMode.In=[未翻译]
task.node.param.notice.1714957947186581506.vehicleCode.In=[未翻译]
task.node.param.notice.1714957947186581506.outValue.Out=[未翻译]
task.node.param.notice.1788855369901137921.result_code.Out=1001: 障害物検出トリガーしていない, 1002 :障害物検出トリガーしました
task.node.param.notice.1788855369901137921.error_message.Out=センサーの障害物回避エリアをリターン（緊急停止、停止、減速、未トリガー）
task.node.param.notice.1630863227787182081.extendParam1.Out=[未翻译]
task.node.param.notice.1630863227787182081.extendParam2.Out=[未翻译]
task.node.param.notice.1630863227787182081.extendParam3.Out=[未翻译]
task.node.param.notice.1630863227787182081.extendParam4.Out=[未翻译]
task.node.param.notice.1630863227787182081.extendParam5.Out=[未翻译]
task.node.param.notice.1742440444115046401.extendParam1.Out=[未翻译]
task.node.param.notice.1742440444115046401.extendParam2.Out=[未翻译]
task.node.param.notice.1742440444115046401.extendParam3.Out=[未翻译]
task.node.param.notice.1742440444115046401.extendParam4.Out=[未翻译]
task.node.param.notice.1742440444115046401.extendParam5.Out=[未翻译]
task.node.param.notice.1630863227799764993.vehicleCode.In=値が空の場合、デフォルトでタスクに割り当てられているロボットを使用します
task.node.param.notice.1630863227799764993.markerCode.In=[未翻译]
task.node.param.notice.1630863227799764993.chargeTime.In=ロボットの充電時間が指定された値を超えた場合のみ中断が可能です
task.node.param.notice.1630863227799764993.batteryCharge.In=ロボットの現在の電力量が指定された値を超えた場合のみ中断が可能です
task.node.param.notice.1630863227799764993.correctChargeCycle.In=ロボットが校正充電中の場合、中断はできません
task.node.param.notice.1821375525306884097.message.In=異常メッセージ
task.node.param.notice.1742439427101184002.warehouseCode.In=[未翻译]
task.node.param.notice.1742439427101184002.occupyStatus.In=[未翻译]
task.node.param.notice.1742439427101184002.containerBarcode.In=[未翻译]
task.node.param.notice.1742439427101184002.extendParam1.In=[未翻译]
task.node.param.notice.1742439427101184002.extendParam2.In=[未翻译]
task.node.param.notice.1742439427101184002.extendParam3.In=[未翻译]
task.node.param.notice.1742439427101184002.extendParam4.In=[未翻译]
task.node.param.notice.1742439427101184002.extendParam5.In=[未翻译]
task.node.param.notice.1831609346757263362.taskTypeId.In=[未翻译]
task.node.param.notice.1831620038075908097.taskTypeId.In=[未翻译]
task.node.param.notice.1831620038075908097.executionMode.In=[未翻译]
task.node.param.notice.1831620038075908097.outValue.Out=[未翻译]
task.node.param.notice.1750822156822622210.QR_dock_id.In=作業位置ID
task.node.param.notice.1750822156822622210.dockingType.In=[未翻译]
task.node.param.notice.1852196093673111553.url.In=リクエストアドレス
task.node.param.notice.1852196093673111553.request.In=[未翻译]
task.node.param.notice.1852196093673111553.response.Out=[未翻译]
task.node.param.notice.1852196093673111553.checkParam.Out=[未翻译]
task.node.param.notice.1738470004770951170.vehicleCode.In=[未翻译]
task.node.param.notice.1738470004770951170.vehicleCode.Out=[未翻译]
task.node.param.notice.1738470004770951170.batteryValue.Out=[未翻译]
task.node.param.notice.1738470004770951170.vehicleTypeCode.Out=[未翻译]
task.node.param.notice.1738470004770951170.vehicleGroupCode.Out=[未翻译]
task.node.param.notice.1738470004770951170.vehicleMapCode.Out=[未翻译]
task.node.param.notice.1738470004770951170.markerCode.Out=[未翻译]
task.node.param.notice.1738470004770951170.storageInfoList.Out=[未翻译]
task.node.param.notice.1654731794802663426.vehicleCode.In=値が空の場合、デフォルトでタスクに割り当てられているロボットを使用します
task.node.param.notice.1654731794802663426.scriptData.In=Mosシステムが実行可能なJsonスクリプト
task.node.param.notice.1654731794802663426.HAND_VISION.Out=[未翻译]
task.node.param.notice.1654731794802663426.OPERATE.Out=[未翻译]
task.node.param.notice.1654731794802663426.STATUS.Out=1：実行中、2：実行異常（元に戻らない）、3：実行完了、4：例外の実行（元に戻した）
task.node.param.notice.1856960932295598081.ip.In=[未翻译]
task.node.param.notice.1856960932295598081.port.In=[未翻译]
task.node.param.notice.1856960932295598081.slaveId.In=[未翻译]
task.node.param.notice.1856960932295598081.address.In=[未翻译]
task.node.param.notice.1856960932295598081.value.In=[未翻译]
task.node.param.notice.1856959739322294274.ip.In=[未翻译]
task.node.param.notice.1856959739322294274.port.In=[未翻译]
task.node.param.notice.1856959739322294274.slaveId.In=[未翻译]
task.node.param.notice.1856959739322294274.address.In=[未翻译]
task.node.param.notice.1856959739322294274.length.In=[未翻译]
task.node.param.notice.1856959739322294274.result.Out=[未翻译]
task.node.param.notice.1851551331579158530.mapAreaCode.In=[未翻译]
task.node.param.notice.1851551331579158530.operation.In=[未翻译]
task.node.param.notice.1856960932295598081.executeMode.In=[未翻译]
task.node.param.notice.1856960932295598081.vehicleCode.In=[未翻译]
task.node.param.notice.1856959739322294274.executeMode.In=[未翻译]
task.node.param.notice.1856959739322294274.vehicleCode.In=[未翻译]
task.node.param.notice.1856959739322294274.code.In=[未翻译]
task.node.param.notice.1856960932295598081.code.In=[未翻译]
task.node.param.notice.1856613384389165058.script.In=[未翻译]
task.node.param.notice.1856613384389165058.param1.In=[未翻译]
task.node.param.notice.1856613384389165058.param2.In=[未翻译]
task.node.param.notice.1856613384389165058.param3.In=[未翻译]
task.node.param.notice.1856613384389165058.param4.In=[未翻译]
task.node.param.notice.1856613384389165058.param5.In=[未翻译]
task.node.param.notice.1856613384389165058.param1.Out=[未翻译]
task.node.param.notice.1856613384389165058.param2.Out=[未翻译]
task.node.param.notice.1856613384389165058.param3.Out=[未翻译]
task.node.param.notice.1856613384389165058.param4.Out=[未翻译]
task.node.param.notice.1856613384389165058.param5.Out=[未翻译]
task.node.param.notice.1912414958361030657.markerCode.In=[未翻译]
task.node.param.notice.1912414958361030657.vehicleCode.In=[未翻译]
task.node.param.notice.1912415217493520385.vehicleCode.In=[未翻译]
task.node.param.value.desc.1738443040093851650.finishType.In.Finished=タスク完成
task.node.param.value.desc.1738443040093851650.finishType.In.Cancel=タスクキャンセル
task.node.param.value.desc.1715183824889581570.ladarSwitch.In.0=エリア0
task.node.param.value.desc.1715183824889581570.ladarSwitch.In.1=エリア1
task.node.param.value.desc.1715184972354686978.obstacleArea.In.1=検出エリア 1
task.node.param.value.desc.1715184972354686978.obstacleArea.In.2=検出エリア 2
task.node.param.value.desc.1715184972354686978.obstacleArea.In.3=検出エリア3
task.node.param.value.desc.1715184972354686978.obstacleArea.In.4=検出エリア 4
task.node.param.value.desc.1715184972354686978.obstacleArea.In.5=検出エリア 5
task.node.param.value.desc.1715184972354686978.obstacleArea.In.6=検出エリア6
task.node.param.value.desc.1715184972354686978.obstacleArea.In.7=検出エリア 7
task.node.param.value.desc.1715184972354686978.obstacleArea.In.8=検出エリア 8
task.node.param.value.desc.1715184972354686978.obstacleArea.In.9=検出エリア 9
task.node.param.value.desc.1715184972354686978.obstacleArea.In.10=検出エリア10
task.node.param.value.desc.1715184972354686978.obstacleArea.In.11=検出エリア 11
task.node.param.value.desc.1715184972354686978.obstacleArea.In.12=検出エリア 12
task.node.param.value.desc.1715184972354686978.obstacleArea.In.13=検出エリア13
task.node.param.value.desc.1715184972354686978.obstacleArea.In.14=検出エリア 14
task.node.param.value.desc.1715184972354686978.obstacleArea.In.15=検出エリア 15
task.node.param.value.desc.1715184972354686978.obstacleArea.In.16=検出エリア 16
task.node.param.value.desc.1738467719873515521.scheduleMode.In.AutoSchedule=自動スケジューリング
task.node.param.value.desc.1738467719873515521.scheduleMode.In.ManualSchedule=手動スケジューリング
task.node.param.value.desc.1630863227623604225.markerType.In.NavigationMarker=ナビゲーションポイント
task.node.param.value.desc.1630863227623604225.markerType.In.WorkMarker=作業ポイント
task.node.param.value.desc.1630863227623604225.markerType.In.ChargingMarker=充電ポイント
task.node.param.value.desc.1630863227707490306.code.In.01=線圈レジスタの読み取り（01）
task.node.param.value.desc.1630863227707490306.code.In.02=離散入力レジスタの読み取り（02）
task.node.param.value.desc.1630863227707490306.code.In.03=保持レジスタの読み取り（03）
task.node.param.value.desc.1630863227707490306.code.In.04=入力レジスタの読み取り（04）
task.node.param.value.desc.1630863227707490306.executeMode.In.Server=サーバー
task.node.param.value.desc.1630863227707490306.executeMode.In.Vehicle=ロボット
task.node.param.value.desc.1645676364679905282.code.In.01=線圈レジスタの読み取り（01）
task.node.param.value.desc.1645676364679905282.code.In.03=保持レジスタの読み取り（03）
task.node.param.value.desc.1630863227724267521.code.In.05=単一のコイルレジスタへの書き込み（05）
task.node.param.value.desc.1630863227724267521.code.In.06=単一のディスクリート入力レジスタへの書き込み（06）
task.node.param.value.desc.1630863227724267521.code.In.15=複数のコイルレジスタへの書き込み（15）
task.node.param.value.desc.1630863227724267521.code.In.16=複数のディスクリート入力レジスタへの書き込み（16）
task.node.param.value.desc.1630863227724267521.executeMode.In.Server=サーバー
task.node.param.value.desc.1630863227724267521.executeMode.In.Vehicle=ロボット
task.node.param.value.desc.1645678201743114241.code.In.05=単一のコイルレジスタへの書き込み（05）
task.node.param.value.desc.1645678201743114241.code.In.06=単一のディスクリート入力レジスタへの書き込み（06）
task.node.param.value.desc.1715188504537468930.code.In.03=保持レジスタの読み取り（03）
task.node.param.value.desc.1641377688272863233.type.In.SoundAndLight=音声およびランプ
task.node.param.value.desc.1641377688272863233.type.In.Sound=音声
task.node.param.value.desc.1641377688272863233.type.In.Light=ランプ
task.node.param.value.desc.1742437277025456130.dispatchPolicy.In.FIFO=先进先出
task.node.param.value.desc.1742437277025456130.dispatchPolicy.In.RANDOM=随机分配
task.node.param.value.desc.1742437277025456130.occupyStatus.In.Store=存储
task.node.param.value.desc.1742437277025456130.occupyStatus.In.Free=空闲
task.node.param.value.desc.1630863227652964354.rateType.In.Relative=相対ロボット現在角度の回転
task.node.param.value.desc.1630863227652964354.rateType.In.Absolute=相対地図の回転
task.node.param.value.desc.1630863227673935874.rateType.In.Relative=相対ロボット現在角度の回転
task.node.param.value.desc.1630863227673935874.rateType.In.Absolute=相対地図の回転
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.1=検出エリア 1
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.2=検出エリア 2
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.3=検出エリア 3
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.4=検出エリア 4
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.5=検出エリア 5
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.6=検出エリア 6
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.7=検出エリア 7
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.8=検出エリア 8
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.9=検出エリア 9
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.10=検出エリア10
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.11=検出エリア 11
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.12=検出エリア 12
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.13=検出エリア 13
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.14=検出エリア 14
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.15=検出エリア 15
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.16=検出エリア 16
task.node.param.value.desc.1738450017482133505.dockingType.In.QR_Down=QRコードドッキング
task.node.param.value.desc.1738450017482133505.dockingType.In.Reflector=反射シールドッキング
task.node.param.value.desc.1738450017482133505.dockingType.In.Symbol_V=V型特徴ドッキング
task.node.param.value.desc.1738450017482133505.dockingType.In.Shelflegs=棚足ドッキング
task.node.param.value.desc.1738450017482133505.dockingType.In.Pallet=パレットドッキング
task.node.param.value.desc.1738450017482133505.dockingType.In.LeaveDocking=ドッキング解除
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.1=検出エリア 1
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.2=検出エリア 2
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.3=検出エリア 3
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.4=検出エリア 4
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.5=検出エリア 5
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.6=検出エリア 6
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.7=検出エリア 7
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.8=検出エリア 8
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.9=検出エリア 9
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.10=検出エリア 10
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.11=検出エリア 11
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.12=検出エリア 12
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.13=検出エリア 13
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.14=検出エリア 14
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.15=検出エリア 15
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.16=検出エリア 16
task.node.param.value.desc.1738450017482133505.dockingDirection.In.Head=正面
task.node.param.value.desc.1738450017482133505.dockingDirection.In.Tail=後面
task.node.param.value.desc.1715186203621986306.type.In.Open=ロボットに相対静止
task.node.param.value.desc.1715186203621986306.type.In.Close=地図に相対静止
task.node.param.value.desc.1630863227757821953.accurate.In.=デフォルト
task.node.param.value.desc.1630863227757821953.accurate.In.true=ON
task.node.param.value.desc.1630863227757821953.accurate.In.false=OFF
task.node.param.value.desc.1630863227757821953.fallPrevent.In.=デフォルト
task.node.param.value.desc.1630863227757821953.fallPrevent.In.true=ON
task.node.param.value.desc.1630863227757821953.fallPrevent.In.false=OFF
task.node.param.value.desc.1630863227757821953.safety3D.In.=デフォルト
task.node.param.value.desc.1630863227757821953.safety3D.In.true=ON
task.node.param.value.desc.1630863227757821953.safety3D.In.false=OFF
task.node.param.value.desc.1630863227757821953.featureFusion.In.=デフォルト
task.node.param.value.desc.1630863227757821953.featureFusion.In.true=ON
task.node.param.value.desc.1630863227757821953.featureFusion.In.false=OFF
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.=デフォルト
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.1=検出エリア1
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.2=検出エリア2
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.3=検出エリア3
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.4=検出エリア4
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.5=検出エリア5
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.6=検出エリア6
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.7=検出エリア7
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.8=検出エリア8
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.9=検出エリア9
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.10=検出エリア10
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.11=検出エリア11
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.12=検出エリア12
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.13=検出エリア13
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.14=検出エリア14
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.15=検出エリア15
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.16=検出エリア16
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In=[未翻译]
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.1=検出エリア1
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.2=検出エリア2
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.3=検出エリア3
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.4=検出エリア4
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.5=検出エリア5
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.6=検出エリア6
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.7=検出エリア7
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.8=検出エリア8
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.9=検出エリア9
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.10=検出エリア10
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.11=検出エリア11
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.12=検出エリア12
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.13=検出エリア13
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.14=検出エリア14
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.15=検出エリア15
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.16=検出エリア16
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.=デフォルト
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.true=ON
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.false=OFF
task.node.param.value.desc.1630863227757821953.agvDirection.In.0=0
task.node.param.value.desc.1630863227757821953.agvDirection.In.90=90
task.node.param.value.desc.1630863227757821953.agvDirection.In.-90=-90
task.node.param.value.desc.1630863227757821953.agvDirection.In.180=180
task.node.param.value.desc.1714957947186581506.code.In.01=读线圈寄存器（01）
task.node.param.value.desc.1714957947186581506.code.In.02=读离散输入寄存器（02）
task.node.param.value.desc.1714957947186581506.code.In.03=读保持寄存器（03）
task.node.param.value.desc.1714957947186581506.code.In.04=读输入寄存器（04）
task.node.param.value.desc.1714957947186581506.executeMode.In.Server=サーバー
task.node.param.value.desc.1714957947186581506.executeMode.In.Vehicle=ロボット
task.node.param.value.desc.1742439427101184002.occupyStatus.In.Free=占用無し
task.node.param.value.desc.1742439427101184002.occupyStatus.In.Store=占用あり
task.node.param.value.desc.1831620038075908097.executionMode.In.Independent=独立执行
task.node.param.value.desc.1831620038075908097.executionMode.In.Embedded=嵌入执行
task.node.param.value.desc.1750822156822622210.dockingType.In.UP=上部QRコード（低速）
task.node.param.value.desc.1750822156822622210.dockingType.In.QR_Up=上部QRコード（高速）
task.node.param.value.desc.1750822156822622210.dockingType.In.DOWN=下部QRコード
task.node.param.value.desc.1750822156822622210.dockingType.In.LEFT=左側QRコード
task.node.param.value.desc.1750822156822622210.dockingType.In.RIGHT=右側QRコード
task.node.param.value.desc.1750822156822622210.dockingType.In.Laser_Side=両辺レーザー
task.node.param.value.desc.1750822156822622210.dockingType.In.Line_Straight=単一片持ち梁
task.node.param.value.desc.1750822156822622210.dockingType.In.Reflector_Adjust=反射シールで精度微調整
task.node.param.value.desc.1851551331579158530.operation.In.Enable=有効化
task.node.param.value.desc.1851551331579158530.operation.In.Disable=無効化
task.node.param.value.desc.1856960932295598081.executeMode.In.Server=サーバー
task.node.param.value.desc.1856960932295598081.executeMode.In.Vehicle=ロボット
task.node.param.value.desc.1856959739322294274.executeMode.In.Server=サーバー
task.node.param.value.desc.1856959739322294274.executeMode.In.Vehicle=ロボット
task.node.param.value.desc.1856959739322294274.code.In.03=读保持寄存器（03）
task.node.param.value.desc.1856960932295598081.code.In.16=複数のディスクリート入力レジスタへの書き込み（16）