CREATE DATABASE IF NOT EXISTS agv_scheduler default charset utf8 COLLATE utf8_general_ci;
use agv_scheduler;

-- ----------------------------
-- Table structure for abnormal_prompt
-- ----------------------------
DROP TABLE IF EXISTS `abnormal_prompt`;
CREATE TABLE `abnormal_prompt`  (
                                    `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                    `abnormal_level` int(11) NULL DEFAULT NULL COMMENT '等级 1：普通 2：警告 3：错误',
                                    `abnormal_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
                                    `abnormal_code` int(11) NULL DEFAULT NULL COMMENT '异常编码',
                                    `abnormal_description` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                                    `help` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '处理建议',
                                    `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '异常提示' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for agv
-- ----------------------------
DROP TABLE IF EXISTS `agv`;
CREATE TABLE `agv`  (
                        `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                        `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '机器人编号',
                        `agv_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '机器人ID（登录密码）',
                        `agv_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '名称',
                        `navigation_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '导航类型：LASER:激光导航 QR_CODE:二维码导航 BLEND:混合导航',
                        `agv_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '机器人类型',
                        `agv_group_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '机器人所属组',
                        `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'agv启用状态 0:未启用,1:启用',
                        `online_status` tinyint(1) NULL DEFAULT 0 COMMENT 'agv上线状态 0:离线,1:在线,2:断线',
                        `shape_info` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '外型信息',
                        `agv_color` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器人颜色',
                        `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                        `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                        `map_status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '地图同步状态,0未同步、1同步',
                        `control_mode` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '控制模式,1自动模式、2手动模式',
                        `appoint_status` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '地图指定状态,0未指定、1已指定',
                        `abnormal_status` tinyint(1) NULL DEFAULT NULL COMMENT '异常状态,1无异常、2工作异常、3充电异常、4归位异常',
                        `work_status` tinyint(1) NULL DEFAULT NULL COMMENT '任务状态,1空闲、2任务、3充电、4归位',
                        `auto_charge` tinyint(1) NULL DEFAULT 2 COMMENT '机器人自动充电,0关闭,1开启,2默认',
                        `auto_park` tinyint(1) NULL DEFAULT 2 COMMENT '机器人自动泊车,0关闭,1开启,2默认',
                        `ip` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器人IP地址',
                        `auto_allocation` tinyint(1) NULL DEFAULT 1 COMMENT '是否自动分配任务，0:关闭，1:开启',
                        `bind_charge_config` tinyint(1) NULL DEFAULT 0 COMMENT '是否开启绑定充电点配置 0：关闭 1:开启',
                        `bind_charge_markers` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '绑定的充电点, 多个以逗号隔开',
                        `bind_park_config` tinyint(1) NULL DEFAULT 0 COMMENT '是否开启绑定泊车点配置 0：关闭 1:开启',
                        `bind_park_markers` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '绑定的泊车点, 多个以逗号隔开',
                        PRIMARY KEY (`id`) USING BTREE,
                        INDEX `agv_code`(`agv_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'Agv' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for agv_group
-- ----------------------------
DROP TABLE IF EXISTS `agv_group`;
CREATE TABLE `agv_group`  (
                              `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '机器人组ID',
                              `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器人组名称',
                              `description` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                              `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'agv_group' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for agv_log
-- ----------------------------
DROP TABLE IF EXISTS `agv_log`;
CREATE TABLE `agv_log`  (
                            `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                            `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv的编号',
                            `type` int(11) NULL DEFAULT NULL COMMENT '类型: 1:在线,2:工作',
                            `mission_work_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型type为2时:该参数有值',
                            `start_time` bigint(20) NULL DEFAULT NULL COMMENT '开始时间',
                            `end_time` bigint(20) NULL DEFAULT NULL COMMENT '结束时间',
                            `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            PRIMARY KEY (`id`) USING BTREE,
                            INDEX `create_time`(`create_time`) USING BTREE,
                            INDEX `agv_code`(`agv_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '机器人日志信息' ROW_FORMAT = DYNAMIC;



-- ----------------------------
-- Table structure for agv_map_update_queue
-- ----------------------------
DROP TABLE IF EXISTS `agv_map_update_queue`;
CREATE TABLE `agv_map_update_queue`  (
                                         `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                         `status` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '完成状态：create、running、finish、failed',
                                         `map_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地图名称',
                                         `type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '类型:current_2_memory、tmp_2_current、draft_2_current',
                                         `level` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '优先级：1（低）、2（普通）、3（高）',
                                         `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '地图更新队列' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for agv_statistics
-- ----------------------------
DROP TABLE IF EXISTS `agv_statistics`;
CREATE TABLE `agv_statistics`  (
                                   `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                   `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv的编号',
                                   `work_time` bigint(20) NULL DEFAULT 0 COMMENT '工作时间 单位:s',
                                   `free_time` bigint(20) NULL DEFAULT 0 COMMENT '空闲时间 单位:s',
                                   `on_time` bigint(20) NULL DEFAULT 0 COMMENT '总在线时间 单位:s',
                                   `mission_count` int(11) NULL DEFAULT 0 COMMENT '总任务数',
                                   `mission_finish_count` int(11) NULL DEFAULT 0 COMMENT '完成任务数',
                                   `mission_cancel_count` int(11) NULL DEFAULT 0 COMMENT '取消任务数',
                                   `data_type` int(11) NULL DEFAULT 0 COMMENT '单位: 1:day,7:week,30:month',
                                   `belong_time` bigint(20) NULL DEFAULT 0 COMMENT '数据归属时间59:59时间戳，日：当前日期；周：当前周第一天，月：当前月第一天；所有：0',
                                   `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   INDEX `create_time`(`create_time`) USING BTREE,
                                   INDEX `agv_code`(`agv_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'avg统计表' ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Table structure for agv_status_log_statistic
-- ----------------------------
DROP TABLE IF EXISTS `agv_status_log_statistic`;
CREATE TABLE `agv_status_log_statistic`  (
                                             `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
                                             `vehicle_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '小车编号',
                                             `log_type` int(11) NULL DEFAULT 0 COMMENT '日志类型：0:任务、1:充电、2:空闲、3:泊车、4:异常',
                                             `total_num` int(11) NULL DEFAULT 0 COMMENT '当天总次数',
                                             `total_time` int(11) NULL DEFAULT 0 COMMENT '当天总时间 单位: 秒',
                                             `statistics_date` datetime NULL DEFAULT NULL COMMENT '归档日期',
                                             `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'AGV状态日志数据统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for agv_type
-- ----------------------------
DROP TABLE IF EXISTS `agv_type`;
CREATE TABLE `agv_type`  (
                             `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'AGV类型ID',
                             `code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'AGV类型',
                             `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型名称',
                             `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'agv_type' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for auto_door
-- ----------------------------
DROP TABLE IF EXISTS `auto_door`;
CREATE TABLE `auto_door`  (
                              `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                              `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
                              `ip` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '自动门ip',
                              `port` int(11) NOT NULL COMMENT '自动门端口',
                              `open_address` int(11) NOT NULL COMMENT '开门地址',
                              `open_status_address` int(11) NOT NULL COMMENT '开门状态地址',
                              `close_address` int(11) NULL DEFAULT NULL COMMENT '关门地址',
                              `close_status_address` int(11) NULL DEFAULT NULL COMMENT '关门状态地址',
                              `current_status` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '门已开:OPEN 门已关:CLOSE 操作中:OPERATING 通讯异常:ERROR 未绑路径:UNBOUND_PATH',
                              `type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型 AUTO_DOOR:自动门 AIR_SHOWER_DOOR:风淋门',
                              `position` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '位置 当type=AIR_SHOWER_DOOR时有值  FRONT:前门  BACK:后门',
                              `relation_door_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '关联门ID 当type=AIR_SHOWER_DOOR时有值',
                              `residence_time` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '停留时间 单位:秒  机器人需要在两道风淋门间停留一段时间',
                              `last_close_door_time` datetime NULL DEFAULT NULL COMMENT '最后一次关门完成时间',
                              `control_mode` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'SCHEDULER' COMMENT '控制模式 LOCAL:本地控制 SCHEDULER:调度控制',
                              `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '自动门' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for camera_preset_param
-- ----------------------------
DROP TABLE IF EXISTS `camera_preset_param`;
CREATE TABLE `camera_preset_param`  (
                                        `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                        `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                                        `osd_str` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'OSD字符叠加(图片水印)',
                                        `p_bright_value` int(11) NULL DEFAULT NULL COMMENT '亮度指针,取值范围[1,10]',
                                        `p_contrast_value` int(11) NULL DEFAULT NULL COMMENT '对比度指针,取值范围[1,10]',
                                        `p_saturation_value` int(11) NULL DEFAULT NULL COMMENT '饱和度指针,取值范围[1,10]',
                                        `by_exposure_mode_set` int(11) NULL DEFAULT NULL COMMENT '球机的曝光模式：0-手动模式，1-自动曝光，2-光圈优先，3-快门优先，4-增益优先',
                                        `by_shutter_set` int(11) NULL DEFAULT NULL COMMENT '快门等级',
                                        `by_max_shutter_set` int(11) NULL DEFAULT NULL COMMENT '最大快门值',
                                        `by_min_shutter_set` int(11) NULL DEFAULT NULL COMMENT '最小快门值',
                                        `by_max_iris_set` int(11) NULL DEFAULT NULL COMMENT '最大光圈限制值(在自动曝光模式下生效)，取值范围：[0,100]',
                                        `by_min_iris_set` int(11) NULL DEFAULT NULL COMMENT '最小光圈限制值(在自动曝光模式下生效)，取值范围：[0,100]',
                                        `by_focus_mode` int(11) NULL DEFAULT NULL COMMENT '聚焦模式：0-自动，1-手动，2-半自动',
                                        `w_zoom_pos` int(11) NULL DEFAULT NULL COMMENT 'Z参数（变倍参数） 取值范围10-666',
                                        `i_iris_set` int(11) NULL DEFAULT NULL COMMENT '光圈，为实际取值*100的值，0表示关闭光圈',
                                        `xml_focus` int(11) NULL DEFAULT NULL COMMENT '可见光单目摄像头调整焦距',
                                        `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '摄像头预置参数' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for charge_scheduler
-- ----------------------------
DROP TABLE IF EXISTS `charge_scheduler`;
CREATE TABLE `charge_scheduler`  (
                                     `id` int(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                     `vehicle_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv id',
                                     `charge_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '充电点ID',
                                     `charge_point_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '充电站点编号',
                                     `charge_type` tinyint(1) NULL DEFAULT 0 COMMENT '充电类型，0:普通充电，1:校验充电',
                                     `charge_point_map_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '充电站点地图名称',
                                     `trigger_type` int(11) NULL DEFAULT NULL COMMENT '触发类型，0：低电量触发 2:空闲触发 3:强制充电触发',
                                     `status` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'CREATE' COMMENT '状态：CREATE,START,CANCEL,SUCCESS,FAULT',
                                     `fault_message` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '异常信息',
                                     `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                     `finish_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     INDEX `vehicle_id_index`(`vehicle_id`) USING BTREE,
                                     INDEX `status_index`(`status`) USING BTREE,
                                     INDEX `vehicle_id_status_index`(`vehicle_id`, `status`) USING BTREE,
                                     INDEX `charge_type_status_index`(`charge_type`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '充电分配队列' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for charge_time_log
-- ----------------------------
DROP TABLE IF EXISTS `charge_time_log`;
CREATE TABLE `charge_time_log`  (
                                    `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
                                    `vehicle_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '小车编号',
                                    `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                    `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                    `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '充电时间日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dashboard
-- ----------------------------
DROP TABLE IF EXISTS `dashboard`;
CREATE TABLE `dashboard`  (
                              `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'id',
                              `agv_map_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地图id',
                              `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                              `description` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                              `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'dashboard' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elevator
-- ----------------------------
DROP TABLE IF EXISTS `elevator`;
CREATE TABLE `elevator`  (
                             `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                             `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                             `description` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                             `customer` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户 TAI_CHI:太极',
                             `ip` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作电梯的ip地址',
                             `port` int(11) NULL DEFAULT NULL COMMENT '操作电梯的端口号',
                             `door_status_address` int(11) NULL DEFAULT 40003 COMMENT '门状态地址 0：未知状态 1：开门过程 2：开门到位保持 3：关门过程 4：关门到位保持',
                             `door_operate_address` int(11) NULL DEFAULT 40022 COMMENT '门操作地址',
                             `open_door_value` int(11) NULL DEFAULT 3 COMMENT '操作开/关门值 3-前门开门  5-后门开门',
                             `close_door_value` int(11) NULL DEFAULT 4 COMMENT '操作开/关门值 4-前门关门  6-后门关门',
                             `floor_info_address` int(11) NULL DEFAULT 40005 COMMENT '楼层信息地址',
                             `det_width` int(11) NULL DEFAULT NULL COMMENT '检测宽度',
                             `det_length` int(11) NULL DEFAULT NULL COMMENT '检测距离',
                             `apply_timeout` int(11) NULL DEFAULT 180 COMMENT '申请电梯超时时间 单位：秒',
                             `control_timeout` int(11) NULL DEFAULT 120 COMMENT '控制电梯超时时间 单位：秒',
                             `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '电梯' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for exception_time_log
-- ----------------------------
DROP TABLE IF EXISTS `exception_time_log`;
CREATE TABLE `exception_time_log`  (
                                       `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
                                       `vehicle_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '小车编号',
                                       `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                       `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                       `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '异常时间日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for floor
-- ----------------------------
DROP TABLE IF EXISTS `floor`;
CREATE TABLE `floor`  (
                          `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                          `elevator_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电梯ID',
                          `agv_map_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地图ID',
                          `marker_id` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标记点ID, type=ELEVATOR_MARKER的标记点才可添加',
                          `marker_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标记点CODE',
                          `number` int(11) NULL DEFAULT NULL COMMENT '楼层数',
                          `read_function_code` char(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '读modbus功能码',
                          `status_address` tinyint(5) NULL DEFAULT 0 COMMENT '门状态地址',
                          `open_status_value` int (11) DEFAULT 1 COMMENT '开门状态值',
                          `close_status_value`int (11) DEFAULT 0 COMMENT '关门状态值',
                          `write_function_code` char(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '写modbus功能码',
                          `operate_address` tinyint(5) NULL DEFAULT 0 COMMENT '门操作地址',
                          `operate_close_value`int(11) DEFAULT 0 COMMENT '关门操作值',
                          `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                          `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '楼层' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for foreign_action
-- ----------------------------
DROP TABLE IF EXISTS `foreign_action`;
CREATE TABLE `foreign_action`  (
                                   `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                   `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv的编号',
                                   `action_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '动作类型',
                                   `parameters` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '动作参数',
                                   `status` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态 START:开始执行 RUNNING:执行中 SUCCESS:执行成功 FAULT:执行错误',
                                   `result_code` int(11) NULL DEFAULT NULL COMMENT '返回编码 1001:正确编码 其他为错误编码',
                                   `result_data` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '返回数据区数据',
                                   `call_back_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '回调Url',
                                   `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                   `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                   `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '外部动作调用表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for free_time_log
-- ----------------------------
DROP TABLE IF EXISTS `free_time_log`;
CREATE TABLE `free_time_log`  (
                                  `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
                                  `vehicle_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '小车编号',
                                  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '空闲时间日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for language
-- ----------------------------
DROP TABLE IF EXISTS `language`;
CREATE TABLE `language`  (
                             `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                             `current_use` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'CHINESE' COMMENT '当前使用语言, 中文:CHINESE  英文:ENGLISH',
                             `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '使用语言' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for license
-- ----------------------------
DROP TABLE IF EXISTS `license`;
CREATE TABLE `license`  (
                            `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                            `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效开始时间',
                            `end_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效结束时间',
                            `server_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '所属服务类型：YOUIFleet、YOUIDrive、YOUIINS、YOUITMS',
                            `company_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公司名称',
                            `file_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '文件名称/文件唯一id',
                            `expire` int(2) NULL DEFAULT 0 COMMENT '是否过期 0:未过期, 1:过期',
                            `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            PRIMARY KEY (`id`) USING BTREE,
                            UNIQUE INDEX `file_id`(`file_id`) USING BTREE,
                            INDEX `license_file_id`(`file_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'license授权信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for logo_info
-- ----------------------------
DROP TABLE IF EXISTS `logo_info`;
CREATE TABLE `logo_info`  (
                              `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                              `company_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公司名称',
                              `version` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统版本',
                              `login_title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'YOUIFleet' COMMENT '网页标题',
                              `login_title_font_size` int(11) NULL DEFAULT 36 COMMENT '登录页标题字体大小',
                              `enterprise_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'https://www.youibot.com/' COMMENT '公司网址',
                              `version_owner` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版权所有',
                              `logo_image` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'logo图片（图片base64）',
                              `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'logo信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for manipulator_arm_preset_param
-- ----------------------------
DROP TABLE IF EXISTS `manipulator_arm_preset_param`;
CREATE TABLE `manipulator_arm_preset_param`  (
                                                 `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                                 `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                                                 `joints1` double NULL DEFAULT NULL COMMENT '第一个关节角度',
                                                 `joints2` double NULL DEFAULT NULL COMMENT '第二个关节角度',
                                                 `joints3` double NULL DEFAULT NULL COMMENT '第三个关节角度',
                                                 `joints4` double NULL DEFAULT NULL COMMENT '第四个关节角度',
                                                 `joints5` double NULL DEFAULT NULL COMMENT '第五个关节角度',
                                                 `joints6` double NULL DEFAULT NULL COMMENT '第六个关节角度',
                                                 `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '摄像头预置参数' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for map_area_type
-- ----------------------------
DROP TABLE IF EXISTS `map_area_type`;
CREATE TABLE `map_area_type`  (
                                  `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                  `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                                  `description` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                                  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '区域类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for map_element_config
-- ----------------------------
DROP TABLE IF EXISTS `map_element_config`;
CREATE TABLE `map_element_config`  (
                                       `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                       `map_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地图名称',
                                       `m_width_height` double NULL DEFAULT NULL COMMENT '标记点宽高',
                                       `line_Width_Height` double NULL DEFAULT NULL COMMENT '路径的宽高',
                                       `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '地图元素配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission
-- ----------------------------
DROP TABLE IF EXISTS `mission`;
CREATE TABLE `mission`  (
                            `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                            `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv的编号',
                            `agv_group_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv组的id',
                            `agv_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv类型',
                            `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                            `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务编号',
                            `description` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                            `mission_group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属任务组',
                            `sequence` int(11) NULL DEFAULT 2 COMMENT '优先级,1:低，2：普通，3：高，4：最高. 默认是2',
                            `version` bigint(20) NULL DEFAULT 0 COMMENT '版本号',
                            `order_by` int(11) NULL DEFAULT 1 COMMENT '任务排序',
                            `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '0未删除,1删除',
                            `warning_battery` double(5, 2) NULL DEFAULT NULL COMMENT '预警电量,低于该值不可任务分配',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission_action
-- ----------------------------
DROP TABLE IF EXISTS `mission_action`;
CREATE TABLE `mission_action`  (
                                   `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                   `mission_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属任务id',
                                   `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                                   `action_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '动作类型',
                                   `parent_action_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父类动作Id',
                                   `pre_Action` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上一个动作',
                                   `next_Action` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '下一个动作',
                                   `child_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '子动作类型 1、IF 2、ELSE 3、WHILE',
                                   `sequence` int(11) NULL DEFAULT NULL COMMENT '顺序编号',
                                   `description` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                                   `is_sub_action` tinyint(1) NULL DEFAULT 0 COMMENT '是否是子动作',
                                   `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '0未删除,1删除',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   INDEX `mission_id_index`(`mission_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务动作' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission_action_data_statistic
-- ----------------------------
DROP TABLE IF EXISTS `mission_action_data_statistic`;
CREATE TABLE `mission_action_data_statistic`  (
                                                  `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
                                                  `agv_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '小车编号',
                                                  `mission_action_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '动作id',
                                                  `mission_action_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '动作名称',
                                                  `total_num` int(11) NULL DEFAULT 0 COMMENT '动作执行次数',
                                                  `total_time` int(11) NULL DEFAULT 0 COMMENT '动作执行总时间 单位: 秒',
                                                  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '动作统计' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission_action_parameter
-- ----------------------------
DROP TABLE IF EXISTS `mission_action_parameter`;
CREATE TABLE `mission_action_parameter`  (
                                             `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                             `mission_action_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务动作id',
                                             `parameter_key` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数key',
                                             `parameter_value` varchar(10240) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数值',
                                             `parameter_value_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数值类型',
                                             `parameter_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数值类型',
                                             `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                             PRIMARY KEY (`id`) USING BTREE,
                                             INDEX `mission_action_id_index`(`mission_action_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务动作参数表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission_global_variable
-- ----------------------------
DROP TABLE IF EXISTS `mission_global_variable`;
CREATE TABLE `mission_global_variable`  (
                                            `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                            `mission_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务ID',
                                            `variable_key` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '变量键',
                                            `variable_value` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '变量值',
                                            `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                            PRIMARY KEY (`id`) USING BTREE,
                                            INDEX `mission_id_index`(`mission_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务全局变量' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission_group
-- ----------------------------
DROP TABLE IF EXISTS `mission_group`;
CREATE TABLE `mission_group`  (
                                  `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                                  `description` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                                  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission_work
-- ----------------------------
DROP TABLE IF EXISTS `mission_work`;
CREATE TABLE `mission_work`  (
                                 `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                 `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv的编号',
                                 `agv_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv的名称',
                                 `mission_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务ID',
                                 `trigger_selector_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '触发器ID',
                                 `schedule_plan_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '调度计划id',
                                 `callback_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '回调url',
                                 `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
                                 `sequence` int(11) NULL DEFAULT 1 COMMENT '顺序编辑',
                                 `description` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                                 `status` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态：WAIT,START,RUNNING,SUCCESS,FAULT,PAUSE,SHUTDOWN',
                                 `message` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '异常信息',
                                 `runtime_param` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '运行时参数(json格式), 如{\"marker1\":\"1001\"}',
                                 `agv_group_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'AGV组ID',
                                 `mission_group_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务组ID',
                                 `agv_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'AGV类型',
                                 `error_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '错误码',
                                 `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                 `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                 `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 INDEX `status_index`(`status`) USING BTREE,
                                 INDEX `create_time_index`(`create_time`) USING BTREE,
                                 INDEX `sequence_index`(`sequence`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务执行' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission_work_action
-- ----------------------------
DROP TABLE IF EXISTS `mission_work_action`;
CREATE TABLE `mission_work_action`  (
                                        `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                        `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv的编号',
                                        `mission_action_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务动作id',
                                        `mission_work_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属任务ID',
                                        `action_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '动作类型',
                                        `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                                        `message` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '执行结果描述',
                                        `sequence` int(11) NULL DEFAULT NULL COMMENT '顺序编号',
                                        `parameters` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数',
                                        `status` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态：START,RUNNING,SUCCESS,FAULT,WAIT_RUNTIME_PARAM',
                                        `parameter_type` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'NORMAL_PARAMETER' COMMENT '参数类别(NORMAL_PARAMETER,RUNTIME_PARAMETER)',
                                        `result_code` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '返回编码',
                                        `result_message` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '返回提示信息',
                                        `result_type` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '返回数据区数据类型(MESSAGE,IMAGE)',
                                        `result_data` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '返回的数据区内容',
                                        `error_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '错误码',
                                        `parent_action_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父类动作Id',
                                        `child_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '子动作类型 1、IF 2、ELSE 3、WHILE',
                                        `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                        `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                        `action_sequence` int(11) NULL DEFAULT NULL COMMENT '执行顺序编号',
                                        `this_loop_completes` tinyint(1) NULL DEFAULT NULL COMMENT '作为循环的子action时，记录本次循环是否完成',
                                        `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务执行动作' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission_work_action_parameter
-- ----------------------------
DROP TABLE IF EXISTS `mission_work_action_parameter`;
CREATE TABLE `mission_work_action_parameter`  (
                                                  `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                                  `mission_work_action_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务动作id',
                                                  `parameter_key` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数key',
                                                  `parameter_value` varchar(10240) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数值',
                                                  `type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据类型',
                                                  `parameter_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数类型',
                                                  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '作业动作参数' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission_work_global_variable
-- ----------------------------
DROP TABLE IF EXISTS `mission_work_global_variable`;
CREATE TABLE `mission_work_global_variable`  (
                                                 `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                                 `mission_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务ID',
                                                 `mission_work_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作ID',
                                                 `variable_key` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '变量键',
                                                 `variable_value` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '变量值',
                                                 `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                 PRIMARY KEY (`id`) USING BTREE,
                                                 INDEX `mission_work_id_index`(`mission_work_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '工作全局变量' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mission_work_log
-- ----------------------------
DROP TABLE IF EXISTS `mission_work_log`;
CREATE TABLE `mission_work_log`  (
                                     `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                     `mission_work_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '作业ID',
                                     `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv的编号',
                                     `status` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态：WAIT,START,RUNNING,SUCCESS,FAULT,PAUSE,SHUTDOWN',
                                     `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                     `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务状态变更日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mq_message
-- ----------------------------
DROP TABLE IF EXISTS `mq_message`;
CREATE TABLE `mq_message`  (
                               `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                               `message_id` bigint(20) NULL DEFAULT NULL COMMENT '消息id',
                               `topic` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主題名称',
                               `message` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'mq消息',
                               `consume` tinyint(4) NULL DEFAULT 0 COMMENT '是否消费：0：未消费，1：已消费',
                               `unique_flag` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '唯一标志, 用于mqtt消息去重, UUID',
                               `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               PRIMARY KEY (`id`) USING BTREE,
                               UNIQUE INDEX `unique_flag`(`unique_flag`) USING BTREE,
                               INDEX `consume_index`(`consume`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'mq接收信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mq_operate_result_info
-- ----------------------------
DROP TABLE IF EXISTS `mq_operate_result_info`;
CREATE TABLE `mq_operate_result_info`  (
                                           `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                           `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'agv的编号',
                                           `operate_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作返回类型',
                                           `result` tinyint(4) NULL DEFAULT 0 COMMENT '是否成功：0：成功，1：失败',
                                           `message` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '错误信息',
                                           `have_read` tinyint(4) NULL DEFAULT 0 COMMENT '是否已读：0：未读，1：已读',
                                           `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE,
                                           INDEX `agv_operate_id`(`agv_code`, `operate_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'mq操作结果信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for notification
-- ----------------------------
DROP TABLE IF EXISTS `notification`;
CREATE TABLE `notification`  (
                                 `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                 `description` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                                 `type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
                                 `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器人编号',
                                 `agv_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器人名称',
                                 `mission_work_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务Id',
                                 `mission_work_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务名称',
                                 `have_read` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '已读',
                                 `scale` int(11) NULL DEFAULT NULL COMMENT '消息级别',
                                 `help` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '处理建议',
                                 `error_code` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '错误编码',
                                 `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 INDEX `have_read`(`have_read`) USING BTREE,
                                 INDEX `agv_code`(`agv_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '通知消息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for operation_record
-- ----------------------------
DROP TABLE IF EXISTS `operation_record`;
CREATE TABLE `operation_record`  (
                                     `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                     `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器人编号',
                                     `agv_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机器人名称',
                                     `operation_type` int(11) NOT NULL COMMENT '操作类型: 0保养、1维修',
                                     `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '描述信息',
                                     `operation_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作/保养人员',
                                     `operation_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作/保养时间',
                                     `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     INDEX `agv_code`(`agv_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '保养和维护操作记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for park_scheduler
-- ----------------------------
DROP TABLE IF EXISTS `park_scheduler`;
CREATE TABLE `park_scheduler`  (
                                   `id` int(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                   `vehicle_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'vehicle id',
                                   `park_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '泊车点ID',
                                   `park_point_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '泊车站点编号',
                                   `status` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'CREATE' COMMENT '状态：CREATE,START,CANCEL,SUCCESS,FAULT',
                                   `fault_message` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '异常信息',
                                   `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                   `finish_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   INDEX `vehicle_id_index`(`vehicle_id`) USING BTREE,
                                   INDEX `status_index`(`status`) USING BTREE,
                                   INDEX `vehicle_id_status_index`(`vehicle_id`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '泊车分泊队列' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for park_time_log
-- ----------------------------
DROP TABLE IF EXISTS `park_time_log`;
CREATE TABLE `park_time_log`  (
                                  `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
                                  `vehicle_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '小车编号',
                                  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '泊车时间日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for path_agv_type
-- ----------------------------
DROP TABLE IF EXISTS `path_agv_type`;
CREATE TABLE `path_agv_type`  (
                                  `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                  `path_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路径ID',
                                  `agv_type_ids` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '车型ID,多个用逗号隔开',
                                  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  UNIQUE INDEX `path_id`(`path_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '路径车型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for record_path
-- ----------------------------
DROP TABLE IF EXISTS `record_path`;
CREATE TABLE `record_path`  (
                                `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                `agv_map_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '地图id',
                                `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
                                `positions` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '路径坐标队列',
                                `length` double NOT NULL COMMENT '长度',
                                `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '路径表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for runtime_parameter
-- ----------------------------
DROP TABLE IF EXISTS `runtime_parameter`;
CREATE TABLE `runtime_parameter`  (
                                      `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                      `mission_work_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务动作id',
                                      `parameter_key` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数key',
                                      `parameter_value` varchar(10240) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数值',
                                      `type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据类型',
                                      `parameter_type` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数类型',
                                      `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务运行时参数' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for schedule_plan
-- ----------------------------
DROP TABLE IF EXISTS `schedule_plan`;
CREATE TABLE `schedule_plan`  (
                                  `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                  `mission_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务id',
                                  `agv_code` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Agv 编号',
                                  `name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '调度计划名称',
                                  `cron` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间表达式',
                                  `description` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
                                  `complete_frequency` int(11) NULL DEFAULT NULL COMMENT '已经完成次数',
                                  `frequency` int(11) NULL DEFAULT NULL COMMENT 'simpleTrigger 重复次数',
                                  `execute_interval` int(11) NULL DEFAULT 1 COMMENT 'simpleTrigger 执行间隔(单位:秒)',
                                  `status` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
                                  `callback_url` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '回调接口',
                                  `execute_over_create_new` tinyint(1) NULL DEFAULT 0 COMMENT '执行方式 0:一次性全部创建missionWork 1:逐步创建（上一个missionWork执行完成后再创建下一条）',
                                  `execute_time` datetime NULL DEFAULT NULL COMMENT '执行时间',
                                  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '执行计划' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for schedule_plan_run_parameter
-- ----------------------------
DROP TABLE IF EXISTS `schedule_plan_run_parameter`;
CREATE TABLE `schedule_plan_run_parameter`  (
                                                `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                                `mission_action_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务动作id',
                                                `schedule_plan_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '调度计划id',
                                                `parameter_key` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数key',
                                                `parameter_value` varchar(10240) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数值',
                                                `parameter_value_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数值类型',
                                                `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务的动态参数表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for scheduler_config
-- ----------------------------
DROP TABLE IF EXISTS `scheduler_config`;
CREATE TABLE `scheduler_config`  (
                                     `id` int(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                     `low_batter_value` double NULL DEFAULT 0 COMMENT '低电量值',
                                     `cancel_battery_value` double NULL DEFAULT 0 COMMENT '可取消充电电量值',
                                     `high_battery_value` double NULL DEFAULT 0 COMMENT '充电最高电量值',
                                     `free_charge_scope` double NULL DEFAULT 0 COMMENT '空闲充电点分数',
                                     `distance_ratio` double NULL DEFAULT 0 COMMENT '距离基数占比',
                                     `battery_value_ratio` double NULL DEFAULT 0 COMMENT '电量基数占比',
                                     `park_scheduler_enable` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用泊车调度，0是禁用，1是启用',
                                     `charge_scheduler_enable` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用充电调度，0是禁用，1是启用',
                                     `park_scheduler_interval` int(11) NULL DEFAULT 0 COMMENT '泊车调度间隔时间，单位是秒。',
                                     `charge_scheduler_interval` int(11) NULL DEFAULT 0 COMMENT '充电调度间隔时间，单位是秒。',
                                     `block_check_enable` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用绕障功能，0是禁用，1是启用',
                                     `block_check_interval` int(11) NULL DEFAULT 30 COMMENT '障碍识别时间，单位是秒。',
                                     `remove_block_interval` int(11) NULL DEFAULT 1800 COMMENT '默认移除障碍（路径权重）时间，单位是秒。',
                                     `minimum_charge_time` int(11) NULL DEFAULT 600 COMMENT '最短充电时间, 单位是秒。',
                                     `correct_charge_interval` int(11) NULL DEFAULT 168 COMMENT '校正充电间隔, 单位是小时。',
                                     `maximum_correct_charge_num` int(11) NULL DEFAULT 1 COMMENT '同时进行校正充电机器人的最大数量。',
                                     `time_ratio` double NULL DEFAULT -1 COMMENT '任务执行时间评分的基数占比',
                                     `mission_work_spend_time` int(11) NULL DEFAULT 400 COMMENT '任务执行平均时长, 单位是秒',
                                     `pre_mission_work_enable` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用作业预分配，0:不启用，1:启用',
                                     `across_charge_enable` tinyint(1) NULL DEFAULT 0 COMMENT '是否跨地图充电，0:不启用，1:启用',
                                     `across_park_enable` tinyint(1) NULL DEFAULT 0 COMMENT '是否跨地图泊车，0:不启用，1:启用',
                                     `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '调度阈值配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sensor
-- ----------------------------
DROP TABLE IF EXISTS `sensor`;
CREATE TABLE `sensor`  (
                           `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
                           `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '编号',
                           `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
                           `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'IP地址',
                           `port` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '端口',
                           `function_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '功能码',
                           `slave_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '从站ID',
                           `start_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '起始地址',
                           `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态,0未触发、1已触发',
                           `is_disabled` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否禁用,0未禁用、1已禁用',
                           `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '传感器表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_sequence
-- ----------------------------
DROP TABLE IF EXISTS `sys_sequence`;
CREATE TABLE `sys_sequence`  (
                                 `seq_name` varchar(50) CHARACTER SET latin1 COLLATE latin1_bin NOT NULL,
                                 `min_value` int(11) NOT NULL,
                                 `max_value` int(11) NOT NULL,
                                 `current_value` int(11) NOT NULL,
                                 `increment_value` int(11) NOT NULL DEFAULT 1,
                                 PRIMARY KEY (`seq_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`  (
                                  `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
                                  `mission_work` int(11) NULL DEFAULT NULL COMMENT '任务日志 单位:天',
                                  `log_file` int(11) NULL DEFAULT NULL COMMENT '系统日志文件 单位:天',
                                  `system_notify` int(11) NULL DEFAULT NULL COMMENT '系统通知 单位:天',
                                  `mq_message` int(11) NULL DEFAULT NULL COMMENT 'mq消息',
                                  `map_file` int(11) NULL DEFAULT NULL COMMENT '地图文件 单位:天',
                                  `time_zone` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时区',
                                  `agv_status_log` int(11) NULL DEFAULT 1 COMMENT '小车状态日志 单位:天',
                                  `ftp_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'ftp主机地址',
                                  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for trigger_selector
-- ----------------------------
DROP TABLE IF EXISTS `trigger_selector`;
CREATE TABLE `trigger_selector`  (
                                     `id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
                                     `code` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '编码',
                                     `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
                                     `mission_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务id',
                                     `type` int(11) NOT NULL COMMENT '触发类型 1:定时触发、2呼叫器触发、3传感器触发',
                                     `pager_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '呼叫器ID',
                                     `start_type` int(11) NULL DEFAULT NULL COMMENT '开始类型 1:即时、2:定时',
                                     `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                     `period` int(11) NULL DEFAULT NULL COMMENT '触发间隔',
                                     `unit` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '触发间隔的时间单位 SENCOND、DAY、WEEK、MONTH',
                                     `sensor_json` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '传感器JSON,[{\"sensorId\":\"xxxx\",\"value\":1}]',
                                     `device_address` int(11) NULL DEFAULT NULL COMMENT '设备地址',
                                     `button_address` int(11) NULL DEFAULT NULL COMMENT '按钮地址',
                                     `execute_times` int(11) NULL DEFAULT 1 COMMENT '执行次数',
                                     `completed_times` int(11) NULL DEFAULT NULL COMMENT '已完成次数',
                                     `is_disabled` int(11) NOT NULL DEFAULT 0 COMMENT '是否禁用',
                                     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '触发器表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
                         `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                         `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
                         `user_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
                         `password` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '登陆密码',
                         `email` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
                         `phone` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电话',
                         `admin` tinyint(1) NULL DEFAULT 0 COMMENT '是否为管理员 0:非管理员，1:管理员',
                         `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                         `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for work_scheduler
-- ----------------------------
DROP TABLE IF EXISTS `work_scheduler`;
CREATE TABLE `work_scheduler`  (
                                   `id` int(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                   `vehicle_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'agv id',
                                   `work_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务ID',
                                   `work_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '工作名称',
                                   `status` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'CREATE' COMMENT '状态：CREATE,START,CANCEL,SUCCESS,FAULT',
                                   `fault_message` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '异常信息',
                                   `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                   `finish_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   INDEX `work_id_status_index`(`work_id`, `status`) USING BTREE,
                                   INDEX `vehicle_id_status_index`(`vehicle_id`, `status`) USING BTREE,
                                   INDEX `status_index`(`status`) USING BTREE,
                                   INDEX `work_id_index`(`work_id`) USING BTREE,
                                   INDEX `vehicle_id_index`(`vehicle_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '作业分配队列' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for work_time_log
-- ----------------------------
DROP TABLE IF EXISTS `work_time_log`;
CREATE TABLE `work_time_log`  (
                                  `id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键ID',
                                  `vehicle_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '小车编号',
                                  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
                                  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
                                  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '任务时间日志表' ROW_FORMAT = DYNAMIC;


alter table floor 
ADD COLUMN `operate_out_open_value`  int  DEFAULT 1 COMMENT '操作外呼开门值' AFTER `operate_address`,
ADD COLUMN `operate_in_open_value`  int  DEFAULT 1 COMMENT '操作内呼开门值' AFTER `operate_out_open_value`,
ADD COLUMN `elevator_arrive_address` int NULL COMMENT '电梯到位地址' AFTER `operate_close_value`;

-- 清理日志create table 添加索引
create index index_create_time on mq_message(create_time);
create index index_create_time on mission_work_log(create_time);
create index index_create_time on mission_work_action(create_time);
create index index_create_time on mission_work_action_parameter(create_time);
create index index_create_time on mission_work_global_variable(create_time);
create index index_create_time on work_scheduler(create_time);
create index index_create_time on park_scheduler(create_time);
create index index_create_time on charge_scheduler(create_time);
create index index_create_time on notification(create_time);
create index index_create_time on agv_map_update_queue(create_time);
create index index_start_time on free_time_log(start_time);
create index index_start_time on work_time_log(start_time);

alter table park_scheduler ADD COLUMN `source` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'Park' COMMENT '来源:Park、Drive' AFTER `status`;

DROP TABLE IF EXISTS `traffic_area_manage`;
CREATE TABLE `traffic_area_manage` (
  `id` varchar(50) NOT NULL COMMENT 'id',
  `resource_id` varchar(50) NOT NULL COMMENT '资源id',
  `area_code` varchar(256) DEFAULT NULL COMMENT '区域编码',
  `occupy_source` varchar(256) DEFAULT NULL COMMENT '占用方（null表示无占用）',
  `occupy_vehicle_codes` varchar(1024) DEFAULT NULL COMMENT '占用的车辆',
  `occupy_date` datetime(3) DEFAULT NULL COMMENT '申请时间',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='第三方交管区域管理表';

-- ----------------------------
-- Table structure for agv_marker_offset
-- ----------------------------
DROP TABLE IF EXISTS `agv_marker_offset`;
CREATE TABLE `agv_marker_offset`  (
                                      `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'ID',
                                      `agv_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'AGV编码',
                                      `agv_map_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地图ID',
                                      `marker_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '导航点ID',
                                      `offset_x` decimal(10, 3) NULL DEFAULT NULL COMMENT 'x方向偏移量(m)',
                                      `offset_y` decimal(10, 3) NULL DEFAULT NULL COMMENT 'y方向偏移量(m)',
                                      `offset_angle` decimal(10, 3) NULL DEFAULT NULL COMMENT '角度旋转量',
                                      `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      UNIQUE INDEX `agvCodeMarkerId_unique`(`agv_code`, `marker_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'AGV导航点偏移参数' ROW_FORMAT = Dynamic;