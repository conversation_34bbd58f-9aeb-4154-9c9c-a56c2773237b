package com.youibot.agv.scheduler.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "charge_scheduler")
@ApiModel(value = "ChargeScheduler", description = "充电分配队列")
public class ChargeScheduler {

    /**
     * 充电调度状态常量
     */
    public final static String STATUS_CREATE = "CREATE";
    public final static String STATUS_START = "START";
    public final static String STATUS_CANCEL = "CANCEL";
    public final static String STATUS_SUCCESS = "SUCCESS";
    public final static String STATUS_FAULT = "FAULT";

    /**
     * 充电调度类型
     */
    public final static int CORRECT_CHARGE = 1;
    public final static int COMMON_CHARGE = 0;

    /**
     * 充电触发类型
     */
    public final static int LOW_BATTERY_TRIGGER = 0;
    public final static int FREE_TRIGGER = 1;
    public final static int CORRECT_CHARGE_TRIGGER = 2;

    @Id
    @ApiModelProperty(value = "ID", position = 0)
    private Long id;

    @Column
    @ApiModelProperty(value = "车辆ID", position = 1)
    private String vehicleId;

    @Column
    @ApiModelProperty(value = "充电ID", position = 2)
    private String chargeId;

    @Column
    @ApiModelProperty(value = "充电站点编号", position = 3)
    private String chargePointCode;

    @Column
    @ApiModelProperty(value = "充电站点所在地图名称",position = 11)
    private String chargePointMapName;

    @Column
    @ApiModelProperty(value = "触发类型，0：低电量触发 2:空闲触发 3:强制充电触发",position = 12)
    private Integer triggerType;

    @Column
    @ApiModelProperty(value = "充电类型，0:普通充电，1:校验充电", position = 4)
    private Integer chargeType;

    @Column
    @ApiModelProperty(value = "状态：CREATE,START,CANCEL,SUCCESS,FAULT", position = 5)
    private String status;

    @Column
    @ApiModelProperty(value = "错误信息，状态为FAULT时有值", position = 6)
    private String faultMessage;

    @Column
    @ApiModelProperty(value = "创建时间", position = 7)
    private Date createTime;

    @Column
    @ApiModelProperty(value="更新时间",position = 8)
    private Date updateTime;

    @Column
    @ApiModelProperty(value = "开始时间", position = 9)
    private Date startTime;

    @Column
    @ApiModelProperty(value = "结束时间", position = 10)
    private Date finishTime;

}
