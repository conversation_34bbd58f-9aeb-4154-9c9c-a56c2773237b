// import ant from 'ant-design-vue/es/locale/en_US'
// import youibot from 'youibot-plus/lib/locale/lang/en'
export const enLang = {
  message: {
    hello: 'Hello',
    edit: 'Edit',
    del: 'Delete',
    add: 'Add',
    success: 'Success',
    fail: 'Fail',
    reasonIs: 'ReasonIs',
    update: 'Update',
    download: 'Download',
    delete: 'Delete',
    export: 'Export',
    multExport: 'MultExport',
    import: 'Import',
    multImport: 'MultImport',
    switchEn: 'Switch EN',
    switchzh: 'Switch CN',
    options: 'Options',
    reset: 'Reset',
    status: 'Status',
    statusList: {
      0: 'Disable',
      1: 'Enable'
    },
    refresh: 'Refresh',
    nickname: 'Account',
    userName: 'User ID',
    menu: 'Menu',
    role: 'Role',
    describe: 'Describe',
    updateTime: 'Update Time',
    createTime: 'Create Time',
    storageUpdateTime: 'Inventory Update Time',
    passWord: 'Pass Word',
    custom: 'custom',
    requiredTips: 'Please Enter',
    pleaseSelect: 'Please Select',
    enableList: {
      0: 'Yes',
      1: 'No'
    },
    addForm: 'Add',
    editForm: 'Edit',
    cancel: 'Cancel',
    submit: 'Submit',
    totalData: 'Total{total} ',
    type: 'Type',
    group: 'Group',
    range: 'Range',
    formRules: {
      phoneLen: 'Enter the correct 11-digit phone number',
      port: 'Enter the correct port number',
      ip: 'Enter correct IP address',
      isLength: `Longer than {max}characters`,
      isNumber: `The value must be a number only`,
      englishFirst_: `English (first character), underscore, and numeric strings only`,
      englishFirst: `English (initials) and numeric strings only`,
      english: 'English strings only',
      isName: 'English, underscore, and numeric strings only'
    },
    ChargingMarker: 'Charging Marker',
    ParkingMarker: 'Parking Marker',
    WorkMarker: 'Work Marker',
    NavigationMarker: 'Navigation Marker',
    message: 'Message',
    language: 'Language',
    languageSwitch: 'language switch',
    importLanguage: 'Language introduction',
    my: 'My',
    delTips: 'Confirm deletion? Deleted content is not recoverable',
    details: 'Details',
    individuation: 'Individuation',
    searchSettings: 'Search Settings',
    complete: 'Complete',
    connectTimeOut: 'Connection timed out',
    vehicle: 'Vehicle',
    lowBattery: 'Low Battery',
    highBattery: 'High Battery',
    pleaseSelectOneVehicle: 'Please select at least one vehicle',
    second: 'S',
    minute: 'Minute',
    hour: 'Hour',
    hour1: 'Hour',
    day: 'Day',
    angle: 'Angle',
    speed: 'Speed',
    orientation: 'Orientation',
    ip: 'IP',
    online: 'Online',
    unconnected: 'Unconnected',
    abnormal: 'Abnormal',
    inExecution: 'InExecution',
    encoding: 'Encoding',
    join: 'Join',
    professional: 'Operate',
    controls: 'Control',
    all: 'All',
    previous: 'Previous Page',
    nextPage: 'Next Page',
    thereIsNoPublishedTaskType: 'No enabled task flow exists',
    solution: 'Solution',
    dragTheMapFileZipIntoThisArea: 'Drag and drop the map file in this area',
    dragLocationMapFileZipIntoThisArea: 'Drag and drop the bitmap file in this area',
    orClickHereToUpload: 'Or click to upload a f ile',
    fileImport: 'File Import',
    uploadSuccessfully: 'Uploaded Successfully',
    confirm: 'Confirm',
    listSetting: 'List Setting',
    pleaseSelectAtLeastOneItem: 'Please select at least one item',
    clickToUpload: 'Click To Upload',
    fileUploadFailed: 'File Upload Failed',
    deleteOrNot: 'Confirm Delete',
    messageCannotReturn: 'Message Unrecoverable',
    quit: 'Quit',
    append: 'Append',
    deleteAll: 'Delete All',
    port: 'Port',
    required: 'Required',
    variable: 'Variable',
    defaultValue: 'Default Value',
    maximumValue: 'Maximum Value',
    minimumValue: 'Minimum Value',
    yes: 'Yes',
    no: 'No',
    value: 'Value',
    typeName: 'Type Name',
    batch: 'Batch',
    create: 'Create',
    batchCreate: 'Batch Create',
    login: 'Login',
    updateAirShower: 'Update Air Shower',
    updateArea: 'Update Area',
    updateAutodoor: 'Update Autodoor',
    updateElevator: 'Update Elevator',
    updateMap: 'Update Map',
    updateMarker: 'Update Marker',
    updatePath: 'Update Path',
    deletePath: 'Delete Path',
    relocationManual: 'Reposition',
    scrapDraft: 'Scrap Drafts',
    publish: 'Publish',
    copy: 'Copy',
    name: 'Name',
    serialNumber: 'Serial Number',
    startTime: 'Start Time',
    endTime: 'End Time',
    createTask: 'Create Task',
    noData: 'No Data',
    arguments: 'Arguments',
    priority: 'Priority',
    selectTheDataYouWantToDelete: 'Select Data',
    selectTheDataThatYouWantToModify: 'Select the data to be modified',
    passwordInconsistencyTip: 'Password and confirmation password differ, re-enter',
    theEnteredPasswordIsInconsistent: 'Inconsistent Password Entry',
    systemServiceException: 'System Service Exception',
    founder: 'Founder',
    result: 'Result',
    meter: 'Meter',
    file: 'File',
    creationMode: 'Creation Mode',
    externalCoding: 'External Coding',
    task: 'Task',
    default: 'Default',
    remark: 'Remark',
    addRemark: 'Add Remark',
    form: 'Form',
    height: 'Height',
    point: 'Point',
    noDataAvailable: 'No Data Available',
    cm: 'CM',
    // 用户权限
    userSetting: {
      roleManage: 'Role Manage',
      accountManage: 'Account Manage',
      auth: 'Authority',
      addRole: 'Add Role',
      rename: 'Rename',
      account: 'Account',
      name: 'Name',
      role: 'Role',
      email: 'Email',
      phone: 'Phone',
      enable: 'Enable',
      updateDate: 'Update Date',
      automaticLogout: 'Automatic Logout',
      password: 'Password',
      surePassword: 'Ensure Password',
      enableList: {
        1: 'Yes',
        0: 'No'
      },
      roleName: 'Values are limited to 20 characters; Only Chinese, English and numbers',
      username: 'Values are limited to 20 characters;English Beginning;Only English, underscores, numbers',
      realName: 'Values are limited to 20 characters;',
      autoLogoutTime: 'Range[0,9999]',
      changePassword: 'changePassword',
      logout: 'logout',
      pleaseSelectARole: 'Please Select A Role',
      nameTip: 'Values are allowed to be null',
      theNameCannotExceed20Characters: 'Value cannot exceed 20 characters'
    },
    changePassword: {
      changePassword: 'Change Password',
      resetPassword: 'Reset Password',
      oldPassword: 'Old Password',
      loginUserPassword: 'Login User Password',
      newPassword: 'New Password',
      confirmNewPassword: 'Confirm New Password'
    },
    // Map
    map: {
      markerName: 'Value cannot exceed 20 characters;Must start with English;Only support English, underscore, and numbers',
      type: {
        Elevator: 'Elevator',
        AutoDoor: 'Automatic Door',
        MapArea: 'Area',
        Marker: 'Marker',
        Vehicle: 'Robot',
        All: 'All Results',
        AirShowerDoor: 'Air Shower Door'
      },
      autoDoor: 'Automatic Door',
      manualDoorOpening: 'Manual Door Opening',
      manualDoorClosing: 'Manual Door Closing',
      automaticDoorCoding: 'Automatic Door Coding',
      autoDoorStatus: {
        OPEN: 'Open',
        CLOSE: 'Close',
        COMMUNICATION_ERROR: 'Communication Error',
        OPERATING: 'Operating',
        PARAM_ERROR: 'Parameter Error'
      },
      areaTypes: {
        ControlArea: 'Control Area',
        SingleAgvArea: 'Single AGV Area',
        ShowArea: 'Show Area',
        ChannelArea: 'Channel Area',
        NoRotatingArea: 'No-Rotating Area',
        NoParkingArea: 'No-Parking Area',
        ForbiddenArea: 'Off-limits area',
        TrafficArea: 'Traffic area',
        ThirdSystemTrafficArea: 'ThirdSystemTrafficArea'
      },
      markerTypes: {
        ChargingMarker: 'Charging Marker',
        WorkMarker: 'Work Marker',
        NavigationMarker: 'Navigation Marker'
      },
      AirShowerDoor: 'Air Shower Door',
      showerDoorCode: 'Shower Door Code',
      manualDoorOpening1: 'Manual Door Opening 1',
      manualDoorClosing1: 'Manual Door Closing 1',
      manualDoorOpening2: 'Manual Door Opening 2',
      manualDoorClosing2: 'Manual Door Closing 2',
      gate1Status: 'Gate 1 Status',
      gate2State: 'Gate 2 Status',
      areaCoding: 'Area Code',
      robotCoding: 'Robot Code',
      offSite: 'Off-site',
      pointPosition: 'Marker Position',
      coordinate: 'Coordinate',
      elevator: 'Elevator',
      elevatorCode: 'Elevator Code',
      searchMapElements: 'Search Map Elements',
      currentMap: 'Current Map',
      map: 'Map',
      currentCoordinate: 'Current Coordinate',
      pointType: 'Point Type',
      customCoding: 'Custom Coding',
      pointCoding: 'Point Coding',
      batchNew: 'Batch New',
      lineNumber: 'Line Number',
      lineSpacing: 'Line Spacing',
      numberOfColumns: 'Number of Columns',
      spaceBetweenColumns: 'Space Between Columns',
      element: 'Element',
      searchElement: 'Search Element',
      area: 'Area',
      select: 'Select',
      justificationLeft: 'Justify Left',
      justifyRight: 'Justify Right',
      topJustification: 'Top Justify',
      alignBottom: 'Align Bottom',
      horizontalEquidistance: 'Horizontal Equidistance',
      verticalEquidistance: 'Vertical Equidistance',
      exportDraft: 'Export Draft',
      publishMap: 'Publish Map',
      scrapDraft: 'Scrap Draft',
      elementList: 'Element List',
      unidirectionalPath: 'Unidirectional Path',
      bidirectionalPath: 'Bidirectional Path',
      quiescentTime: 'Quiescent Time',
      networkIp: 'Network IP',
      door: 'Door',
      networkPort: 'Network Port',
      openDoorControlAddress: 'Open Door Control Address',
      openDoorControlAddressTip:
        'Address to control the opening of air shower doors. The dispatching system uses function code 06H to write to this address.',
      openAutoDoorControlAddressTip:
        'Address to control the opening of automatic doors. The dispatching system uses function code 06H to write to this address.',
      doorControlAddress: 'Door Control Address',
      doorControlAddressTip:
        'Address to control the closing of air shower doors. The dispatching system uses function code 06H to write to this address.',
      autoDoorControlAddressTip:
        'Address to control the closing of automatic doors. The dispatching system uses function code 06H to write to this address.',
      openStateAddress: 'Open State Address',
      openStateAddressTip:
        'Address indicating the completion of opening air shower doors. The dispatching system uses function code 03H to read from this address.',
      autoOpenStateAddressTip:
        'Address indicating the completion of opening automatic doors. The dispatching system uses function code 03H to read from this address.',
      closedAddress: 'Closed State Address',
      closedAddressTip:
        'Address indicating the completion of closing air shower doors. The dispatching system uses function code 03H to read from this address.',
      autoClosedAddressTip:
        'Address indicating the completion of closing automatic doors. The dispatching system uses function code 03H to read from this address.',
      bindingPath: 'Binding Path',
      pleaseSelectAPath: 'Please select the path where the device is located',
      noPointsOfIntersectionCanBeAdded: 'The two paths of the air shower door must have intersections',
      areaType: 'Area Type',
      ControlArea: 'Control Area',
      SingleAgvArea: 'Single AGV Area',
      ShowArea: 'Display Area',
      ChannelArea: 'Channel Area',
      NoRotatingArea: 'No-Rotating Area',
      NoParkingArea: 'No-Parking Area',
      TrafficArea: 'Traffic area',
      ForbiddenArea: 'Off-limits area',
      ThirdSystemTrafficArea: 'ThirdSystemTrafficArea',
      multipleValues: 'Multiple Values',
      displayName: 'Display Name',
      areaColor: 'Area Color',
      position: 'position',
      mapPoint: 'Map Marker',
      callAddress: 'Call Address',
      callAddressTip: 'Address to control elevator ascent and descent. The dispatching system uses function code to write to this address.',
      arrivalStatusAddress: 'Arrival Status Address',
      arrivalStatusAddressTip:
        'Address to determine whether the elevator has arrived at the map. The dispatching system uses function code to read from this address.',
      statusAddressOfTheLadderDoor: 'Ladder Door Status Address',
      statusAddressOfTheLadderDoorTip:
        'Address to determine whether the elevator door has been opened. The dispatching   system uses function code to read from this address.',
      path: 'Path',
      mapCoding: 'Map Coding',
      mapName: 'Map Name',
      mapType: 'Map Type',
      mapResolution: 'Map Resolution',
      mapSize: 'Map Size',
      originMigration: 'Origin Migration',
      releaseTime: 'Release Time',
      qrCodeMap: 'QR Code Map',
      laserMap: 'Laser Map',
      normalResolution: 'Normal Resolution',
      highResolution: 'High Resolution',
      enableParking: 'Enable Parking',
      networkType: 'Network Type',
      networkTypeList: {
        0: 'Intersection Network Point',
        1: 'Normal Network Point'
      },
      chargingAttribute: 'Charging Attribute',
      chargingDirection: 'Charging Direction',
      dockingType: 'Docking Type',
      buttJoint: 'Front Docking',
      buttbutt: 'Rear Docking',
      reflectiveStripFeaturesContactPoints: 'Reflective Strip Feature Contact Points',
      vTypeFeatureContact: 'V-Type Feature Contact Points',
      pathType: 'Path Type',
      stationCode: 'Station Code',
      pathWeight: 'Path Weight',
      vehicleTypeRestriction: 'Vehicle Type Restriction',
      lackOfRobotTypes: 'Lack of Robot Types',
      movingSpeed: 'Moving Speed',
      rotationalSpeed: 'Rotational Speed',
      movingAcceleration: 'Moving Acceleration',
      rotationalAcceleration: 'Rotational Acceleration',
      moveObstacleAvoidanceArea: 'Move Obstacle Avoidance Area',
      obstacleAvoidanceArea: 'Obstacle Avoidance Area',
      rotationObstacleRegion: 'Rotation Obstacle Region',
      noseDirection: 'Nose Direction',
      potholeDetection: 'Pothole Detection',
      dObstacleAvoidance: '3D Obstacle Avoidance',
      featureNavigation: 'Feature Navigation',
      navigationPath: 'Navigation Path',
      QR_Down: 'QR Code Docking',
      LeaveDocking: 'Leave Docking',
      Shelflegs: 'Shelf Legs Docking',
      Symbol_V: 'V-Shaped Plate Docking',
      Reflector: 'Reflector Docking',
      Pallet: 'Pallet Docking',
      unsetRegion: 'Unset Region',
      pleaseSelectAPointPosition: 'Please select a point',
      selectPoint: 'Select Point',
      aPointBitHasBeenSelected: 'Point selected',
      clear: 'Clear',
      pleaseSelectParkingLocation: 'Please select: Parking Marker',
      displayElement: 'Display Element',
      pathDirection: 'Path Direction',
      displayText: 'Display Text',
      areaName: 'Area Name',
      elementSize: 'Element Size',
      forceTheElementSizeToChange: 'Force Change Element Size',
      backgroundSettings: 'Background Settings',
      operationHabit: 'Operation Habit',
      doubleClickCreateElement: 'Double Click to Create Element',
      backgroundColor: 'Background Color',
      showBackground: 'Show Background Image',
      displaysThePngDiagramBorder: 'Show Map Border',
      limitTheDistanceBetweenPointAndPoint: 'Limit Distance Between Point and Point',
      limitTheDistanceBetweenPointsAndPaths: 'Limit Distance Between Points and Paths',
      displayRobot: 'Display Robot',
      realTimePointCloud: 'Real-Time Point Cloud',
      mapEditor: 'Map Editor',
      monitoring: 'Monitoring',
      editParameter: 'Edit Parameter',
      parkingOrNot: 'Parking or Not',
      noMapYet: 'No map yet',
      pleaseRepositionTheRobot: 'Please reposition the robot. Press Esc to cancel.',
      pleaseSelectAnEndpoint: 'Please select an EndPoint marker.',
      recordingPoint: 'Recording marker',
      pointRecordingSucceeded: 'Marker recorded successfully.',
      discardDraftTip: 'This operation cannot be undone. Are you sure you want to discard?',
      publishMapOrNot: 'Publish map or not',
      failedToPublishMap: 'Failed to publish map.',
      publishMapSuccessfullyTip: 'Map published successfully. It will be automatically synchronized with the robot.',
      exitTheRecordingPoint: 'Exit marker recording',
      recordingPointTip: 'Use "↓ ↑ ← →" to move and rotate; press "Enter" to record the marker.',
      remoteControlMode: 'Use "↓ ↑ ← →" to move and rotate; press "Esc" to exit remote control mode.',
      thereAreNoBotsOnTheCurrentMap:
        'There are no robots on the current map. Please switch the robot to this map on the robot list page first.',
      thePathSelectionOperationIsCancelled: 'Path selection operation cancelled.',
      pleaseSelectRobot: 'Please select a robot.',
      robotIsNotConnectedTip: 'Robot is not connected. Recording failed.',
      haveBeenPublishedTip: 'The map has already been published. Please return to the list.',
      haveBeenDiscardedTip: 'The map draft has been discarded. Please return to the list.',
      thePointIsCreatedSuccessfully: 'Marker created successfully.',
      outletElevator: 'Export Elevator',
      leadInElevator: 'Import Elevator',
      transferElevatorFileJson: 'Drag and drop elevator file here',
      doubleClickMultiplexing: 'Double-click to multiplex',
      parkingSign: 'Parking Sign',
      locationMapList: 'Location Map List',
      locationMap: 'Location Map',
      leadinMap: 'Import Location Map',
      setAsDefault: 'Set as Default',
      setTheRobotType: 'Set Robot Type',
      deleteLocationMap: 'Delete Location Map',
      locationMapDeletedTip: 'Location map has been deleted. Please reopen.',
      currentAngle: 'Current Angle',
      fixedAngle: 'Fixed Angle',
      areavehicleTypeNameTip: 'When robot type is empty, all robot types are effective.',
      directionToast: 'Value must be between -180 and 180.',
      offsetX: 'Offset X',
      offsetY: 'Offset Y',
      offsetAngle: 'Offset Angle',
      dockingDirection: 'Docking Direction',
      Head: 'Head Docking',
      Tail: 'Tail Docking',
      revocation: 'Undo',
      renewal: 'Redo',
      cameraObstacleAvoidance: 'Camera Obstacle Avoidance',
      templateNumber: 'Template Number',
      text1: 'Text 1',
      text2: 'Text 2',
      text3: 'Text 3',
      number1: 'Number 1',
      number2: 'Number 2',
      number3: 'Number 3',
      obstacleAvoidance: 'Autonomous Obstacle Avoidance',
      enableAvoidance: 'Enable Avoidance',
      batchNewMaximumTips: 'Maximum limit for batch new markers is 2000',
      unmapped: 'No positioning map data',
      key: 'key',
      value: 'value',
      addExtendParam: 'addparameter',
      fieldDplication: 'Fields are not allowed to duplicate!',
      addOrientationAngle: 'Increase navigation Angle',
      checkDirectionalNavigation: 'Check directional navigation',
      navigationAngle: 'Navigation Angle',
      operationFailedMandatoryFieldsCannotEmpty: 'The operation failed. Mandatory fields cannot be empty',
      readingFunctionCode: 'Reading the function code',
      writingFeatureCode: 'Writing feature code',
      elevatorUsageScenario: 'Elevator Usage Scenario',
      elevatorModeControlAddress: 'Elevator Mode Control Address',
      enterRobotModeValue: 'Value to Enter Robot Mode',
      exitRobotModeValue: 'Value to Exit Robot Mode',
      loadDetectedValue: 'Load Detected Value',
      doorOpenedValue: 'Elevator Door Opened Value',
      doorClosedValue: 'Elevator Door Closed Value',
      arrivalStatusValue: 'Arrival Status Value',
      robotOnly: 'Robot Only',
      humanAndRobotShared: 'Human and Robot Shared',
      modeStatusAddress: 'Mode Status Address',
      robotModeStatusValue: 'Robot Mode Status Value',
      outgoingAddress: 'Outgoing address',
      internalCallAddress: 'Internal call address',
      outOperateOpenValue: 'out Operate Open Value',
      innerOperateOpenValue: 'inner Operate Open Value',
      goodsCheckAddress: 'In stock status address',
      currentStatus: 'State of communication',
      goodsCheckAddressTip:
        'Check whether there is goods in the device before entering. If there is goods, do not enter. The address is null and not checked',
      currentStatusObject: {
        NORMAL: 'Communication is normal',
        ERROR: 'Abnormal communication'
      },
      open: 'Open the door',
      close: 'close the door',
      occupyCode: 'occupier',
      occupyVehicleCode: 'Occupancy robot coding',
      manualRelease: 'Manual release',
      manualReleaseTip: 'The operation cannot be rolled back. Do you want to release the area usage?'
    },
    mapList: {
      releaseStatus: 'Release Status',
      releaseStatusList: {
        1: 'Published',
        0: 'Unpublished'
      },
      resolution: 'Resolution',
      dimension: 'Dimension',
      radioList: {
        normalResolution: 'Normal Resolution (0.05)',
        highResolution: 'High Resolution (0.03)'
      },
      copyMap: 'Copy Map',
      newMapCoding: 'New Map Code',
      newMapName: 'New Map Name',
      selectImportMode: 'Select Import Mode',
      importAll: 'Import All',
      leadInNetwork: 'Import Network',
      leadInLaser: 'Import Laser',
      importMap: 'Import Map',
      importMapTip: 'After successful import, map data with the same ID will be overwritten.',
      fileIsSuccessfullyImportedTip: 'Map file imported successfully'
    },
    // 机器人管理
    robotManage: {
      storageLocation: 'Storage Location',
      vehicle: 'Vehicle',
      status: 'Status',
      abnormalStatus: 'Abnormal Status',
      vehicleCode: 'Vehicle Code',
      controlMode: 'Control Mode',
      controlModeList: {
        Manual: 'Manual Control',
        Auto: 'Auto Control',
        Repair: 'Repair Mode'
      },
      dispatch: 'Schedule',
      scheduleMode: 'Schedule Mode',
      scheduleModeList: {
        ManualSchedule: 'Manual Schedule',
        AutoSchedule: 'Auto Schedule'
      },

      connectStatus: 'Connection Status',
      connectStatusList: {
        Disconnect: 'Disconnected',
        Connect: 'Connected'
      },
      softEmerStopStatus: 'Running',
      softEmerStopStatusList: {
        Close: 'Running',
        Open: 'Paused'
      },
      storageState: {
        FULL: '有料',
        EMPTY: '无料',
        ERROR: '检测错误'
      },
      errorState: {
        0: '正常',
        1: '异常'
      },
      softEmerStopStatusListBut: {
        Close: 'Resume',
        Open: 'Pause'
      },
      // abnormalStatus: 'Abnormal Status',
      abnormalStatusList: {
        Abnormal: 'Abnormal',
        Normal: 'Normal'
      },
      workbenchAbnormalStatusList: {
        Abnormal: 'Abnormal',
        Normal: 'Normal'
      },
      locatedStatus: 'Located Status',
      locatedStatusList: {
        NotLocated: 'Not Located',
        Located: 'Located'
      },

      workStatus: 'Work Status',
      workStatusList: {
        Offline: 'Offline',
        Work: 'Busy',
        Free: 'Idle'
      },
      missionName: 'Mission',
      missionWorkActions: 'Actions',
      rate: 'Battery Level',
      vehicleTypeName: 'Robot Type',
      vehicleGroupName: 'Robot Group',
      pilotVersion: 'Pilot Version',
      mosVersion: 'Mos Version',
      ip: 'IP Address',
      mac: 'MAC Address',
      pause: 'Pause',
      restore: 'Restore',
      manual: 'Manual',
      semiAutomatic: 'Semi-automatic',
      automatic: 'Automatic',
      restart: 'Restart',
      clear: 'Clear',
      assignMap: 'Assign Map',
      stopTask: 'Stop Task',
      viewLog: 'View Log',
      batchOperation: 'Batch Operation',
      setUp: 'Set Up',
      details: 'Details',
      allocationProcess: 'Allocation Process',
      softEmergencyStopBut: {
        openSoftEmergencyStop: 'Pause',
        closeSoftEmergencyStop: 'Resume'
      },
      enableCharging: 'Enable Charging',
      enableParking: 'Enable Parking',
      chargingMarker: 'Charging Marker',
      parkingMarker: 'Parking Marker',
      chargingList: {
        2: 'Default',
        1: 'Enabled',
        0: 'Disabled'
      },
      relocation: 'Relocation',
      powerOff: 'Power Off',
      importRobotGrouping: 'Import Robot Grouping',
      statistics: 'Statistics',
      offSite: 'Off-site',
      historicalTask: 'Historical Tasks',
      setType: 'Set Type',
      setGroup: 'Set Group',
      pleaseSelectARobotGroup: 'Please select a robot group',
      pleaseSelectARobotType: 'Please select a robot type',
      importedRobotType: 'Imported Robot Type',
      allowedRotation: 'Allow Rotation',
      canRotateList: {
        true: 'Yes',
        false: 'No'
      },
      currentLocationMap: 'Current Location Map',
      switchMap: 'Switch Map',
      carryTask: 'Execute Task',
      turnCharge: 'Turn on Charging',
      openParking: 'Turn on Parking',
      autoChargeState: {
        0: 'Disable Auto Charging',
        1: 'Enable Auto Charging'
      },
      autoParkState: {
        0: 'Disable Auto Parking',
        1: 'Enable Auto Parking'
      },
      automaticRelocation: 'Automatic Relocation',
      manualRelocation: 'Manual Relocation',
      resetting: 'Reset',
      ordinaryCharging: 'Ordinary Charging',
      buttReset: 'Reset docking'
    },
    // 节点设置
    actionSetting: {
      code: 'Code',
      name: 'Name',
      type: 'Type',
      enterParamCount: 'Input Parameter Count',
      outParamCount: 'Output Parameter Count',
      add: 'Add',
      export: 'Export',
      import: 'Import',
      preview: 'Preview',
      notice: 'Notice',
      icon: 'Icon',
      parameter: 'Parameter',
      addInputParameters: 'Add Input Parameters',
      addOutputParameters: 'Add Output Parameters',
      parameterType: 'Parameter Type',
      parameterCode: 'Parameter Code',
      parameterName: 'Parameter Name',
      category: 'Category',
      iconUploadComplete: 'Icon Upload Complete',
      isCommonList: {
        true: 'Yes',
        false: 'No'
      },
      // 新
      isAllowSkipList: {
        true: 'Yes',
        false: 'No'
      },
      baseTypeList: {
        Text: 'Text',
        Number: 'Number',
        Common: 'common'
      },
      componentOptions: {
        Default: 'Default',
        Json: 'JSON',
        Bool: 'BOOL',
        RadioList: 'Single-select List',
        MultiList: 'Multi-select List',
        VehicleMapCode: 'Single-select Map',
        VehicleMapCodeList: 'Multi-select Map',
        MarkerCode: 'Single-select Marker',
        MarkerCodeList: 'Multi-select Marker',
        VehicleCode: 'Single-select Robot',
        VehicleCodeList: 'Multi-select Robot',
        VehicleTypeCode: 'Single-select Robot Type',
        VehicleGroupCode: 'Single-select Robot Group',
        WarehouseLocationType: 'Single-select Warehouse Location Type',
        WarehouseLocation: 'Single-select Warehouse Location',
        WarehouseArea: 'Single-select Warehouse Area',
        VehicleGroupCodeList: 'Multi-select Robot Group',
        VehicleTypeCodeList: 'Multi-select Robot Type',
        customParameter: 'Custom parameter',
        MultiText: 'Multiple text'
      },
      nodeSettingsImport: 'Import Node Settings',
      fileFormatJson: 'Drag and Drop File',
      inputBox: 'Input Box',
      // 新的
      skipAllow: 'Skip allow',
      retryNum: 'number of retries',
      allowRetry: 'Allow retry'
    },
    // 任务管理
    taskManage: {
      code: 'Code',
      name: 'Name',
      status: 'Status',
      priority: 'Priority',
      robot: 'Robot',
      callbackUrl: 'Callback URL',
      source: 'Source',
      createDate: 'Creation Date',
      startTime: 'Start Time',
      endTime: 'End Time',
      log: 'Log',
      taskExecution: 'Task Execution',
      currentNode: 'Current Node',
      executionTime: 'Execution Time',
      runningLog: 'Running Log',
      pleaseEnterADescriptionSearch: 'Please enter a description to search',
      statistics: 'Statistics',
      runningTime: 'Running Time',
      runTimes: 'Run Times',
      totalHours: 'Total Hours',
      inputValue: 'Input Value',
      outputValue: 'Output Value',
      taskInformation: 'Task Information',
      nodeInformation: 'Node Information',
      jsonFormatError: 'JSON Format Error',
      cancelATask: 'Cancel Task',
      whetherToCancelATask: 'Cancel the task or not',
      downloadRecord: 'Download Record',
      optionsStatus: {
        Create: 'Pending',
        Running: 'Running',
        Finished: 'Finished',
        Cancel: 'Cancelled'
      },
      optionsSource: {
        Api: 'API',
        Manual: 'Manual Dispatch',
        Charge: 'Charging Strategy',
        Park: 'Parking Strategy',
        Traffic: 'Traffic Management Strategy',
        Pda: 'PDA'
      },
      batchCancellation: 'Batch Cancellation',
      uploadRecord: 'Upload Record',
      importTaskManagement: 'Import Task Management',
      uploadLog: 'Upload Log',
      theCompletedOrCanceledTaskIsSelected: 'A completed or canceled task is selected',
      successfullyCancelledTask: 'Task cancellation successful',
      failedToCancelTheTaskBecause: 'Failed to cancel the task due to',
      failedToCancelTask: 'Failed to cancel task',
      successfulOperation: 'Operation successful',
      operationFailure: 'Operation failure',
      noPublishedTaskProcessExists: 'No published task process exists',
      // 新的
      skip: 'skip',
      remainingDistance: 'surplus distance',
      totalDistance: 'total distance',
      retry: 'retry',
      retryTip: 'Retry or not',
      skipTip: 'Are you sure to skip this node and directly execute subsequent nodes?',
      isBreak: 'interruptible',
      isBreakObject: {
        0: 'yes',
        1: 'no'
      }
    },
    // 任务类型
    taskType: {
      code: 'Code',
      name: 'Name',
      priority: 'Priority',
      selfCheckStatus: 'Self-check Status',
      predictExecTime: 'Predicted Execution Time',
      layout: 'Layout',
      implement: 'Implement',
      clone: 'Clone',
      eventType: 'Event Type',
      eventTypeList: {
        Interface: 'Interface',
        FixedTime: 'Fixed Time',
        Button: 'Button',
        Plc: 'PLC',
        VehiclePlc: 'Robot PLC',
        VehicleAbnormal: 'Robot Abnormal',
        TaskCancel: 'Task Cancel',
        TaskFinished: 'Task Finished'
      },
      optionsTemplateType: {
        Common: 'Common Type',
        Event: 'Event Type'
      },
      optionsPublishStatus: {
        Published: 'Published',
        Unpublished: 'Unpublished'
      },
      typeList: {
        Common: 'Create Common Type',
        Event: 'Create Event Type'
      },
      jobFlowType: 'Job Flow Type',
      releaseStatus: 'Release Status',
      importTaskType: 'Import Task Type'
    },
    // 流程编排
    taskTypeArrangement: {
      Business: 'General Control',
      Common: 'Common',
      Communication: 'Communication Component',
      Process: 'Process Control',
      AllocationResource: 'Resource Allocation',
      ObtainResource: 'Resource Acquisition',
      Other: 'Other',
      Vehicle: 'Robot Control',
      commonNode: 'Common Node',
      import: 'Input',
      export: 'Output',
      otherCondition: 'Other Condition',
      parallelBranch: 'Parallel Branch',
      cycleCondition: 'Cycle Condition',
      endLoop: 'End Loop',
      start: 'Start',
      end: 'End',
      settingsCommonlyUsed: 'Set Commonly Used',
      parallel: 'Parallel',
      parallelNode: 'Parallel Node',
      condition: 'Condition',
      conditionalNode: 'Conditional Node',
      circulation: 'Loop',
      loopNode: 'Loop Node',
      addCondition: 'Add Condition',
      settingCommonNodes: 'Set Common Nodes',
      addParallel: 'Add Parallel',
      displayName: 'Display Name',
      thePropertiesAreNotSavedPressEnterToConfirm: 'Properties are not saved, press "Enter" to confirm',
      cycleTime: 'Cycle Time',
      cycleNumber: 'Cycle Number',
      constantValue: 'Constant Value',
      setOfConditions: 'Condition Set',
      and: 'And',
      or: 'Or',
      addConditionGroup: 'Add Condition Group',
      lt: 'Less Than',
      ne: 'Not Equal To',
      eq: 'Equal To',
      gt: 'Greater Than',
      ge: 'Greater Than or Equal To',
      le: 'Less Than or Equal To',
      belong: 'Belong',
      contain: 'Contain',
      eventType: 'Event Type',
      ip: 'IP Address',
      portNumber: 'Port Number',
      functionCode: 'Function Code',
      registerAddress: 'Register Address',
      registerValue: 'Register Value',
      section: 'Section',
      effectiveScopeRobot: 'Effective Scope (Robot)',
      effectiveScopeTask: 'Effective Scope (Task)',
      taskAttribute: 'Task Attribute',
      conditionalAttribute: 'Conditional Attribute',
      nodeAttribute: 'Node Attribute',
      taskVariableInput: 'Task Variable (Input)',
      variableName: 'Variable Name',
      taskVariableOutput: 'Task Variable (Output)',
      owningNode: 'Owning Node',
      priorityState: {
        5: 'Highest',
        4: 'High',
        3: 'Medium',
        2: 'Low',
        1: 'Lowest'
      },
      interfaceInputFormType: {
        Json: 'JSON',
        MarkerCode: 'Marker',
        VehicleCode: 'Robot',
        Default: 'Default'
      },
      //流程编排 表单类型数据
      variableTypeList: {
        Default: 'Default',
        Bool: 'Boolean',
        RadioList: 'Single Selection List',
        MultiList: 'Multi-selection List',
        VehicleMapCode: 'Single Selection Map',
        VehicleMapCodeList: 'Multi-selection Map',
        MarkerCode: 'Single Selection Marker',
        MarkerCodeList: 'Multi-selection Marker',
        VehicleCode: 'Single Selection Robot',
        VehicleCodeList: 'Multi-selection Robot',
        VehicleTypeCode: 'Single Selection Robot Type',
        VehicleTypeCodeList: 'Multi-selection Robot Type',
        VehicleGroupCode: 'Single Selection Robot Group',
        VehicleGroupCodeList: 'Multi-selection Robot Group',
        WarehouseArea: 'Single Selection Warehouse Area',
        WarehouseLocation: 'Single Selection Warehouse Location',
        WarehouseLocationType: 'Single Selection Warehouse Location Type',
        Json: 'JSON',
        Object: 'Object'
      },
      //流程编排 输入类型数据
      variableCategoryList: {
        Text: 'Text',
        Number: 'Number',
        Common: 'Common'
      },
      effectiveScopeTaskState: {
        1: 'All',
        2: 'Partial'
      },
      variableNameDuplication: 'Duplicate Variable Name',
      settlementOfCondition: 'Condition Settings',
      unpublish: 'Disable',
      haveReleased: 'Enable',
      publishingFailedEmptyLoopExists: 'Failed to enable task, there is an empty loop in a node',
      publishingFailedTaskMustContainOtherNodes: 'Failed to enable the task. The task must contain other nodes',
      isParameterMandatoryTip: 'Failed to enable task, there are nodes with unset parameters',
      eventTypeList: {
        FixedTime: 'Fixed Time',
        Button: 'Button',
        Plc: 'PLC',
        VehiclePlc: 'Robot PLC',
        VehicleAbnormal: 'Robot Abnormal',
        TaskCancel: 'Task Cancel',
        TaskFinished: 'Task Finished',
        Interface: 'Default'
      },
      effectiveDate: 'Effective Date',
      pleaseEnterTheEffectiveStartDate: 'Enter the effective start date',
      pleaseEnterTheEffectiveEndDate: 'Enter the effective end date',
      effectiveTime: 'Effective Time',
      pleaseEnterTheEffectiveStartTime: 'Enter the effective start time',
      pleaseEnterTheEffectiveEndTime: 'Enter the effective end time',
      interval: 'Interval',
      enableOrNot: 'Enable or Not',
      callBoxNumber: 'Call Box Number',
      buttonNumber: 'Button Number',
      robotNumber: 'Robot Number',
      robotLocationMap: 'Robot Location Map',
      exceptionCoding: 'Exception Coding',
      anomalyLevel: 'Anomaly Level',
      exceptionDetails: 'Exception Details',
      robotAssembly: 'Robot Assembly',
      taskPublishingSucceeded: 'Task Published Successfully',
      searchNode: 'Search Node',
      quickAccess: 'Quick Access',
      quickAccessTip: 'Enable to create tasks from robot cards in the monitoring center',
      inputParameter: 'Input Parameter',
      outputParameter: 'Output Parameter',
      missionNumber: 'Task number',
      customOutput: 'Custom output',
      customInput: 'Custom input',
      pda: 'PDA',
      pdaTip: 'Enable to create this task in PDA'
    },

    // 通知
    notice: {
      levelList: {
        1: 'Normal',
        2: 'Warning',
        3: 'Error'
      },
      statusList: {
        0: 'Active',
        1: 'Ignore',
        2: 'Closed'
      },
      ignore: 'Ignore',
      level: 'Level',
      source: 'Source',
      quest: 'Task',
      equipment: 'Equipment',
      closingTime: 'Closing Time',
      intervalTime: 'Interval Time',
      importTheNotificationProfile: 'Import Notification Profile',
      isUpload: 'Whether to report',
      isUploadState: {
        0: 'no',
        1: 'yes'
      }
    },
    // 监控台异常消息
    exception: {
      ignore: 'Ignore',
      cancelIgnore: 'Cancel Ignore',
      exceptionLevel: 'Exception Level',
      sourceSystem: 'Source System',
      exceptionType: 'Exception Type',
      solution: 'Solution',
      exceptionStatus: 'Exception Status',
      ignoreStatus: 'Ignore Status',
      robot: 'Robot',
      taskId: 'Task ID',
      deviceId: 'Device ID',
      mapName: 'Map Name',
      info: 'Info',
      warning: 'Warning',
      error: 'Error',
      unread: 'Unread',
      readAll: 'Mark All as Read',
      read: 'Read',
      closeTime: 'Close Time',
      exceptionStatusList: {
        0: 'Open',
        1: 'Closed'
      },
      ignoreStatusList: {
        0: 'Not Ignored',
        1: 'Ignored'
      },
      exceptionMessage: 'Exception Message',
      exceptionMessageDetails: 'Exception Message Details',
      source: 'Source'
    },
    // 充电策略
    chargeConfig: {
      dialogInputList: {
        lowBattery: 'Low Battery Setting',
        highBattery: 'High Battery Setting',
        minBatteryValue: 'Minimum Charge Battery Level',
        minChargeTime: 'Minimum Charge Time',
        bindChargeMarkers: 'Bind Charging Markers',
        chargeTaskTypeId: 'Charge Task Type'
      },
      lowBattery: 'Low Battery (%)',
      highBattery: 'High Battery (%)',
      minBatteryValue: 'Minimum Charge Battery Level (%)',
      minChargeTime: 'Minimum Charge Time (min)',
      createTask: 'Create Task',
      title: 'Charging Strategy',
      describe: 'Strategy to create charging tasks when robot is low on battery and idle'
    },
    // 泊车策略
    parkConfig: {
      dialogInputList: {
        bindParkMarkers: 'Bind Parking Points',
        parkTaskTypeId: 'Set Task'
      },
      createTask: 'Create Task',
      title: 'Parking Strategy',
      describe: 'Strategy to create parking tasks when robot is idle'
    },
    // 交通配置
    trafficConfig: {
      title: 'Traffic Configuration',
      describe: 'Robot detour, path request, and conflict handling',
      faultOptions: {
        1: 'Wait',
        2: 'Detour'
      },
      banOptions: {
        1: 'Wait',
        2: 'Detour',
        3: 'Eviction'
      },
      collisionOptions: {
        1: 'Navigation Marker',
        2: 'Charging Marker',
        3: 'Work Marker'
      }
    },
    // 异常统计
    errorStatistical: {
      vehicleAbnormalPieChart: 'Robot Abnormality Ratio',
      abnormalDetailPieChart: 'Abnormality Classification Ratio',
      avgHandleDurationPieChart: 'Average Abnormal Handling Time',
      newCountLineChart: 'New Abnormality Count',
      avgHandleDurationLineChart: 'Average Abnormal Handling Time'
    },
    // 任务统计
    taskStatistical: {
      taskStatusPieChart: 'Task Completion Ratio',
      createTaskCountPieChart: 'New Task Count',
      avgAllocationDurationPieChart: 'Average Allocation Duration',
      avgExecuteDurationPieChart: 'Average Execution Duration',
      createTaskCountLineChart: 'New Task Count',
      endTaskCountLineChart: 'End Task Count',
      avgAllocationDurationLineChart: 'Average Allocation Duration',
      avgExecuteDurationDurationLineChart: 'Average Execution Duration'
    },
    // 数据看板
    screen: {
      agvNumStatistical: 'AGV Quantity Statistics',
      taskNumStatistical: 'Task Quantity Statistics',
      agvTotal: 'Total AGV Quantity',
      totalSize: 'Total Task Quantity',
      runningSize: 'Running Task Quantity',
      successSize: 'Successful Task Quantity',
      cancelSize: 'Cancelled Task Quantity',
      waitSize: 'Pending Task Quantity',
      completionRate: 'Task Completion Rate',
      titie: 'Scheduling system visualization platform',
      visualLargeScreen: 'Visual large screen'
    },
    // 服务器监控
    serverMonitoring: {
      serverParameter: 'Server Specifications',
      serverUsage: 'Server Resource Usage',
      cpuLineChart: 'CPU Usage Rate',
      memLineChart: 'Memory Usage Rate',
      diskLineChart: 'Disk Capacity Changes',
      mysqlLineChart: 'MySQL Operation Count',
      cpuCores: 'CPU Cores',
      cpuCoresUnit: 'Cores',
      totalThreads: 'Total Threads',
      thread: 'Thread',
      memory: 'Memory',
      diskCapacity: 'Disk Capacity',
      diskUsage: 'Disk Usage'
    },
    // 机器人统计
    statisticsRobots: {
      statusLineChart: 'Robot Status Trend',
      statusPieChart: 'Robot Status Distribution',
      utilizeRateLineChart: 'Robot Utilization Rate',
      text: 'Robot Statistics'
    },
    // 任务流程统计
    taskTypeStatistic: {
      taskStatusPieChart: 'Task Completion Distribution',
      taskCountLineChart: 'Task Quantity',
      avgAllocationDurationLineChart: 'Average Allocation Duration',
      avgExecuteDurationLineChart: 'Average Execution Duration',
      text: 'Task Workflow Statistics'
    },
    // 机器人单个统计
    robotManagementStatistic: {
      statusPieChart: 'Robot Status Distribution',
      workStatusPieChart: 'Robot Work Distribution',
      statusLineChart: 'Robot Status Trend',
      utilizeRateLineChart: 'Robot Utilization Rate'
    },
    // 监控台机器人统计
    robotMonitorStatistic: {
      taskStatusPieChart: 'Task Status',
      vehicleStatusPieChart: 'Robot Status',
      vehicleBatteryPieChart: 'Robot Battery'
    },
    //许可证
    licence: {
      licenseRemaining: 'License Remaining',
      licenseNotInForce: 'License Not in Force',
      licenseHasExpired: 'License Has Expired',
      lackOfLicense: 'Lack of License',
      renewalOfLicense: 'Renewal of License',
      deleteLicense: 'Delete License',
      renewalAuthorization: 'Renewal Authorization'
    },
    // 登录
    logins: {
      userLogin: 'User Login',
      rememberThePassword: 'Remember the Password',
      copyrightShenzhenYouaiZhiheCoLtd: 'Copyright © Shenzhen Youibot Robotics Co.,Ltd.'
    },
    //日志
    log: {
      causeOfFailure: 'Cause of Failure',
      responseTime: 'Response Time',
      url: 'URL',
      requestInformation: 'Request Information',
      returnInformation: 'Return Information',
      successList: {
        true: 'Success',
        false: 'Failure'
      },
      user: 'User',
      ipAddressOfTheClient: 'Client IP Address',
      operatingTime: 'Operating Time',
      typeList: {
        Error: 'Error',
        Running: 'Running',
        Warning: 'Warning'
      },
      category: 'Category',
      data: 'Data',
      lastTime: 'Last Updated Time',
      downloadDetailsLog: 'Download Detailed Log',
      filename: 'Filename'
    },
    // 系统配置
    systemConfiguration: {
      licenseAllocation: 'License Allocation',
      licenseAllocationDescribe: 'License Information',
      storageConfiguration: 'Storage Configuration',
      storageConfigurationDescribe: 'Log Data Cleanup, File Data Cleanup, Business Data Cleanup',
      // 新的
      pushInterfaceConfiguration: 'Push interface configuration',
      robotState: 'Robot state'
    },
    // 库区
    storageLocation: {
      reservoirArea: 'Reservoir Area',
      row: 'Row',
      column: 'Column',
      layer: 'Layer',
      operatingHeight: 'Operating Height',
      jobPoint: 'Job Point',
      occupiedState: 'State',
      containerBarCode: 'Container Barcode',
      storyHeight: 'Story Height',
      occupyStatus: {
        Lock: 'Lock',
        Free: 'Free',
        Store: 'Store'
      },
      setPoint: 'Set Point',
      importDatabaseLocationArea: 'Import Database Location Area',
      importLibraryType: 'Import Library Type',
      importLocation: 'Import Location',

      usageStatus: {
        Disable: 'Disable',
        Enable: 'Enable'
      },
      enabledState: 'Enabled State'
    },
    today: 'today.',
    yesterday: 'yesterday.',
    thisWeek: 'This week',
    lastWeek: 'Last week',
    thisMonth: 'Current month',
    lastMonth: 'Last month',
    last7Days: 'Last 7 days',
    last30Days: 'Last 30 days',
    //新加
    fullTimeOut: 'Full time out',
    fullRecoveryUnderway: 'Full recovery underway',
    completeTimeout: 'Complete timeout',
    fullRecoverySuccessful: 'Full recovery successful',
    OperationIsTooFrequent: 'The operation is too frequent',
    save: 'save',
    extendedAttribute1: 'Extended attribute 1',
    extendedAttribute2: 'Extended attribute 2',
    extendedAttribute3: 'Extended attribute 3',
    extendedAttribute4: 'Extended attribute 4',
    extendedAttribute5: 'Extended attribute 5',
    containerEncoding: 'Container encoding',
    currentReservoirArea: 'Current reservoir area',
    locationType: 'Location type',
    warehouse: 'warehouse',
    languageConfiguration: 'Language configuration',
    languageConfigurationDescribe: 'Add, download the language pack',
    numberTriggers: 'Number of triggers',
    allowRepetition: 'Allow repetition',
    exceptionMessage: 'abnormal information',
    incomingParameter: 'input parameter',
    isAllowRepeatState: {
      0: 'not allow',
      1: 'allow'
    },
    importEvent: 'Import event',
    eventCoding: 'Event coding',
    beChecking: 'be Checking...',
    endCancelRange: 'End cancel range',
    menuList: {
      menu: {
        monitoringCenter: 'Monitoring center',
        PDA: 'PDA',
        agv: 'Robot',
        task: 'task',
        equipment: 'equipment',
        operations: 'Operation',
        taskManager: 'Task management',
        taskList: 'Task list',
        taskType: 'Task flow',
        eventList: 'Event list',
        robots: 'Robot management',
        robotList: 'List of robots',
        robotType: 'Robot type',
        robotGroup: 'Robot group',
        mapList: 'Map management',
        storageLocation: 'Storage location management',
        storageLocationList: 'Library location list',
        storageLocationType: 'Storage location type',
        storageLocationArea: 'List of reservoir areas',
        notificationManager: 'Notification management',
        systemLog: 'System log',
        operationLog: 'Operation log',
        interfaceLog: 'Interface log',
        runningLog: 'Run log',
        setting: 'System setting',
        schedulingConfiguration: 'Scheduling configuration',
        systemSettings: 'System configuration',
        userSettings: 'Account authority',
        notificationSettings: 'Notification template',
        nodeSettings: 'Node setting',
        statistical: 'Statistical statement',
        statisticsRobots: 'Robot statistics',
        taskStatistical: 'Task statistics',
        errorStatistical: 'Anomaly statistics'
      },
      button: {
        view: 'view',
        relocation: 'relocation',
        switchMap: 'Switch map',
        controlMode: 'Control mode',
        dispatchingMode: 'Scheduling mode',
        ordinaryCharging: 'Ordinary charging',
        autocharge: 'Automatic charging',
        automaticParking: 'self-parking',
        pause_resume: 'Pause/resume',
        reset: 'resetting',
        buttReset: 'Butt reset',
        restart: 'Restart',
        shutdown: 'Power Off',
        departure: 'off-site',
        newTask: 'New task',
        cancelTask: 'Cancel a task',
        taskDetails: 'Task details',
        execution: 'Execution task',
        elevato: 'elevator',
        autoDoor: 'Automatic door',
        airShower: 'Air shower door',
        bulkExport: 'Bulk export',
        batchImport: 'Batch import',
        cancle: 'Cancel',
        batchCancellation: 'Batch cancellation',
        uploadRecord: 'Upload record',
        details: 'details',
        downloadRecord: 'Download record',
        remark: 'remark',
        add: 'new',
        del: 'delete',
        edit: 'edit',
        implement: 'implement',
        layout: 'layout',
        clone: 'clone',
        statistic: 'statistics',
        enabledState: 'Enable/disable',
        assignMap: 'Assigned map',
        SoftEmergencyStop: 'Enable/disable pause',
        schedule: 'Automatic/manual scheduling',
        controlModeState: 'Manual/automatic control',
        historicalTask: 'Historical tasks',
        setType: 'Set the type',
        setGroup: 'Set up groups',
        export: 'Export',
        import: 'Import',
        ignore: 'ignore',
        activation: 'activation',
        downloadDetailedLog: 'Download the detailed logs',
        download: 'Download',
        viewRoles: 'View the roles',
        addRoles: 'New characters',
        rename: 'rename',
        delRoles: 'Delete the role',
        viewAccount: 'View account',
        addAccount: 'Add an account',
        editAccount: 'Edit account',
        delAccount: 'Delete account',
        resetPasswords: 'Reset password',
        containerExit: 'Container exit',
        containerEntry: 'Container entry',
        taskView: 'Task view',
        taskCancel: 'Task cancellation'
      }
    }
  }
}
const youibot = {
  yi: {
    switchLanguage: 'Switch Language',
    map: 'Map',
    size: 'Size',
    deleteModel: 'Delete Element',
    test: 'Test',
    options: 'Options',
    addMarker: 'Add Marker',
    addArea: 'Add Area',
    canvasReset: 'Reset Canvas',
    canvasRotate: 'Rotate Canvas',
    canvasFullScreen: 'Full Screen Canvas',
    displaySetting: 'Display Settings',
    addOneWayPath: 'Add One-way Path',
    addTwoWayPath: 'Add Two-way Path',
    straightenCarve: 'Straighten Path',
    canvasSmallScreen: 'Restore Canvas',
    addPath: 'Add Path',
    systemSetup: 'System Setup',
    endRecordingPoint: 'End Recording Marker',
    recordingPoint: 'Record Marker',
    mapEditor: 'Map Editor',
    batchNew: 'Batch New',
    rangingPath: 'Measure Distance',
    selectedPath: 'Selected Path',
    up: 'Up',
    down: 'Down',
    aleft: 'Left',
    right: 'Right',
    hideSelectedPath: 'Hidden path',
    smoothPath: 'Smooth path'
  }
}
export default {
  // ...ant,
  ...youibot,
  ...enLang
}
