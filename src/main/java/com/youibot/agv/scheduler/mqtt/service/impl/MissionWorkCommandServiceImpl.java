package com.youibot.agv.scheduler.mqtt.service.impl;

import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.entity.AGVLog;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.mqtt.service.MissionWorkCommandService;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import static com.youibot.agv.scheduler.constant.MissionConstant.*;
import static com.youibot.agv.scheduler.constant.VehicleConstant.AGV_LOG_TYPE_WORKING;

@Service
public class MissionWorkCommandServiceImpl implements MissionWorkCommandService {

    Logger logger = LoggerFactory.getLogger(MissionWorkCommandServiceImpl.class);

    @Autowired
    private MissionService missionService;

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private MissionWorkActionService missionWorkActionService;

    @Autowired
    private OperateResultErrorInfoService operateResultErrorInfoService;

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private AGVLogService agvLogService;

    @Autowired
    private WorkSchedulerService workSchedulerService;

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    @Autowired
    private MissionActionService missionActionService;

    @Autowired
    private MissionActionParameterService missionActionParameterService;

    @Autowired
    private MissionGlobalVariableService missionGlobalVariableService;

    private final Integer MISSION_ALLOCATION_ERROR = 40007;

    @Override
    public void updateMissionWork(MissionWork missionWork) {
        String status = missionWork.getStatus();
        String vehicleId = missionWork.getAgvCode();
        Vehicle vehicle = defaultVehiclePool.getVehicle(vehicleId);
        String missionWorkId = missionWork.getId();

        MissionWork missionWorkDB = missionWorkService.selectById(missionWorkId);

        // 如果任务返回状态为拒绝REJECT,取消当前作业调度计划
        if (MISSION_WORK_STATUS_REJECT.equals(status)) {
            logger.debug("机器人:{}拒绝分配作业:{},取消作业调度计划",vehicleId,missionWorkId);
            workSchedulerService.updateCancel(missionWorkId);
            return;
        }

        // 任务完成，分配更新成完成状态。
        if (MISSION_WORK_STATUS_SUCCESS.equals(status)) {
            // update vehicle allocation 's mission work to success.
            workSchedulerService.updateSuccess(missionWorkId);
            // 根据任务状态清理当前任务相关的路径规划请求.
            if (vehicle != null && !CollectionUtils.isEmpty(vehicle.getPathPlanMessages())) {
                vehicle.getPathPlanMessages().removeIf(ppm -> missionWorkId.equals(ppm.getMissionWorkId()));
            }
            checkAndSendPathService.clear(vehicleId, missionWorkId);
            /**
             * 任务正常结束的时候不要清理vehicle.getPathPlanMessages()。有可能把新增加的path请求给清理掉。
             * 或者加上任务ID进行清理。
             */
            agvLogService.updateNullEndTime(vehicleId, missionWorkId, AGV_LOG_TYPE_WORKING, System.currentTimeMillis() / 1000);
        }

        // TODO force stop work like this.
        if (MISSION_WORK_STATUS_SHUTDOWN.equals(status)) {
            //如果这个任务未下发给compass, 停止后compass回调的任务数据缺少数据
            if (StringUtils.isEmpty(missionWork.getMissionId())) {
                missionWork = missionWorkService.selectById(missionWorkId);
                missionWork.setStatus(MISSION_WORK_STATUS_SHUTDOWN);
            }
            // update vehicle allocation 's mission work to cancel.
            workSchedulerService.updateCancel(missionWorkId);
            if (vehicle != null && !CollectionUtils.isEmpty(vehicle.getPathPlanMessages())) {
                vehicle.getPathPlanMessages().clear();
            }
            checkAndSendPathService.clear(vehicleId, missionWorkId);
            agvLogService.updateNullEndTime(vehicleId, missionWorkId, AGV_LOG_TYPE_WORKING, System.currentTimeMillis() / 1000);
        }

        if (MISSION_WORK_STATUS_FAULT.equals(status) || MISSION_WORK_STATUS_DOCKING_ERROR.equals(status)) {
            workSchedulerService.updateFault(missionWorkId, missionWork.getMessage());
            if (vehicle != null && !CollectionUtils.isEmpty(vehicle.getPathPlanMessages())) {
                vehicle.getPathPlanMessages().clear();
            }
            checkAndSendPathService.clear(vehicleId, missionWorkId);
            agvLogService.updateNullEndTime(vehicleId, missionWorkId, AGV_LOG_TYPE_WORKING, System.currentTimeMillis() / 1000);
        }

        if (MISSION_WORK_STATUS_PAUSE.equals(status)) {
            if (vehicle != null && !CollectionUtils.isEmpty(vehicle.getPathPlanMessages())) {
                vehicle.getPathPlanMessages().clear();
            }
            checkAndSendPathService.clear(vehicleId, missionWorkId);
            agvLogService.updateNullEndTime(vehicleId, missionWorkId, AGV_LOG_TYPE_WORKING, System.currentTimeMillis() / 1000);
        }

        if (MISSION_WORK_STATUS_RUNNING.equals(status)) {
            AGVLog agvLog = new AGVLog(vehicleId, AGV_LOG_TYPE_WORKING, System.currentTimeMillis() / 1000);
            agvLog.setMissionWorkId(missionWork.getId());
            agvLogService.insert(agvLog);
        }
        if (MISSION_WORK_STATUS_CREATE.equals(status) && !MISSION_WORK_STATUS_CREATE.equals(missionWorkDB.getStatus())) {
            logger.debug("机器人:{}推送作业状态:{},当前作业已经运行:{}", vehicleId, status, missionWorkDB);
            return;
        }
        //更新missionWork数据到数据库,同时发布状态变更事件
        missionWorkService.updateStatus(missionWork, true, true);
    }

    @Override
    public void updateMissionWorkAction(MissionWorkAction missionWorkAction) {
        missionWorkActionService.mergeUpdateAndInsert(missionWorkAction);
    }


//    @Override
//    public void updateMissionWorkResult(MissionWorkCommandResultMessage missionWorkCommandResultMessage) {
//        boolean result = MqResultConstant.SUCCESS.equals(missionWorkCommandResultMessage.getStatus());
//        String missionWorkId = missionWorkCommandResultMessage.getMissionWorkId();
//        MissionWork missionWorkDto = missionWorkService.selectById(missionWorkId);
//        String agvCode = missionWorkDto == null ? null : missionWorkDto.getAgvCode();
//        if (StringUtils.isEmpty(agvCode)) {
//            logger.debug("missionWorkId is not exits or agvCode is empty, missionWorkId:{}，agvCode:{}", missionWorkId, agvCode);
//            return;
//        }
//        String type = missionWorkCommandResultMessage.getType();
//        if (result) {//执行成功
//            String status;
//            switch (type) {
//                case "stopMission":
//                    status = MissionConstant.MISSION_WORK_STATUS_SHUTDOWN;
//                    break;
//                case "pauseMission":
//                    status = MISSION_WORK_STATUS_PAUSE;
//                    break;
//                case "resumeMission":
//                    status = MissionConstant.MISSION_WORK_STATUS_BEING_RESUME;
//                    break;
//                case "continueMission":
//                    status = MissionConstant.MISSION_WORK_STATUS_RUNNING;
//                    break;
//                default:
//                    status = null;
//                    break;
//            }
////            missionWorkDto.setStatus(status);
//        } else {//执行失败
//            String message = missionWorkCommandResultMessage.getMessage();
//            operateResultErrorInfoService.insetOperateResult(agvCode, type, message, 0);
//        }
//        this.updateMissionWork(missionWorkDto);
//    }
}
