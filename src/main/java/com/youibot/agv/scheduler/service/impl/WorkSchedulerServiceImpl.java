package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.WorkScheduler;
import com.youibot.agv.scheduler.mapper.WorkSchedulerMapper;
import com.youibot.agv.scheduler.service.WorkSchedulerService;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-13 14:44
 */
@Service
public class WorkSchedulerServiceImpl extends BaseServiceImpl<WorkScheduler> implements WorkSchedulerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WorkSchedulerServiceImpl.class);

    @Autowired
    private WorkSchedulerMapper workSchedulerMapper;

    @Override
    public List<WorkScheduler> selectCreate() {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andEqualTo("status", "CREATE");
        return this.selectByExample(example);
    }

    @Override
    public List<WorkScheduler> selectRunning() {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT"));
        return this.selectByExample(example);
    }

    @Override
    public List<WorkScheduler> selectPrepareAndRunning() {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("PREPARE","CREATE", "START", "FAULT"));
        return this.selectByExample(example);
    }

    @Override
    public WorkScheduler selectRunningByWork(String workId) {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT")).andEqualTo("workId", workId);
        return this.selectOneByExample(example);
    }

    @Override
    public WorkScheduler selectPrepareAndRunningByWork(String workId) {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("PREPARE","CREATE", "START", "FAULT")).andEqualTo("workId", workId);
        return this.selectOneByExample(example);
    }

    @Override
    public void updateCancel(String workId) {
        WorkScheduler workScheduler = this.selectPrepareAndRunningByWork(workId);
        if (workScheduler == null) {
            LOGGER.warn("cancel work scheduler is fault, work scheduler is null.");
            return;
        }
        workScheduler.setStatus(workScheduler.STATUS_CANCEL);
        workScheduler.setFinishTime(new Date());
        this.update(workScheduler);
    }

    @Override
    public void updateSuccess(String workId) {
        WorkScheduler workScheduler = this.selectRunningByWork(workId);
        if (workScheduler == null) {
            LOGGER.warn("success work scheduler is fault, work scheduler is null.");
            return;
        }
        workScheduler.setStatus(workScheduler.STATUS_SUCCESS);
        workScheduler.setFinishTime(new Date());
        this.update(workScheduler);
    }

    @Override
    public void updateStart(String workId) {
        WorkScheduler workScheduler = this.selectRunningByWork(workId);
        if (workScheduler == null) {
            LOGGER.warn("start work scheduler is fault, work scheduler is null.");
            return;
        }
        workScheduler.setStatus(workScheduler.STATUS_START);
        workScheduler.setStartTime(new Date());
        this.update(workScheduler);
    }

    @Override
    public void updateFault(String workId, String faultMessage) {
        WorkScheduler workScheduler = this.selectRunningByWork(workId);
        if (workScheduler == null) {
            LOGGER.warn("fault work scheduler is fault, work scheduler is null.");
            return;
        }
        workScheduler.setStatus(workScheduler.STATUS_FAULT);
        workScheduler.setFaultMessage(faultMessage);
        this.update(workScheduler);
    }

    @Override
    public WorkScheduler selectLastOne(String vehicleId){
//        Example example = new Example(WorkScheduler.class);
//        example.createCriteria().andEqualTo("vehicleId",vehicleId);
//        example.setOrderByClause("create_time desc");
//        return this.selectOneByExample(example);
        WorkScheduler workScheduler = workSchedulerMapper.selectLastOne(vehicleId);
        return workScheduler;
    }

    @Override
    public WorkScheduler selectRunningByVehicleId(String vehicleId){
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("CREATE", "START", "FAULT")).andEqualTo("vehicleId",vehicleId);
        return this.selectOneByExample(example);
    }

    @Override
    public WorkScheduler selectPreAllocationWorkByVehicleId(String vehicleId) {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andEqualTo("status", "PREPARE").andEqualTo("vehicleId",vehicleId);
        return this.selectOneByExample(example);
    }

    @Override
    public void deleteExpireDataByTime(Integer missionWork) {
        List<String> ids = workSchedulerMapper.selectExpireDataByTime(missionWork);
        LOGGER.debug("删除work_scheduler数据总大小：{}", CollectionUtils.isEmpty(ids) ? 0 : ids.size());
        SystemConfigJobUtil.batchSplitDelete(ids, this);
    }

    @Override
    public List<WorkScheduler> selectPrepare() {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("PREPARE"));
        return this.selectByExample(example);
    }

    @Override
    public WorkScheduler selectPrepareAndRunningByVehicleId(String vehicleId){
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andIn("status", Arrays.asList("PREPARE", "CREATE", "START", "FAULT")).andEqualTo("vehicleId",vehicleId);
        return this.selectOneByExample(example);
    }

    @Override
    public WorkScheduler selectLastCancelSchedulerByWorkId(String workId) {
        Example example = new Example(WorkScheduler.class);
        example.createCriteria().andEqualTo("workId",workId).andEqualTo("status","CANCEL");
        example.setOrderByClause("create_time desc");
        return this.selectOneByExample(example);
    }
}
