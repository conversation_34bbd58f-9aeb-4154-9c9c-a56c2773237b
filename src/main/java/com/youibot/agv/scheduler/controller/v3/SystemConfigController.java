package com.youibot.agv.scheduler.controller.v3;

import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.config.FtpConfig;
import com.youibot.agv.scheduler.entity.SystemConfig;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.SystemConfigService;
import com.youibot.agv.scheduler.util.FtpUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.util.TimeZoneUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统配置
 * @Author：yangpeilin
 * @Date: 2020/5/9 16:42
 */
@RestController("SystemConfigController")
@RequestMapping("/api/v3/config")
@Api(value = "系统配置", tags = "系统配置", produces = "application/json")
public class SystemConfigController {

    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private FtpConfig ftpConfig;


    @ApiOperation(value = "查看系统配置")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public SystemConfig getSystemConfig() {
        List<SystemConfig> list = systemConfigService.findAll();
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @LogOperation("修改系统配置")
    @ApiOperation(value = "修改系统配置")
    @ApiImplicitParam(name = "systemConfig", value = "系统配置信息", required = true, dataType = "SystemConfig")
    @PutMapping
    @ResponseStatus(value = HttpStatus.OK)
    public void updateSystemConfig(@RequestBody SystemConfig systemConfig) {
        //查看是否有修改时区
        if (!StringUtils.isEmpty(systemConfig.getTimeZone())){
            TimeZoneUtil.setTimeZone(systemConfig.getTimeZone());
        }
        systemConfigService.updateByPrimaryKeySelective(systemConfig);
    }

    @ApiOperation(value = "查看系统ftp配置")
    @GetMapping("/ftp")
    @ResponseStatus(value = HttpStatus.OK)
    public FtpConfig getFtpConfig() {
        return ftpConfig;
    }


    @LogOperation("修改FTP系统配置")
    @ApiOperation(value = "修改ftp系统配置")
    @ApiImplicitParam(name = "ftpConfig", value = "修改ftp系统配置", required = true, dataType = "FtpConfig")
    @PutMapping("/ftp")
    @ResponseStatus(value = HttpStatus.OK)
    public void updateFtpConfig(@RequestBody FtpConfig ftpConfig) {
        //此处可以置空
        if(ftpConfig==null){
            throw new ExecuteException(MessageUtils.getMessage("http.params_data_is_empty"));
        }
        List<SystemConfig> list = systemConfigService.findAll();
        if(CollectionUtils.isEmpty(list)){
            throw new ExecuteException(MessageUtils.getMessage("service.system_config_error"));
        }

        String url = ftpConfig.getUrl() == null ? null : ftpConfig.getUrl().trim();
        SystemConfig systemConfig = list.get(0);
        systemConfig.setFtpUrl(url);
        systemConfigService.update(systemConfig);

        FtpUtils.host = url;
        this.ftpConfig.setUrl(url);
    }

}
