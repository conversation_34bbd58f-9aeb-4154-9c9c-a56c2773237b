package com.youibot.agv.scheduler.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.constant.MissionConstant;
import com.youibot.agv.scheduler.constant.vo.DatePair;
import com.youibot.agv.scheduler.constant.vo.MissionActionStatisticVO;
import com.youibot.agv.scheduler.entity.MissionAction;
import com.youibot.agv.scheduler.entity.MissionActionDataStatistic;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.mapper.MissionActionDataStatisticMapper;
import com.youibot.agv.scheduler.mapper.MissionWorkActionMapper;
import com.youibot.agv.scheduler.service.MissionWorkActionService;
import com.youibot.agv.scheduler.util.Base64Utils;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.DateUtils;
import com.youibot.agv.scheduler.util.SystemConfigJobUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MissionConstant.*;

@Slf4j
@Service
public class MissionWorkActionServiceImpl extends BaseServiceImpl<MissionWorkAction> implements MissionWorkActionService {

    @Autowired
    private MissionWorkActionMapper missionWorkActionMapper;

    @Autowired
    private MissionActionDataStatisticMapper missionActionDataStatisticMapper;

    @Override
    public PageInfo<MissionWorkAction> findPage(Map<String, String> searchMap) {
        Example example = getExample(searchMap);
        return getPageInfo(searchMap, example);
    }

    private Example getExample(Map<String, String> searchMap) {
        Example example = new Example(MissionWork.class);
        Example.Criteria criteria = example.createCriteria();
        if (searchMap != null && !searchMap.isEmpty()) {
            if (searchMap.containsKey("startTime") && searchMap.containsKey("endTime")) {
                if (searchMap.get("startTime") != null && searchMap.get("endTime") != null) {
                    criteria.andBetween("createTime", searchMap.get("startTime"), searchMap.get("endTime"));
                }
            }
            for (String key : searchMap.keySet()) {
                if ("pageNum".equals(key) || "pageSize".equals(key) || "startTime".equals(key) || "endTime".equals(key)) {
                    continue;
                }
                if ("sort".equals(key)) {
                    example.setOrderByClause(searchMap.get(key));
                    continue;
                }
                Object value = handlerMapKey(searchMap.get(key));
                if (key.equals("name") || key.equals("message")) {
                    criteria.andLike(key, "%" + ((String) value) + "%");
                    continue;
                }
                criteria.andEqualTo(key, value);

            }
        }
        return example;
    }

    @Override
    public MissionWorkAction selectById(String id) {
        MissionWorkAction missionWorkAction = super.selectById(id);
        if (missionWorkAction == null) {
            return null;
        }
        if (RESULT_TYPE_IMAGE.equals(missionWorkAction.getResultType())) {
            String resultData = missionWorkAction.getResultData();
            if (!StringUtils.isEmpty(resultData)) {
                JSONObject dataJson = JSONObject.parseObject(resultData);
                if (dataJson != null) {
                    JSONArray urlArray = dataJson.getJSONArray("base64");
                    if (urlArray != null && !urlArray.isEmpty()) {
                        JSONArray base64Array = new JSONArray();
                        for (Object o : urlArray) {
                            String base64 = "data:image/jpg;base64," + Base64Utils.imageToBase64(o.toString());
                            base64Array.add(base64);
                        }
                        dataJson.put("base64", base64Array);
                        missionWorkAction.setResultData(dataJson.toJSONString());
                    }
                }
            }
        }
        return missionWorkAction;
    }

    @Override
    public int updateStatus(MissionWorkAction missionWorkAction) {
        if (MISSION_WORK_ACTION_STATUS_FAULT.equals(missionWorkAction.getStatus()) || MISSION_WORK_ACTION_STATUS_SUCCESS.equals(missionWorkAction.getStatus())) {
            missionWorkAction.setEndTime(new Date());
        }
        return this.update(missionWorkAction);
    }

    @Override
    public void updateStatus(MissionWorkAction missionWorkAction, String status) {
        if (missionWorkAction != null) {
            missionWorkAction.setStatus(status);
            this.updateStatus(missionWorkAction);
        }
    }

    @Override
    public MissionWorkAction createMissionWorkAction(MissionAction missionAction, MissionWork missionWork) {
        Integer actionSequence = missionWork.getCurrentActionSequence() != null ? missionWork.getCurrentActionSequence() + 1 : 1;
        missionWork.setCurrentActionSequence(actionSequence);
        missionWork.setCurrentActionName(missionAction.getName());
        MissionWorkAction missionWorkAction = new MissionWorkAction();
        missionWorkAction.setAgvCode(missionWork.getAgvCode());
        missionWorkAction.setMissionActionId(missionAction.getId());
        missionWorkAction.setMissionWorkId(missionWork.getId());
        missionWorkAction.setActionType(missionAction.getActionType());
        missionWorkAction.setName(missionAction.getName());
        missionWorkAction.setChildType(missionAction.getChildType());
        missionWorkAction.setParentActionId(missionAction.getParentActionId());
        missionWorkAction.setStatus(MISSION_WORK_ACTION_STATUS_START);
        missionWorkAction.setStartTime(new Date());
//        this.insert(missionWorkAction);
        return missionWorkAction;
    }

    @Override
    public List<MissionWorkAction> selectByMissionWorkIdAndType(String missionWorkId, String actionType) {
        Example example = new Example(MissionWorkAction.class);
        example.createCriteria().andEqualTo("actionType", actionType)
                .andEqualTo("missionWorkId", missionWorkId)
                .andNotEqualTo("thisLoopCompletes", false);
        return super.selectByExample(example);
    }

    @Override
    public List<MissionWorkAction> selectByMissionWorkId(String missionWorkId) {
        Example example = new Example(MissionWorkAction.class);
        example.createCriteria().andEqualTo("missionWorkId", missionWorkId);
        example.setOrderByClause("update_time asc");
        return super.selectByExample(example);
    }

    @Override
    public MissionWorkAction selectRunningActionByMissionWorkId(String missionWorkId) {
        Example example = new Example(MissionWorkAction.class);
        List<String> statusList = Arrays.asList(MISSION_WORK_ACTION_STATUS_START,MISSION_WORK_ACTION_STATUS_RUNNING,MISSION_WORK_ACTION_STATUS_FAULT);
        example.createCriteria().andEqualTo("missionWorkId", missionWorkId)
                .andIn("status",statusList);
        return super.selectOneByExample(example);
    }

    @Override
    public void deleteExpireDataByTime(Integer missionWork) {
        List<String> idList = missionWorkActionMapper.selectExpireDataByTime(missionWork);
        log.debug("删除mission_work_action数据总大小：{}", CollectionUtils.isEmpty(idList) ? 0 : idList.size());
        SystemConfigJobUtil.batchSplitDelete(idList, this);
    }

    @Override
    public void mergeUpdateAndInsert(MissionWorkAction missionWorkAction) {
        MissionWorkAction missionWorkActionDto = selectById(missionWorkAction.getId());
        if (missionWorkActionDto == null) {
            super.insert(missionWorkAction);
        } else {
            missionWorkAction.setUpdateTime(new Date());
            super.update(missionWorkAction);
        }
    }

    @Override
    public void updateMissionWorkActionStatus(String missionWorkId, String status) {
        missionWorkActionMapper.updateMissionWorkActionStatus(missionWorkId, status);
    }


    /**
     * 查询某个时间范围内，任务动作统计信息
     *
     * 如果小车编码agvCode存在，就查询单个小车的
     * 否则，就查询所有小车的
     */
    @Override
    public Map<String,List<MissionActionStatisticVO>> getMissionActionStatistic(String agvCode, String startTime, String endTime,String type) {
        Map<String,List<MissionActionStatisticVO>> resultList = new HashMap<>();
        switch (type){
            case MissionConstant.MISSION_statistic_type_none :
                resultList = processNone(agvCode,startTime,endTime);
                break;
            case MissionConstant.MISSION_statistic_type_hour :
                //查询当天（24个小时）的数据统计，查询实时原始表
                resultList = processHour(agvCode,startTime,endTime);
                break;
            case MissionConstant.MISSION_statistic_type_day :
                //查询当天（24个小时）的数据统计，查询实时原始表
                resultList = processDay(agvCode,startTime,endTime);
                break;
            case MissionConstant.MISSION_statistic_type_month :
                //查询单月（30天）的数据统计，查询归档表
                resultList = processMonth(agvCode,startTime,endTime);
                break;
        }

        resultList = sort(resultList);

        return resultList;
    }

    private Map<String,List<MissionActionStatisticVO>> sort(Map<String,List<MissionActionStatisticVO>> map){
        if(map==null || map.size()<=0){
            return map;
        }
        TreeMap<String,List<MissionActionStatisticVO>> treeMap = new TreeMap<>(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                long time1 = DateUtils.getFormatDate(o1).getTime();
                long time2 = DateUtils.getFormatDate(o2).getTime();
                return time1 == time2 ? 0 : ( time1 - time2 >0 ? 1 :-1 );
            }
        });
        treeMap.putAll(map);
        return treeMap;
    }


    private Map<String,List<MissionActionStatisticVO>> processNone(String agvCode, String startTime, String endTime){
        Map<String,List<MissionActionStatisticVO>> data = new HashMap<>();

        Date now = new Date();
        //如果时间范围为空，则 end = now()  start = end - 1年
        Date start = StringUtils.isEmpty(startTime) ? DateUtils.add(now,Calendar.YEAR,-1) : DateUtils.getFormatDate(startTime);
        Date end = StringUtils.isEmpty(endTime) ? now : DateUtils.getFormatDate(endTime);

        Date boundry = DateUtils.add(DateUtils.getBeginOfDay(now), Calendar.HOUR_OF_DAY, 8);
        if(now.after(boundry)){
            //8点之后
            doProcessNone(agvCode,start,end,boundry,data);
        }else{
            //8点之前
            Date yestoday_boundry = DateUtils.addDays(boundry, -1);
            doProcessNone(agvCode,start,end,yestoday_boundry,data);
        }

        return data;
    }
    private void doProcessNone(String agvCode, Date start, Date end, Date boundry, Map<String,List<MissionActionStatisticVO>> data){

        //将时间范围按当天的8点进行拆分：8点之前的时间段，走归档统计报表；8点之后的时间段，走实时的
        if(end.before(boundry)){

            //走归档
            List<MissionActionDataStatistic> queryList = missionActionDataStatisticMapper.queryMonthList(agvCode, start, end);
            if(CollectionUtils.isEmpty(queryList)){
                queryList = Collections.emptyList();
            }
            List<MissionActionStatisticVO> list = new ArrayList<>();
            fill(list,queryList);

            String startTimeStr = DateUtils.getFullDate(start);
            data.put(startTimeStr,list);

        }else if(boundry.before(start)){

            //走实时
            String startTimeStr = DateUtils.getFullDate(start);
            String endTimeStr = DateUtils.getFullDate(end);
            List<MissionActionStatisticVO> realList = doGetMissionActionStatistic(agvCode, startTimeStr, endTimeStr);
            if(CollectionUtils.isEmpty(realList)){
                realList = Collections.emptyList();
            }
            data.put(startTimeStr,realList);

        }else{

            //1、先走归档
            List<MissionActionDataStatistic> queryList = missionActionDataStatisticMapper.queryMonthList(agvCode, start, boundry);
            if(CollectionUtils.isEmpty(queryList)){
                queryList = Collections.emptyList();
            }
            List<MissionActionStatisticVO> historyList = new ArrayList<>();
            fill(historyList,queryList);

            //2、再走实时
            String startTimeStr = DateUtils.getFullDate(boundry);
            String endTimeStr = DateUtils.getFullDate(end);
            List<MissionActionStatisticVO> realList = doGetMissionActionStatistic(agvCode, startTimeStr, endTimeStr);
            if(CollectionUtils.isEmpty(realList)){
                realList = Collections.emptyList();
            }

            //3、合并
            Map<String, MissionActionStatisticVO> history = historyList.stream().collect(Collectors.toMap((k0)->{
                return k0.getAgvCode() + "-" + k0.getMissionActionId();}, (k1) -> k1));
            Map<String, MissionActionStatisticVO> real = realList.stream().collect(Collectors.toMap((k0)->{
                return k0.getAgvCode() + "-" + k0.getMissionActionId();}, (k1) -> k1));

            for(Map.Entry<String, MissionActionStatisticVO> item : real.entrySet()){
                if(!history.containsKey(item.getKey())){
                    history.put(item.getKey(),item.getValue());
                }else{
                    MissionActionStatisticVO hisItem = history.get(item.getKey());
                    MissionActionStatisticVO realItem = item.getValue();
                    if(hisItem==null){
                        history.put(item.getKey(),item.getValue());
                    }else if(hisItem!=null && realItem!=null){
                        hisItem.setTotalNum(hisItem.getTotalNum()+realItem.getTotalNum());
                        hisItem.setTotalTime(hisItem.getTotalTime()+realItem.getTotalTime());
                    }
                }
            }
            ArrayList<MissionActionStatisticVO> result = new ArrayList<>(history.values());
            data.put(startTimeStr,result);
        }
    }

    private Map<String,List<MissionActionStatisticVO>> processHour(String agvCode, String startTime, String endTime){

        Map<String,List<MissionActionStatisticVO>> data = new HashMap<>();
        Map<String, DatePair> dateMap = DateUtils.getHoursV2(startTime, endTime);
        for(String key : dateMap.keySet()){
            DatePair pair = dateMap.get(key);
            Date start = pair.getStart();
            Date end = pair.getEnd();

            String startTimeStr = DateUtils.getFullDate(start);
            String endTimeStr = DateUtils.getFullDate(end);
            List<MissionActionStatisticVO> missionActionStatisticVOS = doGetMissionActionStatistic(agvCode, startTimeStr, endTimeStr);
            if(CollectionUtils.isEmpty(missionActionStatisticVOS)){
                missionActionStatisticVOS = Collections.emptyList();
            }
            data.put(startTimeStr,missionActionStatisticVOS);
        }
        return data;
    }

    private Map<String,List<MissionActionStatisticVO>> processDay(String agvCode, String startTime, String endTime){
        Map<String,List<MissionActionStatisticVO>> data = new HashMap<>();

        Map<String, DatePair> dateMap = DateUtils.getDaysV2(startTime, endTime);
        for(String key : dateMap.keySet()){
            DatePair pair = dateMap.get(key);
            Date start = pair.getStart();
            Date end = pair.getEnd();

            List<MissionActionDataStatistic> queryList = missionActionDataStatisticMapper.queryDaysList(agvCode, start, end);
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(queryList)){
                queryList = Collections.emptyList();
            }

            List<MissionActionStatisticVO> list = new ArrayList<>();
            fill(list,queryList);
            String startTimeStr = DateUtils.getFullDate(start);
            data.put(startTimeStr,list);
        }

        //查询当天的数据，替换本周当天的数据
        Date start = DateUtils.getFormatDate(startTime);
        Date end = DateUtils.getFormatDate(endTime);
        Date now = new Date();
        //今天的早上8点
        Date boundry = DateUtils.add(DateUtils.getBeginOfDay(now), Calendar.HOUR_OF_DAY, 8);
        /**
         * 需要区分时间是8点之前，还是之后
         * 8点之前，没有统计昨天数据
         * 8点之后统计了昨天数据
         */
        if(now.after(boundry)){
            //8点之后
            if(boundry.compareTo(end)<0){
                Date tmp_start = boundry.before(start) ? start : boundry;
                Date tmp_end = now.after(end) ? end : now;
                handleTodayCase(agvCode,tmp_start,tmp_end,boundry,data);
            }
        }else{
            //8点之前
            Date yestoday_boundry = DateUtils.addDays(boundry, -1);
            if(yestoday_boundry.compareTo(end)<0){
                Date tmp_start = yestoday_boundry.before(start) ? start : yestoday_boundry;
                Date tmp_end = now.after(end) ? end : now;
                handleTodayCase(agvCode,tmp_start,tmp_end,yestoday_boundry,data);
            }
        }
        return data;
    }

    private Map<String,List<MissionActionStatisticVO>> processMonth(String agvCode, String startTime, String endTime){
        Map<String,List<MissionActionStatisticVO>> data = new HashMap<>();
        Map<String, DatePair> dateMap = DateUtils.getMonths(startTime, endTime);
        for(String key : dateMap.keySet()){
            DatePair pair = dateMap.get(key);
            Date start = pair.getStart();
            Date end = pair.getEnd();
            List<MissionActionDataStatistic> queryList = missionActionDataStatisticMapper.queryMonthList(agvCode, start, end);
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(queryList)){
                queryList = Collections.emptyList();
            }

            List<MissionActionStatisticVO> list = new ArrayList<>();
            fill(list,queryList);

            String startTimeStr = DateUtils.getFullDate(DateUtils.getFirstDayOfMonth(start));
            data.put(startTimeStr,list);
        }

        Date start = DateUtils.getFormatDate(startTime);
        Date end = DateUtils.getFormatDate(endTime);
        Date now = new Date();
        //今天的早上8点
        Date boundry = DateUtils.add(DateUtils.getBeginOfDay(now), Calendar.HOUR_OF_DAY, 8);
        /**
         * 需要区分时间是8点之前，还是之后
         * 8点之前，没有统计昨天数据
         * 8点之后统计了昨天数据
         */
        if(now.after(boundry)){
            //8点之后
            if(boundry.compareTo(end)<0){
                Date tmp_start = boundry.before(start) ? start : boundry;
                Date tmp_end = now.after(end) ? end : now;
                handleCurrentMonthCase(agvCode,tmp_start,tmp_end,boundry,data);
            }
        }else{
            //8点之前
            Date yestoday_boundry = DateUtils.addDays(boundry, -1);
            if(yestoday_boundry.compareTo(end)<0){
                Date tmp_start = yestoday_boundry.before(start) ? start : yestoday_boundry;
                Date tmp_end = now.after(end) ? end : now;
                handleCurrentMonthCase(agvCode,tmp_start,tmp_end,yestoday_boundry,data);
            }
        }
        return data;
    }

    private void fill(List<MissionActionStatisticVO> list, List<MissionActionDataStatistic> queryList){
        if(CollectionUtils.isNotEmpty(queryList)){
            for(MissionActionDataStatistic item : queryList){
                MissionActionStatisticVO tmp = new MissionActionStatisticVO();
                BeanUtils.copyPropertiesIgnoreNull(item,tmp);
                list.add(tmp);
            }
        }
    }

    /**
     * 处理当月的数据
     */
    private void handleCurrentMonthCase(String agvCode,Date start,Date end,Date boundry,Map<String,List<MissionActionStatisticVO>> data){

        String startTime = DateUtils.getFullDate(start);
        String endTime = DateUtils.getFullDate(end);

        List<MissionActionStatisticVO> todayMonthList = doGetMissionActionStatistic(agvCode, startTime,endTime);
        if(CollectionUtils.isEmpty(todayMonthList)){
            return;
        }

        String str = DateUtils.getFullDate(DateUtils.add(DateUtils.getFirstDayOfMonth(boundry),Calendar.HOUR_OF_DAY,8));
        List<MissionActionStatisticVO> missionStatisticVOS = data.get(str);
        if(CollectionUtils.isEmpty(missionStatisticVOS)){
            data.put(str,todayMonthList);
            return;
        }

        Map<String, List<MissionActionStatisticVO>> collect = todayMonthList.stream().collect(Collectors.groupingBy(MissionActionStatisticVO::getAgvCode));
        for(MissionActionStatisticVO item : missionStatisticVOS){
            String itemAgvCode = item.getAgvCode();
            List<MissionActionStatisticVO> tmpList = collect.get(itemAgvCode);
            if(tmpList==null || tmpList.size()<=0){
                continue;
            }
            for(MissionActionStatisticVO tmp : tmpList){
                if(tmp.getMissionActionId().equals(item.getMissionActionId())){
                    item.setTotalTime(item.getTotalTime() + tmp.getTotalTime());
                    item.setTotalNum(item.getTotalNum() + tmp.getTotalNum());
                }
            }
        }

        data.put(str,missionStatisticVOS);
    }

    /**
     * 处理当天的数据
     */
    private void handleTodayCase(String agvCode,Date start,Date end,Date boundry,Map<String,List<MissionActionStatisticVO>> data){

        String startTime = DateUtils.getFullDate(start);
        String endTime = DateUtils.getFullDate(end);

        List<MissionActionStatisticVO> todayList = doGetMissionActionStatistic(agvCode, startTime,endTime);
        if(CollectionUtils.isNotEmpty(todayList)){
            String str = DateUtils.getFullDate(boundry);
            data.put(str,todayList);
        }
    }

    public List<MissionActionStatisticVO> doGetMissionActionStatistic(String agvCode, String startTime, String endTime) {
        Date now = new Date();
        Date startDate = DateUtils.getFormatDate(startTime);
        //如果开始时间晚于当前时间，没有数据，不用查询
        if(startDate.after(now)){
            return Collections.emptyList();

        }
        Date endDate = DateUtils.getFormatDate(endTime);
        if(now.before(endDate)){
            endDate = now;
        }

        List<MissionWorkAction> actionList = missionWorkActionMapper.getMissionWorkActionStatistic(agvCode, startTime, endTime);
        if(CollectionUtils.isEmpty(actionList)){
            return Collections.emptyList();
        }
        //按小车分组
        Map<String, List<MissionWorkAction>> agvMap = actionList.stream().collect(Collectors.groupingBy(MissionWorkAction::getAgvCode));
        if(agvMap==null || agvMap.size()<=0){
            return Collections.emptyList();
        }

        List<MissionActionStatisticVO> resultList = new ArrayList<>();
        for(Map.Entry<String, List<MissionWorkAction>> item : agvMap.entrySet()){
            String agv = item.getKey();
            List<MissionWorkAction> tmpList = item.getValue();
            if(CollectionUtils.isEmpty(tmpList)){
                continue;
            }
            //处理单个小车的任务动作统计
            resultList.addAll(computeSingleVehicleActionStatistic(startDate, endDate, agv, tmpList));
        }

        return resultList;
    }

    //单个小车的所有动作统计
    private List<MissionActionStatisticVO> computeSingleVehicleActionStatistic(Date startDate, Date endDate, String agvCode,List<MissionWorkAction> actionList){
        List<MissionActionStatisticVO> resultList = new ArrayList<>();

        //按任务动作id分组
        Map<String, List<MissionWorkAction>> collect = actionList.stream().collect(Collectors.groupingBy(MissionWorkAction::getMissionActionId));
        for(Map.Entry<String, List<MissionWorkAction>> item : collect.entrySet()){
            List<MissionWorkAction> tmpList = item.getValue();
            if(CollectionUtils.isEmpty(tmpList)){
                continue;
            }

            MissionActionStatisticVO vo = new MissionActionStatisticVO();
            vo.setAgvCode(agvCode);
            vo.setMissionActionId(item.getKey());
            vo.setMissionActionName(tmpList.get(0).getName());
            vo.setTotalNum(tmpList.size());

            //计算秒值
            long seconds = tmpList.stream().mapToLong((action)->{
                if(action.getStartTime()==null){
                    return 0L;
                }

                Date tmpStart = action.getStartTime();
                if(action.getStartTime().before(startDate)){
                    tmpStart = startDate;
                }
                //此为运行中的动作
                if(action.getEndTime()==null){
                    return DateUtils.getSeconds(tmpStart, endDate);
                }

                //此为运行结束的动作
                Date tmpEnd = action.getEndTime();
                if(action.getEndTime().after(endDate)){
                    tmpEnd = endDate;
                }
                return DateUtils.getSeconds(tmpStart, tmpEnd);
            }).sum();
            vo.setTotalTime(seconds);

            resultList.add(vo);
        }
        return resultList;
    }


}
