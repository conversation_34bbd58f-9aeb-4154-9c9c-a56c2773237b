package com.youibot.agv.scheduler.service.impl;


import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.entity.vo.SidePathVo;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.MapGraphInfo;
import com.youibot.agv.scheduler.map.entity.PathResultData;
import com.youibot.agv.scheduler.map.utils.MapFileUtils;
import com.youibot.agv.scheduler.service.SidePathService;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.ConvertUtils;
import com.youibot.agv.scheduler.util.FtpUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.ROAD_NETWORK_SUFFIX;

@Service
public class SidePathServiceImpl implements SidePathService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PathServiceImpl.class);

    @Override
    public List<SidePath> selectByAGVMapId(String agvMapId, boolean isDraft) {
        List<SidePath> sidePaths = new ArrayList<>();
        FTPClient client = null;
        try {
            if (isDraft) {
                client = FtpUtils.getConnectClient();
                String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapId, true);
                String result = MapFileUtils.getDraftFileData(filePath, agvMapId, client);
                if (StringUtils.isEmpty(result)) {
                    return new ArrayList<>();
                }
                PathResultData roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);
                sidePaths = new ArrayList<>(roadNetwork.getSidePaths().values());
            } else {
                MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(agvMapId);
                if (mapGraphInfo != null && !CollectionUtils.isEmpty(mapGraphInfo.getPaths())) {
                    sidePaths = new ArrayList<>(mapGraphInfo.getSidePaths().values());
                }
            }
            return sidePaths;
        } catch (Exception e) {
            LOGGER.error("read path file is null e:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public List<SidePath> selectByAGVMapIds(List<String> agvMapIds, boolean isDraft) {
        List<SidePath> sidePaths = new ArrayList<>();
        agvMapIds.forEach(id -> sidePaths.addAll(this.selectByAGVMapId(id, isDraft)));
        return sidePaths;
    }

    @Override
    public List<SidePath> selectByAGVMapIdAndUsageStatus(String agvMapId, String usageStatus, boolean isDraft) {
        FTPClient client = null;
        try {
            if (isDraft) {
                client = FtpUtils.getConnectClient();
                String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapId, true);
                String result = MapFileUtils.getDraftFileData(filePath, agvMapId, client);
                if (StringUtils.isEmpty(result)) {
                    return null;
                }
                PathResultData roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);
                return roadNetwork.getSidePaths().values().stream().filter(sidePath -> sidePath.getUsageStatus().equals(usageStatus)).collect(Collectors.toList());
            }
            MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(agvMapId);
            if (mapGraphInfo != null && !CollectionUtils.isEmpty(mapGraphInfo.getPaths())) {
                return mapGraphInfo.getSidePaths().values().stream().filter(sidePath -> sidePath.getUsageStatus().equals(usageStatus)).collect(Collectors.toList());
            }
        } catch (Exception e) {
            LOGGER.error("read path file is null e:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
        return null;
    }

    @Override
    public Map<String, List<SidePath>> mapByAgvMapName(List<String> agvMapList, boolean isDraft) {
        return BeanUtils.listToMapByFiled(this.selectByAGVMapIds(agvMapList, isDraft), "agvMapName");
    }


    @Override
    public List<SidePath> selectByPathId(String id, String agvMapName, boolean isDraft) {
        List<SidePath> sidePaths = selectByAGVMapId(agvMapName, isDraft);
        return sidePaths.stream().filter(sidePath -> sidePath.getPathId().equals(id)).collect(Collectors.toList());
    }

    @Override
    public List<SidePath> selectByPathIds(Set<String> pathIds, String agvMapName, boolean isDraft) {
        List<SidePath> sidePaths = selectByAGVMapId(agvMapName, isDraft);
        return sidePaths.stream().filter(sidePath -> pathIds.contains(sidePath.getPathId())).collect(Collectors.toList());
    }

    @Override
    public SidePath selectById(String agvMapName, String id, boolean isDraft) {
        List<SidePath> sidePaths = selectByAGVMapId(agvMapName, isDraft);
        return sidePaths.stream().filter(sidePath -> sidePath.getId().equals(id)).findFirst().orElse(null);
    }

    @Override
    public List<SidePath> searchAll(Map<String, String> searchMap, boolean isDraft) {
        if (CollectionUtils.isEmpty(searchMap) && AGVMapInfoCache.getCache(searchMap.get("agvMapName")) != null) {
            return null;
        }
        try {
            List<SidePath> sidePaths = selectByAGVMapId(searchMap.get("agvMapName"), isDraft);
            if (CollectionUtils.isEmpty(sidePaths)) {
                return new ArrayList<>();
            }
            List<SidePath> result = new ArrayList<>();
            for (String attribute : searchMap.keySet()) {
                if (attribute.equals("agvMapName")) {
                    continue;
                }
                for (SidePath sidePath : sidePaths) {
                    if (MapFileUtils.getGetMethod(sidePath, attribute, searchMap.get(attribute))) {
                        result.add(sidePath);
                    }
                }
            }
            return result;
        } catch (Exception e) {
            LOGGER.error("searchAll path failed ：{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
        }
    }

    @Override
    public SidePath selectByAGVMapNameAndStopMarker(String agvMapId, String startMarker, String endMarker, boolean isDraft) {
        List<SidePath> sidePaths = selectByAGVMapId(agvMapId, isDraft);
        return sidePaths.stream().filter(sidePath -> sidePath.getAgvMapName().equals(agvMapId) &&
                sidePath.getStartMarkerId().equals(startMarker) && sidePath.getEndMarkerId().equals(endMarker)).findFirst().orElse(null);
    }

    @Override
    public void batchUpdate(String agvMapName, List<SidePath> sidePaths, boolean isDraft) {
        FTPClient client = null;
        try {
            String file = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapName, isDraft);
            client = FtpUtils.getConnectClient();
            String result = MapFileUtils.getDraftFileData(file, agvMapName, client);
            PathResultData pathResult = MapFileUtils.pathFileDataToPathResultData(result);
            if (pathResult == null) {
                LOGGER.error("marker data is null");
                throw new ExecuteException(MessageUtils.getMessage("service.read_map_file_fail"));
            }
            Map<String, SidePath> sidePathMaps = pathResult.getSidePaths();
            if (!CollectionUtils.isEmpty(sidePathMaps)) {
                sidePaths.forEach(sidePath -> {
                    if (sidePathMaps.containsKey(sidePath.getId())) {
                        sidePathMaps.put(sidePath.getId(), sidePath);
                    }
                });
                pathResult.setSidePaths(sidePathMaps);
            }
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(pathResult), file, client);
        } catch (Exception e) {
            LOGGER.error("update marker failed :{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.update_marker_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }


    @Override
    public List<SidePathVo> selectSidePathVosByPathId(String pathId, String agvMapName, boolean isDraft) {
        List<SidePathVo> sidePathVos = null;
        List<SidePath> sidePaths = selectByPathId(pathId, agvMapName, isDraft);
        if (!CollectionUtils.isEmpty(sidePaths)){
            sidePathVos = ConvertUtils.sourceToTarget(sidePaths,SidePathVo.class);
        }
        return sidePathVos;
    }

    @Override
    public Map<String,List<SidePathVo>> selectSidePathVosByPathIdList(List<String> pathIdList, String agvMapName, boolean isDraft) {

        Map<String,List<SidePathVo>> map = new HashMap<>();
        if(CollectionUtils.isEmpty(pathIdList)){
            return map;
        }
        List<SidePath> sidePaths = selectByAGVMapId(agvMapName, isDraft);
        if (CollectionUtils.isEmpty(sidePaths)){
            return map;
        }

        for(String pathId : pathIdList){
            List<SidePath> collect = sidePaths.stream().filter(item -> pathId.equalsIgnoreCase(item.getPathId())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(collect)){
                List<SidePathVo> sidePathVos = ConvertUtils.sourceToTarget(collect,SidePathVo.class);
                map.put(pathId,sidePathVos);
            }
        }
        return map;
    }

}
