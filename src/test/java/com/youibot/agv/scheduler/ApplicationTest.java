package com.youibot.agv.scheduler;

import com.intelligt.modbus.jlibmodbus.master.ModbusMaster;
import com.youibot.agv.scheduler.util.JLibModbusUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.UnknownHostException;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ApplicationTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApplicationTest.class);

    @Test
    public void test3() throws UnknownHostException {
        ModbusMaster master = JLibModbusUtils.createModbusMaster("127.0.0.1", 3001, 500);
        long i = 0;
        int j = 0;
        while (true) {
            try {
                i++;
                if (i >= 500000) {
                    Thread.sleep(1);
                } else if (i >= 400000) {
                    Thread.sleep(10);
                } else if (i >= 300000) {
                    Thread.sleep(20);
                } else if (i >= 200000) {
                    Thread.sleep(30);
                } else if (i >= 100000) {
                    Thread.sleep(40);
                } else {
                    Thread.sleep(50);
                }
                master.writeSingleCoil(1, 2, true);
                boolean value = master.readCoils(1, 2, 1)[0];
                if (i % 10 == 0) {
                    LOGGER.debug("第" + i + "次循环, 第一次读取值：" + value + ", 失败次数：" + j);
                }
                master.writeSingleCoil(1, 2, false);
                value = master.readCoils(1, 2, 1)[0];
                if (i % 10 == 0) {
                    LOGGER.debug("第" + i + "次循环, 第二次读取值：" + value + ", 失败次数：" + j);
                }
            } catch (Exception e) {
                j++;
                LOGGER.error("操作寄存器第" + j + "次出错", e);
            }
        }
    }

    @Test
    public void test() {
        String str = "{'value':'asdjhigfew123456'}";
        File file = new File("path/userConfig.json");
        BufferedOutputStream fos = null;
        try {
            if (!file.exists()) {
                File parentFile = file.getParentFile();
                if (!parentFile.exists()) {
                    parentFile.mkdirs();
                }
                file.createNewFile();
            }
            fos = new BufferedOutputStream(new FileOutputStream(file));
            byte[] bytes = str.getBytes();
            fos.write(bytes);
            fos.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
