package com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.UndirectedEdge;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.UndirectedGraph;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.UndirectedNode;
import com.youibot.agv.scheduler.engine.pathplan.algorithm.DFSLoopDetectManager;
import com.youibot.agv.scheduler.engine.pathplan.entity.MarkerPathResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.Pair;
import com.youibot.agv.scheduler.engine.pathplan.entity.SidePathPlanResult;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleLocation;
import com.youibot.agv.scheduler.engine.pathplan.service.*;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ConflictManager;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool.SingleAreaPathResourcePool;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.TrafficManager;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.entity.VehicleConflictInfo;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.service.ConflictCheck;
import com.youibot.agv.scheduler.engine.pathplan.util.SidePathUtils;
import com.youibot.agv.scheduler.entity.Marker;
import com.youibot.agv.scheduler.entity.ParkScheduler;
import com.youibot.agv.scheduler.entity.Path;
import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.mqtt.bean.scribe.pathplan.PathPlanMessage;
import com.youibot.agv.scheduler.service.ParkSchedulerService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 9/1/21 4:19 PM
 */
@Service
public class ConflictCheckImpl implements ConflictCheck {

    private static final Logger logger = LoggerFactory.getLogger(ConflictCheckImpl.class);

    @Autowired
    private VehiclePool vehiclePool;
    @Autowired
    private VehiclePoolService vehiclePoolService;
    @Autowired
    private CheckAndSendPathService checkAndSendPathService;
    @Autowired
    private LocationService locationService;
    @Autowired
    private SingleAreaPathResourcePool singleAreaPathResourcePool;
    @Autowired
    private MarkerAllocationService markerAllocationService;
    @Autowired
    private ParkSchedulerService parkSchedulerService;
    @Autowired
    private PathPlanService pathPlanService;
    @Autowired
    private TrafficManager trafficManager;

    private DFSLoopDetectManager dfsLoopDetectManager = DFSLoopDetectManager.getInstance();

    @Value("${PATH_PLAN.OPEN_OBSTRUCT_DETOUR}")
    private boolean obstructDetour;


    @Override
    public List<List<String>> getOppositeConflicts() {
        List<List<String>> result = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRunningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();
        if (CollectionUtils.isEmpty(agvToPlanedSidePaths)) {
            return result;
        }
        String[] agvCodes = agvToPlanedSidePaths.keySet().stream().toArray(String[]::new);
        /**
         * 对向冲突检测及处理。
         * 1：获取所有已执行路径的AGVCode。
         * 2：获取所有的机器人。规划的路径。运行的路径，已执行的路径。
         * 3：如果机器人的runningSidePath不为空。则不存在冲突，忽略。
         * 4：如果机器人的planedPath为空。则不存在冲突。忽略。
         * 5：获得一辆车。再获得所有的别的车的planedPath中是否有冲突。
         * 6：如果有冲突。就把冲突的车放到冲突处理服务中进行解决。conflictProcessThread
         */
        for (int i = 0; i < agvCodes.length; i++) {
            LinkedBlockingDeque<SidePath> psA = agvToPlanedSidePaths.get(agvCodes[i]);
            LinkedBlockingDeque<SidePath> rsA = agvToRunningSidePaths.get(agvCodes[i]);
            if (psA.isEmpty()) continue;
            for (int j = i + 1; j < agvCodes.length; j++) {
                LinkedBlockingDeque<SidePath> psB = agvToPlanedSidePaths.get(agvCodes[j]);
                LinkedBlockingDeque<SidePath> rsB = agvToRunningSidePaths.get(agvCodes[j]);
                if (psB.isEmpty()) continue;
                if (detectConflict(psA, psB, rsA, rsB)) {
                    List<String> conflictList = new ArrayList<>();
                    conflictList.add(agvCodes[i]);
                    conflictList.add(agvCodes[j]);
                    result.add(conflictList);
                }
            }
        }
        return result;
    }

    /**
     * 如果路径是属于相同的Path。表示他们发生了对向冲突。返回true.
     */
    private boolean detectConflict(LinkedBlockingDeque<SidePath> planedSidePathsA, LinkedBlockingDeque<SidePath> planedSidePathsB, LinkedBlockingDeque<SidePath> runningSidePathsA, LinkedBlockingDeque<SidePath> runningSidePathsB) {
        if (planedSidePathsA.isEmpty() || planedSidePathsB.isEmpty()) {
            return false;
        }
        SidePath sidePathA = planedSidePathsA.peekFirst();
        SidePath sidePathB = planedSidePathsB.peekFirst();
        /**
         * 检测是否对向行驶
         */
        if (sidePathA.getStartMarkerId().equals(sidePathB.getEndMarkerId())
                && sidePathA.getEndMarkerId().equals(sidePathB.getStartMarkerId())
                && !sidePathA.getId().equals(sidePathB.getId())) {
            return true;
        }
        /**
         * 如果存在T字路和一字路会存在机器人连接两个路径发生了冲突。并且进不去。
         */
        if (planedSidePathsA.size() >= 2 && planedSidePathsB.size() >= 2) {
            SidePath sidePathA1 = (SidePath) planedSidePathsA.toArray()[0];
            SidePath sidePathA2 = (SidePath) planedSidePathsA.toArray()[1];
            SidePath sidePathB1 = (SidePath) planedSidePathsB.toArray()[0];
            SidePath sidePathB2 = (SidePath) planedSidePathsB.toArray()[1];

            if (sidePathA1.getStartMarkerId().equals(sidePathB2.getEndMarkerId())
                    && sidePathA1.getEndMarkerId().equals(sidePathB2.getStartMarkerId())
                    && sidePathA2.getStartMarkerId().equals(sidePathB1.getEndMarkerId())
                    && sidePathA2.getEndMarkerId().equals(sidePathB1.getStartMarkerId())) {
                return true;
            }
            //苏州绿点对向冲突特殊识别处理，其他版本不用,当开启绕路功能时才需要该识别机制
            if (obstructDetour) {
                if (CollectionUtils.isEmpty(runningSidePathsA) && CollectionUtils.isEmpty(runningSidePathsB)) {
                    if (sidePathA1.getPathId().equals(sidePathB2.getPathId()) || sidePathA2.getPathId().equals(sidePathB1.getPathId())) {
                        logger.debug("无正在运行路径下得两车识别冲突...");
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public List<List<VehicleConflictInfo>> getVehicleOppositeConflicts() {
        List<List<VehicleConflictInfo>> result = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        if (CollectionUtils.isEmpty(agvToPlanedSidePaths)) {
            return result;
        }
        String[] agvCodes = agvToPlanedSidePaths.keySet().toArray(new String[0]);

        List<VehicleConflictInfo> vehicleConflictInfos = null;
        for (int i = 0; i < agvCodes.length; i++) {
            LinkedBlockingDeque<SidePath> psA = agvToPlanedSidePaths.get(agvCodes[i]);
            if (CollectionUtils.isEmpty(psA)) {
                continue;
            }
            for (int j = i + 1; j < agvCodes.length; j++) {
                LinkedBlockingDeque<SidePath> psB = agvToPlanedSidePaths.get(agvCodes[j]);
                if (CollectionUtils.isEmpty(psB)) {
                    continue;
                }
                vehicleConflictInfos = this.detectConflict(agvCodes[i], agvCodes[j], psA, psB);
                if (vehicleConflictInfos != null) {
                    logger.debug("识别对象冲突车辆:{}", vehicleConflictInfos);
                    result.add(vehicleConflictInfos);
                }
            }
        }
        return result;
    }

    private List<VehicleConflictInfo> detectConflict(String agvCodeA, String agvCodeB, LinkedBlockingDeque<SidePath> planedSidePathsA, LinkedBlockingDeque<SidePath> planedSidePathsB) {
        if (StringUtils.isBlank(agvCodeA) || StringUtils.isBlank(agvCodeB) || planedSidePathsA.isEmpty() || planedSidePathsB.isEmpty()) {
            return null;
        }
        SidePath sidePathA = planedSidePathsA.peekFirst();
        SidePath sidePathB = planedSidePathsB.peekFirst();
        /**
         * 检测是否对向行驶
         */
        if (sidePathA.getStartMarkerId().equals(sidePathB.getEndMarkerId())
                && sidePathA.getEndMarkerId().equals(sidePathB.getStartMarkerId())
                && !sidePathA.getId().equals(sidePathB.getId())) {
            ConflictManager.conflictCheckLog.debug("【对向冲突】 机器人：{} 所在点位 {} 申请点位 {}, 机器人 {} 所在点位 {} 申请点位 {}",
                    agvCodeA, MapGraphUtil.getMarkerByMarkerId(sidePathA.getStartMarkerId()).getCode(),
                    MapGraphUtil.getMarkerByMarkerId(sidePathA.getEndMarkerId()).getCode(), agvCodeB,
                    MapGraphUtil.getMarkerByMarkerId(sidePathB.getStartMarkerId()).getCode(),
                    MapGraphUtil.getMarkerByMarkerId(sidePathB.getEndMarkerId()).getCode());
            return this.createConflictInfoList(sidePathA, sidePathB, agvCodeA, agvCodeB);
        }
        /**
         * 如果存在T字路和一字路会存在机器人连接两个路径发生了冲突。并且进不去。
         */
        if (planedSidePathsA.size() >= 2 && planedSidePathsB.size() >= 2) {
            SidePath sidePathA1 = (SidePath) planedSidePathsA.toArray()[0];
            SidePath sidePathA2 = (SidePath) planedSidePathsA.toArray()[1];
            SidePath sidePathB1 = (SidePath) planedSidePathsB.toArray()[0];
            SidePath sidePathB2 = (SidePath) planedSidePathsB.toArray()[1];

            if (sidePathA1.getStartMarkerId().equals(sidePathB2.getEndMarkerId())
                    && sidePathA1.getEndMarkerId().equals(sidePathB2.getStartMarkerId())
                    && sidePathA2.getStartMarkerId().equals(sidePathB1.getEndMarkerId())
                    && sidePathA2.getEndMarkerId().equals(sidePathB1.getStartMarkerId())) {
                ConflictManager.conflictCheckLog.debug("【对向冲突】 机器人：{} 所在点位 {} 申请点位 {}, 机器人 {} 所在点位 {} 申请点位 {}",
                        agvCodeA, MapGraphUtil.getMarkerByMarkerId(sidePathA.getStartMarkerId()).getCode(),
                        MapGraphUtil.getMarkerByMarkerId(sidePathA.getEndMarkerId()).getCode(), agvCodeB,
                        MapGraphUtil.getMarkerByMarkerId(sidePathB.getStartMarkerId()).getCode(),
                        MapGraphUtil.getMarkerByMarkerId(sidePathB.getEndMarkerId()).getCode());
                return this.createConflictInfoList(sidePathA1, sidePathB1, agvCodeA, agvCodeB);
            }
        }
        return null;
    }

    private List<VehicleConflictInfo> createConflictInfoList(SidePath sidePathA, SidePath sidePathB, String agvCodeA, String agvCodeB) {
        List<VehicleConflictInfo> vehicleConflictInfoList = new ArrayList<>();
        VehicleConflictInfo vehicleConflictInfoA = new VehicleConflictInfo();
        vehicleConflictInfoA.setVehicleCode(agvCodeA);
        List<String> markerIdAs = new ArrayList<>();
        markerIdAs.add(sidePathA.getStartMarkerId());
        markerIdAs.add(sidePathA.getEndMarkerId());
        vehicleConflictInfoA.setConflictMarkerIds(markerIdAs);
        vehicleConflictInfoA.setNextMarkerId(sidePathA.getEndMarkerId());
        vehicleConflictInfoList.add(vehicleConflictInfoA);

        VehicleConflictInfo vehicleConflictInfoB = new VehicleConflictInfo();
        vehicleConflictInfoB.setVehicleCode(agvCodeB);
        List<String> markerIdBs = new ArrayList<>();
        markerIdBs.add(sidePathB.getStartMarkerId());
        markerIdBs.add(sidePathB.getEndMarkerId());
        vehicleConflictInfoB.setConflictMarkerIds(markerIdBs);
        vehicleConflictInfoB.setNextMarkerId(sidePathB.getEndMarkerId());
        vehicleConflictInfoList.add(vehicleConflictInfoB);
        return vehicleConflictInfoList;
    }

    @Override
    public List<List<String>> getRingConflicts() {
        List<List<String>> result = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        if (CollectionUtils.isEmpty(agvToPlanedSidePaths)) {
            return result;
        }
        String[] agvCodes = agvToPlanedSidePaths.keySet().stream().toArray(String[]::new);
        /**
         * 环形冲突检测及处理。
         * 1：生成车型规划路径无向图。
         * 2：如果无向图有机咕噜人路径行成了环。
         * 3：把机器人添加到冲突队列中。进行处理。
         */
        Map<String, String> nodeIdToAGVCode = new ConcurrentHashMap<>();
        UndirectedGraph graph = this.generateUndirectedGraph(agvCodes, agvToPlanedSidePaths, nodeIdToAGVCode);
        if (graph.hasCyclic()) {
            dfsLoopDetectManager.setUndirectedGraph(graph);
            List<ArrayList<String>> loops = dfsLoopDetectManager.getLoops();
            if (!CollectionUtils.isEmpty(loops)) {
                for (ArrayList<String> loop : loops) {
                    List<String> conflicts = new ArrayList<>();
                    loop.forEach(nodeId -> {
                        conflicts.add(nodeIdToAGVCode.get(nodeId));
                    });
                    result.add(conflicts);
                }
            }
        }
        return result;
    }

    @Override
    public List<List<VehicleConflictInfo>> getVehicleRingAreaConflicts() {

        Map<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        if (CollectionUtils.isEmpty(agvToPlanedSidePaths)) {
            return new ArrayList<>();
        }
        Map<String, LinkedBlockingDeque<SidePath>> agvToRunningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();
        List<List<VehicleConflictInfo>> result = new ArrayList<>();

        label:
        for (String agvCode : ConflictManager.conflictAgv.keySet()) {
            List<List<String>> ringList = getRingAgvCodes(agvCode, 0, ConflictManager.conflictAgv.size(), Lists.newArrayList(agvCode), ConflictManager.conflictAgv);
            ringList = ringList.stream().filter(i -> !i.isEmpty()).sorted(Comparator.comparing(List::size)).collect(Collectors.toList());
            Collections.reverse(ringList);
            if (!ringList.isEmpty()) {
                //获取最长的那个环
                Set<String> ringSet = new HashSet<>(ringList.get(0));
                if (ringSet.size() >= 2) {
                    List<List<String>> collect = result.stream().map(l -> l.stream().map(VehicleConflictInfo::getVehicleCode).collect(Collectors.toList())).collect(Collectors.toList());
                    //是否是重复的环
                    boolean repeatRing = false;
                    for (int i = 0; i < collect.size(); i++) {
                        List<String> ring = collect.get(i);
                        if (ring.containsAll(ringSet)) {
                            repeatRing = true;
                            break;
                        }
                        if (ringSet.containsAll(ring)) {
                            result.remove(i);
                            break;
                        }
                    }
                    if (repeatRing) {
                        continue;
                    }

                    List<VehicleConflictInfo> vehicleConflictInfos = new ArrayList<>();
                    for (String agv : ringSet) {
                        LinkedBlockingDeque<SidePath> ps = agvToPlanedSidePaths.get(agv);
                        LinkedBlockingDeque<SidePath> rs = agvToRunningSidePaths.get(agv);
                        //如果没有计划中的路径，则不成环，存在运行中的路径，也不进行避让，因为AGV还没走完路径，走完时环可能就不存在了，防止无效的提前避让
                        if (CollectionUtils.isEmpty(ps) || !CollectionUtils.isEmpty(rs)) {
                            continue label;
                        }
                        VehicleConflictInfo info = new VehicleConflictInfo();
                        info.setVehicleCode(agv);
                        info.setConflictMarkerIds(Lists.newArrayList(ps.peekFirst().getStartMarkerId(), ps.peekFirst().getEndMarkerId()));
                        info.setNextMarkerId(ps.getFirst().getEndMarkerId());
                        vehicleConflictInfos.add(info);
                    }
                    result.add(vehicleConflictInfos);
                    ConflictManager.conflictCheckLog.debug("【机器人资源冲突】 机器人：{}", ringSet);
                    vehicleConflictInfos.forEach(info -> {
                        ConflictManager.conflictCheckLog.debug("【机器人资源冲突】 机器人：{} , 所在点：{}， 计划申请的点{}", info.getVehicleCode(), MapGraphUtil.getMarkerByMarkerId(info.getConflictMarkerIds().get(0)).getCode(), MapGraphUtil.getMarkerByMarkerId(info.getConflictMarkerIds().get(1)).getCode());
                    });
                }
            }
        }
        return result;
    }

    /**
     * 得到环形的冲突列表，可能存在多个环，目前处理的是最短的一个环
     * @param count 当前递归次数
     * @param maxCount 最大递归次数
     * @param ring 环
     */
    private static List<List<String>> getRingAgvCodes(String agvCode, int count, int maxCount, List<String> ring, Map<String, List<String>> conflictMap) {
        List<String> list = conflictMap.get(agvCode);
        count++;
        //当该agv没有冲突的小车，或者说已达到最大递归深度，则中止递归
        if (CollectionUtils.isEmpty(list) || count > maxCount) {
            return Lists.newArrayList();
        }
        List<List<String>> conflicts = new ArrayList<>();
        //已成环
        if (list.contains(ring.get(0))) {
            conflicts.add(new ArrayList<>(ring));
        } else {
            for (String agv : list) {
                List<String> tmpRing = new ArrayList<>(ring);
                tmpRing.add(agv);
                conflicts.addAll(getRingAgvCodes(agv, count, maxCount, tmpRing, conflictMap));
            }
        }
        return conflicts;
    }

    @Override
    public List<Pair<String,List<String>>> getObstructConflicts() {
        List<Pair<String,List<String>>> result = new ArrayList<>();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToTotalSidePaths = checkAndSendPathService.getAgvToTotalSidePaths();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths = checkAndSendPathService.getAgvToPlanedSidePaths();
        ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToRunningSidePaths = checkAndSendPathService.getAgvToRunningSidePaths();
        if (CollectionUtils.isEmpty(agvToPlanedSidePaths)) {
            return result;
        }
        agvToPlanedSidePaths.forEach((agvCode, psA) -> {
            if (!CollectionUtils.isEmpty(agvToRunningSidePaths.get(agvCode))) {
                return;//机器人行驶中, 不触发绕障
            }
            if (psA.size() <= 1) {
                return;//如果当前只存在一段路径, 不触发绕障（目标点被占用, 绕路无意义）
            }
            List<ParkScheduler> parkSchedulers = parkSchedulerService.selectCreateAndStart();
            //获取除本身之外的所有vehicle
            List<Vehicle> vehicles = vehiclePool.getAll().stream().filter(vehicle -> !agvCode.equals(vehicle.getId())).collect(Collectors.toList());
            vehicles.forEach(otherVehicle -> {
                //车是不是被驱赶中
                if (isDriving(otherVehicle, parkSchedulers)) {
                    return;
                }
                LinkedBlockingDeque<SidePath> tsB = Optional.ofNullable(agvToTotalSidePaths.get(otherVehicle.getId())).orElse(new LinkedBlockingDeque<>());
                Set<String> occupyMarkerIds = trafficManager.queryUsedMarkerResourceByVehicleId(otherVehicle.getId());
                if (detectObstructConflict(agvCode, psA, tsB, otherVehicle) && !this.isOriginalRoute(agvCode, new ArrayList<>(tsB), new ArrayList<>(occupyMarkerIds))) {
                    result.add(new Pair<>(agvCode, new ArrayList<>(occupyMarkerIds)));
                }
            });
        });
        return result;
    }

    private boolean isDriving(Vehicle otherVehicle, List<ParkScheduler> parkSchedulers) {
        ParkScheduler parkScheduler = null;
        if (CollUtil.isNotEmpty(parkSchedulers)) {
            parkScheduler = parkSchedulers.stream().filter(p -> "Drive".equals(p.getSource()) && otherVehicle != null && otherVehicle.getId().equals(p.getVehicleId())).findFirst().orElse(null);
        }
        return parkScheduler != null;
    }

    private boolean isOriginalRoute(String agvCode, List<SidePath> originSidePaths, List<String> excludeOccupyMarkerIds) {
        AtomicBoolean isOriginalRoute = new AtomicBoolean(false);
        try {
            Optional.ofNullable(checkAndSendPathService.getPathPlanMessage(agvCode))
                    .map(PathPlanMessage::getAimMarkerId)
                    .ifPresent(aimMarkerId->{
                        MarkerPathResult markerPathResult = pathPlanService.search(agvCode, aimMarkerId, excludeOccupyMarkerIds);
                        Optional.ofNullable(pathPlanService.linkerListPathToSidePath(agvCode, markerPathResult))
                                .map(SidePathPlanResult::getSidePaths)
                                .filter(sidePaths -> !CollectionUtils.isEmpty(sidePaths))
                                .ifPresent(sidePaths -> {
                                    Set<String> originSidePathIds = originSidePaths.stream().map(SidePath::getId).collect(Collectors.toSet());
                                    Set<String> newSidePathIds = sidePaths.stream().map(SidePath::getId).collect(Collectors.toSet());
                                    if (originSidePathIds.containsAll(newSidePathIds)) {
                                        logger.debug("机器人{}检测到前方有静止机器人阻挡, 但当前无更优路径, 继续行走", agvCode);
                                        ConflictManager.conflictCheckLog.debug("【阻挡冲突】 机器人{}检测到前方有静止机器人阻挡, 但当前无更优路径, 继续行走", agvCode);
                                        isOriginalRoute.set(true);
                                    }
                                });
                    });
        } catch (Exception e) {
            logger.error("阻挡冲突时, 路径规划出错, {}", e.getMessage());
            isOriginalRoute.set(true);
        }
        return isOriginalRoute.get();
    }

    private boolean detectObstructConflict(String agvCode, LinkedBlockingDeque<SidePath> psA, LinkedBlockingDeque<SidePath> psB, Vehicle vehicleB) {
        if (!CollectionUtils.isEmpty(psB)) {//B机器人存在路径导航, 不触发阻挡绕障
            return false;
        }
        VehicleLocation vehicleLocation = locationService.getVehicleLocation(vehicleB.getId());//获取机器人占用的点位/路径
        if (vehicleLocation == null) {
            return false;//B机器人不在轨道上, 不触发阻挡绕障
        }
        LinkedList<SidePath> sidePaths = new LinkedList<>(psA);
        Marker currentMarker = vehicleLocation.getMarker();
        List<SidePath> currentSidePaths = vehicleLocation.getSidePaths();
        if (currentMarker != null) {//如果在点位上
            /**
             * 查看A机器人接下来要走的两个点位, 是否被B机器人占用
             */
            String markerId = currentMarker.getId();
            for (SidePath sidePath : sidePaths) {
                if (markerId.equals(sidePath.getEndMarkerId())) {
                    logger.debug("检测到机器人在点位上阻挡冲突, 阻挡的机器人:{},被挡的机器人：{}", vehicleB.getId(),agvCode);
                    return true;//如果B机器人阻挡在A机器人接下来要行驶的两个点的其中一个点位上
                }
            }
            /**
             * 查看A机器人接下来要走的两个点位, 是否存在单机区域且被B机器人占用
             */
            //获取A机器人需要申请的单机区域
            List<String> applyMarkerIds = sidePaths.stream().map(SidePath::getEndMarkerId).collect(Collectors.toList());
            Set<String> applySingleAreaIds = singleAreaPathResourcePool.querySingleAreaResourceIdsByMarkerIds(new LinkedList<>(applyMarkerIds));
            if (!CollectionUtils.isEmpty(applySingleAreaIds)) {//A机器人有需要申请的单机区域
                //获取B机器人占用的单机区域
                HashSet<String> occupySingleAreaIds = singleAreaPathResourcePool.querySingleAreaResourceIdByMarkerId(markerId);
                applySingleAreaIds.retainAll(occupySingleAreaIds);//取差集, 判断A机器人申请的单机区域是否被B机器人占用
                if (!CollectionUtils.isEmpty(applySingleAreaIds)) {
                    logger.debug("检测到机器人在单机区域上阻挡冲突, 阻挡的机器人:{},被挡的机器人：{}", vehicleB.getId(),agvCode);
                    return true;
                }
            }
        } else if (!CollectionUtils.isEmpty(currentSidePaths)) {//如果在路径上
            /**
             * 查看A机器人接下来要走的两段路径, 是否被B机器人占用
             */
            List<String> sidePathIds = sidePaths.stream().map(SidePath::getId).collect(Collectors.toList());
            List<SidePath> conflictPaths = currentSidePaths.stream().filter(sidePath -> sidePathIds.contains(sidePath.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(conflictPaths)) {
                logger.debug("检测到机器人在路径上阻挡冲突, 阻挡的机器人:{},被挡的机器人：{}", vehicleB.getId(),agvCode);
                return true;
            }
            /**
             * 查看A机器人接下来要走的两个点位, 是否存在单机区域且被B机器人占用
             */
            //获取A机器人需要申请的单机区域
            List<String> applyMarkerIds = sidePaths.stream().map(SidePath::getEndMarkerId).collect(Collectors.toList());
            Set<String> applySingleAreaIds = singleAreaPathResourcePool.querySingleAreaResourceIdsByMarkerIds(new LinkedList<>(applyMarkerIds));
            if (!CollectionUtils.isEmpty(applySingleAreaIds)) {//A机器人有需要申请的单机区域
                //获取B机器人占用的单机区域
                LinkedList<String> occupyMarkerIds = SidePathUtils.getMarkerIdsFromSidePaths(currentSidePaths);
                Set<String> occupySingleAreaIds = singleAreaPathResourcePool.querySingleAreaResourceIdsByMarkerIds(occupyMarkerIds);
                applySingleAreaIds.retainAll(occupySingleAreaIds);//取差集, 判断A机器人申请的单机区域是否被B机器人占用
                if (!CollectionUtils.isEmpty(applySingleAreaIds)) {
                    logger.debug("检测到机器人在单机区域上阻挡冲突, 阻挡的机器人:{},被挡的机器人：{}", vehicleB.getId(),agvCode);
                    return true;
                }
            }
        }
        return false;
    }

    private UndirectedGraph generateUndirectedGraph(String[] agvCodes,
                                                    ConcurrentHashMap<String, LinkedBlockingDeque<SidePath>> agvToPlanedSidePaths,
                                                    Map<String, String> nodeIdToAGVCode) {
        UndirectedGraph graph = new UndirectedGraph();
        for (String agvCode : agvCodes) {
            LinkedBlockingDeque<SidePath> psA = agvToPlanedSidePaths.get(agvCode);
            if (psA.isEmpty()) continue;
            Marker startMarker = MapGraphUtil.getMarkerByMarkerId(psA.peekFirst().getStartMarkerId());
            Marker endMarker = MapGraphUtil.getMarkerByMarkerId(psA.peekFirst().getEndMarkerId());
            UndirectedNode nodeU = new UndirectedNode(startMarker);
            UndirectedNode nodeV = new UndirectedNode(endMarker);
            UndirectedEdge edge = new UndirectedEdge(psA.peekFirst(), startMarker.getCode(), endMarker.getCode(), 0.8d, 0.8d);
            nodeIdToAGVCode.put(startMarker.getId(), agvCode);
            graph.addNode(nodeU);
            graph.addNode(nodeV);
            graph.addEdge(edge);
        }
        return graph;
    }

    /**
     * 空闲驱赶
     */
    @Override
    public void vehicleBlockDrive() {
        //获取正在路径导航的机器人
        List<Vehicle> navigationVehicles = vehiclePoolService.selectAll().stream().filter(vehicle -> checkAndSendPathService.getPathPlanMessage(vehicle.getId()) != null).collect(Collectors.toList());
        //根据设定的策略, 获取可触发被档机器人绕路的挡路机器人
        List<String> drivePolicyVehicleCodes = this.getDrivePolicyVehicles().stream().map(Vehicle::getId).collect(Collectors.toList());
        navigationVehicles.forEach(navigationVehicle -> {
            List<Path> planedPaths = navigationVehicle.getPlanedPaths();
            List<Path> runningPaths = navigationVehicle.getRunningPaths();
            if (CollUtil.isEmpty(planedPaths) || CollUtil.isNotEmpty(runningPaths)) {//如果已无规划路径、或者有正在行走的路径
                return;
            }
            //校验导航机器人是否被阻挡并可绕路, 绕路时不规划阻挡机器人所占用的点位
            Optional.ofNullable(navigationVehicle.getId())
                    .map(ConflictManager.conflictAgv::get)
                    .map(conflictVehicleCodes -> CollUtil.intersectionDistinct(conflictVehicleCodes, drivePolicyVehicleCodes))
                    .filter(CollUtil::isNotEmpty)
                    .ifPresent(drivenVehicleIds -> drivenVehicleIds.forEach(drivenVehicleId -> {
                        try {
                            //机器人是否已存在正在执行驱赶任务检查
                            ParkScheduler parkScheduler = parkSchedulerService.selectRunningByVehicle(drivenVehicleId);
                            if (parkScheduler != null) {
                                return;
                            }
                            //获取最优可用驱赶点
                            Marker optimalDriveMarker = markerAllocationService.getOptimalDriveMarker(navigationVehicle.getId(), drivenVehicleId);
                            if (optimalDriveMarker == null) {
                                return;
                            }
                            logger.debug("路径导航检测到前方有机器人阻挡, 驱赶机器人:[{}], 被驱赶机器人:[{}]，最优可用驱赶点:[{}], ", navigationVehicle.getId(), drivenVehicleId, optimalDriveMarker.getCode());
                            //添加驱赶任务
                            ParkScheduler driveScheduler = new ParkScheduler();
                            driveScheduler.setVehicleId(drivenVehicleId);
                            driveScheduler.setParkId(optimalDriveMarker.getId());
                            driveScheduler.setParkPointCode(optimalDriveMarker.getCode());
                            driveScheduler.setSource("Drive");
                            parkSchedulerService.insert(driveScheduler);
                        } catch (Exception e) {
                            logger.error("路径导航检测到前方有机器人阻挡触发驱赶出错, vehicleCode:[{}]", navigationVehicle.getId(), e);
                        }
                    }));
        });
    }

    private List<Vehicle> getDrivePolicyVehicles() {
        //获取满足驱赶条件的机器人
        List<Vehicle> waitWork = vehiclePoolService.getCanDriveAway();
        //筛选开启泊车功能的小车
        if (CollUtil.isNotEmpty(waitWork)) {
            waitWork = waitWork.stream().filter(Vehicle::isAutoPark).collect(Collectors.toList());
        }
        return waitWork;
    }
}
