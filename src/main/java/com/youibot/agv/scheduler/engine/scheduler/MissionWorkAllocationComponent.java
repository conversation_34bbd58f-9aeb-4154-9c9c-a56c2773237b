package com.youibot.agv.scheduler.engine.scheduler;

import cn.hutool.core.collection.CollUtil;
import com.youibot.agv.scheduler.constant.ExceptionInfoEnum;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.engine.pathplan.entity.VehicleScope;
import com.youibot.agv.scheduler.engine.pathplan.service.FullLocationService;
import com.youibot.agv.scheduler.engine.pathplan.service.VehicleScopeService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool.NoCancelTaskAreaPathResourcePool;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePool;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_FAULT;
import static com.youibot.agv.scheduler.constant.VehicleConstant.ABNORMAL_STATUS_NO;
import static com.youibot.agv.scheduler.constant.VehicleConstant.TASK_STATUS_CHARGE;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-12 19:06
 */
@Component
public class MissionWorkAllocationComponent {

    private static final Logger logger = LoggerFactory.getLogger(MissionWorkAllocationComponent.class);

    private static final Integer ALLOCATION_MISSION_WORK_MAX = 100;

    @Autowired
    private MissionWorkService missionWorkService;
    @Autowired
    private MissionService missionService;
    @Autowired
    private VehiclePoolService vehiclePoolService;
    @Autowired
    private WorkSchedulerService workSchedulerService;
    @Autowired
    private VehicleScopeService vehicleScopeService;
    @Autowired
    private SchedulerConfigService schedulerConfigService;
    @Autowired
    private VehiclePool defaultVehiclePool;
    @Autowired
    private AbnormalPromptService abnormalPromptService;
    @Autowired
    private NoCancelTaskAreaPathResourcePool noCancelTaskAreaResourcePool;
    @Autowired
    private FullLocationService fullLocationService;

    @Value("${MISSION_WORK.CAN_EXECUTE.REJECT_INTERVAL}")
    private Integer rejectInterval;


    /**
     * 分配作业给机器人，
     * 1：预分配检测。
     * 2：任务预分配
     * 3：预分配转分配
     */
    public void allocation() {
        /**
         * 预分配检测。已经预分配的任务，
         * 如果发现在机器人的状态及机器人的作业存在异常，则取消预分配。
         */
        List<WorkScheduler> preWorkSchedulers = workSchedulerService.selectPrepare();
        if (!CollectionUtils.isEmpty((preWorkSchedulers))) {
            for (WorkScheduler workScheduler : preWorkSchedulers) {
                Vehicle vehicle = defaultVehiclePool.getVehicle(workScheduler.getVehicleId());
                if (vehicle == null || vehicle.getWorkStatus() .equals( TASK_STATUS_CHARGE) || !vehicle.getAbnormalStatus().equals(ABNORMAL_STATUS_NO) || vehicle.getStatus().equals(VehicleConstant.DISABLE)
                        || !vehicle.getOnlineStatus().equals(VehicleConstant.ONLINE)) {
                    workScheduler.setStatus(WorkScheduler.STATUS_CANCEL);
                    workScheduler.setFaultMessage("取消机器人预分配任务，请检查机器人状态");
                    workScheduler.setFinishTime(new Date());
                    workSchedulerService.update(workScheduler);
                }
            }
        }
        /**
         * 任务进行预分配
         *
         */
        List<MissionWork> missionWorks = missionWorkService.selectByUnAllocation(ALLOCATION_MISSION_WORK_MAX);
        /**
         * 获取所有可用的车辆,包括作业中的车辆，但不包括正在进行校正充电的机器人以及普通充电时长未达到设定时长的机器人
         */
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        List<Vehicle> workableVehicles = vehiclePoolService.getWaitWorkAndWorking(schedulerConfig);
        if (schedulerConfig.getPreMissionWorkEnable() == 0) {//没有预分配
            workableVehicles = workableVehicles.stream().filter(vehicle -> workSchedulerService.selectPrepareAndRunningByVehicleId(vehicle.getId()) == null).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(missionWorks)) {
            for (MissionWork missionWork : missionWorks) {
                try {
                    /**
                     * 作业未分配，开始进行机器人分配
                     * 分配最优的机器人,优先高的任务可以替换优先级低的预分配任务。
                     */
                    List<Vehicle> vehicles = this.selectVehicleByMissionWork(missionWork, workableVehicles);
                    if (!CollectionUtils.isEmpty(vehicles)) {
                        for (Vehicle vehicle : vehicles) {
                            ThreadContext.put("ROUTINGKEY",vehicle.getId());
                            if (schedulerConfig.getPreMissionWorkEnable() == 1) {
                                /**
                                 * 根据查询出来的车辆查看是否存在预分配。如果存在预分配则对比优先级。
                                 * 如果当前任务的优先级高于已经预分配的作业。则取消作业的预分配。并且把新的任务预分配给机器人。
                                 * TODO 性能优化。
                                 *
                                 */
                                WorkScheduler vehiclePreWorkScheduler = workSchedulerService.selectPreAllocationWorkByVehicleId(vehicle.getId());
                                if (vehiclePreWorkScheduler != null) {
                                    MissionWork vehicleMissionWork = missionWorkService.selectById(vehiclePreWorkScheduler.getWorkId());
                                    if (vehicleMissionWork != null && vehicleMissionWork.getSequence() < missionWork.getSequence()) {
                                        /**
                                         * 取消机器人预分配任务。
                                         */
                                        logger.debug("agvCode:[{}],event:[作业预分配],content:[机器人已预分配作业:{}被高优先级作业:{}取消]",vehicle.getId(),vehiclePreWorkScheduler.getWorkId(),missionWork.getId());
                                        vehiclePreWorkScheduler.setStatus(WorkScheduler.STATUS_CANCEL);
                                        vehiclePreWorkScheduler.setFaultMessage("取消机器人预分配任务，有更高优先级的任务占用。");
                                        vehiclePreWorkScheduler.setFinishTime(new Date());
                                        workSchedulerService.update(vehiclePreWorkScheduler);
                                        /**
                                         * 创建新的预分配任务。
                                         */
                                        logger.debug("agvCode:[{}],event:[作业预分配],content:[机器人预分配作业:{}]",vehicle.getId(),missionWork.getId());
                                        WorkScheduler workScheduler = new WorkScheduler();
                                        workScheduler.setVehicleId(vehicle.getId());
                                        workScheduler.setWorkId(missionWork.getId());
                                        workScheduler.setWorkName(missionWork.getName());
                                        workScheduler.setStatus(WorkScheduler.STATUS_PREPARE);
                                        workSchedulerService.insert(workScheduler);
                                        break;
                                    }
                                } else {
                                    logger.debug("agvCode:[{}],event:[作业预分配],content:[机器预分配作业:{}]",vehicle.getId(),missionWork.getId());
                                    WorkScheduler workScheduler = new WorkScheduler();
                                    workScheduler.setVehicleId(vehicle.getId());
                                    workScheduler.setWorkId(missionWork.getId());
                                    workScheduler.setWorkName(missionWork.getName());
                                    workScheduler.setStatus(WorkScheduler.STATUS_PREPARE);
                                    workSchedulerService.insert(workScheduler);
                                    break;
                                }
                            } else {
                                WorkScheduler vehicleRunningWorkScheduler = workSchedulerService.selectPrepareAndRunningByVehicleId(vehicle.getId());
                                if (null == vehicleRunningWorkScheduler) {
                                    logger.debug("agvCode:[{}],event:[作业分配],content:[机器人分配作业:{}]",vehicle.getId(),missionWork.getId());
                                    WorkScheduler workScheduler = new WorkScheduler();
                                    workScheduler.setVehicleId(vehicle.getId());
                                    workScheduler.setWorkId(missionWork.getId());
                                    workScheduler.setWorkName(missionWork.getName());
                                    workScheduler.setStatus(WorkScheduler.STATUS_PREPARE);
                                    workSchedulerService.insert(workScheduler);
                                    workableVehicles.removeIf(v -> v.getId().equals(vehicle.getId()));
                                    break;
                                }
                            }
                            ThreadContext.remove("ROUTINGKEY");
                        }
                    }
                } catch (ExecuteException e) {
                    logger.error("Mission work allocation fault.", e);
                    missionWorkStatusFault(missionWork, ExceptionInfoEnum.MISSION_WORK_ALLOCATION_ERROR.getErrorCode(), ExceptionInfoEnum.MISSION_WORK_ALLOCATION_ERROR.getMessage());
                } catch (Exception e) {
                    logger.error("Mission work allocation fault.", e);
                    missionWorkStatusFault(missionWork, ExceptionInfoEnum.MISSION_WORK_ALLOCATION_ERROR.getErrorCode(), ExceptionInfoEnum.MISSION_WORK_ALLOCATION_ERROR.getMessage());
                }
            }
        }

        /**
         * 预分配任务转成已创建
         */
        preWorkSchedulers = workSchedulerService.selectPrepare();
        for (WorkScheduler workScheduler : preWorkSchedulers) {
            WorkScheduler vehicleRunningWorkScheduler = workSchedulerService.selectRunningByVehicleId(workScheduler.getVehicleId());
            if (vehicleRunningWorkScheduler == null) {

                workScheduler.setStatus(WorkScheduler.STATUS_CREATE);
                workSchedulerService.update(workScheduler);
            }
        }
    }

    /**
     * 获取评分最近的机器人
     * 1：先根据作业参数过滤机器人。
     * 2：再获取车辆评分最高的机器人。
     *
     * @param
     * @param missionWork
     * @return
     */
    private List<Vehicle> selectVehicleByMissionWork(MissionWork missionWork, List<Vehicle> workableVehicles) {
        //获取调度配置参数
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        if (CollectionUtils.isEmpty(workableVehicles) || missionWork == null) {
            return null;
        }
        Mission mission = missionService.selectById(missionWork.getMissionId());
        if (mission == null) {
            logger.error("This mission work parent mission is not exist.");
            missionWorkStatusFault(missionWork, ExceptionInfoEnum.MISSION_WORK_NOT_MISSION.getErrorCode(), ExceptionInfoEnum.MISSION_WORK_NOT_MISSION.getMessage());
            return null;
        }
        // 指定分配
        List<Vehicle> vehicles = null;
        String agvCode = missionWork.getAgvCode();
        if (!StringUtils.isEmpty(agvCode)) {
            vehicles = workableVehicles.stream().filter((vehicle -> agvCode.equals(vehicle.getId()))).collect(Collectors.toList());
            // 如果不是指定分配则校验当前机器人是否开启自动任务分配
        } else {
            vehicles = workableVehicles.stream().filter(Vehicle::isAutoAllocation).collect(Collectors.toList());
        }
        // 按组分配
        String agvGroupId = missionWork.getAgvGroupId();
        if (!StringUtils.isEmpty(agvGroupId)) {
            vehicles = vehicles.stream().filter((vehicle -> agvGroupId.equals(vehicle.getAgvGroupId()))).collect(Collectors.toList());
        }
        // 类型分配
        String agvType = missionWork.getAgvType();
        if (!StringUtils.isEmpty(agvType)) {
            vehicles = vehicles.stream().filter((vehicle -> agvType.equals(vehicle.getAgvType()))).collect(Collectors.toList());
        }
        // 自由分配不过滤
        // 根据电量过滤。
        Double limitBatteryValue = mission.getWarningBattery();
        if (!StringUtils.isEmpty(limitBatteryValue)) {
            vehicles = vehicles.stream().filter(x -> x.getDefaultVehicleStatus() != null && x.getDefaultVehicleStatus().getBattery() != null && x.getDefaultVehicleStatus().getBattery().getBattery_value()
                    .compareTo(limitBatteryValue) >= 0).collect(Collectors.toList());
        }

        //过滤在执行乘梯任务的机器人
        vehicles = vehicles.stream().filter(vehicle -> !vehicle.isUseElevator()).collect(Collectors.toList());

        //过滤在禁止取消任务区域内执行泊车任务的机器人
        vehicles = getNotInNoCancelTaskAreaVehicles(vehicles);

        // 过滤完的车辆等于空，结束。
        if (CollectionUtils.isEmpty(vehicles)) {
            logger.error("当前没有符合执行作业:{}的机器人，请检查", missionWork.getId());
            return null;
        }
        Marker targetMarker = missionWorkService.getFirstMarker(missionWork);
        List<VehicleScope> scopes = new ArrayList<>();
        /**
         * TODO 性能优化。每个missionWork都需要比对所有的车辆。
         */
        for (Vehicle currentVehicle : vehicles) {
            try {
                MissionWork runningMissionWork = missionWorkService.selectMissionWorkByAgvCodeAndWorkingStatus(currentVehicle.getId());
                Marker runningLastMarker = null;
                if (null != runningMissionWork) {
                    runningLastMarker = missionWorkService.getLastMarker(runningMissionWork);
                }
                if (!StringUtils.isEmpty(targetMarker)) {
                    /**
                     * 如果评分返回空值
                     * 1：机器人无法导航到定位点。
                     * 2：机器人的定位信息为空。
                     * 3：机器人的电量信息为空。
                     */
                    Double distanceScope = vehicleScopeService.scopeByDistance(targetMarker.getId(), currentVehicle, runningLastMarker);
                    Double timeScope = vehicleScopeService.scopeByTime(currentVehicle, runningMissionWork);
                    Double batteryScope = vehicleScopeService.scopeByBattery(currentVehicle, schedulerConfig.getBatteryValueRatio());
                    if (distanceScope != null && timeScope != null) {
                        scopes.add(new VehicleScope(currentVehicle, distanceScope + timeScope + batteryScope));
                    }
                } else {
                    /**
                     * 如果没有目标作业点。则按电量排序分配。
                     */
                    Double batteryScope = vehicleScopeService.scopeByBattery(currentVehicle, schedulerConfig.getBatteryValueRatio());
                    Double timeScope = vehicleScopeService.scopeByTime(currentVehicle, runningMissionWork);
                    if (batteryScope != null && timeScope != null) {
                        scopes.add(new VehicleScope(currentVehicle, batteryScope + timeScope));
                    }
                }
            } catch (Exception e) {
                logger.warn("The vehicle scope error.", e);
            }
        }
        // 降序排序
        if (!CollectionUtils.isEmpty(scopes)) {
            scopes.sort((o1, o2) -> o2.getScope().compareTo(o1.getScope()));
            return scopes.stream().map(x -> x.getVehicle()).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 过滤在禁止取消任务区域内执行泊车任务的机器人
     */
    private List<Vehicle> getNotInNoCancelTaskAreaVehicles(List<Vehicle> vehicles) {
        if (CollUtil.isNotEmpty(vehicles)) {
            Set<String> markerSet = noCancelTaskAreaResourcePool.queryAllNoCancelTaskAreaMarkerIds();
            final List<Vehicle> originVehicles = vehicles;
            vehicles = vehicles.stream().filter(v -> {
                Set<String> currentMarkerIds = fullLocationService.getCurrentMarkerIdsByPosition(v.getId());
                Set<String> commonSet = CollUtil.intersectionDistinct(markerSet, currentMarkerIds);
                if (VehicleConstant.TASK_STATUS_PARK.equals(v.getWorkStatus())
                        && CollUtil.isNotEmpty(commonSet)) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            if (originVehicles.size() != vehicles.size()) {
                Set<String> before = originVehicles.stream().map(Vehicle::getId).collect(Collectors.toSet());
                Set<String> after = vehicles.stream().map(Vehicle::getId).collect(Collectors.toSet());
                Set<String> removeSet = before.stream().filter(c -> !after.contains(c)).collect(Collectors.toSet());
                logger.warn("过滤掉在禁止取消任务区域内执行泊车任务的机器人：{}", removeSet);
            }
        }
        return vehicles;
    }


    private void missionWorkStatusFault(MissionWork missionWork, Integer errorCode, String message) {
        if (missionWork != null) {
            missionWork.setStatus(MISSION_WORK_STATUS_FAULT);
            missionWork.setMessage(message);
            if (null != errorCode) {
                missionWork.setErrorCode(errorCode);
                String errorMessage = abnormalPromptService.getAbnormalMsgByCode(errorCode);
                if (StringUtils.isEmpty(errorMessage)) {
                    missionWork.setMessage(message);
                }
            }
            missionWorkService.updateStatus(missionWork, true, true);
        }
    }
}
