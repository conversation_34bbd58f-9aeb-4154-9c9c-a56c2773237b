package com.youibot.agv.scheduler.mapper;

import com.youibot.agv.scheduler.entity.MissionWorkAction;
import com.youibot.agv.scheduler.util.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface MissionWorkActionMapper extends BaseMapper<MissionWorkAction> {

    @Select("select id from mission_work_action where create_time < DATE_SUB(NOW(), INTERVAL #{missionWork} day)")
    List<String> selectExpireDataByTime(@Param("missionWork") Integer missionWork);

    @Update("update mission_work_action set status = #{status} where mission_work_id = #{missionWorkId} and status != 'SUCCESS'")
    void updateMissionWorkActionStatus(@Param("missionWorkId") String missionWorkId, @Param("status") String status);

    @Select({"<script>" +
            "select t.* " +
            "from mission_work_action t " +
            "where t.status in ('SUCCESS','FAULT','RUNNING') " +
            "<if test='agvCode!=null'> " +
                "and t.agv_code = #{agvCode} " +
            "</if> " +
            "<if test='startTime!=null and endTime!=null'> " +
                " and (" +
                        "(t.start_time <![CDATA[ >= ]]> DATE_FORMAT(#{startTime},'%Y-%m-%d %H:%i:%s') " +
                        " and t.start_time <![CDATA[ < ]]> DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s')" +
                        ") or " +
                        "(t.end_time <![CDATA[ > ]]> DATE_FORMAT(#{startTime},'%Y-%m-%d %H:%i:%s') " +
                        " and t.end_time <![CDATA[ <= ]]> DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s')" +
                        ")or " +
                        "(t.start_time <![CDATA[ <= ]]> DATE_FORMAT(#{startTime},'%Y-%m-%d %H:%i:%s') " +
                        " and IFNULL(t.end_time,now()) <![CDATA[ >= ]]> DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s')" +
                        ") " +
                ")" +
            "</if> " +
          "</script>"})
    List<MissionWorkAction> getMissionWorkActionStatistic(@Param("agvCode") String agvCode,@Param("startTime") String startTime,@Param("endTime") String endTime);
}
