package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.Notification;

import java.util.List;

/**
 * <AUTHOR>
 * @Date :Created in 下午3:01 2019/12/31
 * @Description :
 * @Modified By :
 * @Version :
 */
public interface NotificationService extends BaseService<Notification> {
    void sendMessage(Notification notification);

    void deleteExpireDataByTime(Integer notify);

    /**
     * 更新所有未读状态为已读
     */
    int updateReadStatus();

    List<String> getNotificationTypeList();
}
