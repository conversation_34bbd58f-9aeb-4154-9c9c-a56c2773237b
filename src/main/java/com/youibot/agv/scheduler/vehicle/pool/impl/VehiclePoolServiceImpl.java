package com.youibot.agv.scheduler.vehicle.pool.impl;

import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.entity.ChargeScheduler;
import com.youibot.agv.scheduler.entity.ParkScheduler;
import com.youibot.agv.scheduler.entity.SchedulerConfig;
import com.youibot.agv.scheduler.entity.WorkScheduler;
import com.youibot.agv.scheduler.service.ChargeSchedulerService;
import com.youibot.agv.scheduler.service.ParkSchedulerService;
import com.youibot.agv.scheduler.service.SchedulerConfigService;
import com.youibot.agv.scheduler.service.WorkSchedulerService;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.vo.FreeAgvAndNotFreeReasonVO;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.VehicleConstant.*;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2021-07-12 16:26
 */
@Service
public class VehiclePoolServiceImpl implements VehiclePoolService {

    @Autowired
    private DefaultVehiclePool defaultVehiclePool;

    @Autowired
    private WorkSchedulerService workSchedulerService;

    @Autowired
    private ChargeSchedulerService chargeSchedulerService;

    @Autowired
    private ParkSchedulerService parkSchedulerService;

    @Autowired
    private SchedulerConfigService schedulerConfigService;

    @Value("${scheduler.time.diff}")
    private String timeDiff;

    public List<Vehicle> getWaitWork() {
        List<Vehicle> vehicles = this.getAvailable();
        // 移除工作状态的机器人。
        vehicles = vehicles.stream().filter(x -> !VehicleConstant.TASK_STATUS_WORK.equals(x.getWorkStatus())).collect(Collectors.toList());

        // filter allocation vehicle 过滤已分配的机器人。
        List<WorkScheduler> workSchedulers = workSchedulerService.selectRunning();
        for (WorkScheduler workScheduler : workSchedulers) {
            vehicles = vehicles.stream().filter(x -> !workScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        //获取调度配置参数
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        // 移除当前低量小于最低可作业电量的机器人。
        vehicles = vehicles.stream().filter(x -> x.getBatteryValue() > schedulerConfig.getLowBatterValue()).collect(Collectors.toList());
        //过滤正在执行充电, 且已充电时间未达到最短充电时间的机器人
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            long chargeStartTime = chargeScheduler.getStartTime() != null ? chargeScheduler.getStartTime().getTime() : System.currentTimeMillis();
            long chargedTime = (System.currentTimeMillis() - chargeStartTime) / 1000;//已充电时间, 秒
            vehicles = vehicles.stream().filter(x -> !(chargeScheduler.getVehicleId().equals(x.getId())
                    && chargedTime < schedulerConfig.getMinimumChargeTime())).collect(Collectors.toList());
        }
        //过滤掉正在进行校正充电的机器人
        List<ChargeScheduler> correctChargeSchedulers = chargeSchedulerService.selectRunningCorrectCharge();
        if (!CollectionUtils.isEmpty(correctChargeSchedulers)) {
            for (ChargeScheduler correctChargeScheduler : correctChargeSchedulers) {
                vehicles = vehicles.stream().filter(x -> !(correctChargeScheduler.getVehicleId().equals(x.getId())
                        && correctChargeScheduler.getChargeType() == 1)).collect(Collectors.toList());
            }
        }

        return vehicles;
    }

    /**
     * @return
     */
    public FreeAgvAndNotFreeReasonVO getWaitWorkAndNotFreeReason() {

        List<Vehicle> vehicles = this.defaultVehiclePool.getAll();
        ArrayList<Vehicle> copyVehicles = new ArrayList<>(vehicles);

        HashMap<String, String> notFreeReason = new HashMap<>();
        if (CollectionUtils.isEmpty(vehicles)) {
            FreeAgvAndNotFreeReasonVO freeAgvAndNotFreeReasonVO = new FreeAgvAndNotFreeReasonVO();
            freeAgvAndNotFreeReasonVO.setFreeVehicles(new ArrayList<>());
            freeAgvAndNotFreeReasonVO.setNotFreeReasonMap(notFreeReason);
            return freeAgvAndNotFreeReasonVO;
        }
        // filter auto module vehicle.
        vehicles = vehicles.stream().filter(x -> AUTO_CONTROL_MODE.equals(x.getControlMode())).collect(Collectors.toList());
        copyVehicles.removeAll(vehicles);
        for (Vehicle copyVehicle : copyVehicles) {
            notFreeReason.put(copyVehicle.getId(), "机器人处于手动模式");
        }
        // filter enable status vehicle.
        vehicles = vehicles.stream().filter(x -> ENABLE.equals(x.getStatus())).collect(Collectors.toList());
        copyVehicles.removeAll(vehicles);
        for (Vehicle copyVehicle : copyVehicles) {
            notFreeReason.put(copyVehicle.getId(), "机器人已被禁用");
        }
        // filter normal status  vehicle.
        vehicles = vehicles.stream().filter(x -> ABNORMAL_STATUS_NO.equals(x.getAbnormalStatus())).collect(Collectors.toList());
        copyVehicles.removeAll(vehicles);
        for (Vehicle copyVehicle : copyVehicles) {
            notFreeReason.put(copyVehicle.getId(), "机器人处于异常状态");
        }
        // filter success location vehicle.
        vehicles = vehicles.stream().filter(x -> APPOINT_SUCCESS.equals(x.getAppointStatus())).collect(Collectors.toList());
        copyVehicles.removeAll(vehicles);
        for (Vehicle copyVehicle : copyVehicles) {
            notFreeReason.put(copyVehicle.getId(), "机器人地图未指定");
        }
        // filter online status vehicle.
        vehicles = vehicles.stream().filter(x -> ONLINE.equals(x.getOnlineStatus())).collect(Collectors.toList());
        copyVehicles.removeAll(vehicles);
        for (Vehicle copyVehicle : copyVehicles) {
            notFreeReason.put(copyVehicle.getId(), "机器人已离线");
        }
        // 过滤掉电量为空的机器人
        vehicles = vehicles.stream().filter(x -> !StringUtils.isEmpty(x.getBatteryValue())).collect(Collectors.toList());
        copyVehicles.removeAll(vehicles);
        for (Vehicle copyVehicle : copyVehicles) {
            notFreeReason.put(copyVehicle.getId(), "机器人电量为空");
        }
        // 移除工作状态的机器人。
        vehicles = vehicles.stream().filter(x -> !VehicleConstant.TASK_STATUS_WORK.equals(x.getWorkStatus())).collect(Collectors.toList());
        copyVehicles.removeAll(vehicles);
        for (Vehicle copyVehicle : copyVehicles) {
            String missionName = copyVehicle.getMissionName() == null ? "" : copyVehicle.getMissionName();
            notFreeReason.put(copyVehicle.getId(), "机器人处于任务状态，任务名称：" + missionName);
        }

        ArrayList<Vehicle> copyVehicles2 = new ArrayList<>(vehicles);
        // filter allocation vehicle 过滤已分配的机器人。
        List<WorkScheduler> workSchedulers = workSchedulerService.selectRunning();
        for (WorkScheduler workScheduler : workSchedulers) {
            vehicles = vehicles.stream().filter(x -> !workScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        //将被排除的机器人排除原因展示出来
        copyVehicles2.removeAll(vehicles);
        for (Vehicle vehicle : copyVehicles2) {
            for (WorkScheduler workScheduler : workSchedulers) {
                if (vehicle.getId().equals(workScheduler.getVehicleId())) {
                    String missionName = workScheduler.getWorkName() == null ? "" : workScheduler.getWorkName();
                    notFreeReason.put(vehicle.getId(), "机器人已经分配任务了，任务名称：" + missionName);
                }
            }
        }
        //获取调度配置参数
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        ArrayList<Vehicle> copyVehicles3 = new ArrayList<>(vehicles);
        // 移除当前低量小于最低可作业电量的机器人。
        vehicles = vehicles.stream().filter(x -> x.getBatteryValue() > schedulerConfig.getLowBatterValue()).collect(Collectors.toList());
        copyVehicles3.removeAll(vehicles);
        for (Vehicle vehicle : copyVehicles3) {
            notFreeReason.put(vehicle.getId(), "机器人电量低于配置的可作业电量，机器人当前电量：" + vehicle.getBatteryValue() + "; 配置的可作业电量：" + schedulerConfig.getLowBatterValue());
        }
        //过滤正在执行充电, 且已充电时间未达到最短充电时间的机器人
        ArrayList<Vehicle> copyVehicles4 = new ArrayList<>(vehicles);
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            long chargeStartTime = chargeScheduler.getStartTime() != null ? chargeScheduler.getStartTime().getTime() : System.currentTimeMillis();
            long chargedTime = (System.currentTimeMillis() - chargeStartTime) / 1000;//已充电时间, 秒
            vehicles = vehicles.stream().filter(x -> !(chargeScheduler.getVehicleId().equals(x.getId())
                    && chargedTime < schedulerConfig.getMinimumChargeTime())).collect(Collectors.toList());
        }
        copyVehicles4.removeAll(vehicles);
        for (Vehicle vehicle : copyVehicles4) {
            notFreeReason.put(vehicle.getId(), "机器人正在充电，并且充电时间低于配置的最短充电时间，机器人当前电量：" + vehicle.getBatteryValue());
        }
        //过滤掉正在进行校正充电的机器人
        ArrayList<Vehicle> copyVehicles5 = new ArrayList<>(vehicles);
        List<ChargeScheduler> correctChargeSchedulers = chargeSchedulerService.selectRunningCorrectCharge();
        if (!CollectionUtils.isEmpty(correctChargeSchedulers)) {
            for (ChargeScheduler correctChargeScheduler : correctChargeSchedulers) {
                vehicles = vehicles.stream().filter(x -> !(correctChargeScheduler.getVehicleId().equals(x.getId())
                        && correctChargeScheduler.getChargeType() == 1)).collect(Collectors.toList());
            }
        }
        copyVehicles5.removeAll(vehicles);
        for (Vehicle vehicle : copyVehicles5) {
            notFreeReason.put(vehicle.getId(), "机器人正在校正充电，机器人当前电量：" + vehicle.getBatteryValue());
        }
        FreeAgvAndNotFreeReasonVO freeAgvAndNotFreeReasonVO = new FreeAgvAndNotFreeReasonVO();
        freeAgvAndNotFreeReasonVO.setFreeVehicles(vehicles);
        freeAgvAndNotFreeReasonVO.setNotFreeReasonMap(notFreeReason);
        return freeAgvAndNotFreeReasonVO;
    }


    public List<Vehicle> getWaitWorkAndWorking(SchedulerConfig schedulerConfig) {
        List<Vehicle> vehicles = this.getAvailable();
        //过滤掉有预分配作业的机器人
//        List<WorkScheduler> workSchedulers = workSchedulerService.selectAllPreAllocationMissionWork();
//        for (WorkScheduler workScheduler : workSchedulers) {
//            vehicles = vehicles.stream().filter(x -> !workScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
//        }
        // 移除当前低量小于最低可作业电量的机器人。
        vehicles = vehicles.stream().filter(x -> x.getBatteryValue() > schedulerConfig.getLowBatterValue()).collect(Collectors.toList());
        //过滤正在执行充电, 且已充电时间未达到最短充电时间的机器人
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            long chargeStartTime = chargeScheduler.getStartTime() != null ? chargeScheduler.getStartTime().getTime() : System.currentTimeMillis();
            long chargedTime = (System.currentTimeMillis() - chargeStartTime) / 1000;//已充电时间, 秒
            vehicles = vehicles.stream().filter(x -> !(chargeScheduler.getVehicleId().equals(x.getId())
                    && chargedTime < schedulerConfig.getMinimumChargeTime())).collect(Collectors.toList());
        }
        //过滤掉正在进行校正充电的机器人
        List<ChargeScheduler> correctChargeSchedulers = chargeSchedulerService.selectRunningCorrectCharge();
        if (!CollectionUtils.isEmpty(correctChargeSchedulers)) {
            for (ChargeScheduler correctChargeScheduler : correctChargeSchedulers) {
                vehicles = vehicles.stream().filter(x -> !(correctChargeScheduler.getVehicleId().equals(x.getId())
                        && correctChargeScheduler.getChargeType() == 1)).collect(Collectors.toList());
            }
        }

        return vehicles;
    }


    public Vehicle selectById(String vehicleId) {
        return defaultVehiclePool.getVehicle(vehicleId);
    }

    /**
     * 获取所有的可以充电的机器人。
     *
     * @param
     * @return
     */
    public List<Vehicle> getWaitCharge() {
        List<Vehicle> vehicles = this.getAvailable();
        // 移除掉工作状态的车辆
        vehicles = vehicles.stream().filter(x -> !VehicleConstant.TASK_STATUS_WORK.equals(x.getWorkStatus())).collect(Collectors.toList());
        //过滤在执行乘梯任务的机器人
        vehicles = vehicles.stream().filter(vehicle -> !vehicle.isUseElevator()).collect(Collectors.toList());
        // 移除充电状态的车辆
        vehicles = vehicles.stream().filter(x -> !VehicleConstant.TASK_STATUS_CHARGE.equals(x.getWorkStatus())).collect(Collectors.toList());
        //移除掉十秒内取消充电的机器人
        List<String> vehicleIdList = chargeSchedulerService.selectCancelByFinishTimeDiff(timeDiff);
        if (!CollectionUtils.isEmpty(vehicleIdList)) {
            vehicles = vehicles.stream().filter(x -> !vehicleIdList.contains(x.getId())).collect(Collectors.toList());
        }
        // filter allocation vehicle 过滤已分配的机器人。
        List<WorkScheduler> workSchedulers = workSchedulerService.selectPrepareAndRunning();
        for (WorkScheduler workScheduler : workSchedulers) {
            vehicles = vehicles.stream().filter(x -> !workScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        // 过滤已分配充电的机器人。
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            vehicles = vehicles.stream().filter(x -> !chargeScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        //过滤掉关掉机器人自动充电的机器人
        vehicles = vehicles.stream().filter(Vehicle::isAutoCharge).collect(Collectors.toList());
        //获取调度配置参数
        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        // 过滤掉电量大于充电电量的。
        vehicles = vehicles.stream().filter(x -> x.getBatteryValue() <= schedulerConfig.getHighBatteryValue()).collect(Collectors.toList());
        // 过滤掉最近一次调度时间间隔小于配置的机器人。（获取机器人最后一次作业调度，充电调度，泊车调度）
        if (!CollectionUtils.isEmpty(vehicles) && schedulerConfig.getChargeSchedulerInterval() > 0) {
            Long currentTime = System.currentTimeMillis();
            Iterator<Vehicle> iterator = vehicles.iterator();
            while (iterator.hasNext()) {
                Vehicle vehicle = iterator.next();
                WorkScheduler workScheduler = workSchedulerService.selectLastOne(vehicle.getId());
                ParkScheduler parkScheduler = parkSchedulerService.selectLastOne(vehicle.getId());
                ChargeScheduler chargeScheduler = chargeSchedulerService.selectLastOne(vehicle.getId());
                Long workFinishedTime = workScheduler != null && workScheduler.getFinishTime() != null ? workScheduler.getFinishTime().getTime() : 0L;
                Long parkFinishedTime = parkScheduler != null && parkScheduler.getFinishTime() != null ? parkScheduler.getFinishTime().getTime() : 0L;
                Long chargeFinishedTime = chargeScheduler != null && chargeScheduler.getFinishTime() != null ? chargeScheduler.getFinishTime().getTime() : 0L;
                Long lastTime = Math.max(workFinishedTime, chargeFinishedTime);
                lastTime = Math.max(parkFinishedTime, lastTime);
                if (currentTime - lastTime < schedulerConfig.getChargeSchedulerInterval() * 1000) {
                    iterator.remove();
                }
            }
        }
        return vehicles;
    }

    /**
     * 获取所有需要泊车的机器人。
     *
     * @param
     * @return
     */
    public List<Vehicle> getWaitPark() {
        List<Vehicle> vehicles = this.getAvailable();
        // 过滤出空闲状态的机器人
        vehicles = vehicles.stream().filter(x -> TASK_STATUS_FREE.equals(x.getWorkStatus())).collect(Collectors.toList());
        // filter allocation vehicle 过滤已分配工作的机器人。
        List<WorkScheduler> workSchedulers = workSchedulerService.selectPrepareAndRunning();
        for (WorkScheduler workScheduler : workSchedulers) {
            vehicles = vehicles.stream().filter(x -> !workScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        // 过滤已分配充电的机器人。
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            vehicles = vehicles.stream().filter(x -> !chargeScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        // 过滤已分配泊车的机器人。
        List<ParkScheduler> parkSchedulers = parkSchedulerService.selectRunning();
        for (ParkScheduler parkScheduler : parkSchedulers) {
            vehicles = vehicles.stream().filter(x -> !parkScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        //过滤掉关掉机器人自动充电的机器人
        vehicles = vehicles.stream().filter(Vehicle::isAutoPark).collect(Collectors.toList());
        //过滤掉掉十秒内取消泊车的机器人
        List<String> vehicleIdList = parkSchedulerService.selectCancelByFinishTimeDiff(timeDiff);
        if (!CollectionUtils.isEmpty(vehicleIdList)) {
            vehicles = vehicles.stream().filter(x -> !vehicleIdList.contains(x.getId())).collect(Collectors.toList());
        }
        //过滤在执行乘梯任务的机器人
        vehicles = vehicles.stream().filter(vehicle -> !vehicle.isUseElevator()).collect(Collectors.toList());

        SchedulerConfig schedulerConfig = schedulerConfigService.selectSchedulerConfig();
        // 过滤掉最近一次调度时间间隔小于配置的机器人。（获取机器人最后一次作业调度，充电调度，泊车调度）
        if (!CollectionUtils.isEmpty(vehicles) && schedulerConfig.getParkSchedulerInterval() > 0) {
            Long currentTime = System.currentTimeMillis();
            Iterator<Vehicle> iterator = vehicles.iterator();
            while (iterator.hasNext()) {
                Vehicle vehicle = iterator.next();
                WorkScheduler workScheduler = workSchedulerService.selectLastOne(vehicle.getId());
                ParkScheduler parkScheduler = parkSchedulerService.selectLastOne(vehicle.getId());
                ChargeScheduler chargeScheduler = chargeSchedulerService.selectLastOne(vehicle.getId());
                Long workFinishedTime = workScheduler != null && workScheduler.getFinishTime() != null ? workScheduler.getFinishTime().getTime() : 0L;
                Long parkFinishedTime = parkScheduler != null && parkScheduler.getFinishTime() != null ? parkScheduler.getFinishTime().getTime() : 0L;
                Long chargeFinishedTime = chargeScheduler != null && chargeScheduler.getFinishTime() != null ? chargeScheduler.getFinishTime().getTime() : 0L;
                Long lastTime = Math.max(workFinishedTime, chargeFinishedTime);
                lastTime = Math.max(parkFinishedTime, lastTime);
                if (currentTime - lastTime < schedulerConfig.getParkSchedulerInterval() * 1000) {
                    iterator.remove();
                }
            }
        }
        return vehicles;
    }

    @Override
    public List<Vehicle> getCanDriveAway() {
        List<Vehicle> vehicles = this.getAvailable();
        // 过滤出空闲状态的机器人
        vehicles = vehicles.stream().filter(x -> TASK_STATUS_FREE.equals(x.getWorkStatus())).collect(Collectors.toList());
        // filter allocation vehicle 过滤已分配工作的机器人。
        List<WorkScheduler> workSchedulers = workSchedulerService.selectPrepareAndRunning();
        for (WorkScheduler workScheduler : workSchedulers) {
            vehicles = vehicles.stream().filter(x -> !workScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        // 过滤已分配充电的机器人。
        List<ChargeScheduler> chargeSchedulers = chargeSchedulerService.selectRunning();
        for (ChargeScheduler chargeScheduler : chargeSchedulers) {
            vehicles = vehicles.stream().filter(x -> !chargeScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        // 过滤已分配泊车的机器人。
        List<ParkScheduler> parkSchedulers = parkSchedulerService.selectRunning();
        for (ParkScheduler parkScheduler : parkSchedulers) {
            vehicles = vehicles.stream().filter(x -> !parkScheduler.getVehicleId().equals(x.getId())).collect(Collectors.toList());
        }
        //过滤掉关掉机器人自动泊车的机器人
        vehicles = vehicles.stream().filter(Vehicle::isAutoPark).collect(Collectors.toList());
        //过滤在执行乘梯任务的机器人
        return vehicles.stream().filter(vehicle -> !vehicle.isUseElevator()).collect(Collectors.toList());
    }

    private List<Vehicle> getAvailable() {
        List<Vehicle> vehicles = this.defaultVehiclePool.getAll();
        if (CollectionUtils.isEmpty(vehicles)) {
            return vehicles;
        }
        // filter auto module vehicle.
        vehicles = vehicles.stream().filter(x -> AUTO_CONTROL_MODE.equals(x.getControlMode())).collect(Collectors.toList());
        // filter enable status vehicle.
        vehicles = vehicles.stream().filter(x -> ENABLE.equals(x.getStatus())).collect(Collectors.toList());
        // filter normal status  vehicle.
        vehicles = vehicles.stream().filter(x -> ABNORMAL_STATUS_NO.equals(x.getAbnormalStatus())).collect(Collectors.toList());
        // filter success location vehicle.
        vehicles = vehicles.stream().filter(x -> APPOINT_SUCCESS.equals(x.getAppointStatus())).collect(Collectors.toList());
        // filter online status vehicle.
        vehicles = vehicles.stream().filter(x -> ONLINE.equals(x.getOnlineStatus())).collect(Collectors.toList());
        // 过滤掉电量为空的机器人
        vehicles = vehicles.stream().filter(x -> !StringUtils.isEmpty(x.getBatteryValue())).collect(Collectors.toList());
        // return all available vehicle.
        return vehicles;
    }

    @Override
    public void updateVehicle(Vehicle vehicle) {
        defaultVehiclePool.attachVehicle(vehicle);
    }

    @Override
    public List<Vehicle> selectAll() {
        return this.defaultVehiclePool.getAll();
    }
}
