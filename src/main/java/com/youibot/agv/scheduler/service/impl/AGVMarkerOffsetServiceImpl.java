package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.entity.AGVMarkerOffset;
import com.youibot.agv.scheduler.mapper.AGVMarkerOffsetMapper;
import com.youibot.agv.scheduler.service.AGVMarkerOffsetService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class AGVMarkerOffsetServiceImpl extends BaseServiceImpl<AGVMarkerOffset> implements AGVMarkerOffsetService  {

    @Resource
    private AGVMarkerOffsetMapper agvMarkerOffsetMapper;

    @Override
    public AGVMarkerOffset findByAGVCodeAndMarkerId(String agvCode, String markerId) {
        return agvMarkerOffsetMapper.findByAGVCodeAndMarkerId(agvCode,markerId);
    }

}
