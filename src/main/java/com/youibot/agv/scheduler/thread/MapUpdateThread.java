package com.youibot.agv.scheduler.thread;

import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger;
import com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.ResourcePool.ElevatorPathResourcePool;
import com.youibot.agv.scheduler.engine.util.AGVPropertiesUtils;
import com.youibot.agv.scheduler.entity.AGVMapUpdateQueue;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.AGVMapMd5;
import com.youibot.agv.scheduler.map.entity.MapGraphInfo;
import com.youibot.agv.scheduler.map.entity.PathMd5;
import com.youibot.agv.scheduler.map.utils.MapFileUtils;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.AGVMapUpdateQueueService;
import com.youibot.agv.scheduler.util.CommonUtils;
import com.youibot.agv.scheduler.util.DateUtils;
import com.youibot.agv.scheduler.util.FtpUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.MD5_SUFFIX;
import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.ROAD_NETWORK_SUFFIX;
import static com.youibot.agv.scheduler.map.utils.MapFileUtils.*;

/**
 * 地图数据更新线程
 * 将 tmp 目录的 地图文件 转到 current 目录
 *
 * <AUTHOR>
 * @date 2020/9/8 16:10
 */
@Service
@ConditionalOnBean(name = "AGVPropertiesConfig")
public class MapUpdateThread extends Thread {
    private static final Logger LOGGER = LoggerFactory.getLogger(MapUpdateThread.class);

    @Autowired
    private AGVMapUpdateQueueService agvMapUpdateQueueService;
    @Autowired
    private DefaultVehiclePool defaultVehiclePool;
    @Autowired
    private CheckAndSendPathService checkAndSendPathService;
    @Autowired
    private ElevatorPathResourcePool elevatorResourcePool;
    @Autowired
    private AGVMapService agvMapService;

    @Value("${MAP_FILE_PATH.MAP_INFO_FILE}")
    private String mapParentPath;
    @Value("${MAP_FILE_PATH.PATH_FILE}")
    private String roadNetworkPath;
    @Value("${MAP_FILE_PATH.LOCATION_FILE}")
    private String locationPath;

    private static final String PATH_NAME = "path";
    private static final String LOCATION_NAME = "locating";
    //保持多线程可见, 地图更新时记录的,使用电梯的小车
    public static volatile ConcurrentHashMap<String, Set<String>> ElevatorVehicle = new ConcurrentHashMap();

    @Override
    public void run() {
        Thread.currentThread().setName("MapUpdateThread");
        Long time_interval = AGVPropertiesUtils.getLong("MAP_UPDATE.QUEUE_LOOP_INTERVAL", 1L);

        //1、获取ftpclient
        FTPClient ftpClient = null;
        while (true) {
            try {
                //间隔time_interval秒再处理
                TimeUnit.SECONDS.sleep(time_interval);
                //ftpClient保持长连接
                if (ftpClient == null || !ftpClient.isConnected()) {
                    FtpUtils.disconnect(ftpClient);
                    ftpClient = FtpUtils.getConnectClient();
                }
                //没用更新任务就跳过
                AGVMapUpdateQueue agvMapUpdateQueue = agvMapUpdateQueueService.selectLastestOne();
                if (agvMapUpdateQueue == null) {
                    continue;
                }
                try {
                    this.updateAgvMapUpdateQueueStatus(agvMapUpdateQueue, AGVMapUpdateQueue.STATUS_RUNNING);
                    this.handleMapDataByType(ftpClient, agvMapUpdateQueue);
                    this.updateAgvMapUpdateQueueStatus(agvMapUpdateQueue, AGVMapUpdateQueue.STATUS_FINISH);
                } catch (Exception e) {
                    if(e instanceof IOException){
                        ftpClient = null;
                    }
                    this.updateAgvMapUpdateQueueStatus(agvMapUpdateQueue, AGVMapUpdateQueue.STATUS_FINISH);
                    LOGGER.error("handler error：", e);
                }
            } catch (Exception e) {
                LOGGER.error("MapUpdateThread error： ", e);
            }
        }
    }

    private void updateAgvMapUpdateQueueStatus(AGVMapUpdateQueue agvMapUpdateQueue, String status) {
        agvMapUpdateQueue.setStatus(status);
        agvMapUpdateQueueService.update(agvMapUpdateQueue);
    }

    public void handleMapDataByType(FTPClient ftpClient, AGVMapUpdateQueue log) throws Exception {
        String type = log.getType();
        switch (type) {
            //用户在浏览器，手动点击发布触发
            case "draft_2_current":
                transMapDataFromDraftToCurrent(ftpClient, log);
                break;
            case "current_2_memory":
                transMapDataFromCurrentToMemory(ftpClient);
                break;
            //单独一个线程，不断往数据库插入数据
            case "tmp_2_current":
                transMapDataFromTmpToCurrent(ftpClient);
                break;
            default:
        }
    }

    //将 current 目录的 地图文件 转到 memory
    public void transMapDataFromCurrentToMemory(FTPClient ftpClient) throws Exception {
        Map<String, MapGraphInfo> agvMapInfoCache = AGVMapInfoCache.getInstance();
        ftpClient.changeWorkingDirectory(mapParentPath);
        FTPFile[] ftpFiles = ftpClient.listFiles();

        //查询当前内存中数据是否为空，为空则将文件中所有文件添加到内存中
        if (CollectionUtils.isEmpty(agvMapInfoCache)) {
            MapFileUtils.getMapGraphs(ftpClient, mapParentPath);
        } else {
            //遍历地图文件，查询未添加到内存中的地图，或者是修改过的地图文件添加到内存中
            for (FTPFile file : ftpFiles) {
                //查询内存中是否存在当前这张地图，没有则添加
                if ("tmp".equals(file.getName())) {
                    continue;
                }
                try {
                    if (!agvMapInfoCache.containsKey(file.getName())) {
                        MapGraphInfo mapGraph = MapFileUtils.getMapGraph(ftpClient, mapParentPath + file.getName(), new MapGraphInfo());
                        if (mapGraph != null && mapGraph.getAgvMap() != null) {
                            AGVMapInfoCache.setCache(file.getName(), mapGraph);
                            MapGraphUtil.addAGVMap(file.getName());
                        }
                        //如果当前地图存在，则检查当前的path数据文件和locating数据文件是否有变更，路网文件变更则重新添加导航内存数据
                    } else {
                        boolean flag = false;
                        PathMd5 pathMd5 = JSONObject.parseObject(MapFileUtils.getMD5File(ftpClient, file.getName(), PATH_NAME), PathMd5.class);
                        MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(file.getName());
                        //校验路网MD5值，查询是否有变更的数据
                        if ((pathMd5 != null && pathMd5.getPathMd5() != null) && (mapGraphInfo.getPathMd5() == null || (mapGraphInfo.getPathMd5().getPathMd5() == null || !pathMd5.getPathMd5().equals(mapGraphInfo.getPathMd5().getPathMd5())))) {
                            String pathFile = mapParentPath + file.getName() + roadNetworkPath;
                            MapGraphInfo pathFileData = MapFileUtils.getMapGraph(ftpClient, pathFile, new MapGraphInfo());
                            if (pathFileData != null) {
                                flag = true;
                                mapGraphInfo.setPathMd5(pathFileData.getPathMd5());
                                mapGraphInfo.setPaths(pathFileData.getPaths());
                                mapGraphInfo.setDataCodeMatrices(pathFileData.getDataCodeMatrices());
                                mapGraphInfo.setMapAreas(pathFileData.getMapAreas());
                                mapGraphInfo.setMarkers(pathFileData.getMarkers());
                                mapGraphInfo.setSidePaths(pathFileData.getSidePaths());
                                mapGraphInfo.setAdjustActions(pathFileData.getAdjustActions());
                                mapGraphInfo.setLastUpdateTime(pathFileData.getLastUpdateTime());
                            }
                        }
                        AGVMapMd5 agvMapMd5 = JSONObject.parseObject(MapFileUtils.getMD5File(ftpClient, file.getName(), LOCATION_NAME), AGVMapMd5.class);
                        AGVMapMd5 agvMapMd5Cache = mapGraphInfo.getAgvMapMd5();
                        if (agvMapMd5 == null || agvMapMd5.getInfoMd5() == null) {
                            LOGGER.warn("{}地图info文件为空", file.getName());
                            continue;
                        }

                        if (!agvMapMd5.getInfoMd5().equals(agvMapMd5Cache.getInfoMd5())) {
                            String pathFile = mapParentPath + file.getName() + locationPath;
                            MapGraphInfo mapInfoData = MapFileUtils.getMapGraph(ftpClient, pathFile, new MapGraphInfo());
                            if (mapInfoData != null) {
                                mapGraphInfo.setAgvMapMd5(mapInfoData.getAgvMapMd5());
                                mapGraphInfo.setAgvMap(mapInfoData.getAgvMap());
                                mapGraphInfo.setLastUpdateTime(mapInfoData.getLastUpdateTime());
                            }
                        }
                        AGVMapInfoCache.setCache(file.getName(), mapGraphInfo);
                        if (flag) {
                            doPushMap(file.getName());
                            // 同步仿真系统地图
                            if (mapGraphInfo.getAgvMap() != null) {
                                agvMapService.syncSimulationAgvMap(mapGraphInfo.getAgvMap());
                            }
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("地图{}，更新内存数据异常：", file.getName(), e);
                }
            }
        }
    }

    //资源占用情况：
    //1、地图发布之前，如果有电梯占用，则发布完成后，根据是否在用，决定是否重新占用
    //2、自动门、风淋门，在地图发布后、定位生成后，会自动处理开门关门逻辑，不用管
    //3、小车的定位自动生成后，需要将占用的资源(T字路、一字路、单机区域)重新占用，然后再开启路径规划
    private void doPushMap(String mapName) {
        //此处先将需要处理的地图名称加入set中，让路径规划跳过当前地图
        //当前地图的所有的小车的路径规划，都不处理
        PathPlanManger.waitUpdateMapSet.add(mapName);

        //删除地图时，不会将当前地图上所有的小车的资源占用清空
        MapGraphUtil.addAGVMap(mapName);

        // 取消在当前地图上进行路径导航的小车的路径导航，等待地图发布完成后，重新规划
        List<Vehicle> unMoveVehicles = checkAndSendPathService.getUnMoveVehicles(mapName);

        //电梯占用恢复
        if (!CollectionUtils.isEmpty(ElevatorVehicle)) {
            ElevatorVehicle.forEach((agvCode, sets) -> {
                Vehicle vehicle = defaultVehiclePool.getVehicle(agvCode);
                if (vehicle.isUseElevator() && !CollectionUtils.isEmpty(sets)) {
                    Set<String> resourceIds = elevatorResourcePool.queryElevatorResourceIdsByMarkerIds(new LinkedList<String>(sets));
                    elevatorResourcePool.applyPathResources(agvCode, resourceIds);
                }
            });
            ElevatorVehicle.clear();
        }

        //重新申请资源
        if (!CollectionUtils.isEmpty(unMoveVehicles)) {
            unMoveVehicles.forEach(vehicle -> {
                checkAndSendPathService.reApplyResource(vehicle);
            });
        }

        //等待1s，防止刚释放地图，定位线程(500ms)还没计算定位，规划线程立马开始规划路径，从而导致脱轨
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        //删除
        PathPlanManger.waitUpdateMapSet.remove(mapName);
    }

    //将 tmp 目录的 地图文件 转到 current 目录
    public void transMapDataFromTmpToCurrent(FTPClient ftpClient) throws Exception {
        //1、校验当前目录tmp的合法性
        //2、将current目录打包zip存入back
        //3、将current删除
        //4、将tmp 存入 current
        //5、将tmp删除

        //1、切换到  /home/<USER>/map
        boolean changeWorkingDirectory = ftpClient.changeWorkingDirectory(FtpUtils.server_path);
        if (!changeWorkingDirectory) {
            throw new RuntimeException("change dir failed!");
        }

        //2、遍历所有的地图
        FTPFile[] ftpFiles = ftpClient.listDirectories();
        if (ftpFiles == null || ftpFiles.length <= 0) {
            return;
        }
        for (FTPFile ftpFile : ftpFiles) {
            //过滤掉tmp目录为空的地图
            if (ftpFile.getName().equals("tmp")) {
                continue;
            }
            String locationTmpFile = MapFileUtils.MAP_INFO_PATH + ftpFile.getName() + LOCATION_TMP_PATH;
            FTPFile[] locationTmpFiles = ftpClient.listFiles(locationTmpFile);
            if (locationTmpFiles.length == 0) {
                continue;
            }
            //处理 locating
            handleLocatingTmp(ftpClient, ftpFile, "/locating");

            //处理 path
            handleLocatingTmp(ftpClient, ftpFile, "/path");
        }
    }

    //校验通过后，将目录 打包成zip 存放到back 目录  将tmp拷贝到current
    private void handleLocatingTmp(FTPClient ftpClient, FTPFile ftpFile, String prePath) throws Exception {
        //1、校验合法性
        String filePath = MapFileUtils.MAP_INFO_PATH + ftpFile.getName() + prePath;
        String mapName = ftpFile.getName();
        LOGGER.debug("开始比较地图：{}文件数据", mapName);
        if ("/locating".equalsIgnoreCase(prePath)) {
            if (!CommonUtils.checkValidOfLocating(ftpClient, mapName, filePath + "/tmp")) {
                return;
            }
        } else if ("/path".equalsIgnoreCase(prePath)) {
            if (!CommonUtils.checkValidOfPath(ftpClient, mapName, filePath + "/tmp")) {
                return;
            }
        }

        LOGGER.debug("校验通过，开始迁移目录，path：{}", filePath  + "/tmp");

        //2、压缩locating /locating/current，存入/locating/back/xxx.zip
        String locating_current = MapFileUtils.MAP_INFO_PATH + ftpFile.getName() + prePath + "/current";
        byte[] lo_bytes = CommonUtils.compressDirToZip(ftpClient, MapFileUtils.MAP_INFO_PATH, locating_current, null);
        String backDir = MapFileUtils.MAP_INFO_PATH + ftpFile.getName() + prePath + "/back/";
        if (!ftpClient.changeWorkingDirectory(backDir)) {
            FtpUtils.CreateDirectory(ftpClient, backDir);
        }
        String lo_backDir = backDir + DateUtils.getFullDate(new Date()) + ".zip";
        boolean storeFile = ftpClient.storeFile(lo_backDir, new ByteArrayInputStream(lo_bytes));
        if (!storeFile) {
            LOGGER.error("store file failed!  file name is " + lo_backDir);
            throw new ExecuteException(MessageUtils.getMessage("service.save_ftp_file_fail"));
        }

        LOGGER.debug("开始删除current目录");
        //3、删除对应地图的current目录
        String locatingTmp = ftpFile.getName() + prePath + "/current/*";
        MapFileUtils.deleteFiles(MapFileUtils.MAP_INFO_PATH + locatingTmp, ftpClient);

        LOGGER.debug("copy tmp目录文件至current");
        //4、拷贝
        String lo_src = MapFileUtils.MAP_INFO_PATH + ftpFile.getName() + prePath + "/tmp/";
        String lo_dest = MapFileUtils.MAP_INFO_PATH + ftpFile.getName() + prePath + "/current/";
        CommonUtils.copyFolder(ftpClient, lo_src, lo_dest);

        LOGGER.debug("删除tmp目录");
        //5、删除
        String locatingTmp1 = ftpFile.getName() + prePath + "/tmp/*";
        MapFileUtils.deleteFiles(MapFileUtils.MAP_INFO_PATH + locatingTmp1, ftpClient);
    }

    //将 草稿 目录的 地图文件 转到 current 目录
    public void transMapDataFromDraftToCurrent(FTPClient ftpClient, AGVMapUpdateQueue agvMapUpdateQueue) throws Exception {
        String mapName = agvMapUpdateQueue.getMapName();
        //获取草稿文件地址
        AGVMapInfoCache.getCache(agvMapUpdateQueue.getMapName()).setLastUpdateTime(System.currentTimeMillis());
        String draftFile = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, mapName, true);
        //获取草稿文件父级目录
        String remoteDirectory = draftFile.substring(0, draftFile.lastIndexOf("/"));
        //切换到文件父级文件目录
        boolean changeWorkingDirectory = ftpClient.changeWorkingDirectory(remoteDirectory);
        if (!changeWorkingDirectory) {
            LOGGER.error("地图{}，草稿数据转正式数据，切换目录异常：{}", mapName, remoteDirectory);
            return;
        }

        //获取目标文件地址
        String currentPathFile = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, mapName, false);
        String remoteFile = currentPathFile.substring(0, currentPathFile.lastIndexOf("/") + 1);
        MapFileUtils.copyFolder(draftFile, remoteFile, ftpClient);
        //生成md5文件
        if (ftpClient.changeWorkingDirectory(remoteFile)) {
            FTPFile[] ftpFiles = ftpClient.listFiles();
            for (FTPFile ftpFile : ftpFiles) {
                if (ftpFile.getName().contains(MD5_SUFFIX)) {
                    continue;
                }
                InputStream inputStream = ftpClient.retrieveFileStream(ftpFile.getName());
                if (inputStream != null) {
                    String md5ByFilePath = DigestUtils.md5DigestAsHex(inputStream);
                    ftpClient.completePendingCommand();
                    inputStream.close();
                    PathMd5 pathMd5 = new PathMd5();
                    pathMd5.setPathMd5(md5ByFilePath);
                    String md5FilePath = MAP_INFO_PATH + mapName + ROAD_NETWORK_PATH + mapName + ".md5";
                    MapFileUtils.writeValue(JSONObject.toJSONString(pathMd5), md5FilePath, ftpClient);
                }
            }
        }
        MapFileUtils.deleteFiles(remoteDirectory, ftpClient);
    }
}
