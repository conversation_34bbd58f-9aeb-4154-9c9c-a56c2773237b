package com.youibot.agv.scheduler.constant;

/**
 * <AUTHOR>
 * @Date :Created in 下午2:10 2020/8/19
 * @Description :
 * @Modified By :
 * @Version :
 */
public class PathPlanConstant {

    public final static String PATH_PLAN_TYPE_NORMAL = "PATH_PLAN_TYPE_NORMAL";
    public final static String PATH_PLAN_TYPE_IN_ORIGIN_GRAPH = "PATH_PLAN_TYPE_IN_ORIGIN_GRAPH";
    public final static String PATH_PLAN_TYPE_PLAN_NEAR = "PATH_PLAN_TYPE_PLAN_NEAR";

    public final static String PLUS_WEIGHT = "PATH_WEIGHT_PLUS";
    public final static String MINUS_WEIGHT = "PATH_WEIGHT_MINUS";
    public final static String MULTIPLY_WEIGHT = "PATH_WEIGHT_MULTIPLY";
    public final static String RESET_WEIGHT = "PATH_WEIGHT_RESET";
    public final static String SET_WEIGHT = "PATH_WEIGHT_SET";

    public final static String SINGLE_AGV_AREA = "SINGLE_AGV_AREA";//单机区域
    public final static String CHANNEL_AREA = "CHANNEL_AREA";//通道区域
    public final static String NO_CANCEL_TASK_AREA = "NO_CANCEL_TASK_AREA";//禁止取消任务区域
    public static final String MAP_AREA_TYPE_TRAFFIC = "TrafficArea";//交管区域

    public final static String ALL_VEHICLE = "ALL_VEHICLE";

    public final static Integer FAIL = 0;
    public final static Integer SUCCESS = 1;

    //路径导航中路径的导航类型
    public static final int SIDE_PATH_NAVIGATION_TYPE_NORMAL = 0;           //正常导航
    public static final int SIDE_PATH_NAVIGATION_TYPE_ADJUSTMENT = 1;       //方向调整+导航
    public static final int SIDE_PATH_NAVIGATION_TYPE_ENTER_ELEVATOR = 2;   //进入电梯
    public static final int SIDE_PATH_NAVIGATION_TYPE_OUT_ELEVATOR = 3;     //出来电梯
    public static final int SIDE_PATH_NAVIGATION_TYPE_TAKE_ELEVATOR = 4;    //乘坐电梯（包含切换地图）

    //路径导航中与切换地图点相邻的点的类型
    public static final Integer MARKER_NAVIGATION_TYPE_NORMAL = 0;          //正常点
    public static final Integer MARKER_NAVIGATION_TYPE_IN_OUT = 1;          //进出电梯点
    public static final Integer MARKER_NAVIGATION_TYPE_ELEVATOR = 2;        //电梯点
    public static final Integer MARKER_NAVIGATION_TYPE_ADJUST = 3;          //调整点
    public static final Integer MARKER_NAVIGATION_TYPE_ADJUST_DEST = 4;     //调整目标点

    //当missionWorkId为以下值时，直接清理路径
    public static final String PATH_PLAN_MISSION_WORK_ID = "_PATH_PLAN_";

    //下发路径类型
    public static final Integer SEND_SIDE_PATH_TYPE_SEND_PATH = 1;//下发路径
    public static final Integer SEND_SIDE_PATH_TYPE_COMPLETE_NOTICE = 2;//完成通知
}
