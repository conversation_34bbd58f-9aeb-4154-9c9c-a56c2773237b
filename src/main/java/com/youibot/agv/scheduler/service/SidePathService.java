package com.youibot.agv.scheduler.service;

import com.youibot.agv.scheduler.entity.SidePath;
import com.youibot.agv.scheduler.entity.vo.SidePathVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SidePathService {

    List<SidePath> selectByAGVMapId(String agvMapId, boolean isDraft);

    List<SidePath> selectByAGVMapIds(List<String> agvMapIds, boolean isDraft);

    List<SidePath> selectByAGVMapIdAndUsageStatus(String agvMapId, String usageStatus, boolean isDraft);

    Map<String, List<SidePath>> mapByAgvMapName(List<String> agvMapList, boolean isDraft);

    List<SidePath> selectByPathId(String id, String agvMapName, boolean isDraft);

    List<SidePath> selectByPathIds(Set<String> pathIds, String agvMapName, boolean isDraft);

    SidePath selectById(String agvMapName, String id, boolean isDraft);

    List<SidePath> searchAll(Map<String, String> searchMap, boolean isDraft);

    SidePath selectByAGVMapNameAndStopMarker(String agvMapId, String startMarker, String endMarker, boolean isDraft);

    void batchUpdate(String agvMapName, List<SidePath> sidePaths, boolean isDraft);

    List<SidePathVo> selectSidePathVosByPathId(String pathId, String agvMapName, boolean isDraft);

    Map<String,List<SidePathVo>> selectSidePathVosByPathIdList(List<String> pathIdList, String agvMapName, boolean isDraft);
}
