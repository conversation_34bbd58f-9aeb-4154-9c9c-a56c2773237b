package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.entity.AGVMap;
import com.youibot.agv.scheduler.entity.Agv;
import com.youibot.agv.scheduler.entity.MissionWork;
import com.youibot.agv.scheduler.entity.VehicleData;
import com.youibot.agv.scheduler.entity.vo.AGVBindMarkerVo;
import com.youibot.agv.scheduler.entity.vo.AGVConfigVo;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.exception.YOUIFleetException;
import com.youibot.agv.scheduler.param.DefaultVehicleStatusParam;
import com.youibot.agv.scheduler.service.AGVMapService;
import com.youibot.agv.scheduler.service.AGVService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.service.MissionWorkService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.entity.DefaultVehicleStatus;
import com.youibot.agv.scheduler.vehicle.entity.vo.FreeAgvAndNotFreeReasonVO;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import com.youibot.agv.scheduler.vehicle.pool.impl.DefaultVehiclePool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_WAIT;
import static com.youibot.agv.scheduler.constant.MissionConstant.MISSION_WORK_STATUS_WAIT_INPUT;

@RestController("vehicleControllerV3")
@RequestMapping(value = "/api/v3/vehicles", produces = "application/json")
@Api(value = "机器人", tags = "机器人", description = "机器人的实例对象，所有的连接成功的机器人都是通过这个对象进行操作。")
public class VehicleController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(VehicleController.class);

    @Autowired
    private DefaultVehiclePool vehiclePool;

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private MissionWorkService missionWorkService;

    @Autowired
    private AGVMapService agvMapService;

    @Autowired
    private MarkerService markerService;

    @Autowired
    private AGVService agvService;

    private static final Integer NOT = 0;

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<VehicleData> list() {
        List<Vehicle> vehicles = vehiclePool.getAll();
        List<VehicleData> vehicleDataList = new ArrayList<>();
        for (Vehicle vehicle : vehicles) {
            vehicleDataList.add(new VehicleData(vehicle));
        }
        return vehicleDataList;
    }

    @ApiOperation(value = "可用的机器人列表")
    @GetMapping("/free")
    @ResponseStatus(value = HttpStatus.OK)
    public List<VehicleData> freeVehicleList() {
        //2021.5.8 TMS需要提供空闲（可用）的机器人列表
        List<Vehicle> vehicles = vehiclePoolService.getWaitWork();
        List<VehicleData> vehicleDataList = new ArrayList<>();
        for (Vehicle vehicle : vehicles) {
            vehicleDataList.add(new VehicleData(vehicle));
        }
        return vehicleDataList;
    }

    @ApiOperation(value = "获取可工作的机器人和不可工作的机器人及其不可工作的原因")
    @GetMapping("/freeAndNotFreeReason")
    @ResponseStatus(value = HttpStatus.OK)
    public FreeAgvAndNotFreeReasonVO freeAndNotFreeReason() {
        //2021.5.8 TMS需要提供空闲（可用）的机器人列表
//        List<Vehicle> vehicles = vehiclePoolService.getWaitWork();
        FreeAgvAndNotFreeReasonVO waitWorkAndNotFreeReason = vehiclePoolService.getWaitWorkAndNotFreeReason();
        List<Vehicle> vehicles = waitWorkAndNotFreeReason.getFreeVehicles();
        List<VehicleData> vehicleDataList = new ArrayList<>();
        for (Vehicle vehicle : vehicles) {
            vehicleDataList.add(new VehicleData(vehicle));
        }
        waitWorkAndNotFreeReason.setFreeVehicleDataList(vehicleDataList);
        return waitWorkAndNotFreeReason;
    }

    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{agvCode:.+}")//:.+ 适配参数传入.的形式
    @ResponseStatus(value = HttpStatus.OK)
    public VehicleData get(@PathVariable("agvCode") String agvCode) {
        Vehicle vehicle = this.vehiclePool.getVehicle(agvCode);
        if (vehicle == null) {
            throw new YOUIFleetException(MessageUtils.getMessage("http.agv_not_login"));
        }
        //机器人详情,当前站点ID显示站点code
        if (vehicle.getDefaultVehicleStatus() != null && vehicle.getDefaultVehicleStatus().getPosition() != null
                && !StringUtils.isEmpty(vehicle.getDefaultVehicleStatus().getPosition().getPos_current_station())) {
            String posCurrentStationId = vehicle.getDefaultVehicleStatus().getPosition().getPos_current_station();
            DefaultVehicleStatus.PositionStatus position = vehicle.getDefaultVehicleStatus().getPosition();
            if (!StringUtils.isEmpty(vehicle.getAgvMapId())) {
                Optional.ofNullable(markerService.selectById(vehicle.getAgvMapId(), posCurrentStationId, false)).ifPresent(x -> position.setPos_current_station_code(x.getCode()));
            }
        }
        return new VehicleData(vehicle);
    }

    @ApiOperation(value = "获取AGV当前使用地图")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @GetMapping(value = "/{agvCode}/agvMap")
    @ResponseStatus(value = HttpStatus.OK)
    public AGVMap getMapData(@PathVariable("agvCode") String agvCode) {
        Vehicle vehicle = this.vehiclePool.getVehicle(agvCode);
        if (vehicle != null || !StringUtils.isEmpty(vehicle.getAgvMapId())) {
            return agvMapService.selectById(vehicle.getAgvMapId());
        }
        return null;
    }

//    @ApiOperation(value = "自由导航到指定坐标")
//    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
//    @PostMapping(value = "/{agvCode}/moveToPosition")
//    @ResponseStatus(value = HttpStatus.OK)
//    public void moveToPosition(@PathVariable("agvCode") String agvCode, @RequestBody MoveToPositionParam param) throws IOException {
//    }

    @ApiOperation(value = "继续执行任务")
    @PostMapping(value = "/missionWork/{missionWorkId}/continue")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation("继续执行任务")
    public void workContinue(@PathVariable("missionWorkId") String missionWorkId) {
        MissionWork missionWork = missionWorkService.selectById(missionWorkId);
        String agvCode = missionWork.getAgvCode();
        if (missionWork == null || StringUtils.isEmpty(agvCode)) {
            throw new YOUIFleetException(MessageUtils.getMessage("service.mission_is_null"));
        }
        Vehicle vehicle = super.getVehicle(agvCode);
//        MissionWork missionWork = vehicle.getMissionWork();
        if (missionWork == null) {
            logger.error("agv has no execute mission work now, agvCode: " + agvCode);
            throw new ExecuteException(MessageUtils.getMessage("http.agv_has_no_execute_work"));
        }
        if (!MISSION_WORK_STATUS_WAIT.equals(missionWork.getStatus()) && !MISSION_WORK_STATUS_WAIT_INPUT.equals(missionWork.getStatus())) {
            logger.error("mission work status is not wait or wait input, agvCode: " + agvCode);
            throw new ExecuteException(MessageUtils.getMessage("http.mission_work_status_is_not_wait_or_wait_input"));
        }

        ThreadContext.put("ROUTINGKEY", agvCode);
        this.getVehicle(agvCode).continueMissionWork(missionWorkId);
        logger.debug("agvCode:[{}],event:[继续执行任务],任务数据：[{}]", agvCode, JSONObject.toJSON(missionWork));
        ThreadContext.remove("ROUTINGKEY");
    }

    @LogOperation("AGV重启")
    @ApiOperation(value = "AGV重启")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{agvCode}/reboot")
    @ResponseStatus(value = HttpStatus.OK)
    public void reboot(@PathVariable("agvCode") String agvCode) throws IOException {
        ThreadContext.put("ROUTINGKEY", agvCode);
        this.getVehicle(agvCode).agvRestart();
        logger.debug("agvCode:[{}],event:[AGV重启],content:[{}]", agvCode, "指令下发完成");
        ThreadContext.remove("ROUTINGKEY");
    }

    @ApiOperation(value = "AGV一键恢复(充电异常恢复、归位异常恢复、任务异常恢复)")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{agvCode}/oneKeyResume")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation("AGV一键恢复")
    public void oneKeyResume(@PathVariable("agvCode") String agvCode) {

        ThreadContext.put("ROUTINGKEY", agvCode);
        this.getVehicle(agvCode).oneKeyResume();
        logger.debug("agvCode:[{}],event:[一键恢复],content:[{}]", agvCode, "指令下发完成");
        ThreadContext.remove("ROUTINGKEY");
    }

    @ApiOperation(value = "AGV一键停止(停止充电动作、归位动作、作业)")
    @ApiImplicitParam(name = "agvCode", value = "Agv 编号", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{agvCode}/oneKeyStop")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation("AGV一键停止")
    public void oneKeyStop(@PathVariable("agvCode") String agvCode) {

        ThreadContext.put("ROUTINGKEY", agvCode);
        this.getVehicle(agvCode).oneKeyStop();
        logger.debug("agvCode:[{}],event:[一键停止],content:[{}]", agvCode, "指令下发完成");
        ThreadContext.remove("ROUTINGKEY");
    }

    @ApiOperation(value = "AGV一键重置(清错充电异常、归位异常、工作异常)")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{agvCode}/oneKeyReset")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation("AGV一键重置")
    public void oneKeyReset(@PathVariable("agvCode") String agvCode) {

        ThreadContext.put("ROUTINGKEY", agvCode);
        this.getVehicle(agvCode).oneKeyReset();
        logger.debug("agvCode:[{}],event:[一键重置],content:[{}]", agvCode, "指令下发完成");
        ThreadContext.remove("ROUTINGKEY");
    }

    @ApiOperation(value = "AGV一键复位")
    @ApiImplicitParam(name = "agvCode", value = "Agv ID", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{agvCode}/oneKeyResetLocation")
    @ResponseStatus(value = HttpStatus.OK)
    public void oneKeyResetLocation(@PathVariable("agvCode") String agvCode) {
        this.getVehicle(agvCode).oneKeyResetLocation();
    }

//    @ApiOperation(value = "批量同步地图")
//    @ApiImplicitParam(name = "agvCodeList", value = "agvCode列表", dataType = "String", required = true)
//    @PostMapping("/sync/youibot_map/batch")
//    @ResponseStatus(value = HttpStatus.OK)
//    public void syncMapBatch(@RequestBody List<String> agvCodeList) {
//        if (CollectionUtils.isEmpty(agvCodeList)) {
//            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
//        }
//
//        List<AGVMap> agvMaps = agvMapService.getEnableAGVMap();
//        if (CollectionUtils.isEmpty(agvMaps)) {
//            throw new YOUIFleetException(MessageUtils.getMessage("http.have_no_enable_map"));
//        }
//
//        List<Vehicle> vehicleList = agvCodeList.stream().map(super::getVehicle).collect(Collectors.toList());
//        vehicleList = vehicleList.stream().filter(x -> NOT.equals(x.getMapStatus())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(vehicleList)) {
//            throw new ADSParameterException(MessageUtils.getMessage("vehicle.already_sync"));
//        }
//        agvMapService.batchSyncMaps(agvCodeList);
//    }


    @ApiOperation(value = "批量指定地图")
    @ApiImplicitParam(name = "agvCodeList", value = "agvCode列表", dataType = "String", required = true)
    @PostMapping("/{mapId}/batch")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation("批量指定地图")
    public void appointMapBatch(@PathVariable String mapId, @RequestBody List<String> agvCodeList) {
        if (CollectionUtils.isEmpty(agvCodeList)) {
            throw new ADSParameterException(MessageUtils.getMessage("http.missing_parameter"));
        }
        List<Vehicle> vehicleList = agvCodeList.stream().map(super::getVehicle).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicleList)) {
            throw new YOUIFleetException(MessageUtils.getMessage("service.no_usable_agv"));
        }
        List<Vehicle> errorVehicles = vehicleList.stream().filter(x -> VehicleConstant.AUTO_CONTROL_MODE.equals(x.getControlMode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(errorVehicles)) {
            throw new ExecuteException(MessageUtils.getMessage("service.exist_manual_or_exception_agv"));
        }
        agvMapService.batchAppointMap(mapId, agvCodeList);
    }

    @ApiOperation(value = "获取点云数据")
    @ApiImplicitParam(name = "agvCode", value = "机器人编号", dataType = "String", required = true)
    @GetMapping("/{agvCode}/laserData")
    @ResponseStatus(value = HttpStatus.OK)
    public Map<String, Object> getLaserDataByAgvCode(@PathVariable String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        if (vehicle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }
//        Map<String, Object> laserDataMap = new HashMap<>(3);//  2/0.75
//        laserDataMap.put("agvId", vehicle.getId());
//        String data = "{\"code\":\"LASERDATA\",\"data\":{\"laserData\":{\"laser\":[{\"x\":11.554788196304909,\"y\":10.172586801486396},{\"x\":11.511733026659602,\"y\":10.14322311430281},{\"x\":11.463267580521531,\"y\":10.109297465566346},{\"x\":11.413601027553435,\"y\":10.074656343802534},{\"x\":11.366892774529857,\"y\":10.039359376397302},{\"x\":11.35113213265384,\"y\":10.033066247869401},{\"x\":11.315007834572988,\"y\":10.006527415403015},{\"x\":11.264017442750106,\"y\":9.9721845674775746},{\"x\":11.216718894997101,\"y\":9.936438909011299},{\"x\":11.164826365452289,\"y\":9.9034460778796873},{\"x\":3.7533019662180394,\"y\":10.515819313792447},{\"x\":3.749168316845644,\"y\":10.494494624925865},{\"x\":3.7321337112902935,\"y\":10.474192185890223},{\"x\":3.700538313874226,\"y\":10.454937905529652},{\"x\":3.6534127589369287,\"y\":10.414798365423277},{\"x\":3.5950254355296405,\"y\":10.374954595478824},{\"x\":11.116408480682072,\"y\":9.868117344401524},{\"x\":2.8289027538943294,\"y\":10.378303297913963},{\"x\":2.8355802685146738,\"y\":10.329921481377472},{\"x\":2.881697297515144,\"y\":10.303456534695339},{\"x\":2.9177352248549742,\"y\":10.254010297963351},{\"x\":11.067591912522328,\"y\":9.8337240285277918},{\"x\":8.2145359561387963,\"y\":9.8763600428770015},{\"x\":8.213208118911405,\"y\":9.8466214637156675},{\"x\":8.2628433747693144,\"y\":9.8069768469481176},{\"x\":11.026597584902825,\"y\":9.8061159434666774},{\"x\":8.2324991881749341,\"y\":9.7872204237768266},{\"x\":2.5016537207605793,\"y\":9.7042644248531786},{\"x\":8.2009100737129188,\"y\":9.7472282940003794},{\"x\":8.1881607682179816,\"y\":9.7370343718792007},{\"x\":2.3950707419658404,\"y\":9.5778191654330698},{\"x\":2.3394477966600498,\"y\":9.5512757038168097},{\"x\":2.2837691269362992,\"y\":9.5244372560201676},{\"x\":2.2389867476655212,\"y\":9.4721726754435096},{\"x\":2.2136415491344579,\"y\":9.4457499123224533},{\"x\":7.723552601000427,\"y\":9.5601388957806144},{\"x\":7.7413794737173625,\"y\":9.5052116888980116},{\"x\":7.7655467201342994,\"y\":9.4623381704666247},{\"x\":5.9932215533295707,\"y\":9.3022556051572209},{\"x\":7.7429917808037088,\"y\":9.438061985718365},{\"x\":7.0721927789972545,\"y\":9.3539636367867889},{\"x\":7.2663683426062757,\"y\":9.3593179127513295},{\"x\":6.1358312611988293,\"y\":9.2215411430460943},{\"x\":6.1563856233829233,\"y\":9.1927386497391765},{\"x\":6.1791861313083682,\"y\":9.1643662807012287},{\"x\":6.2036860749316789,\"y\":9.1363947622598118},{\"x\":6.2421585358433704,\"y\":9.0950651370233437},{\"x\":6.269468730557934,\"y\":9.0527898946511609},{\"x\":8.8109129585372088,\"y\":9.3355183937057351},{\"x\":8.8128124968621773,\"y\":9.3015801592668836},{\"x\":8.8321576422474202,\"y\":9.244840061182078},{\"x\":8.8583361713809108,\"y\":9.198328002281146},{\"x\":8.8806542176745928,\"y\":9.1686110141457249},{\"x\":9.2697660263887336,\"y\":9.2253817449558682},{\"x\":9.3090160778875237,\"y\":9.2114948532168111},{\"x\":9.2940682242378649,\"y\":9.185812954259017},{\"x\":9.0805445801226305,\"y\":9.1209716985721041},{\"x\":9.4889421782861181,\"y\":9.1954258495042343},{\"x\":9.5171574177130367,\"y\":9.1884101487867866},{\"x\":9.3916103494366627,\"y\":9.1500843219722281},{\"x\":9.4022833614504737,\"y\":9.12366048400472},{\"x\":9.6044263052370749,\"y\":9.1426741881237152},{\"x\":9.6312304017137507,\"y\":9.1231557748317247},{\"x\":9.6544775750599179,\"y\":9.1164367143819938},{\"x\":9.5549796667592855,\"y\":9.0734134465167902},{\"x\":9.513677247438995,\"y\":9.0540009230733762},{\"x\":9.4757081287436709,\"y\":9.0208380046783407},{\"x\":9.6081944314335335,\"y\":9.0341084762668125},{\"x\":9.5405581239303423,\"y\":8.9909922795539146},{\"x\":9.5045554849118901,\"y\":8.9718704733198091},{\"x\":9.7101083712170926,\"y\":9.0199626917601812},{\"x\":9.681510544751033,\"y\":8.9965820061996489},{\"x\":9.6259350327170168,\"y\":8.9492510453667808},{\"x\":9.5867570850888981,\"y\":8.9136789237473177},{\"x\":9.6026773333613029,\"y\":8.8763397648185833},{\"x\":9.5686548486286984,\"y\":8.8559038077982812},{\"x\":9.7050821792899438,\"y\":8.9018377819517696},{\"x\":9.5158547203735111,\"y\":8.7976750670964989},{\"x\":9.4603579743778941,\"y\":8.7597763967989621},{\"x\":9.4172046313758866,\"y\":8.7342062716594384},{\"x\":9.3746364409711518,\"y\":8.7085995084541405},{\"x\":9.3148097973483459,\"y\":8.6672082034071973},{\"x\":9.2719352503873207,\"y\":8.6322516425800604},{\"x\":9.2239199838929622,\"y\":8.6031150313116704},{\"x\":9.1755917902381281,\"y\":8.5647090957597189},{\"x\":9.121652907173047,\"y\":8.5231417455686191},{\"x\":9.0573179315104433,\"y\":8.4854237830738484},{\"x\":9.0141794120271665,\"y\":8.456795698890863},{\"x\":8.9739046169366326,\"y\":8.4291702774035464},{\"x\":8.931024511026127,\"y\":8.4000846611661029},{\"x\":8.8873672561822108,\"y\":8.370350820286161},{\"x\":8.826337909576301,\"y\":8.3221962976360029},{\"x\":8.7544938006786825,\"y\":8.2784658934600088},{\"x\":8.705053440242553,\"y\":8.2448031356832203},{\"x\":8.6584787420194047,\"y\":8.2121587327369792},{\"x\":8.4711600309004069,\"y\":8.1006621677407082},{\"x\":7.310613479415812,\"y\":7.5280412910651906},{\"x\":7.3262673982666886,\"y\":7.505367804057645},{\"x\":7.3624762523274061,\"y\":7.4474073236911345},{\"x\":7.395363100975521,\"y\":7.4032052836138575},{\"x\":7.4282447990627878,\"y\":7.359208125182386},{\"x\":7.4680612224657894,\"y\":7.3037819422577428},{\"x\":7.5008311495120186,\"y\":7.2601347359425912},{\"x\":7.5320882260565458,\"y\":7.2157862520495941},{\"x\":7.5712427089594136,\"y\":7.1605657057988115},{\"x\":7.6082582473461429,\"y\":7.1042020503413577},{\"x\":7.6402564999638694,\"y\":7.0606544212323268},{\"x\":7.6780858578394851,\"y\":7.0049594403800013},{\"x\":7.7081778965916126,\"y\":6.9603279700294181},{\"x\":7.740615091543269,\"y\":6.9172921460303307},{\"x\":7.7786795715382837,\"y\":6.8618746482667827},{\"x\":7.8084143583769112,\"y\":6.8170267237980742},{\"x\":7.8458869334714016,\"y\":6.7611255751098209},{\"x\":7.8854360841251054,\"y\":6.7066938252271466},{\"x\":7.9167261022499922,\"y\":6.6628596335136994},{\"x\":7.946333768418623,\"y\":6.6176624643429252},{\"x\":7.9780194809736962,\"y\":6.5740050885287307},{\"x\":8.0179562170960921,\"y\":6.5196077746365635},{\"x\":8.046897119130584,\"y\":6.47348201930998},{\"x\":8.0850847126745187,\"y\":6.417266208032224},{\"x\":8.1217393628031438,\"y\":6.3594291454189165},{\"x\":8.1522000987181755,\"y\":6.3139138316599199},{\"x\":8.1837565984123479,\"y\":6.2691851566927488},{\"x\":8.2259015683724837,\"y\":6.2155388213056941},{\"x\":8.2582619926769318,\"y\":6.1711531771183239},{\"x\":8.2918089228309864,\"y\":6.1277165457626994},{\"x\":8.3069315266922867,\"y\":6.1042830775025916},{\"x\":8.3307549266326255,\"y\":6.0701232594134504},{\"x\":8.3667602283253153,\"y\":6.0286914019003852},{\"x\":8.5355174087575953,\"y\":6.1462014419975102},{\"x\":8.5908487973234351,\"y\":6.1664930112724052},{\"x\":8.6271093609442708,\"y\":6.1671649242413986},{\"x\":8.6697232321202033,\"y\":6.1369696387748895},{\"x\":8.6931899538192674,\"y\":6.085596716635238},{\"x\":8.6847404581902303,\"y\":6.0170537033412366},{\"x\":8.6518689921016829,\"y\":5.9596368217011735},{\"x\":8.4817147773415158,\"y\":5.7229489231048092},{\"x\":8.4769213770218261,\"y\":5.6741819609267488},{\"x\":8.4932247372390819,\"y\":5.6054906232473902},{\"x\":8.4676687631118224,\"y\":5.5301202272881884},{\"x\":8.4513767575679424,\"y\":5.4647870794266638},{\"x\":8.3845254619834506,\"y\":5.3363845147299074},{\"x\":8.3832912692460866,\"y\":5.2870467887558359},{\"x\":8.3391394735524838,\"y\":5.1584796766235126},{\"x\":8.3617591698513998,\"y\":5.1373021088696422},{\"x\":8.4859900328311859,\"y\":4.9672032212047599},{\"x\":8.5396737136840439,\"y\":4.9350081071432053},{\"x\":8.5186798693858048,\"y\":4.7963231053130322},{\"x\":8.583105919128851,\"y\":4.7777721141871119},{\"x\":8.6229213929735522,\"y\":4.780020047310475},{\"x\":8.6704728797542057,\"y\":4.7374217298421106},{\"x\":8.6990219861213607,\"y\":4.6943106945885527},{\"x\":8.7386831872788235,\"y\":4.6384066074419188},{\"x\":8.7652098864021326,\"y\":4.5906648565970993},{\"x\":8.8024376798769914,\"y\":4.5595523363102135},{\"x\":7.2483935384422464,\"y\":1.8458192590041715},{\"x\":7.2901272533303185,\"y\":1.8215089710669101},{\"x\":8.2700068396423809,\"y\":3.4357996167787164},{\"x\":8.2867919910572763,\"y\":3.4263934354262986},{\"x\":6.2953111015940664,\"y\":-0.14846121117568956},{\"x\":6.3337741290768133,\"y\":-0.14139611206358005},{\"x\":6.3835332783914502,\"y\":-0.11420507698910143},{\"x\":6.4404955164442237,\"y\":-0.073765065335271274},{\"x\":6.5242818192611818,\"y\":-0.044823000230481469},{\"x\":6.5816034225375857,\"y\":-0.0020257482217136413},{\"x\":6.6386677390774196,\"y\":0.041005460268207727},{\"x\":8.0795844655670823,\"y\":2.6084684126159772},{\"x\":6.8647851570439187,\"y\":0.21630766798281975},{\"x\":6.9153494730322667,\"y\":0.25078601225298236},{\"x\":6.9671082455788813,\"y\":0.28811126247556906},{\"x\":7.0191052515311538,\"y\":0.32652655161446731},{\"x\":7.0708696755387148,\"y\":0.36514782694633219},{\"x\":7.1228607943281119,\"y\":0.40486163923839946},{\"x\":7.1727731520671725,\"y\":0.44123365175262919},{\"x\":7.2472696701624937,\"y\":0.4663697358356611},{\"x\":7.3213641093867521,\"y\":0.49200884948637658},{\"x\":7.3699630278767856,\"y\":0.52833420183374713},{\"x\":7.4165551431491075,\"y\":0.56126875552906519},{\"x\":7.4651654394469888,\"y\":0.59886406440929996},{\"x\":7.5144328988246061,\"y\":0.63844556491098814},{\"x\":7.5625844212454449,\"y\":0.67642383574840892},{\"x\":7.6096348279468744,\"y\":0.71279279121679551},{\"x\":7.655163345947944,\"y\":0.74664457853504551},{\"x\":7.7004859599356728,\"y\":0.78067111700146974},{\"x\":7.7456027461718939,\"y\":0.81487412418467109},{\"x\":7.812972262215645,\"y\":0.836453315053026},{\"x\":7.8567441447363819,\"y\":0.86930472211841625},{\"x\":7.9003170113751313,\"y\":0.90232501448424962},{\"x\":7.9452242834347233,\"y\":0.93884565410572662},{\"x\":8.0021410111771569,\"y\":0.94137968754283285},{\"x\":8.0832925844584587,\"y\":1.0597675392284227},{\"x\":8.1098646096176132,\"y\":1.0580491871965894},{\"x\":8.1469811654623676,\"y\":1.0801672932474791},{\"x\":8.1839568780665743,\"y\":1.1024197978202537},{\"x\":8.2264114503742434,\"y\":1.1376269046065275},{\"x\":8.2949633362724811,\"y\":1.1721325079204092},{\"x\":8.4006716536639061,\"y\":1.2954109941174625},{\"x\":8.425744964688425,\"y\":1.2929228025648776},{\"x\":8.4874578099756341,\"y\":1.3159951552816889},{\"x\":8.5514263170360962,\"y\":1.3457912044542102},{\"x\":8.5882953375766711,\"y\":1.3732048237202967},{\"x\":8.6543931911954495,\"y\":1.410657242939779},{\"x\":8.6986455649916099,\"y\":1.4581107973876382},{\"x\":9.6718335073280635,\"y\":3.808631017416797},{\"x\":9.7068733908863152,\"y\":3.8523885433264891},{\"x\":9.765439881384518,\"y\":3.8660680307620563},{\"x\":9.779720880304259,\"y\":3.7136720117698356},{\"x\":9.8546712353961077,\"y\":3.8211486012424274},{\"x\":9.8837263857908049,\"y\":3.8040111770507998},{\"x\":9.7306152397272854,\"y\":3.268013074884327},{\"x\":9.710257839118027,\"y\":3.1547651120884144},{\"x\":9.7239536242262616,\"y\":3.0816627144758346},{\"x\":9.7306398001959664,\"y\":3.0438515438632088},{\"x\":9.7581757371831763,\"y\":3.009000935027407},{\"x\":9.8036854017417454,\"y\":2.9681234840363615},{\"x\":9.8319110658192432,\"y\":2.9335476538699412},{\"x\":9.9021253752562206,\"y\":3.0319818818938504},{\"x\":9.942774662958092,\"y\":3.1005398330806946},{\"x\":9.9939586373553446,\"y\":3.1431065539034719},{\"x\":10.042206297015476,\"y\":3.1778863318244435},{\"x\":10.086881851612418,\"y\":3.2022473301390413},{\"x\":10.133053688771682,\"y\":3.2331522113719453},{\"x\":10.167873553705981,\"y\":3.2903293840097536},{\"x\":11.051528544936406,\"y\":6.2470436724626168},{\"x\":11.097618216182568,\"y\":6.2334910722522814},{\"x\":11.105199644914787,\"y\":6.2244071008926838},{\"x\":11.096538457716258,\"y\":6.1112189814915761},{\"x\":11.127969543533663,\"y\":6.1145634473453647},{\"x\":11.151246164010994,\"y\":6.1269480926490951},{\"x\":11.194940064062303,\"y\":6.1415808980326689},{\"x\":11.242583226600228,\"y\":6.1305595513768916},{\"x\":11.265545415054115,\"y\":6.0965188568459698},{\"x\":11.276755144416256,\"y\":6.0520609998856276},{\"x\":10.859514949348187,\"y\":3.2598770023179746},{\"x\":10.901885873700534,\"y\":3.1996083291813502},{\"x\":10.946884263022499,\"y\":3.1504299833165881},{\"x\":10.973439494026856,\"y\":3.0934084383075238},{\"x\":11.005807747265633,\"y\":3.0710832325537929},{\"x\":11.153291625123455,\"y\":2.967221160232949},{\"x\":11.189295058978987,\"y\":2.9661299871535753},{\"x\":10.960592221741667,\"y\":0.28068959368277291},{\"x\":10.987811269593875,\"y\":0.3002607734492404},{\"x\":11.040761413891115,\"y\":0.32848222756209289},{\"x\":11.0945427941597,\"y\":0.36865679777794469},{\"x\":11.147973015427812,\"y\":0.41013238060635437},{\"x\":11.200178034009687,\"y\":0.44320876941211118},{\"x\":11.251767701968427,\"y\":0.47360383009392848},{\"x\":11.303774297420146,\"y\":0.51397330085624127},{\"x\":11.330436202157506,\"y\":0.54520618172515967},{\"x\":11.380904960106696,\"y\":0.57432428827841164},{\"x\":11.431713559524876,\"y\":0.61391678923630444},{\"x\":11.505154875172121,\"y\":0.63934224763987935},{\"x\":11.575418199931446,\"y\":0.60957032201786099},{\"x\":11.597207385541694,\"y\":0.56267852114362071},{\"x\":11.642927131471332,\"y\":0.50333953353907646},{\"x\":11.689179840539538,\"y\":0.44221157942708622},{\"x\":11.712242604300531,\"y\":0.40286455492964812},{\"x\":11.73550798832585,\"y\":0.36357591587742277},{\"x\":11.783154368875518,\"y\":0.30493666856167323},{\"x\":11.806516579374213,\"y\":0.24606389905289738},{\"x\":11.855448924780784,\"y\":0.20053457804459818},{\"x\":11.879763757004906,\"y\":0.15661114476145244},{\"x\":11.904395375316616,\"y\":0.1197478287533098},{\"x\":11.981510634315622,\"y\":3.6519589597139319},{\"x\":12.013077341032563,\"y\":3.4605913679718849},{\"x\":12.029545765522464,\"y\":3.6239995177699926},{\"x\":12.153369550319848,\"y\":0.344124486155577},{\"x\":12.177238541344551,\"y\":0.39121434326073334},{\"x\":12.226069455673947,\"y\":0.39884134449765263},{\"x\":12.275210927123901,\"y\":0.39201389332219172},{\"x\":12.349517397006428,\"y\":0.36896731528618965},{\"x\":12.399574600243835,\"y\":0.34602310481388443},{\"x\":12.424365757651628,\"y\":0.34252307597986409},{\"x\":12.470496577030067,\"y\":-0.14036230927730742},{\"x\":12.523070308833809,\"y\":-0.15487570182040677},{\"x\":12.613690940909301,\"y\":-0.37533455945401073},{\"x\":12.628801022268995,\"y\":-0.18464536001475729},{\"x\":12.681037296546362,\"y\":-0.60166289669927941},{\"x\":12.735249315057128,\"y\":-0.59935613944480615},{\"x\":12.385903443838018,\"y\":4.7377470677867155},{\"x\":12.738475723856741,\"y\":0.10000568084463524},{\"x\":12.762338647832589,\"y\":0.12008078981988213},{\"x\":12.436870240239923,\"y\":4.7634354382070692},{\"x\":12.487158727408037,\"y\":4.7916344839065639},{\"x\":12.550601762092173,\"y\":4.8147240050157549},{\"x\":12.600537704525816,\"y\":4.8376142025769582},{\"x\":12.638046329039032,\"y\":4.8524147428719484},{\"x\":12.662747450649892,\"y\":4.8639648863695513},{\"x\":12.684127550231285,\"y\":4.8997155425892931},{\"x\":12.69616565702438,\"y\":5.0002511858472154},{\"x\":12.547395940172475,\"y\":6.1411753632383839},{\"x\":12.728006523483291,\"y\":4.9563449283296617},{\"x\":12.564897484001293,\"y\":6.1545860256675793},{\"x\":12.761169998022661,\"y\":4.9069688668843314},{\"x\":12.815350694709355,\"y\":5.0531469752813285},{\"x\":12.855817490890489,\"y\":5.0398838833000426},{\"x\":12.849782305615641,\"y\":5.1466502878457696},{\"x\":12.91483224606106,\"y\":5.0666458898114293},{\"x\":12.997897783115613,\"y\":4.7676351799784404},{\"x\":13.039982346348214,\"y\":4.6216020731061569},{\"x\":12.830909016663819,\"y\":5.7496834298446169},{\"x\":13.066786631354084,\"y\":4.6277234772144},{\"x\":12.781486900117844,\"y\":6.101031469147582},{\"x\":12.773389351321583,\"y\":6.1891569189534881},{\"x\":13.133764398506504,\"y\":4.6430363269707247},{\"x\":13.225864864440995,\"y\":4.5478799380780135},{\"x\":12.936996390318384,\"y\":5.8642213013615452},{\"x\":13.248422235754424,\"y\":4.5747093821088294},{\"x\":13.310214071662259,\"y\":4.6159410511881296},{\"x\":13.316077580694072,\"y\":4.6498767372744947},{\"x\":13.311990688504899,\"y\":4.7225663900537374},{\"x\":13.39467740756899,\"y\":4.6770854779881006},{\"x\":13.406244950539362,\"y\":4.6872407271869729},{\"x\":13.455553324945207,\"y\":4.7157171421826494},{\"x\":13.503011504488738,\"y\":4.7494915905301838},{\"x\":13.270182750691724,\"y\":5.5891089867314232},{\"x\":13.282986702448762,\"y\":5.586175555215207},{\"x\":13.541185577655966,\"y\":4.7653088584850698},{\"x\":13.573220546634031,\"y\":4.8008047900842685},{\"x\":13.595913642082321,\"y\":4.8195059634965709},{\"x\":13.642633538549765,\"y\":4.8517070783031198},{\"x\":13.688927040224366,\"y\":4.8841207328873883},{\"x\":13.748180038131265,\"y\":4.9192977568616882},{\"x\":13.417377833353861,\"y\":5.9243974715257384},{\"x\":13.795522868235976,\"y\":4.9468592527149031},{\"x\":13.864040062444701,\"y\":4.9936961102052191},{\"x\":13.89830408931708,\"y\":5.0164709586590268},{\"x\":13.954356560413558,\"y\":5.0562279682198366},{\"x\":13.678749301804029,\"y\":5.7665288286689407},{\"x\":13.925496171847321,\"y\":5.1964977395317504},{\"x\":13.526730804980605,\"y\":6.1927112125438573},{\"x\":14.067277854132058,\"y\":5.1308431829504579},{\"x\":14.099253516642117,\"y\":5.1568967512266726},{\"x\":14.143365436898197,\"y\":5.1877785190458949},{\"x\":13.850151959751207,\"y\":5.8556069857519866},{\"x\":13.847153363647127,\"y\":5.8889614859066359},{\"x\":14.129706296966246,\"y\":5.3710144504117876},{\"x\":14.208933129280505,\"y\":5.2344875323767068},{\"x\":14.242282236630626,\"y\":5.2563010279731053},{\"x\":14.274209398628862,\"y\":5.2808047960745945},{\"x\":14.25382986679821,\"y\":5.3799063845006945},{\"x\":14.051966534800977,\"y\":5.8076040010917955},{\"x\":14.084016341371228,\"y\":5.8470964890579911},{\"x\":14.260680842367544,\"y\":5.5875020119600798},{\"x\":14.379504589051761,\"y\":5.4182116504684315},{\"x\":14.42430363326265,\"y\":5.3899283157267019}]},\"agvId\":\"c41742fc-eeb2-4bbd-b158-1747e3d7c297\"}}";
//        laserDataMap.put("laserData", JSONObject.parseObject(data));


//        laserDataMap.put("laserData", JSONObject.parseObject(vehicle.getLaserData()));
        return JSONObject.parseObject(vehicle.getLaserData());
    }

//    @ApiOperation(value = "打开自动充电和泊车")
//    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", paramType = "path", required = true)
//    @PostMapping("/{agvCode}/openAutoParkOrCharge")
//    @ResponseStatus(value = HttpStatus.OK)
//    public void openAutoParkOrCharge(@PathVariable String agvCode) {
//        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
//        if (vehicle == null) {
//            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
//        }
//        vehicle.setParkOrChargeScheduler(true);
//    }

//    @ApiOperation(value = "关闭自动充电和泊车")
//    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", paramType = "path", required = true)
//    @PostMapping("/{agvCode}/closeAutoParkOrChage")
//    @ResponseStatus(value = HttpStatus.OK)
//    public void closeAutoParkOrChage(@PathVariable String agvCode) {
//        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
//        if (vehicle == null) {
//            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
//        }
//        vehicle.setParkOrChargeScheduler(false);
//    }

    @LogOperation("打开自动充电")
    @ApiOperation(value = "打开自动充电")
    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", paramType = "path", required = true)
    @PostMapping("/{agvCode}/openAutoCharge")
    @ResponseStatus(value = HttpStatus.OK)
    public void openAutoCharge(@PathVariable String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (vehicle == null || agv == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }

        ThreadContext.put("ROUTINGKEY", agvCode);
        agv.setAutoCharge(1);
        agvService.update(agv);
        vehicle.setAutoCharge(true);
        logger.debug("agvCode:[{}],event:[打开自动充电],content:[{}]", agvCode, "设置完成");
        ThreadContext.remove("ROUTINGKEY");
    }

    @LogOperation("关闭自动充电")
    @ApiOperation(value = "关闭自动充电")
    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", paramType = "path", required = true)
    @PostMapping("/{agvCode}/closeAutoCharge")
    @ResponseStatus(value = HttpStatus.OK)
    public void closeAutoCharge(@PathVariable String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (vehicle == null || agv == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }

        ThreadContext.put("ROUTINGKEY", agvCode);
        agv.setAutoCharge(0);
        agvService.update(agv);
        vehicle.setAutoCharge(false);
        logger.debug("agvCode:[{}],event:[关闭自动充电],content:[{}]", agvCode, "设置完成");
        ThreadContext.remove("ROUTINGKEY");
    }

    @LogOperation("打开自动泊车")
    @ApiOperation(value = "打开自动泊车")
    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", paramType = "path", required = true)
    @PostMapping("/{agvCode}/openAutoPark")
    @ResponseStatus(value = HttpStatus.OK)
    public void openAutoPark(@PathVariable String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (vehicle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }

        ThreadContext.put("ROUTINGKEY", agvCode);
        agv.setAutoPark(1);
        agvService.update(agv);
        vehicle.setAutoPark(true);
        logger.debug("agvCode:[{}],event:[打开自动泊车],content:[{}]", agvCode, "设置完成");
        ThreadContext.remove("ROUTINGKEY");
    }

    @LogOperation("关闭自动泊车")
    @ApiOperation(value = "关闭自动泊车")
    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", paramType = "path", required = true)
    @PostMapping("/{agvCode}/closeAutoPark")
    @ResponseStatus(value = HttpStatus.OK)
    public void closeAutoPark(@PathVariable String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (vehicle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }

        ThreadContext.put("ROUTINGKEY", agvCode);
        agv.setAutoPark(0);
        agvService.update(agv);
        vehicle.setAutoPark(false);
        logger.debug("agvCode:[{}],event:[关闭自动泊车],content:[{}]", agvCode, "设置完成");
        ThreadContext.remove("ROUTINGKEY");
    }

    @ApiOperation(value = "开启泊车点绑定配置")
    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", paramType = "path", required = true)
    @PostMapping("/{agvCode}/openBindParkConfig")
    @ResponseStatus(value = HttpStatus.OK)
    public void openBindParkConfig(@PathVariable String agvCode, @RequestBody List<AGVBindMarkerVo> agvBindMarkerVos) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (vehicle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }
        ThreadContext.put("ROUTINGKEY", agvCode);
        agv.setBindParkConfig(true);
        agv.setBindParkMarkers(JSONObject.toJSONString(agvBindMarkerVos));
        agvService.update(agv);
        vehicle.setBindParkConfig(true);
        vehicle.setAllowParkMarkerIds(agvService.getAGVBindMarkers(agv.getBindParkMarkers()));
        logger.debug("agvCode:[{}],event:[开启泊车点绑定配置],content:[{}]", agvCode, agv.toString());
        ThreadContext.remove("ROUTINGKEY");
    }


    @ApiOperation(value = "关闭泊车点绑定配置")
    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", paramType = "path", required = true)
    @PostMapping("/{agvCode}/closeBindParkConfig")
    @ResponseStatus(value = HttpStatus.OK)
    public void closeBindParkConfig(@PathVariable String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (vehicle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }
        ThreadContext.put("ROUTINGKEY", agvCode);
        agv.setBindParkConfig(false);
        vehicle.setBindParkConfig(false);
        agvService.update(agv);
        logger.debug("agvCode:[{}],event:[关闭泊车点绑定配置]", agvCode);
        ThreadContext.remove("ROUTINGKEY");
    }


    @ApiOperation(value = "开启充电点绑定配置")
    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", paramType = "path", required = true)
    @PostMapping("/{agvCode}/openBindChargeConfig")
    @ResponseStatus(value = HttpStatus.OK)
    public void openBindChargeConfig(@PathVariable String agvCode, @RequestBody List<AGVBindMarkerVo> agvBindMarkerVos) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (vehicle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }
        ThreadContext.put("ROUTINGKEY", agvCode);


        agv.setBindChargeConfig(true);
        agv.setBindChargeMarkers(JSONObject.toJSONString(agvBindMarkerVos));
        agvService.update(agv);
        vehicle.setBindChargeConfig(true);
        vehicle.setAllowChargeMarkerIds(agvService.getAGVBindMarkers(agv.getBindChargeMarkers()));
        logger.debug("agvCode:[{}],event:[开启充电点绑定配置],content:[{}]", agvCode, agv.toString());
        ThreadContext.remove("ROUTINGKEY");
    }

    @ApiOperation(value = "关闭充电点绑定配置")
    @ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", paramType = "path", required = true)
    @PostMapping("/{agvCode}/closeBindChargeConfig")
    @ResponseStatus(value = HttpStatus.OK)
    public void closeBindChargeConfig(@PathVariable String agvCode) {
        Vehicle vehicle = vehiclePool.getVehicle(agvCode);
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (vehicle == null) {
            throw new ADSParameterException(MessageUtils.getMessage("http.agv_not_login"));
        }
        ThreadContext.put("ROUTINGKEY", agvCode);
        agv.setBindChargeConfig(false);
        agvService.update(agv);
        vehicle.setBindChargeConfig(false);
        logger.debug("agvCode:[{}],event:[关闭充电泊车配置]", agvCode);
        ThreadContext.remove("ROUTINGKEY");
    }

    @ApiOperation(value = "机器人路径信息")
    @ApiImplicitParam(name = "agvCodeList", value = "agvCode列表", required = true, dataType = "String")
    @PostMapping(value = "/agvPathList")
    @ResponseStatus(value = HttpStatus.OK)
    public JSONArray agvPathList(@RequestBody(required = false) List<String> agvCodeList) {
        JSONArray agvArray = new JSONArray();
        if (!CollectionUtils.isEmpty(agvCodeList) && agvCodeList.size() > 0) {
            agvCodeList.forEach(agvCode -> {
                Vehicle vehicle = vehiclePool.getVehicle(agvCode);
                JSONObject agv = new JSONObject();
                if (vehicle.getDefaultVehicleStatus() != null && vehicle.getDefaultVehicleStatus().getPosition() != null) {
                    agv.put("x", vehicle.getDefaultVehicleStatus().getPosition().getPos_x());
                    agv.put("y", vehicle.getDefaultVehicleStatus().getPosition().getPos_y());
                    agv.put("angle", vehicle.getDefaultVehicleStatus().getPosition().getPos_angle());
                } else {
                    agv.put("x", null);
                    agv.put("y", null);
                    agv.put("angle", null);
                }
                agv.put("planedPaths", vehicle.getPlanedPaths());
                agv.put("runningPaths", vehicle.getRunningPaths());
                agv.put("executedPaths", vehicle.getExecutedPaths());
                agv.put("colors", vehicle.getColors());
                agv.put("agvColor", vehicle.getAgvColor());
                agvArray.add(agv);
            });
        }
        return agvArray;
    }


    @LogOperation("配置机器人参数")
    @ApiOperation(value = "配置机器人参数")
    @ApiImplicitParams({@ApiImplicitParam(name = "agvCode", value = "agvCode", dataType = "String", required = true, paramType = "path"),
            @ApiImplicitParam(name = "agvConfigVo", value = "是否自动分配任务，0:关闭，1:开启", dataType = "AGVConfigVo")})
    @PostMapping("/{agvCode}/updateAGVConfig")
    @ResponseStatus(HttpStatus.OK)
    public void setAGVConfig(@PathVariable(value = "agvCode") String agvCode, @RequestBody AGVConfigVo agvConfigVo) {
        Agv agv = agvService.selectByAgvCode(agvCode);
        if (agvConfigVo.getAutoCharge() != null) {
            agv.setAutoCharge(agvConfigVo.getAutoCharge());
        }
        if (agvConfigVo.getAutoPark() != null) {
            agv.setAutoPark(agvConfigVo.getAutoPark());
        }
        if (agvConfigVo.getAutoAllocation() != null) {
            agv.setAutoAllocation(agvConfigVo.getAutoAllocation());
        }
        agvService.update(agv);
        vehiclePool.updateVehicleByAgv(agv.getAgvCode());
    }


    @ApiOperation(value = "工作台所有机器人详情数据，可筛选路径")
    @PostMapping(value = "/agvDetailList")
    @ResponseStatus(value = HttpStatus.OK)
    public JSONArray agvDetailList(@RequestBody(required = false) List<String> agvIds) {
        JSONArray agvArray = new JSONArray();
        List<Vehicle> allVehicles = vehiclePool.getAll();
        if (!CollectionUtils.isEmpty(allVehicles) && allVehicles.size() > 0) {
            allVehicles.forEach(vehicle -> {
                JSONObject agv = new JSONObject();
                DefaultVehicleStatusParam defaultVehicleStatusParam = new DefaultVehicleStatusParam();
                if (vehicle.getDefaultVehicleStatus() != null) {
                    if (vehicle.getDefaultVehicleStatus().getPosition() != null) {
                        DefaultVehicleStatus.PositionStatus position = vehicle.getDefaultVehicleStatus().getPosition();
                        DefaultVehicleStatusParam.PositionStatus positionParam = new DefaultVehicleStatusParam.PositionStatus();
                        positionParam.setPos_x(position.getPos_x());
                        positionParam.setPos_y(position.getPos_y());
                        positionParam.setPos_angle(position.getPos_angle());
                        positionParam.setPos_confidence(position.getPos_confidence());
                        positionParam.setPos_islocated(position.getPos_islocated());
                        defaultVehicleStatusParam.setPosition(positionParam);
                    }
                    if (vehicle.getDefaultVehicleStatus().getSpeed() != null) {
                        DefaultVehicleStatusParam.SpeedStatus speedParam = new DefaultVehicleStatusParam.SpeedStatus();
                        speedParam.setSpeed_vx(vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_vx());
                        speedParam.setSpeed_w(vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_w());
                        speedParam.setSpeed_vy(vehicle.getDefaultVehicleStatus().getSpeed().getSpeed_vy());
                        defaultVehicleStatusParam.setSpeed(speedParam);
                    }
                    if (vehicle.getDefaultVehicleStatus().getBattery() != null) {
                        DefaultVehicleStatus.BatteryStatus battery = vehicle.getDefaultVehicleStatus().getBattery();
                        DefaultVehicleStatusParam.BatteryStatus batteryParam = new DefaultVehicleStatusParam.BatteryStatus();
                        batteryParam.setBattery_value(battery.getBattery_value());
                        batteryParam.setBattery_charge(battery.getBattery_charge());
                        batteryParam.setBattery_discharge(battery.getBattery_discharge());
                        defaultVehicleStatusParam.setBattery(batteryParam);
                    }
                    defaultVehicleStatusParam.setMotorStatus(vehicle.getDefaultVehicleStatus().getMotorStatus());
                    defaultVehicleStatusParam.setErrorMessages(vehicle.getDefaultVehicleStatus().getErrorMessages());
                }
                agv.put("defaultVehicleStatus", defaultVehicleStatusParam);
                agv.put("mapStatus", vehicle.getMapStatus());
                agv.put("appointStatus", vehicle.getAppointStatus());
                agv.put("abnormalStatus", vehicle.getAbnormalStatus());
                agv.put("errorMessage", vehicle.getErrorMessage());
                agv.put("controlMode", vehicle.getControlMode());
                agv.put("navigationType", vehicle.getNavigationType());
                agv.put("missionName", vehicle.getMissionName());
                agv.put("name", vehicle.getName());
                agv.put("id", vehicle.getId());
                agv.put("agvMapId", vehicle.getAgvMapId());
                agv.put("onlineStatus", vehicle.getOnlineStatus());
                agv.put("colors", vehicle.getColors());
                agv.put("agvColor", vehicle.getAgvColor());
                agv.put("workStatus", vehicle.getWorkStatus());
                if (agvIds == null) {
                    agv.put("planedPaths", vehicle.getPlanedPaths());
                    agv.put("runningPaths", vehicle.getRunningPaths());
                    agv.put("executedPaths", vehicle.getExecutedPaths());
                } else if (agvIds.size() > 0) {
                    if (agvIds.contains(vehicle.getId())) {
                        agv.put("planedPaths", vehicle.getPlanedPaths());
                        agv.put("runningPaths", vehicle.getRunningPaths());
                        agv.put("executedPaths", vehicle.getExecutedPaths());
                    }
                }
                agvArray.add(agv);
            });
        }
        return agvArray;
    }
}
