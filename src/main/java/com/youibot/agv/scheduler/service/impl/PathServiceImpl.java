package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.engine.pathplan.util.PathUtils;
import com.youibot.agv.scheduler.engine.pathplan.util.SidePathUtils;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.entity.vo.PathVo;
import com.youibot.agv.scheduler.entity.vo.SidePathVo;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.map.cache.AGVMapInfoCache;
import com.youibot.agv.scheduler.map.entity.MapGraphInfo;
import com.youibot.agv.scheduler.map.entity.PathResultData;
import com.youibot.agv.scheduler.map.utils.MapFileUtils;
import com.youibot.agv.scheduler.service.AutoDoorService;
import com.youibot.agv.scheduler.service.MarkerService;
import com.youibot.agv.scheduler.service.PathService;
import com.youibot.agv.scheduler.service.SidePathService;
import com.youibot.agv.scheduler.util.BeanUtils;
import com.youibot.agv.scheduler.util.FtpUtils;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.youibot.agv.scheduler.constant.MapConstant.*;
import static com.youibot.agv.scheduler.constant.MapFileSuffixConstant.ROAD_NETWORK_SUFFIX;

@Service
public class PathServiceImpl implements PathService {

    @Autowired
    private SidePathService sidePathService;

    @Autowired
    private MarkerService markerService;

    @Autowired
    private AutoDoorService autoDoorService;

    private static final Logger LOGGER = LoggerFactory.getLogger(PathServiceImpl.class);

    @Override
    public Path insert(Path path) {
        FTPClient client = null;
        path.setId(UUID.randomUUID().toString());
        //添加sidePath数据
        try {

            client = FtpUtils.getConnectClient();
            String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, path.getAgvMapName(), true);
            String result = MapFileUtils.getDraftFileData(filePath, path.getAgvMapName(), client);
            PathResultData roadNetwork;
            Map<String, Path> paths = new HashMap<>();
            Map<String, SidePath> sidePaths = new HashMap<>();
            Map<String, Marker> markers = new HashMap<>();
            if (StringUtils.isEmpty(result)) {
                LOGGER.error("read path file is null");
                throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
            }
            roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);

            if (!CollectionUtils.isEmpty(roadNetwork.getPaths())) {
                paths = roadNetwork.getPaths();
                sidePaths = roadNetwork.getSidePaths();
            }
            markers = roadNetwork.getMarkers();
            path.setLength(PathUtils.getLength(path, markers.get(path.getStartMarkerId()), markers.get(path.getEndMarkerId())));
            paths.put(path.getId(), path);
            List<SidePath> sidePathList = createSidePathsV2(path, markers.get(path.getStartMarkerId()), markers.get(path.getEndMarkerId()));
            for (SidePath sidePath : sidePathList) {
                sidePaths.put(sidePath.getId(), sidePath);
            }
            roadNetwork.setPaths(paths);
            roadNetwork.setSidePaths(sidePaths);
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(roadNetwork), filePath, client);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("add path failed :{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.add_path_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
        return path;
    }

    @Override
    public synchronized PathVo update(PathVo pathVo) {
        FTPClient client = null;
        try {
            Path path = pathVo.getPath();
            client = FtpUtils.getConnectClient();
            String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, path.getAgvMapName(), true);
            String result = MapFileUtils.getDraftFileData(filePath, path.getAgvMapName(), client);
            if (StringUtils.isEmpty(result)) {
                LOGGER.error("read path file is null");
                throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
            }
            PathResultData roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);
            Map<String, Path> paths = roadNetwork.getPaths();
            Map<String, SidePath> sidePaths = roadNetwork.getSidePaths();
            Map<String, Marker> markers = roadNetwork.getMarkers();
            List<SidePath> delSidePaths = sidePathService.selectByPathId(path.getId(), path.getAgvMapName(), true);
            if (delSidePaths != null) {
                delSidePaths.forEach(sidePath -> sidePaths.remove(sidePath.getId()));
            }
            path.setLength(PathUtils.getLength(path, markers.get(path.getStartMarkerId()), markers.get(path.getEndMarkerId())));
            paths.put(path.getId(), path);
            List<SidePath> sidePathList = createSidePathsByPathVo(pathVo, markers.get(path.getStartMarkerId()), markers.get(path.getEndMarkerId()));
            sidePathList.forEach(sidePath -> sidePaths.put(sidePath.getId(), sidePath));
            roadNetwork.setPaths(paths);
            roadNetwork.setSidePaths(sidePaths);
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(roadNetwork), filePath, client);

            // 填充自动门类型
            if (!StringUtils.isEmpty(path.getAutoDoorId())) {
                AutoDoor autoDoor = autoDoorService.selectById(path.getAutoDoorId());
                path.setAutoDoorType(autoDoor.getType());
            }

            return pathVo;
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("update path failed:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.update_path_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    private void fillAutoDoorType(List<Path> pathList) {
        Map<String, List<Path>> autoDoorPaths = pathList.stream().filter(path -> !StringUtils.isEmpty(path.getAutoDoorId()))
                .collect(Collectors.groupingBy(Path::getAutoDoorId, Collectors.toList()));
        if(!CollectionUtils.isEmpty(autoDoorPaths)){
            List<AutoDoor> autoDoors = autoDoorService.selectByIds(new ArrayList<>(autoDoorPaths.keySet()));
            for (AutoDoor autoDoor : autoDoors) {
                for (Path path : autoDoorPaths.get(autoDoor.getId())) {
                    path.setAutoDoorType(autoDoor.getType());
                }
            }
        }
    }

    private List<PathVo> searchAllPathVo(String agvMapName, boolean isDraft) {

        long start = System.currentTimeMillis();
        List<Path> pathList = selectByAGVMapId(agvMapName, isDraft);
        LOGGER.debug("地图页面加载path路径耗时 ：{}", System.currentTimeMillis() - start);

        List<PathVo> pathVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(pathList)) {
            return pathVos;
        }

        start = System.currentTimeMillis();
        List<String> pathIdList = pathList.stream().map(Path::getId).collect(Collectors.toList());
        Map<String, List<SidePathVo>> sidePathVoMap = sidePathService.selectSidePathVosByPathIdList(pathIdList, agvMapName, isDraft);
        LOGGER.debug("地图页面加载sidepath路径耗时 ：{}", System.currentTimeMillis() - start);

        // 填充自动门类型
        start = System.currentTimeMillis();
        fillAutoDoorType(pathList);
        LOGGER.debug("填充自动门类型耗时 ：{}", System.currentTimeMillis() - start);

        for (Path path : pathList) {
            List<SidePathVo> sidePathVos = sidePathVoMap.get(path.getId());
            PathVo pathVo = new PathVo();
            pathVo.setPath(path);
            pathVo.setSidePathVos(sidePathVos);
            pathVos.add(pathVo);
        }
        return pathVos;
    }

    @Override
    public List<PathVo> searchAll(Map<String, String> searchMap, boolean isDraft) {
        String agvMapName = searchMap.get("agvMapName");
        if (CollectionUtils.isEmpty(searchMap) && AGVMapInfoCache.getCache(agvMapName) != null) {
            return null;
        }
        List<Path> pathList = selectByAGVMapId(agvMapName, isDraft);

        // 填充自动门类型
        fillAutoDoorType(pathList);

        try {
            if (CollectionUtils.isEmpty(pathList)) {
                return new ArrayList<>();
            }
            if (searchMap.containsKey("agvMapName") && searchMap.containsKey("isDraft") && searchMap.size() == 2) {
                return searchAllPathVo(agvMapName, isDraft);
            }
            Set<PathVo> result = new HashSet<>();
            for (String attribute : searchMap.keySet()) {
                if ("agvMapName".equals(attribute) || "isDraft".equals(attribute)) {
                    continue;
                }

                List<Path> tmpList = new ArrayList<>();
                for (Path path : pathList) {
                    if (MapFileUtils.getGetMethod(path, attribute, searchMap.get(attribute))) {
                        tmpList.add(path);
                    }
                }
                if (!CollectionUtils.isEmpty(tmpList)) {
                    List<String> pathIdList = tmpList.stream().map(Path::getId).collect(Collectors.toList());
                    Map<String, List<SidePathVo>> sidePathVoMap = sidePathService.selectSidePathVosByPathIdList(pathIdList, agvMapName, isDraft);
                    for (Path path : tmpList) {
                        List<SidePathVo> sidePathVos = sidePathVoMap.get(path.getId());
                        PathVo pathVo = new PathVo();
                        pathVo.setPath(path);
                        pathVo.setSidePathVos(sidePathVos);
                        result.add(pathVo);
                    }
                }
            }
            return new ArrayList<>(result);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("searchAll path failed ：{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
        }
    }

    @Override
    public void checkPathNavigationType(Path path, boolean isDraft) {
        //如果路径的停靠点是电梯点或者是调整点，则无法再路径上再添加自动门
        Marker startMarker = markerService.selectById(path.getAgvMapName(), path.getStartMarkerId(), isDraft);
        Marker endMarker = markerService.selectById(path.getAgvMapName(), path.getEndMarkerId(), isDraft);
        if (startMarker.getType().equals("ELEVATOR_MARKER") || startMarker.getType().equals("ADJUST_MARKER")) {
            throw new ExecuteException(MessageUtils.getMessage("service.The_path_has_bind_elevator_marker_or_adjust_marker"));
        }
        if (endMarker.getType().equals("ELEVATOR_MARKER")) {
            throw new ExecuteException(MessageUtils.getMessage("service.The_path_has_bind_elevator_marker"));
        }
        if (path.getDirection() == 0 && endMarker.getType().equals("ADJUST_MARKER")) {
            throw new ExecuteException(MessageUtils.getMessage("service.The_path_has_bind_adjust_marker"));
        }
    }

    @Override
    public Path selectById(String agvMapName, String id, boolean isDraft) {
        return selectByAGVMapId(agvMapName, isDraft).stream().filter(path -> path.getId().equals(id)).findFirst().orElse(null);
    }

    @Override
    public synchronized void delete(String agvMapName, String id) {
        FTPClient client = null;
        try {
            client = FtpUtils.getConnectClient();
            String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapName, true);
            String result = MapFileUtils.getDraftFileData(filePath, agvMapName, client);
            if (StringUtils.isEmpty(result)) {
                LOGGER.error("read path file is null");
                throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
            }
            PathResultData roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);
            Map<String, Path> paths = roadNetwork.getPaths();
            Map<String, SidePath> sidePaths = roadNetwork.getSidePaths();
            List<SidePath> delSidePaths = sidePathService.selectByPathId(id, agvMapName, true);
            if (delSidePaths != null) {
                delSidePaths.forEach(sidePath -> sidePaths.remove(sidePath.getId()));
            }
            paths.remove(id);
            roadNetwork.setPaths(paths);
            roadNetwork.setSidePaths(sidePaths);
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(roadNetwork), filePath, client);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("delete path failed:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.delete_path_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    /**
     * 根据path创建sidePath数据
     *
     * @param pathVo
     */
    @Override
    public List<SidePath> createSidePathsByPathVo(PathVo pathVo, Marker startMarker, Marker endMarker) {
        List<SidePath> sidePaths = new ArrayList<>();
        Path path = pathVo.getPath();
        if (PATH_DIRECTION_TWO_WAY.equals(path.getDirection())) {
            sidePaths.add(this.createSidePathByForward(pathVo, startMarker, endMarker));
            sidePaths.add(this.createSidePathByReverse(pathVo, startMarker, endMarker));
        } else if (PATH_DIRECTION_FORWARD.equals(path.getDirection())) {
            sidePaths.add(this.createSidePathByForward(pathVo, startMarker, endMarker));
        } else if (PATH_DIRECTION_REVERSE.equals(path.getDirection())) {
            sidePaths.add(this.createSidePathByReverse(pathVo, startMarker, endMarker));
        }
        return sidePaths;
    }

    @Override
    public List<Path> selectByAutoDoorIds(List<String> autoDoorIds) {
        List<Path> paths = new ArrayList<>();
        autoDoorIds.forEach(autoDoorId -> paths.addAll(selectByAutoDoorId(autoDoorId)));
        return paths;
    }

    @Override
    public List<Path> selectByAutoDoorId(String autoDoorId) {
        List<Path> dataList = MapFileUtils.getDataList(Path.class);
        return dataList.stream().filter(path -> autoDoorId.equals(path.getAutoDoorId())).collect(Collectors.toList());
    }

    @Override
    public List<SidePath> createSidePathsV2(Path path, Marker startMarker, Marker endMarker) {
        List<SidePath> sidePaths = new ArrayList<>();
        if (PATH_DIRECTION_TWO_WAY.equals(path.getDirection())) {
            sidePaths.add(this.createSidePathByForward2(path, startMarker, endMarker));
            sidePaths.add(this.createSidePathByReverse2(path, startMarker, endMarker));
        } else if (PATH_DIRECTION_FORWARD.equals(path.getDirection())) {
            sidePaths.add(this.createSidePathByForward2(path, startMarker, endMarker));
        } else if (PATH_DIRECTION_REVERSE.equals(path.getDirection())) {
            sidePaths.add(this.createSidePathByReverse2(path, startMarker, endMarker));
        }
        return sidePaths;
    }

    @Override
    public List<Path> selectByAgvMapIdMarkerId(String id, String agvMapName, boolean isDraft) {
        List<Path> paths = selectByAGVMapId(agvMapName, isDraft);
        return paths.stream().filter(path -> path.getEndMarkerId().equals(id) || path.getStartMarkerId().equals(id)).collect(Collectors.toList());
    }

    @Override
    public void deleteByAGVMapId(String agvMapId) {
        FTPClient client = null;
        try {
            client = FtpUtils.getConnectClient();
            String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapId, true);
            String result = MapFileUtils.getDraftFileData(filePath, agvMapId, client);
            if (StringUtils.isEmpty(result)) {
                LOGGER.error("read path file is null");
                throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
            }
            PathResultData roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);
            roadNetwork.setPaths(null);
            roadNetwork.setSidePaths(null);
            MapFileUtils.writeValue(MapFileUtils.PathResultDataToString(roadNetwork), filePath, client);
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("Failed execute to deleteByAGVMapId method:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.delete_path_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }


    /**
     * path的方向为正向时创建sidePath数据
     *
     * @param pathVo
     */
    private SidePath createSidePathByForward(PathVo pathVo, Marker startMarker, Marker endMarker) {
        SidePath sidePath = new SidePath();
        Path path = pathVo.getPath();
        sidePath.setId(UUID.randomUUID().toString());
        sidePath.setAgvMapName(path.getAgvMapName());
        sidePath.setPathId(path.getId());
        sidePath.setStartMarkerId(path.getStartMarkerId());
        sidePath.setEndMarkerId(path.getEndMarkerId());
        sidePath.setStartControl(path.getStartControl());
        sidePath.setEndControl(path.getEndControl());
        sidePath.setLength(path.getLength());
        sidePath.setLineType(path.getLineType());
        sidePath.setAgvDirection(path.getForwardAgvDirection());
        sidePath.setUsageStatus(path.getUsageStatus());
        Double[] inOutAngle = SidePathUtils.getInOutAngle2(sidePath, startMarker, endMarker);
        sidePath.setInAngle(inOutAngle[0]);
        sidePath.setOutAngle(inOutAngle[1]);
        sidePath.setAutoDoorId(path.getAutoDoorId());
        sidePath.setAllowStopNavigation(path.getAllowStopNavigation());
        for (SidePathVo sidePathVo : pathVo.getSidePathVos()) {
            if (sidePathVo.getStartMarkerId().equals(path.getStartMarkerId())) {
                sidePath.setPathType(sidePathVo.getPathType());
                sidePath.setWorkStationCode(sidePathVo.getWorkStationCode());
                sidePath.setAgvDirection(sidePathVo.getAgvDirection());
                sidePath.setDirectionAgv(sidePathVo.getDirectionAgv());
                sidePath.setDirectionAgv2(sidePathVo.getDirectionAgv2());
                sidePath.setDirectionShelf(sidePathVo.getDirectionShelf());
                sidePath.setDirectionShelf2(sidePathVo.getDirectionShelf2());
                sidePath.setRpmShelf(sidePathVo.getRpmShelf());
                sidePath.setPathParam(sidePathVo.getPathParam());
                sidePath.setPosition(sidePathVo.getPosition());
                sidePath.setEffectivePath(sidePathVo.getEffectivePath());
                sidePath.setHasAction(sidePathVo.getHasAction());
                sidePath.setWeightRatio(sidePathVo.getWeightRatio());
                sidePath.setAgvDirection2(sidePathVo.getAgvDirection2());

                sidePath.setBeforeMission(sidePathVo.getBeforeMission());
                sidePath.setAfterMission(sidePathVo.getAfterMission());

                sidePath.setShelfType(sidePathVo.getShelfType());
                sidePath.setCamType(sidePathVo.getCamType());
                sidePath.setUpQrDockId(sidePathVo.getUpQrDockId());
                sidePath.setDownQrDockId(sidePathVo.getDownQrDockId());
            }
        }
        return sidePath;
//        sidePathService.insert(sidePath);
    }

    /**
     * path的方向为反向时创建sidePath数据
     *
     * @param pathVo
     */
    private SidePath createSidePathByReverse(PathVo pathVo, Marker startMarker, Marker endMarker) {
        SidePath sidePath = new SidePath();
        Path path = pathVo.getPath();
        sidePath.setId(UUID.randomUUID().toString());
        sidePath.setAgvMapName(path.getAgvMapName());
        sidePath.setPathId(path.getId());
        sidePath.setStartMarkerId(path.getEndMarkerId());
        sidePath.setEndMarkerId(path.getStartMarkerId());
        sidePath.setStartControl(path.getEndControl());
        sidePath.setEndControl(path.getStartControl());
        sidePath.setLength(path.getLength());
        sidePath.setLineType(path.getLineType());
        sidePath.setAgvDirection(path.getReverseAgvDirection());
        sidePath.setUsageStatus(path.getUsageStatus());
        Double[] inOutAngle = SidePathUtils.getInOutAngle2(sidePath, startMarker, endMarker);
        sidePath.setInAngle(inOutAngle[0]);
        sidePath.setOutAngle(inOutAngle[1]);
        sidePath.setAutoDoorId(path.getAutoDoorId());
        sidePath.setAllowStopNavigation(path.getAllowStopNavigation());
        for (SidePathVo sidePathVo : pathVo.getSidePathVos()) {
            if (sidePathVo.getStartMarkerId().equals(path.getEndMarkerId())) {
                sidePath.setPathType(sidePathVo.getPathType());
                sidePath.setWorkStationCode(sidePathVo.getWorkStationCode());
                sidePath.setAgvDirection(sidePathVo.getAgvDirection());
                sidePath.setAgvDirection(sidePathVo.getAgvDirection());
                sidePath.setDirectionAgv(sidePathVo.getDirectionAgv());
                sidePath.setDirectionAgv2(sidePathVo.getDirectionAgv2());
                sidePath.setDirectionShelf(sidePathVo.getDirectionShelf());
                sidePath.setDirectionShelf2(sidePathVo.getDirectionShelf2());
                sidePath.setRpmShelf(sidePathVo.getRpmShelf());
                sidePath.setPosition(sidePathVo.getPosition());
                sidePath.setPathParam(sidePathVo.getPathParam());
                sidePath.setEffectivePath(sidePathVo.getEffectivePath());
                sidePath.setHasAction(sidePathVo.getHasAction());
                sidePath.setWeightRatio(sidePathVo.getWeightRatio());
                sidePath.setAgvDirection2(sidePathVo.getAgvDirection2());

                sidePath.setBeforeMission(sidePathVo.getBeforeMission());
                sidePath.setAfterMission(sidePathVo.getAfterMission());

                sidePath.setShelfType(sidePathVo.getShelfType());
                sidePath.setCamType(sidePathVo.getCamType());
                sidePath.setUpQrDockId(sidePathVo.getUpQrDockId());
                sidePath.setDownQrDockId(sidePathVo.getDownQrDockId());
            }
        }
        return sidePath;
    }

    /**
     * path的方向为正向时创建sidePath数据
     *
     * @param path
     */
    private SidePath createSidePathByForward2(Path path, Marker startMarker, Marker endMarker) {
        SidePath sidePath = new SidePath();
        sidePath.setId(UUID.randomUUID().toString());
        sidePath.setAgvMapName(path.getAgvMapName());
        sidePath.setPathId(path.getId());
        sidePath.setStartMarkerId(path.getStartMarkerId());
        sidePath.setEndMarkerId(path.getEndMarkerId());
        sidePath.setStartControl(path.getStartControl());
        sidePath.setEndControl(path.getEndControl());
        sidePath.setLength(path.getLength());
        sidePath.setLineType(path.getLineType());
        sidePath.setAgvDirection(path.getForwardAgvDirection());
        sidePath.setUsageStatus(path.getUsageStatus());
        Double[] inOutAngle = SidePathUtils.getInOutAngle2(sidePath, startMarker, endMarker);
        sidePath.setInAngle(inOutAngle[0]);
        sidePath.setOutAngle(inOutAngle[1]);
        sidePath.setAutoDoorId(path.getAutoDoorId());
        sidePath.setWeightRatio(1.0);
        sidePath.setAllowStopNavigation(path.getAllowStopNavigation());
        return sidePath;
//        sidePathService.insert(sidePath);
    }

    /**
     * path的方向为反向时创建sidePath数据
     *
     * @param path
     */
    private SidePath createSidePathByReverse2(Path path, Marker startMarker, Marker endMarker) {
        SidePath sidePath = new SidePath();
        sidePath.setId(UUID.randomUUID().toString());
        sidePath.setAgvMapName(path.getAgvMapName());
        sidePath.setPathId(path.getId());
        sidePath.setStartMarkerId(path.getEndMarkerId());
        sidePath.setEndMarkerId(path.getStartMarkerId());
        sidePath.setStartControl(path.getEndControl());
        sidePath.setEndControl(path.getStartControl());
        sidePath.setLength(path.getLength());
        sidePath.setLineType(path.getLineType());
        sidePath.setAgvDirection(path.getReverseAgvDirection());
        sidePath.setUsageStatus(path.getUsageStatus());
        Double[] inOutAngle = SidePathUtils.getInOutAngle2(sidePath, endMarker, startMarker);
        sidePath.setInAngle(inOutAngle[0]);
        sidePath.setOutAngle(inOutAngle[1]);
        sidePath.setAutoDoorId(path.getAutoDoorId());
        sidePath.setWeightRatio(1.0);
        sidePath.setAllowStopNavigation(path.getAllowStopNavigation());
        return sidePath;
    }

    @Override
    public List<Path> selectByAGVMapId(String agvMapId, boolean isDraft) {
        List<Path> paths = new ArrayList<>();
        FTPClient client = null;
        try {
            if (isDraft) {
                client = FtpUtils.getConnectClient();
                String filePath = MapFileUtils.getFilePath(ROAD_NETWORK_SUFFIX, agvMapId, true);
                String result = MapFileUtils.getDraftFileData(filePath, agvMapId, client);
                if (StringUtils.isEmpty(result)) {
                    return new ArrayList<>();
                }
                PathResultData roadNetwork = MapFileUtils.pathFileDataToPathResultData(result);
                paths = new ArrayList<>(roadNetwork.getPaths().values());
            } else {
                MapGraphInfo mapGraphInfo = AGVMapInfoCache.getCache(agvMapId);
                if (mapGraphInfo != null && !CollectionUtils.isEmpty(mapGraphInfo.getPaths())) {
                    paths = new ArrayList<>(mapGraphInfo.getPaths().values());
                }
            }
            return paths;
        } catch (ExecuteException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("Failed execute to selectByAGVMapId method e:{}", e);
            throw new ExecuteException(MessageUtils.getMessage("service.read_path_file_fail"));
        } finally {
            if (client != null) {
                FtpUtils.disconnect(client);
            }
        }
    }

    @Override
    public List<Path> selectByAGVMapIds(List<String> agvMapIds, boolean isDraft) {
        List<Path> paths = new ArrayList<>();
        agvMapIds.forEach(agvMapId -> paths.addAll(selectByAGVMapId(agvMapId, isDraft)));
        return paths;
    }

    @Override
    public List<Path> selectByAGVMapIdAndUsageStatus(String agvMapId, String usageStatus, boolean isDraft) {
        List<Path> paths = selectByAGVMapId(agvMapId, isDraft);
        return paths.stream().filter(path -> path.getUsageStatus().equals(PATH_USAGE_STATUS_ENABLE)).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<Path>> mapByAgvMapName(List<String> agvMapList, boolean isDraft) {
        return BeanUtils.listToMapByFiled(this.selectByAGVMapIds(agvMapList, isDraft), "agvMapName");
    }

    @Override
    public List<Path> selectByAgvMapIdAndStartIdOrEndId(String startId, String endId, String agvMapId, boolean isDraft) {
        List<Path> paths = selectByAGVMapId(agvMapId, isDraft);
        return paths.stream().filter(path -> (path.getStartMarkerId().equals(startId) && path.getEndMarkerId().equals(endId))
                || (path.getEndMarkerId().equals(startId) && path.getStartMarkerId().equals(endId))).collect(Collectors.toList());
    }

    @Override
    public void deleteByAgvMapIds(List<String> agvMapIdList) {
        agvMapIdList.forEach(this::deleteByAGVMapId);
    }
}
