#system
system.missing.parameter=缺少参数
system.data.type.error=数据类型错误
system.code.format.error=编码[%s]格式错误
system.name.format.error=名称[%s]格式错误
system.code.duplicate.error=[%s]值不能重复
system.code.is.empty.error=[%s]值不能为空
system.operate.timeout=操作超时
system.directory.is.empty=当前目录[%s]下文件为空
system.directory.is.not.exists=当前目录[%s]不存在
system.account.is.not.exists=账号不存在
system.account.is.already.exists=账号已存在
system.account.passwd.is.error=账号与密码不匹配
system.account.old.passwd.is.error=原密码不正确
system.account.is.disable=账号已被停用
system.account.has.no.permission=该账户没有任何页面权限，请联系管理员
system.menu.config.is.error=上级菜单不能为自身
system.menu.delete.error=先删除子菜单或按钮
system.account.permission.deny=用户权限不足
system.account.token.invalid=未登录或登录已失效
system.db.record.duplicate.error=数据库中已存在该记录
system.no.avaliable.marker=传入点位集合不可用
system.no.avaliable.vehicle=传入机器人集合不可用
system.version.is.dismatch=当前系统版本不匹配

#license
license.certificate.failure=证书未知异常
license.certificate.not.uploaded=证书未上传!
license.certificate.validate.failed=已上传的证书数据错误
license.certificate.expired=证书已过期

#excel
excel.export.error=导出Excel文件异常
excel.import.error=解析Excel文件异常
excel.import.code.empty=导入失败, 第[{0}]行数据验证失败, 原因:编码为空
excel.import.name.empty=导入失败, 第[{0}]行数据验证失败, 原因:名称为空
excel.import.type.empty=导入失败, 第[{0}]行数据验证失败, 原因:类型为空
excel.import.row.empty=导入失败, 第[{0}]行数据验证失败, 原因:排数为空
excel.import.colum.empty=导入失败, 第[{0}]行数据验证失败, 原因:列数为空
excel.import.layer.empty=导入失败, 第[{0}]行数据验证失败, 原因:层数为空
excel.import.workHeight.empty=导入失败, 第[{0}]行数据验证失败, 原因:作业高度为空
excel.import.code.exists=导入失败, 第[{0}]行数据验证失败, 原因:编码[{1}]已存在
excel.import.barcode.exists=导入失败, 第[{0}]行数据验证失败, 原因:容器编码[{1}]已存在
excel.import.usage.status.error=导入失败, 第[{0}]行数据验证失败, 原因:启用状态错误或与当前系统语言不匹配
excel.import.occupy.status.error=导入失败, 第[{0}]行数据验证失败, 原因:占用状态错误或与当前系统语言不匹配
excel.import.notice.level.error=导入失败, 第[{0}]行数据验证失败, 原因:通知等级错误或与当前系统语言不匹配
excel.import.event.type.error=导入失败, 第[{0}]行数据验证失败, 原因:事件类型错误或与当前系统语言不匹配

excel.import.code.repeat=导入失败, Excel表中存在重复编码[{0}]
excel.import.barcode.repeat=导入失败, Excel表中存在重复容器编码[{0}]
excel.import.data.convert.fail=导入失败, 第[{0}]行数据验证失败, 原因:第[{1}]列数据格式错误
excel.import.format.error=表格格式不正确，请导出模板后重新导入
excel.import.name.format.error=导入的文件格式不正确

#language
language.empty=语言不存在
language.inuse=该语言正在使用
language.upload.file.error=上传的文件格式不正确
language.upload.missing.info=语言包缺少info文件
language.upload.missing.service=语言包缺少service文件
language.upload.missing.web=语言包缺少web文件
language.code.duplicate=编码[{0}]已存在
language.code.nonstandard=编码[{0}]不符合国际化要求

#vehicle
vehicle.batchOperation.result={0}个机器人操作成功, {1}个机器人操作失败
vehicle.operation.fail=操作机器人[%s]失败，[%s]
vehicle.connect.fail=channel未连接或已断开
vehicle.request.timeout=请求超时，%s
vehicle.is.not.login.error=机器人未登录,请先登录！
vehicle.network.error=操作机器人失败，机器人[%s]网络未连接
vehicle.code.duplicate=编码[{0}]已存在, 请重新输入
vehicle.name.duplicate=名称[{0}]已存在, 请重新输入
vehicle.name.pattern=当前值[{0}]需要由字母或数字、下划线组成
vehicle.network.anomaly=通讯失败, 请检查机器人是否已连接
vehicle.navigation.cancel=机器人路径导航被取消
vehicle.locationMapCode.empty=机器人无使用中的定位图
vehicle.out.of.trace.error=机器人脱轨！
vehicle.aim.marker.unreachable.error=目标点不可达!
vehicle.controlStatus.repair.error=机器人处于检修模式，不允许进行控制模式切换
vehicle.empty=机器人不存在
vehicle.type.empty=机器人类型[%s]不存在
vehicle.type.bind.duplicate=机器人类型[%s]绑定重复
vehicle.type.excel.head.code=编码
vehicle.type.excel.head.name=名称
vehicle.type.excel.head.rotatable=允许旋转
vehicle.group.excel.head.code=编码
vehicle.group.excel.head.name=名称
vehicle.wait.reason.marker.occupied=前方点位[{0}]被其他机器人占用
vehicle.wait.reason.marker.inControlArea=前方点位[{0}]处于封控区域
vehicle.wait.reason.noParkArea.occupied=前方禁停区域被其他机器人占用
vehicle.wait.reason.auto.door.closed=前方自动门设备未开门
vehicle.wait.reason.airshower.door.closed=前方风淋门设备未开门
vehicle.wait.reason.elevator.door.closed=前方电梯设备未开门
vehicle.wait.reason.marker.inForbiddenArea=前方点位[{0}]处于禁入区域

#warehouse
warehouse.code.empty=库位编码为空
warehouse.material.type.absent=物料编码[{0}]不存在
warehouse.code.duplicate=库位编码[{0}]已存在, 请重新输入
warehouse.barcode.duplicate=容器编码[{0}]已存在, 请重新输入
warehouse.start.need.less.than.end=开始排/列/层必须小于结束排/列/层
warehouse.height.number.consistent=层数数量与作业高度数据的数量要相等
warehouse.material.type.error=获取物料类型数据出错
warehouse.empty.or.disable=库位不存在或已禁用
warehouse.status.lock=该库位已被锁定
warehouse.status.store=该库位为存储状态
warehouse.barcode.inuse=该容器条码已被其他库位使用
warehouse.area.code.duplicate=库区编码[{0}]已存在, 请重新输入
warehouse.type.code.duplicate=类型编码[{0}]已存在, 请重新输入
warehouse.area.excel.head.code=编码
warehouse.area.excel.head.name=名称
warehouse.type.excel.head.code=编码
warehouse.type.excel.head.name=名称
warehouse.excel.head.code=编码
warehouse.excel.head.type.code=类型编码
warehouse.excel.head.area.code=库区编码
warehouse.excel.head.row=排数
warehouse.excel.head.colum=列数
warehouse.excel.head.layer=层数
warehouse.excel.head.work.height=作业高度
warehouse.excel.head.work.marker=作业点位
warehouse.excel.head.occupy.status=占用状态
warehouse.excel.head.barcode=容器条码
warehouse.excel.head.usage.code=启用状态
warehouse.excel.head.param1=扩展属性1
warehouse.excel.head.param2=扩展属性2
warehouse.excel.head.param3=扩展属性3
warehouse.excel.head.param4=扩展属性4
warehouse.excel.head.param5=扩展属性5
warehouse.excel.head.status.updatedate=库存更新时间
warehouse.usage.status.enable=启用
warehouse.usage.status.disable=禁用
warehouse.occupy.status.lock=锁定
warehouse.occupy.status.store=存储
warehouse.occupy.status.free=空闲

#statistics
statistics.cannot.gt.today=选择的日期不能超过今天
statistics.cannot.lt.one.year.ago=选择的日期不能在一年之前
statistics.start.cannot.gt.end=开始时间不能大于结束时间
statistics.name.avgHandleTime=平均处理时间
statistics.name.number=数量
statistics.name.other=其他
statistics.name.no.map=无地图
statistics.name.low=低
statistics.name.lower=较低
statistics.name.medium=中
statistics.name.higher=较高
statistics.name.high=高
statistics.name.busy=忙碌
statistics.name.free=空闲
statistics.name.abnormal=异常
statistics.name.charge=充电
statistics.name.park=泊车
statistics.name.work=作业
statistics.name.disconnect=未连接
statistics.name.wait=等待
statistics.name.running=执行
statistics.name.total.task=总任务
statistics.name.create=新建
statistics.name.finished=完成
statistics.name.cancel=取消
statistics.name.avgExecuteTime=平均执行时间
statistics.name.avgAllocateTime=平均执行时间
statistics.name.actual.rate=实际稼动率
statistics.name.theory.rate=理论稼动率
statistics.name.cpu.rate.total=总使用率
statistics.name.cpu.rate.java=JAVA
statistics.name.cpu.rate.mysql=MySQL
statistics.name.cpu.rate.other=其他
statistics.name.memo.rate.total=总内存
statistics.name.memo.rate.java=JAVA
statistics.name.memo.rate.mysql=MySQL
statistics.name.memo.rate.other=其他
statistics.name.disk.total=总存储
statistics.name.disk.used=已使用容量
statistics.name.disk.free=空闲容量
statistics.name.mysql.select=Select
statistics.name.mysql.delete=Delete
statistics.name.mysql.update=Update
statistics.name.mysql.insert=Insert
statistics.name.mysql.times.select=Select
statistics.name.mysql.times.delete=Delete
statistics.name.mysql.times.update=Update
statistics.name.mysql.times.insert=Insert
statistics.unit.number=个
statistics.unit.tai=台
statistics.unit.time=次
statistics.unit.second=秒
statistics.unit.minute=分钟
statistics.unit.hour=小时
statistics.unit.day=天

#charge.station
charge.station.not.exist.error=充电桩不存在
charge.station.connect.fail=充电桩连接失败
charge.station.request.timeout=充电桩请求超时，请检查网络连接
charge.station.reset.error=充电桩复位异常
charge.station.break_discharge.error=充电桩终止放电异常
charge.station.bind.marker.delete.error=该充电桩与充电点有绑定，请先解除绑定关系
charge.station.duplicate.bind.marker.error=充电桩[%s]已经绑定了点位[%s]，请重新选择

#config
config.unit.day=天
config.unit.meter=米
config.unit.second=秒
config.value.range=配置的数据范围
config.company.name=深圳优艾智合机器人科技有限公司
config.title.systemVersion=版本号
config.remark.systemVersion=版本号
config.title.ownCompany=版权所有
config.remark.ownCompany=版权所有
config.title.licenseCompanyName=授权信息-公司名称
config.remark.licenseCompanyName=授权信息-公司名称
config.title.licenseValidTimeRange=授权信息-有效期
config.remark.licenseValidTimeRange=授权信息-有效期
config.title.userOptLogExpireTime=操作日志
config.remark.userOptLogExpireTime=用户操作日志保留时间
config.title.interfaceLogExpireTime=接口日志
config.remark.interfaceLogExpireTime=系统接口日志保留时间
config.title.runningLogExpireTime=运行日志
config.remark.runningLogExpireTime=系统运行日志保留时间
config.title.notificationExpireTime=通知
config.remark.notificationExpireTime=系统告警通知保留时间
config.title.businessDataExpireTime=业务数据
config.remark.businessDataExpireTime=业务的运行数据保留时间，包含任务列表等业务数据
config.title.reportDataExpireTime=报表数据
config.remark.reportDataExpireTime=统计归档后的报表数据保留时间
config.title.markerSpacingCheck=点位间距
config.remark.markerSpacingCheck=点位间距判断开启
config.title.markerSpacing=点位间距
config.remark.markerSpacing=点位间距（mm）
config.title.markerAndPathSpacingCheck=点到路径间距
config.remark.markerAndPathSpacingCheck=点到路径间距判断开启
config.title.markerAndPathSpacing=点到路径间距
config.remark.markerAndPathSpacing=点到路径间距（mm）
config.title.blockCheckEnable=避障重规划
config.remark.blockCheckEnable=机器人遇到障碍物时，调度系统重新规划路径使机器人绕行
config.title.blockCheckInterval=避障重规划
config.remark.blockCheckInterval=避障触发时长
config.title.removeBlockInterval=避障重规划
config.remark.removeBlockInterval=障碍重置时长
config.title.abnormalVehicleRunPolicy=故障机器人
config.remark.abnormalVehicleRunPolicy=前方遇到故障或离线机器人，设定机器人的执行策略
config.title.freeVehicleRunPolicy=空闲机器人
config.remark.freeVehicleRunPolicy=前方遇到空闲机器人，设定机器人的执行策略
config.title.workVehicleRunPolicy=忙碌机器人
config.remark.workVehicleRunPolicy=前方遇到忙碌机器人，设定机器人的执行策略
config.title.avoidMarkerTypes=冲突避让点类型
config.remark.avoidMarkerTypes=多个机器人路径冲突时，允许机器人去避让的点类型
config.title.pathApplyLength=下发路径距离
config.remark.pathApplyLength=调度系统下发给机器人的路径长度，当网络环境较差时可增加该值
config.title.autoReleaseResource=断线释放资源
config.remark.autoReleaseResource=机器人断开网络一定时间后，调度系统释放该机器人占用的位置和区域
config.title.disconnectionTime=断线释放资源
config.remark.disconnectionTime=断线时长
config.title.occupyResourceRange=机器人虚拟半径
config.remark.occupyResourceRange=当机器人不在点位或路径时，以机器人中心为圆心，以该值为半径，占用该圆包含的所有点位
config.title.trackRadius=轨道半径
config.remark.trackRadius=当机器人到最近的点位或路径距离超过该值时, 系统判定机器人脱轨
config.title.channelAvoidance=通道避让
config.remark.channelAvoidance=启用通道避让后，对向机器人可在通道外主动避让
config.title.autoDoorAdvanceLength=提前呼叫自动门
config.remark.autoDoorAdvanceLength=机器人到自动门前点位的距离小于该值时呼叫开门，该值为0时不会提前呼叫
config.title.showerDoorAdvanceLength=提前呼叫风淋门
config.remark.showerDoorAdvanceLength=机器人到风淋门前点位的距离小于该值时呼叫开门，该值为0时不会提前呼叫
config.title.elevatorAdvanceLength=提前呼叫电梯
config.remark.elevatorAdvanceLength=机器人到电梯前点位的距离小于该值时呼叫开门，该值为0时不会提前呼叫
config.title.highPerformanceMode=高性能模式
config.remark.highPerformanceMode=当调度大规模机器人时，可启用高性能模式提高调度效率
config.title.highBattery=高电量（%）
config.remark.highBattery=高电量（%）
config.title.lowBattery=低电量（%）
config.remark.lowBattery=低电量（%）
config.title.chargeTaskTypeId=创建任务
config.remark.chargeTaskTypeId=创建任务
config.title.autoCharge=状态
config.remark.autoCharge=状态
config.title.parkTaskTypeId=创建任务
config.remark.parkTaskTypeId=创建任务
config.title.autoPark=状态
config.remark.autoPark=状态
config.title.pushCycle=推送周期
config.remark.pushCycle=在该时间范围内只推送一次
config.title.vehicleStatusPushUrl=机器人状态接口地址
config.remark.vehicleStatusPushUrl=机器人状态发生变化时, 推送机器人消息到该接口地址
config.title.noticePushUrl=消息推送地址
config.remark.noticePushUrl=错误类型的消息出现或消失时，推送异常数据到该接口地址
config.title.pdaVersion=pda最新版本
config.remark.pdaVersion=pda最新版本
config.title.vehicleStatusPushInterval=监控台机器人状态推送间隔(ms)
config.remark.vehicleStatusPushInterval=监控台机器人状态推送间隔(ms)
config.title.noticePushInterval=监控台小铃铛推送间隔(ms)
config.remark.noticePushInterval=监控台小铃铛推送间隔(ms)
config.title.mapElementPushInterval=监控台地图元素状态变更推送间隔(ms)
config.remark.mapElementPushInterval=监控台地图元素状态变更推送间隔(ms)
config.title.thirdSystemTrafficAreaReqUrl=交管区域资源申请地址
config.remark.thirdSystemTrafficAreaReqUrl=Fleet作为客户端时向服务端申请交管区域资源，设定申请接口地址
config.title.driveFreeVehicleFreeTime=空闲时长
config.remark.driveFreeVehicleFreeTime=当驱赶空闲机器人时，空闲机器人需要达到指定空闲时长
config.property.is.exist.error=系统属性已存在！
config.property.type.is.duplicate.error=系统类型分类重复！
config.title.globalPauseExecutingArmScriptIsStop=全场暂停
config.remark.globalPauseExecutingArmScriptIsStop=全场暂停时正在执行机械臂动作的机器人
config.title.noticePushLanguageType=消息语言配置
config.remark.noticePushLanguageType=定义错误类型的消息推送时的语言
config.title.exceptionNotifyTime=通知时间
config.remark.exceptionNotifyTime=机器人触发急停，分配/申请不到点位、区城资源达到设定时间时发起更高等级通知


#notice
notice.missing.notice.config=系统缺少该异常码配置信息
notice.level.common=普通
notice.level.warning=警告
notice.level.error=错误
notice.record.status.not.close=未关闭
notice.record.status.closed=已关闭
notice.config.excel.head.code=编码
notice.config.excel.head.level=等级
notice.config.excel.head.source=来源
notice.config.excel.head.invalidTime=间隔时间
notice.config.excel.head.desc=描述
notice.config.excel.head.solution=措施
notice.record.excel.head.code=编码
notice.record.excel.head.level=等级
notice.record.excel.head.source=来源
notice.record.excel.head.desc=描述
notice.record.excel.head.solution=措施
notice.record.excel.head.status=状态
notice.record.excel.head.vehicle=机器人
notice.record.excel.head.task=任务ID
notice.record.excel.head.device=设备ID
notice.record.excel.head.map=地图
notice.record.excel.head.create.date=创建时间
notice.record.excel.head.update.date=更新时间
notice.record.excel.head.close.date=关闭时间
notice.record.http.request.param=异常通知状态变更HTTP推送, 请求参数
notice.record.http.response.param=异常通知状态变更HTTP推送, 响应参数

notice.description.100001=base心跳超时
notice.solution.100001=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.100002=核心板启动超时
notice.solution.100002=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.100020=状态灯通讯故障
notice.solution.100020=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100021=信号灯通讯故障
notice.solution.100021=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100030=单点阵列雷达1通讯超时
notice.solution.100030=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100031=单点阵列雷达1故障
notice.solution.100031=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100032=单点阵列雷达2通讯超时
notice.solution.100032=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100033=单点阵列雷达2故障
notice.solution.100033=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100034=单点阵列雷达3通讯超时
notice.solution.100034=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100035=单点阵列雷达3故障
notice.solution.100035=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100036=单点阵列雷达4通讯超时
notice.solution.100036=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100037=单点阵列雷达4故障
notice.solution.100037=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100050=安全雷达1通讯超时
notice.solution.100050=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100051=安全雷达1故障
notice.solution.100051=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100052=安全雷达2通讯超时
notice.solution.100052=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100053=安全雷达2故障
notice.solution.100053=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100054=安全雷达3通讯超时
notice.solution.100054=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100055=安全雷达3故障
notice.solution.100055=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100056=安全雷达4通讯超时
notice.solution.100056=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100057=安全雷达4故障
notice.solution.100057=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100100=RE扩展板0通讯超时
notice.solution.100100=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100101=RE扩展板1通讯超时
notice.solution.100101=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100102=RE扩展板2通讯超时
notice.solution.100102=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100103=RE扩展板3通讯超时
notice.solution.100103=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100104=RE扩展板4通讯超时
notice.solution.100104=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100105=RE扩展板5通讯超时
notice.solution.100105=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100106=RE扩展板6通讯超时
notice.solution.100106=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100107=RE扩展板7通讯超时
notice.solution.100107=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100108=RE扩展板8通讯超时
notice.solution.100108=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100109=RE扩展板9通讯超时
notice.solution.100109=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100110=RE扩展板10通讯超时
notice.solution.100110=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100111=RE扩展板11通讯超时
notice.solution.100111=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100112=RE扩展板12通讯超时
notice.solution.100112=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100113=RE扩展板13通讯超时
notice.solution.100113=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100114=RE扩展板14通讯超时
notice.solution.100114=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.100115=RE扩展板15通讯超时
notice.solution.100115=可能接线松动，如果无法恢复或者异常多次出现请联系售后
notice.description.105000=左侧驱动器CAN通讯故障
notice.solution.105000=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105001=左侧驱动器Can节点启动超时
notice.solution.105001=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105002=左驱动器上电启动超时
notice.solution.105002=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105003=左驱动器PDO配置文件缺少
notice.solution.105003=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105009=左驱动器未定义错误
notice.solution.105009=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105010=左侧驱动器编码器故障
notice.solution.105010=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105011=左侧驱动器的电压过高
notice.solution.105011=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105012=左侧驱动器的电压过低
notice.solution.105012=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105013=左侧驱动器过流
notice.solution.105013=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105014=左侧驱动器温度过高
notice.solution.105014=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105015=左侧驱动器运行误差过大
notice.solution.105015=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105016=左侧驱动器逻辑电压异常
notice.solution.105016=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105017=左侧驱动器电机故障
notice.solution.105017=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105018=左侧驱动器故障
notice.solution.105018=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105019=左侧驱动器的系统数据错误
notice.solution.105019=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105020=左侧驱动器软件运行报错
notice.solution.105020=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105021=左侧驱动器的电机配置错误
notice.solution.105021=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105022=左侧驱动器的正限位报错
notice.solution.105022=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105023=左侧驱动器的负限位报错
notice.solution.105023=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105024=左侧驱动器的超速报警
notice.solution.105024=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105025=左侧驱动器过载
notice.solution.105025=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105026=左侧驱动器CAN BUS总线故障
notice.solution.105026=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105027=左侧驱动器OpenCan参数错误
notice.solution.105027=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105028=左侧驱动器OpenCan通讯异常
notice.solution.105028=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105029=左侧驱动器抱闸异常
notice.solution.105029=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105030=左侧驱动器异常停止
notice.solution.105030=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105031=左侧驱动器相电压异常
notice.solution.105031=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105100=右侧驱动器CAN通讯故障
notice.solution.105100=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105101=右侧驱动器Can节点启动超时
notice.solution.105101=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105102=右驱动器上电启动超时
notice.solution.105102=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105103=右驱动器PDO配置文件缺少
notice.solution.105103=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105109=右驱动器未定义错误
notice.solution.105109=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105110=右侧驱动器编码器故障
notice.solution.105110=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105111=右侧驱动器的电压过高
notice.solution.105111=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105112=右侧驱动器的电压过低
notice.solution.105112=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105113=右侧驱动器过流
notice.solution.105113=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105114=右侧驱动器温度过高
notice.solution.105114=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105115=右侧驱动器运行误差过大
notice.solution.105115=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105116=右侧驱动器逻辑电压异常
notice.solution.105116=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105117=右侧驱动器电机故障
notice.solution.105117=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105118=右侧驱动器故障
notice.solution.105118=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105119=右侧驱动器的系统数据错误
notice.solution.105119=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105120=右侧驱动器软件运行报错
notice.solution.105120=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105121=右侧驱动器的电机配置错误
notice.solution.105121=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105122=右侧驱动器的正限位报错
notice.solution.105122=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105123=右侧驱动器的负限位报错
notice.solution.105123=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105124=右侧驱动器的超速报警
notice.solution.105124=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105125=右侧驱动器过载
notice.solution.105125=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105126=右侧驱动器CAN BUS总线故障
notice.solution.105126=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105127=右侧驱动器OpenCan参数错误
notice.solution.105127=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105128=右侧驱动器OpenCan通讯异常
notice.solution.105128=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105129=右侧驱动器抱闸异常
notice.solution.105129=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105130=右侧驱动器异常停止
notice.solution.105130=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105131=右侧驱动器相电压异常
notice.solution.105131=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105200=升降驱动器CAN通讯故障
notice.solution.105200=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105201=升降驱动器 Can节点启动超时
notice.solution.105201=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105202=升降驱动器找原点超时
notice.solution.105202=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105203=升降驱动器上电启动超时
notice.solution.105203=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105204=升降驱动器PDO配置参数缺少
notice.solution.105204=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105209=升降驱动器未定义错误
notice.solution.105209=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105210=升降驱动器编码器故障
notice.solution.105210=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105211=升降驱动器的电压过高
notice.solution.105211=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105212=升降驱动器的电压过低
notice.solution.105212=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105213=升降驱动器过流
notice.solution.105213=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105214=升降驱动器温度过高
notice.solution.105214=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105215=升降驱动器运行误差过大
notice.solution.105215=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105216=升降驱动器逻辑电压异常
notice.solution.105216=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105217=升降驱动器电机故障
notice.solution.105217=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105218=升降驱动器故障
notice.solution.105218=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105219=升降驱动器的系统数据错误
notice.solution.105219=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105220=升降驱动器软件运行报错
notice.solution.105220=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105221=升降驱动器的电机配置错误
notice.solution.105221=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105222=升降驱动器的正限位报错
notice.solution.105222=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105223=升降驱动器的负限位报错
notice.solution.105223=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105224=升降驱动器的超速报警
notice.solution.105224=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105225=升降驱动器过载
notice.solution.105225=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105226=升降驱动器CAN BUS总线故障
notice.solution.105226=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105227=升降驱动器OpenCan参数错误
notice.solution.105227=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105228=升降驱动器OpenCan通讯异常
notice.solution.105228=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105229=升降驱动器异常停止
notice.solution.105229=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105230=升降驱动器相电压异常
notice.solution.105230=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105300=旋转驱动器CAN通讯故障
notice.solution.105300=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105301=旋转驱动器 Can节点启动超时
notice.solution.105301=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105302=旋转驱动器找原点超时
notice.solution.105302=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105303=旋转驱动器上电启动超时
notice.solution.105303=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105304=旋转驱动器PDO配置参数缺少
notice.solution.105304=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105309=旋转驱动器未定义错误
notice.solution.105309=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105310=旋转驱动器编码器故障
notice.solution.105310=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105311=旋转驱动器的电压过高
notice.solution.105311=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105312=旋转驱动器的电压过低
notice.solution.105312=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105313=旋转驱动器过流
notice.solution.105313=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105314=旋转驱动器温度过高
notice.solution.105314=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105315=旋转驱动器运行误差过大
notice.solution.105315=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105316=旋转驱动器逻辑电压异常
notice.solution.105316=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105317=旋转驱动器电机故障
notice.solution.105317=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105318=旋转驱动器故障
notice.solution.105318=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105319=旋转驱动器的系统数据错误
notice.solution.105319=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105320=旋转驱动器软件运行报错
notice.solution.105320=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105321=旋转驱动器的电机配置错误
notice.solution.105321=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105322=旋转驱动器的正限位报错
notice.solution.105322=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105323=旋转驱动器的负限位报错
notice.solution.105323=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105324=旋转驱动器的超速报警
notice.solution.105324=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105325=旋转驱动器过载
notice.solution.105325=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105326=旋转驱动器CAN BUS总线故障
notice.solution.105326=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105327=旋转驱动器OpenCan参数错误
notice.solution.105327=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105328=旋转驱动器OpenCan通讯异常
notice.solution.105328=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105329=旋转驱动器异常停止
notice.solution.105329=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105330=旋转驱动器相电压异常
notice.solution.105330=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105500=BMS 通讯异常
notice.solution.105500=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105501=BMS未定义错误
notice.solution.105501=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105502=充电过流异常
notice.solution.105502=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105503=放电过流异常
notice.solution.105503=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105504=电芯欠压
notice.solution.105504=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105505=电芯过压
notice.solution.105505=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105506=总体欠压
notice.solution.105506=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105507=总体过压
notice.solution.105507=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105508=压差高于充电允许压差上限
notice.solution.105508=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105509=电芯压差超过上限
notice.solution.105509=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105510=电芯温度超过充电温度上限
notice.solution.105510=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105511=电芯温度超过放电温度上限
notice.solution.105511=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105512=电芯温差超过充电温度上限
notice.solution.105512=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105513=电芯温差超过放电温度上限
notice.solution.105513=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105514=电芯温度低于充放电下限
notice.solution.105514=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105515=MOS管温度超过充电温度上限
notice.solution.105515=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105516=MOS管温差超过放单温差上限
notice.solution.105516=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105551=bms异常（包括保护、采样故障……）
notice.solution.105551=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105552=BMS包异常(包括过压，过流，高低温故障等信息)
notice.solution.105552=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105610=PDO1 过流
notice.solution.105610=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105611=PDO2 过流
notice.solution.105611=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105612=PDO3 过流
notice.solution.105612=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105613=PDO4过流
notice.solution.105613=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105614=PDO5 过流
notice.solution.105614=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105615=PDO6 过流
notice.solution.105615=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105616=PDO7 过流
notice.solution.105616=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105617=总PDO电流过大
notice.solution.105617=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105700=四轮四转，左侧转向电机未定义错误
notice.solution.105700=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105701=四轮四转，左侧转向电机软件错误
notice.solution.105701=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105702=四轮四转，左侧转向电机过压
notice.solution.105702=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105703=四轮四转，左侧转向电机低压
notice.solution.105703=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105704=四轮四转，左侧转向电机启动错误
notice.solution.105704=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105705=四轮四转，左侧转向电机过流
notice.solution.105705=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105706=四轮四转，左侧转向电机编码器错误
notice.solution.105706=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105707=四轮四转，左侧转向电机温度过高
notice.solution.105707=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105708=四轮四转，左侧转向电机电路板过高
notice.solution.105708=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105709=四轮四转，左侧转向电机通讯超时
notice.solution.105709=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105800=四轮四转，右侧转向电机未定义错误
notice.solution.105800=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105801=四轮四转，右侧转向电机软件错误
notice.solution.105801=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105802=四轮四转，右侧转向电机过压
notice.solution.105802=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105803=四轮四转，右侧转向电机低压
notice.solution.105803=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105804=四轮四转，右侧转向电机启动错误
notice.solution.105804=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105805=四轮四转，右侧转向电机过流
notice.solution.105805=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105806=四轮四转，右侧转向电机编码器错误
notice.solution.105806=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105807=四轮四转，右侧转向电机温度过高
notice.solution.105807=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105808=四轮四转，右侧转向电机电路板过高
notice.solution.105808=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105809=四轮四转，右侧转向电机通讯超时
notice.solution.105809=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105900=舵轮，前轮行走驱动器CAN通讯故障
notice.solution.105900=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105901=舵轮，前轮行走驱动器Can节点启动超时
notice.solution.105901=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105902=舵轮，前轮行走驱动器上电启动超时
notice.solution.105902=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105903=舵轮，前轮行走驱动器PDO配置参数缺少
notice.solution.105903=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105909=舵轮，前轮行走驱动器未定义错误
notice.solution.105909=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105910=舵轮，前轮行走驱动器编码器故障
notice.solution.105910=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105911=舵轮，前轮行走轮驱动器电压过高
notice.solution.105911=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105912=舵轮，前轮行走轮驱动器电压过低
notice.solution.105912=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105913=舵轮，前轮行走驱动器过流
notice.solution.105913=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105914=舵轮，前轮行走电机温度过高
notice.solution.105914=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105915=舵轮，前轮行走电机运行误差过大
notice.solution.105915=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105916=舵轮，前轮行走电机逻辑电压异常
notice.solution.105916=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105917=舵轮，前轮行走电机故障
notice.solution.105917=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105918=舵轮，前轮行走驱动器故障
notice.solution.105918=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105919=舵轮，前轮行走驱动器系统数据错误
notice.solution.105919=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105920=舵轮，前轮行走驱动器软件运行报错
notice.solution.105920=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105921=舵轮，前轮行轮电机配置错误
notice.solution.105921=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105922=舵轮，前轮行走电机超速报警
notice.solution.105922=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105923=舵轮，前轮行走电机过载
notice.solution.105923=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105924=舵轮，前轮行走驱动器CAN BUS总线故障
notice.solution.105924=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105925=舵轮，前轮行走驱动器OpenCan参数错误
notice.solution.105925=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105926=舵轮，前轮行走驱动器OpenCan通讯异常
notice.solution.105926=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105927=舵轮，前轮抱闸异常
notice.solution.105927=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105928=舵轮，前轮行走驱动器异常停止
notice.solution.105928=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.105929=舵轮，前轮行走电机相电压异常
notice.solution.105929=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106000=舵轮，前轮转向驱动器CAN通讯故障
notice.solution.106000=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106001=舵轮，前轮转向驱动器Can节点启动超时
notice.solution.106001=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106002=舵轮，前轮转向驱动器上电启动超时
notice.solution.106002=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106003=舵轮，前轮转向驱动器PDO配置参数缺少
notice.solution.106003=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106004=舵轮，前轮转向驱动器找原点超时
notice.solution.106004=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106009=舵轮，前轮转向驱动器未定义错误
notice.solution.106009=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106010=舵轮，前轮转向驱动器编码器故障
notice.solution.106010=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106011=舵轮，前轮转向驱动器电压过高
notice.solution.106011=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106012=舵轮，前轮转向驱动器电压过低
notice.solution.106012=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106013=舵轮，前轮转向驱动器过流
notice.solution.106013=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106014=舵轮，前轮转向电机温度过高
notice.solution.106014=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106015=舵轮，前轮转向电机运行误差过大
notice.solution.106015=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106016=舵轮，前轮转向电机逻辑电压异常
notice.solution.106016=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106017=舵轮，前轮转向电机故障
notice.solution.106017=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106018=舵轮，前轮转向驱动器故障
notice.solution.106018=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106019=舵轮，前轮转向驱动器系统数据错误
notice.solution.106019=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106020=舵轮，前轮转向驱动器软件运行报错
notice.solution.106020=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106021=舵轮，前轮转向电机配置错误
notice.solution.106021=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106022=舵轮，前轮转向驱动器正限位报错
notice.solution.106022=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106023=舵轮，前轮转向驱动器负限位报错
notice.solution.106023=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106024=舵轮，前轮转向电机超速报警
notice.solution.106024=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106025=舵轮，前轮转向电机过载
notice.solution.106025=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106026=舵轮，前轮转向驱动器CAN BUS总线故障
notice.solution.106026=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106027=舵轮，前轮转向驱动器OpenCan参数错误
notice.solution.106027=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106028=舵轮，前轮转向驱动器OpenCan通讯异常
notice.solution.106028=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106029=舵轮，前轮转向驱动器异常停止
notice.solution.106029=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106030=舵轮，前轮转向电机相电压异常
notice.solution.106030=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106100=舵轮，后轮行走驱动器CAN通讯故障
notice.solution.106100=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106101=舵轮，后轮行走驱动器Can节点启动超时
notice.solution.106101=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106102=舵轮，后轮行走驱动器上电启动超时
notice.solution.106102=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106103=舵轮，后轮行走驱动器PDO配置参数缺少
notice.solution.106103=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106109=舵轮，后轮行走驱动器未定义错误
notice.solution.106109=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106110=舵轮，后轮行走驱动器编码器故障
notice.solution.106110=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106111=舵轮，后轮行走轮驱动器电压过高
notice.solution.106111=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106112=舵轮，后轮行走轮驱动器电压过低
notice.solution.106112=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106113=舵轮，后轮行走驱动器过流
notice.solution.106113=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106114=舵轮，后轮行走电机温度过高
notice.solution.106114=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106115=舵轮，后轮行走电机运行误差过大
notice.solution.106115=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106116=舵轮，后轮行走电机逻辑电压异常
notice.solution.106116=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106117=舵轮，后轮行走电机故障
notice.solution.106117=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106118=舵轮，后轮行走驱动器故障
notice.solution.106118=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106119=舵轮，后轮行走驱动器系统数据错误
notice.solution.106119=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106120=舵轮，后轮行走驱动器软件运行报错
notice.solution.106120=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106121=舵轮，后轮行轮电机配置错误
notice.solution.106121=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106122=舵轮，后轮行走电机超速报警
notice.solution.106122=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106123=舵轮，后轮行走电机过载
notice.solution.106123=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106124=舵轮，后轮行走驱动器CAN BUS总线故障
notice.solution.106124=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106125=舵轮，后轮行走驱动器OpenCan参数错误
notice.solution.106125=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106126=舵轮，后轮行走驱动器OpenCan通讯异常
notice.solution.106126=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106127=舵轮，后轮抱闸异常
notice.solution.106127=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106128=舵轮，后轮行走异常停止
notice.solution.106128=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106129=舵轮，后轮行走电机相电压异常
notice.solution.106129=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106200=舵轮，后轮转向驱动器CAN通讯故障
notice.solution.106200=需要重启恢复，如果无法恢复或者异常多次出现请联系售后需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106201=舵轮，后轮转向驱动器Can节点启动超时
notice.solution.106201=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106202=舵轮，后轮转向驱动器上电启动超时
notice.solution.106202=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106203=舵轮，后轮转向驱动器PDO配置参数缺少
notice.solution.106203=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106204=舵轮，后轮转向驱动器找原点超时
notice.solution.106204=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106209=舵轮，后轮转向驱动器未定义错误
notice.solution.106209=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106210=舵轮，后轮转向驱动器编码器故障
notice.solution.106210=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106211=舵轮，后轮转向驱动器电压过高
notice.solution.106211=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106212=舵轮，后轮转向驱动器电压过低
notice.solution.106212=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106213=舵轮，后轮转向驱动器过流
notice.solution.106213=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106214=舵轮，后轮转向电机温度过高
notice.solution.106214=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106215=舵轮，后轮转向电机运行误差过大
notice.solution.106215=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106216=舵轮，后轮转向电机逻辑电压异常
notice.solution.106216=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106217=舵轮，后轮转向电机故障
notice.solution.106217=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106218=舵轮，后轮转向驱动器故障
notice.solution.106218=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106219=舵轮，后轮转向驱动器系统数据错误
notice.solution.106219=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106220=舵轮，后轮转向驱动器软件运行报错
notice.solution.106220=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106221=舵轮，后轮转向电机配置错误
notice.solution.106221=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106222=舵轮，后轮转向驱动器正限位报错
notice.solution.106222=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106223=舵轮，后轮转向驱动器负限位报错
notice.solution.106223=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106224=舵轮，后轮转向电机超速报警
notice.solution.106224=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106225=舵轮，后轮转向电机过载
notice.solution.106225=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106226=舵轮，后轮转向驱动器CAN BUS总线故障
notice.solution.106226=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106227=舵轮，后轮转向驱动器OpenCan参数错误
notice.solution.106227=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106228=舵轮，后轮转向驱动器OpenCan通讯异常
notice.solution.106228=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106229=舵轮，后轮转向驱动器异常停止
notice.solution.106229=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.106230=舵轮，后轮转向电机相电压异常
notice.solution.106230=需要重启恢复，如果无法恢复或者异常多次出现请联系售后
notice.description.110001=左驱动器-异常
notice.solution.110001=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110002=左驱动器-异常
notice.solution.110002=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110003=左驱动器-异常
notice.solution.110003=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110004=左驱动器-异常
notice.solution.110004=驱动器异常：需要关机并静置一小时后再使用。如果无法恢复或者异常多次出现请联系售后。
notice.description.110005=左驱动器-异常
notice.solution.110005=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110006=左驱动器-异常
notice.solution.110006=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110007=左驱动器-异常
notice.solution.110007=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110008=左驱动器-异常
notice.solution.110008=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110009=左驱动器-异常
notice.solution.110009=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110010=左驱动器-异常
notice.solution.110010=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110011=左驱动器-异常
notice.solution.110011=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110012=左驱动器-异常
notice.solution.110012=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110013=左驱动器-异常
notice.solution.110013=驱动器异常：需要关机并静置一小时后再使用。如果无法恢复或者异常多次出现请联系售后。
notice.description.110014=左驱动器-异常
notice.solution.110014=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110015=左驱动器-异常
notice.solution.110015=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110016=左驱动器-异常
notice.solution.110016=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110017=左驱动器-异常
notice.solution.110017=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110018=左驱动器-异常
notice.solution.110018=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110019=左驱动器-异常
notice.solution.110019=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110020=左驱动器-异常
notice.solution.110020=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110021=左驱动器-异常
notice.solution.110021=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110022=左驱动器-异常
notice.solution.110022=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110023=左驱动器-异常
notice.solution.110023=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110024=左驱动器-异常
notice.solution.110024=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110025=左驱动器-异常
notice.solution.110025=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110026=左驱动器-异常
notice.solution.110026=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110027=左驱动器-异常
notice.solution.110027=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110028=左驱动器-异常
notice.solution.110028=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110029=左驱动器-异常
notice.solution.110029=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110030=左驱动器-异常
notice.solution.110030=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110031=左驱动器-异常
notice.solution.110031=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110032=右驱动器-异常
notice.solution.110032=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110033=右驱动器-异常
notice.solution.110033=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110034=右驱动器-异常
notice.solution.110034=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110035=右驱动器-异常
notice.solution.110035=驱动器异常：需要关机并静置一小时后再使用。如果无法恢复或者异常多次出现请联系售后。
notice.description.110036=右驱动器-异常
notice.solution.110036=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110037=右驱动器-异常
notice.solution.110037=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110038=右驱动器-异常
notice.solution.110038=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110039=右驱动器-异常
notice.solution.110039=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110040=右驱动器-异常
notice.solution.110040=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110041=右驱动器-异常
notice.solution.110041=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110042=右驱动器-异常
notice.solution.110042=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110043=右驱动器-异常
notice.solution.110043=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110044=右驱动器-异常
notice.solution.110044=驱动器异常：需要关机并静置一小时后再使用。如果无法恢复或者异常多次出现请联系售后。
notice.description.110045=右驱动器-异常
notice.solution.110045=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110046=右驱动器-异常
notice.solution.110046=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110047=右驱动器-异常
notice.solution.110047=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110048=右驱动器-异常
notice.solution.110048=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110049=右驱动器-异常
notice.solution.110049=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110050=右驱动器-异常
notice.solution.110050=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110051=右驱动器-异常
notice.solution.110051=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110052=右驱动器-异常
notice.solution.110052=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110053=右驱动器-异常
notice.solution.110053=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110054=右驱动器-异常
notice.solution.110054=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110055=右驱动器-异常
notice.solution.110055=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110056=右驱动器-异常
notice.solution.110056=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110057=右驱动器-异常
notice.solution.110057=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110058=右驱动器-异常
notice.solution.110058=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110059=右驱动器-异常
notice.solution.110059=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110060=右驱动器-异常
notice.solution.110060=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110061=右驱动器-异常
notice.solution.110061=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110062=右驱动器-异常
notice.solution.110062=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110063=顶升驱动器-异常
notice.solution.110063=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110064=顶升驱动器-异常
notice.solution.110064=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110065=顶升驱动器-异常
notice.solution.110065=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110066=顶升驱动器-异常
notice.solution.110066=驱动器异常：需要关机并静置一小时后再使用。如果无法恢复或者异常多次出现请联系售后。
notice.description.110067=顶升驱动器-异常
notice.solution.110067=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110068=顶升驱动器-异常
notice.solution.110068=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110069=顶升驱动器-异常
notice.solution.110069=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110070=顶升驱动器-异常
notice.solution.110070=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110071=顶升驱动器-异常
notice.solution.110071=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110072=顶升驱动器-异常
notice.solution.110072=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110073=顶升驱动器-异常
notice.solution.110073=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110074=顶升驱动器-异常
notice.solution.110074=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110075=顶升驱动器-异常
notice.solution.110075=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110076=顶升驱动器-异常
notice.solution.110076=驱动器异常：需要关机并静置一小时后再使用。如果无法恢复或者异常多次出现请联系售后。
notice.description.110077=顶升驱动器-异常
notice.solution.110077=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110078=顶升驱动器-异常
notice.solution.110078=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110079=顶升驱动器-异常
notice.solution.110079=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110080=顶升驱动器-异常
notice.solution.110080=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110081=顶升驱动器-异常
notice.solution.110081=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110082=顶升驱动器-异常
notice.solution.110082=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110083=顶升驱动器-异常
notice.solution.110083=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110084=顶升驱动器-异常
notice.solution.110084=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110085=顶升驱动器-异常
notice.solution.110085=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110086=顶升驱动器-异常
notice.solution.110086=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110087=顶升驱动器-异常
notice.solution.110087=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110088=顶升驱动器-异常
notice.solution.110088=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110089=顶升驱动器-异常
notice.solution.110089=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110090=顶升驱动器-异常
notice.solution.110090=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110091=旋转驱动器-异常
notice.solution.110091=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110092=旋转驱动器-异常
notice.solution.110092=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110093=旋转驱动器-异常
notice.solution.110093=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110094=旋转驱动器-异常
notice.solution.110094=驱动器异常：需要关机并静置一小时后再使用。如果无法恢复或者异常多次出现请联系售后。
notice.description.110095=旋转驱动器-异常
notice.solution.110095=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110096=旋转驱动器-异常
notice.solution.110096=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110097=旋转驱动器-异常
notice.solution.110097=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110098=旋转驱动器-异常
notice.solution.110098=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110099=旋转驱动器-异常
notice.solution.110099=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110100=旋转驱动器-异常
notice.solution.110100=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110101=旋转驱动器-异常
notice.solution.110101=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110102=旋转驱动器-异常
notice.solution.110102=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110103=旋转驱动器-异常
notice.solution.110103=驱动器异常：需要关机并静置一小时后再使用。如果无法恢复或者异常多次出现请联系售后。
notice.description.110104=旋转驱动器-异常
notice.solution.110104=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110105=旋转驱动器-异常
notice.solution.110105=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110106=旋转驱动器-异常
notice.solution.110106=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110107=旋转驱动器-异常
notice.solution.110107=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110108=旋转驱动器-异常
notice.solution.110108=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110109=旋转驱动器-异常
notice.solution.110109=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110110=旋转驱动器-异常
notice.solution.110110=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110111=旋转驱动器-异常
notice.solution.110111=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110112=旋转驱动器-异常
notice.solution.110112=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110113=旋转驱动器-异常
notice.solution.110113=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110114=旋转驱动器-异常
notice.solution.110114=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110115=旋转驱动器-异常
notice.solution.110115=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110116=旋转驱动器-异常
notice.solution.110116=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110117=旋转驱动器-异常
notice.solution.110117=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110118=旋转驱动器-异常
notice.solution.110118=驱动器异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.110200=电机初始化-异常
notice.solution.110200=行走电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接。重启若无法恢复请联系售后。
notice.description.110201=电机初始化-异常
notice.solution.110201=升降电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接。重启若无法恢复请联系售后。
notice.description.110202=电机初始化-异常
notice.solution.110202=插取电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接。重启若无法恢复请联系售后。
notice.description.110203=电机初始化-异常
notice.solution.110203=旋转电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接。重启若无法恢复请联系售后。
notice.description.110204=电机初始化-异常
notice.solution.110204=夹取电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接。重启若无法恢复请联系售后。
notice.description.110205=电机初始化-异常
notice.solution.110205=森创升降电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接。重启若无法恢复请联系售后。
notice.description.110206=电机初始化-异常
notice.solution.110206=森创旋转电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接。重启若无法恢复请联系售后。
notice.description.110207=电机初始化-异常
notice.solution.110207=SR电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接。重启若无法恢复请联系售后。
notice.description.110300=电机控制-异常
notice.solution.110300=下发的旋转指令超过范围：-180~180。清除错误后重新下发即可。
notice.description.110301=电机控制-异常
notice.solution.110301=下发的旋转速度指令超过范围。清除错误后重新下发即可。速度最大8转每分钟。
notice.description.110302=电机控制-异常
notice.solution.110302=下发的升降指令超过范围。清除错误后重新下发即可。若在合理范围内一直不能成功，请联系售后。
notice.description.110303=电机控制-异常
notice.solution.110303=下发的升降速度指令超过范围。清除错误后重新下发即可。速度10mm/s。
notice.description.110400=电机运行-异常
notice.solution.110400=触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后
notice.description.110401=电机运行-异常
notice.solution.110401=触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后
notice.description.110402=电机运行-异常
notice.solution.110402=触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后
notice.description.110403=电机运行-异常
notice.solution.110403=触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后
notice.description.113001=Can卡-异常
notice.solution.113001=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113002=Can卡-异常
notice.solution.113002=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113003=Can卡-异常
notice.solution.113003=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113004=Can卡-异常
notice.solution.113004=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113005=Can卡-异常
notice.solution.113005=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113006=Can卡-异常
notice.solution.113006=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113007=Can卡-异常
notice.solution.113007=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113008=Can卡-异常
notice.solution.113008=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113009=Can卡-异常
notice.solution.113009=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113010=Can卡-异常
notice.solution.113010=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113011=Can卡-异常
notice.solution.113011=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113012=Can卡-异常
notice.solution.113012=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113013=Can卡-异常
notice.solution.113013=Can卡异常：需要重启恢复。如果无法恢复或者异常多次出现请联系售后。
notice.description.113015=Can卡-异常
notice.solution.113015=Can卡异常：可能CAN卡未连接或者连接线断开。若重启无法解决，请联系售后。
notice.description.113016=Can卡-异常
notice.solution.113016=Can卡异常：可能CAN卡设备异常，若多次重启未恢复，请联系售后。
notice.description.113017=Can卡-异常
notice.solution.113017=Can卡异常：可能CAN卡设备异常，若多次重启未恢复，请联系售后。
notice.description.114000=IMU-异常
notice.solution.114000=检查串口端口号是否正确。
notice.description.114001=IMU-异常
notice.solution.114001=检查串口是否连接正常。
notice.description.114002=IMU-异常
notice.solution.114002=检查串口是否连接正常。
notice.description.114003=IMU-异常
notice.solution.114003=检查串口是否连接正常，以及干扰情况。
notice.description.114004=IMU-异常
notice.solution.114004=检查串口是否连接正常，以及干扰情况。
notice.description.120001=充电-异常
notice.solution.120001=可能电池的通信线连接不良，或者间歇性通信失败。重新执行任务，若依旧出错请联系售后。
notice.description.120002=充电-异常
notice.solution.120002=重启重新尝试后若依旧报错请联系售后。
notice.description.120003=充电-异常
notice.solution.120003=重启重新尝试后若依旧报错请联系售后。
notice.description.120004=充电-异常
notice.solution.120004=重启重新尝试后若依旧报错请联系售后。
notice.description.120005=充电-异常
notice.solution.120005=可能环境因素导致对接信息采集失败。调整环境至良好无干扰的情况再重新尝试任务，若调整后依旧失败请联系售后。
notice.description.120006=充电-异常
notice.solution.120006=可能环境因素导致对接信息采集失败。调整环境至良好无干扰的情况再重新尝试任务，若调整后依旧失败请联系售后。
notice.description.120007=充电-异常
notice.solution.120007=若没良好对接充电桩，请调整对接参数。确保充电桩处于自动模式。
notice.description.120008=充电-异常
notice.solution.120008=降低充满百分比，默认为97%。若降低到89%依旧还有问题，请联系售后。
notice.description.120100=bms-异常
notice.solution.120100=检查串口是否正常
notice.description.120101=bms-异常
notice.solution.120101=检查读取指令数据是否正确，检查电池通信协议。
notice.description.120102=bms-异常
notice.solution.120102=检查写入指令数据是否正确，检查电池通信协议。
notice.description.120103=bms-异常
notice.solution.120103=检查串口是否正常。
notice.description.120104=bms-异常
notice.solution.120104=检查串口是否正常。
notice.description.120106=bms-异常
notice.solution.120106=检查串口数据是否干扰。
notice.description.120107=bms-异常
notice.solution.120107=检查电池数量
notice.description.120108=bms-异常
notice.solution.120108=检查双电池电压
notice.description.121001=音频-异常
notice.solution.121001=确认所指定播放的音频名字存在与AGV内，注意音频名字末尾不要加后缀，如.mp3。确保音频为mp3格式。
notice.description.123004=Socket-异常
notice.solution.123004=重新确认API端口号与接口号是否正确
notice.description.123005=Socket-异常
notice.solution.123005=需要重启恢复，如果无法恢复或者异常多次出现请联系售后。
notice.description.123006=Socket-异常
notice.solution.123006=无法获取配置信息，请联系售后
notice.description.127001=导航执行异常
notice.solution.127001=导航执行异常：需要停止当前任务，并联系售后。
notice.description.127002=导航执行异常
notice.solution.127002=导航执行异常：需要停止当前任务，并且人工判断机器人是否脱轨，如果脱轨，请将机器人移动到路径上。如果无法恢复或者异常多次出现请联系售后。
notice.description.127003=导航执行异常
notice.solution.127003=导航执行异常：需要停止当前任务，并且人工判断定位数据是否存在。如果无法恢复或者异常多次出现请联系售后。
notice.description.127004=导航执行异常
notice.solution.127004=导航执行异常：需要停止当前任务，并且人工判断货架下方标记物是否识别正常。如果无法恢复或者异常多次出现请联系售后。
notice.description.127005=导航执行异常
notice.solution.127005=导航执行异常：需要停止当前任务，并且判断是否存在雷达以及pcl相关报错。如果无法恢复或者异常多次出现请联系售后。
notice.description.127006=导航执行异常
notice.solution.127006=导航执行异常：需要停止当前任务，并且判断是否存在电机相关报错。如果无法恢复或者异常多次出现请联系售后。
notice.description.127007=导航执行异常
notice.solution.127007=导航执行异常：需要停止当前任务，并且判断是否存在定位相关报错，如果没有，激光定位情况下请判断是否存在雷达相关报错，二维码定位判断是否存在二维码传感器通讯异常。如果无法恢复或者异常多次出现请联系售后。
notice.description.127008=导航执行异常
notice.solution.127008=导航执行异常：需要停止当前任务，判断激光雷达数据是否正常。如果无法恢复或者异常多次出现请联系售后。
notice.description.127009=导航执行异常
notice.solution.127009=导航执行异常：需要停止当前任务，当前路径周遭人工特征是否存在被遮挡的情况，如果存在，请避免遮挡。如果无法恢复或者异常多次出现请联系售后。
notice.description.127010=导航执行异常
notice.solution.127010=导航执行异常：需要停止当前任务，并且判断当前激光定位数据是否正常。如果无法恢复或者异常多次出现请联系售后。
notice.description.127011=导航执行异常111
notice.solution.127011=导航执行异常：需要停止当前任务，并且判断当前激光定位数据是否正常。如果无法恢复或者异常多次出现请联系售后。222
notice.description.127012=定向曲线角度变化范围过大
notice.solution.127012=请减小路径弯曲程度，使路径更平滑
notice.description.128001=对接执行异常
notice.solution.128001=对接执行异常：需要清除错误状态，并且判断当前机器人是否在执行对接，或者是否在之前执行对接指令后，未执行脱离对接。如果无法恢复或者异常多次出现请联系售后。
notice.description.128002=对接执行异常
notice.solution.128002=对接执行异常：需要清除错误状态，并且判断当前指定的对接目标是否合理。如果无法恢复或者异常多次出现请联系售后。
notice.description.128003=对接执行异常
notice.solution.128003=对接执行异常：需要清除错误状态，并且判断特征检测模块运行状态。如果无法恢复或者异常多次出现请联系售后。
notice.description.128004=对接执行异常
notice.solution.128004=对接执行异常：需要清除错误状态，并且判断特征检测模块运行状态。如果无法恢复或者异常多次出现请联系售后。
notice.description.128005=对接执行异常
notice.solution.128005=对接执行异常：需要清除错误状态，并且判断特征检测模块运行状态。如果无法恢复或者异常多次出现请联系售后。
notice.description.128006=对接执行异常
notice.solution.128006=对接执行异常：需要清除错误状态，并请联系售后。
notice.description.128007=对接执行异常
notice.solution.128007=对接执行异常：需要清除错误状态，并且判断特征检测模块运行状态。如果无法恢复或者异常多次出现请联系售后。
notice.description.128008=对接执行异常
notice.solution.128008=对接执行异常：需要清除错误状态，需要清除错误状态，并且判断当前机器人是否在执行脱离对接，或者是否在之前未执行对接动作。如果无法恢复或者异常多次出现请联系售后。
notice.description.128009=对接执行异常
notice.solution.128009=需要清除错误状态，并且判断是否存在定位相关报错，如果没有，请判断是否存在雷达相关报错。如果无法恢复或者异常多次出现请联系售后。
notice.description.128010=对接执行异常
notice.solution.128010=对接执行异常：需清楚错误状态，并且判断特征检测运行状态。如果无法恢复或异常出现多次请联系售后
notice.description.128011=二维码货架对准异常
notice.solution.128011=二维码货架对准异常：需要清除错误状态，并判断是否在二维码货架对准过程中又执行了一次对准
notice.description.128012=二维码货架对准异常
notice.solution.128012=二维码货架对准异常：检查是否照到二维码，若照到检查是否开启二维码识别节点，以及节点参数是否配置正确
notice.description.128100=侧面对准异常
notice.solution.128100=侧面对准异常：需要清除错误状态，并判断是否在侧面对准过程中又执行了一次对准
notice.description.128101=侧面对准异常
notice.solution.128101=检查侧面对接传感器与机台的距离是否在阈值范围内
notice.description.130001=定位异常
notice.solution.130001=请尝试重定位。如果无法恢复或者异常多次出现请联系售后。
notice.description.133001=特征检测执行异常
notice.solution.133001=特征检测执行异常：人工判断机器人是否在对接范围内，特征是否与机器人雷达高度一致。如果无法恢复或者异常多次出现请联系售后。
notice.description.133002=特征检测执行异常
notice.solution.133002=特征检测执行异常：人工判断是否有相似特征，调整机器人对接距离或方向。如果无法回复或异常多次出现请联系售后。
notice.description.133003=特征检测执行异常
notice.solution.133003=特征检测执行异常：判断是否存在激光雷达相关报错。如果无法恢复或异常多次出现请联系售后。
notice.description.133004=特征检测执行异常
notice.solution.133004=特征检测执行异常：判断是否存在定位相关报错。如果无法恢复或异常出现多次请联系售后。
notice.description.135001=建图执行异常
notice.solution.135001=建图执行异常：判断是否存在激光雷达相关报错。如果无法恢复或异常多次出现请联系售后。
notice.description.135002=建图执行异常
notice.solution.135002=建图执行异常：人工判断绘图过程是否形成回环。如果一直没有回环请联系售后。
notice.description.140000=脚本异常
notice.solution.140000=脚本异常：请检查socket或者lua指令的参数，例如：长度、类型等。
notice.description.140001=脚本异常
notice.solution.140001=脚本异常：指定控制的脚本不存在，请检查脚本的名字或者ID。
notice.description.140002=脚本异常
notice.solution.140002=脚本异常：脚本正在运行中，不运行用户当前的控制指令。
notice.description.140003=脚本异常
notice.solution.140003=脚本异常：脚本子线程没有正常退出，这种情况属于致命BUG，请联系开发人员
notice.description.140004=脚本异常
notice.solution.140004=脚本异常：启动脚本超时，检测脚本存放路径是否正确。如检测无误，请联系开发人员。
notice.description.140005=脚本异常
notice.solution.140005=脚本异常：停止脚本超时，检测脚本是否已经退出。如检测无误，请联系开发人员。
notice.description.140006=脚本异常
notice.solution.140006=脚本异常：下发的控制指令不存在，请检查json指令的command。
notice.description.140007=脚本异常
notice.solution.140007=脚本异常：指定的脚本变量地址错误，请检查下发的脚本变量的地址是否不在指定范围内【0，31】。
notice.description.140008=脚本异常
notice.solution.140008=脚本异常：请联系开发人员。
notice.description.140009=脚本异常
notice.solution.140009=脚本异常：请检查lua脚本中是否有main函数
notice.description.140010=脚本异常
notice.solution.140010=脚本异常：请检查lua脚本中是否有exception函数
notice.description.140011=脚本异常
notice.solution.140011=脚本异常：请检查lua脚本中是否有cancel函数
notice.description.140012=脚本异常
notice.solution.140012=脚本异常：这种情况要检查配置文件
notice.description.140013=脚本异常
notice.solution.140013=脚本异常：用户传输的json有问题，检查以下json数据并联系开发人员
notice.description.140014=脚本异常
notice.solution.140014=脚本异常：用户传输的json有问题，检查以下json数据并联系开发人员
notice.description.140015=脚本异常
notice.solution.140015=脚本异常：核对接口要求的变量类型。
notice.description.140016=脚本异常
notice.solution.140016=脚本异常： 1.检查目标点是否存在； 2.检查当前位置是否有路径通往目标点； 3.检查机器人定位是否失效； 若不是联系开发人员。
notice.description.140017=脚本异常
notice.solution.140017=脚本异常： 1.检查目标点是否存在； 2.检查当前位置是否有路径通往目标点； 3.检查机器人定位是否失效； 若不是联系开发人员。
notice.description.140018=脚本异常
notice.solution.140018=脚本异常：compass下发的字段有问题，请联系开发人员。
notice.description.140019=脚本异常
notice.solution.140019=脚本异常：compass的socket服务断开连接。
notice.description.140020=脚本异常
notice.solution.140020=脚本异常：直线运动出错，请联系开发人员。
notice.description.140021=脚本异常
notice.solution.140021=脚本异常：检查环境是否满足对接条件，请联系开发人员。
notice.description.140022=脚本异常
notice.solution.140022=脚本异常：检查算法节点是否已经异常，请联系开发人员。
notice.description.140023=脚本异常
notice.solution.140023=脚本异常：检查脚本的格式是否正确，请联系开发人员。
notice.description.140024=脚本异常
notice.solution.140024=脚本异常：脚本执行过程中发现异常，请联系开发人员。
notice.description.140025=脚本异常
notice.solution.140025=脚本异常：脚本执行路径导航异常，请联系开发人员。
notice.description.140026=脚本异常
notice.solution.140026=脚本异常：call新的脚本异常，请联系开发人员。
notice.description.140027=脚本异常
notice.solution.140027=脚本异常：请正确编辑脚本的后缀名为“.lua”。
notice.description.140028=脚本异常
notice.solution.140028=脚本异常：未知异常，请联系开发人员。
notice.description.145000=脚本异常
notice.solution.145000=脚本异常：连接机械臂超时，请检测机械臂是否已经上电、网线是否正常。
notice.description.145001=脚本异常
notice.solution.145001=脚本异常：机械臂没有建立连接，请检测机械臂是否已经上电、网线是否正常。
notice.description.145002=脚本异常
notice.solution.145002=脚本异常：请联系开发人员。
notice.description.145003=脚本异常
notice.solution.145003=脚本异常：控制机械臂输入的参数错误，请根据协议检查参数。
notice.description.145004=脚本异常
notice.solution.145004=脚本异常：机械臂返回的消息错误，请联系开发人员。
notice.description.145005=脚本异常
notice.solution.145005=脚本异常：下发控制机械臂的指令错误，请根据协议检查下发的指令。
notice.description.145006=脚本异常
notice.solution.145006=脚本异常：请联系开发人员。
notice.description.145007=脚本异常
notice.solution.145007=脚本异常：请联系开发人员。
notice.description.146000=脚本异常
notice.solution.146000=脚本异常：请联系开发人员。
notice.description.146001=脚本异常
notice.solution.146001=脚本异常：请联系开发人员。
notice.description.147001=声光系统-异常
notice.solution.147001=重启后若依旧无法解决，则联系售后
notice.description.147002=单点雷达-异常
notice.solution.147002=重启后若依旧无法解决，则联系售后
notice.description.147004=侧面光电对接-异常
notice.solution.147004=检查网络IP地址。
notice.description.150000=雷达异常
notice.solution.150000=检查网络端口和ip地址。
notice.description.150002=雷达异常
notice.solution.150002=检查设置频率是否在合理范围。
notice.description.150003=雷达异常
notice.solution.150003=检查设置采样率是否在合理范围。
notice.description.150004=雷达异常
notice.solution.150004=检查网络是否正常。
notice.description.150005=雷达异常
notice.solution.150005=检查网络是否正常。
notice.description.150100=PLC客户端异常
notice.solution.150100=检查网络ip地址和端口号。
notice.description.150101=PLC客户端异常
notice.solution.150101=检查网络ip地址和端口号。
notice.description.150102=PLC客户端异常
notice.solution.150102=检查网络是否正常。
notice.description.150103=PLC客户端异常
notice.solution.150103=检查网络是否正常。
notice.description.150104=PLC客户端异常
notice.solution.150104=检查是否有急停信号触发。
notice.description.150151=安全PLC客户端异常
notice.solution.150151=检查网络ip地址和端口号。
notice.description.150152=安全PLC客户端异常
notice.solution.150152=检查网络是否正常。
notice.description.150153=安全PLC客户端异常
notice.solution.150153=检查网络是否正常。
notice.description.150154=安全PLC客户端异常
notice.solution.150154=检查是否有急停信号触发。
notice.description.150155=安全PLC客户端异常
notice.solution.150155=检查编码器报警。
notice.description.150300=二维码异常
notice.solution.150300=检查串口端口号是否正确。
notice.description.150301=二维码异常
notice.solution.150301=检查串口是否连接正常。
notice.description.150302=二维码异常
notice.solution.150302=检查串口是否连接正常。
notice.description.150303=二维码异常
notice.solution.150303=检查串口是否连接正常，以及干扰情况。
notice.description.150304=二维码异常
notice.solution.150304=检查串口是否连接正常，以及干扰情况。
notice.description.150310=二维码异常
notice.solution.150310=检查相机是否连接正常
notice.description.150311=二维码异常
notice.solution.150311=检查相机是否连接正常
notice.description.150312=二维码异常
notice.solution.150312=检查相机是否连接正常
notice.description.150313=二维码异常
notice.solution.150313=检查相机是否连接正常
notice.description.150400=3D相机异常
notice.solution.150400=检查相机是否连接正常。
notice.description.150401=3D相机异常
notice.solution.150401=检查相机是否配置正常。
notice.description.150500=超声波异常
notice.solution.150500=检查超声波是否连接正常。
notice.description.150501=超声波异常
notice.solution.150501=检查超声波是否配置正常。
notice.description.170001=上集成异常
notice.solution.170001=上集成异常：请联系开发人员。
notice.description.170002=上集成异常
notice.solution.170002=上集成报警：请按照上集成操作手册复位。
notice.description.170003=上集成异常
notice.solution.170003=上集成异常：请复位驱动器清除故障，或者断电重启。
notice.description.170004=上集成异常
notice.solution.170004=上集成异常：请复位驱动器清除故障，或者断电重启。
notice.description.170005=上集成异常
notice.solution.170005=上集成异常：请复位驱动器清除故障，或者断电重启。
notice.description.170006=上集成异常
notice.solution.170006=上集成异常：请复位驱动器清除故障，或者断电重启。
notice.description.170007=上集成异常
notice.solution.170007=上集成异常：请复位驱动器清除故障，或者断电重启。
notice.description.170008=上集成异常
notice.solution.170008=上集成异常：松开急停可恢复
notice.description.171001=海康云台（上集成）异常
notice.solution.171001=海康云台异常: 1.检查网线是否松动 2.检查网络通信是否正常
notice.description.171002=声纳传感器（上集成）异常
notice.solution.171002=声纳传感器异常:请检查设备连接是否正常
notice.description.171003=防跌落传感器异常
notice.solution.171003=防跌落传感器异常:请检查设备连接是否正常
notice.description.171004=防跌落传感器异常
notice.solution.171004=防跌落传感器异常:请检查设备连接是否正常
notice.description.171005=防跌落传感器异常
notice.solution.171005=防跌落传感器异常:请检查设备连接是否正常
notice.description.171006=防跌落传感器异常
notice.solution.171006=防跌落传感器异常:请检查设备连接是否正常
notice.description.171007=超声波传感器（上集成）异常
notice.solution.171007=超声波传感器异常:请检查设备连接是否正常
notice.description.171008=超声波传感器（上集成）异常
notice.solution.171008=超声波传感器异常:请检查设备连接是否正常
notice.description.171009=超声波传感器（上集成）异常
notice.solution.171009=超声波传感器异常:请检查设备连接是否正常
notice.description.171010=超声波传感器（上集成）异常
notice.solution.171010=超声波传感器异常:请检查设备连接是否正常
notice.description.200017=找不到已下发给Pilot的指令
notice.solution.200017=请联系技术支持人员
notice.description.200018=指令执行失败
notice.solution.200018=请联系技术支持人员
notice.description.200101=按钮急停
notice.solution.200101=请检查机器人状态
notice.description.200102=安全设备急停
notice.solution.200102=请检查机器人状态
notice.description.200103=碰撞急停
notice.solution.200103=请检查机器人状态
notice.description.200104=路径导航急停异常
notice.solution.200104=请检查机器人状态
notice.description.200105=机器人升降急停
notice.solution.200105=机器人升降急停
notice.description.200106=机器人升降错误
notice.solution.200106=机器人升降错误
notice.description.200107=机器人滚筒急停
notice.solution.200107=机器人滚筒急停
notice.description.200108=机器人滚筒错误
notice.solution.200108=机器人滚筒错误
notice.description.200109=机器人开启暂停
notice.solution.200109=机器人开启暂停
notice.description.200110=机器人手动控制模式
notice.solution.200110=机器人手动控制模式
notice.description.200111=机器人未定位
notice.solution.200111=机器人未定位
notice.description.200112=该机器人的控制模式只支持硬件旋钮控制！
notice.solution.200112=该机器人的控制模式只支持硬件旋钮控制！
notice.description.200113=机械臂处于未就绪
notice.solution.200113=检查机器臂状态
notice.description.200114=机器人异常
notice.solution.200114=检查机器人状态
notice.description.200120=仿真不支持该指令
notice.solution.200120=检查机器人下发的指令
notice.description.300001=系统内部错误
notice.solution.300001=请联系技术支持人员
notice.description.300002=统计模块程序异常
notice.solution.300002=请联系技术支持人员
notice.description.300003=地图模块程序异常
notice.solution.300003=请联系技术支持人员
notice.description.300004=机器人模块程序异常
notice.solution.300004=请联系技术支持人员
notice.description.300005=任务模块程序异常
notice.solution.300005=请联系技术支持人员
notice.description.300006=交管模块程序异常
notice.solution.300006=请联系技术支持人员
notice.description.300007=事件模块程序异常
notice.solution.300007=请联系技术支持人员
notice.description.300008=风淋门模块异常
notice.solution.300008=请联系技术支持人员
notice.description.300009=自动门模块异常
notice.solution.300009=请联系技术支持人员
notice.description.300010=电梯模块异常
notice.solution.300010=请联系技术支持人员
notice.description.300011=呼叫盒模块异常
notice.solution.300011=请联系技术支持人员
notice.description.300012=异常
notice.solution.300012=请联系技术支持人员
notice.description.300013=CPU资源占用过高
notice.solution.300013=请联系技术支持人员
notice.description.300014=内存资源占用过高
notice.solution.300014=请联系技术支持人员
notice.description.300015=硬盘资源占用过高
notice.solution.300015=请联系技术支持人员
notice.description.300016=充电桩状态模块程序异常
notice.solution.300016=请联系技术支持人员
notice.description.300101=机器人占用点位失败
notice.solution.300101=机器人所在点位已被其它机器人占用，请移动机器人到路网上线
notice.description.300102=机器人占用区域失败
notice.solution.300102=机器人所在单机区域已被其它机器人占用，请移动机器人到路网上线
notice.description.300103=机器人占用电梯失败
notice.solution.300103=机器人所在电梯已被其它机器人占用，请移动机器人到路网上线
notice.description.300104=机器人已断线
notice.solution.300104=1：请检查机器人和服务器的网络是否连接正常； 2：请检查机器人的电源是否已打开； 3：请检查机器人配置的系统IP和端口是否正确；
notice.description.300105=机器人脱离轨道
notice.solution.300105=请检查机器人的定位状态是否准确，如果准确，请移动机器人到路网上线
notice.description.300106=机器人手动控制模式
notice.solution.300106=机器人处于手动控制模式，请切换成自动控制模式
notice.description.300107=机器人检修控制模式
notice.solution.300107=机器人处于检修控制模式，请切换成自动控制模式
notice.description.300201=自动门连接失败
notice.solution.300201=1：请检查设备和服务器的网络是否连接正常； 2：请检查设备的电源是否已打开； 3：请在地图编排界面检查设备IP、端口号配置是否正确；
notice.description.300202=风淋门连接失败
notice.solution.300202=1：请检查设备和服务器的网络是否连接正常； 2：请检查设备的电源是否已打开； 3：请在地图编排界面检查设备IP、端口号配置是否正确；
notice.description.300203=电梯连接失败
notice.solution.300203=1：请检查设备和服务器的网络是否连接正常； 2：请检查设备的电源是否已打开； 3：请在地图编排界面检查设备IP、端口号配置是否正确；
notice.description.300204=读取自动门指令失败
notice.solution.300204=1：请检查设备和服务器的网络是否连接正常； 2：请检查设备的电源是否已打开； 3：请在地图编排界面检查设备读取地址配置是否正确；
notice.description.300205=读取风淋门指令失败
notice.solution.300205=1：请检查设备和服务器的网络是否连接正常； 2：请检查设备的电源是否已打开； 3：请在地图编排界面检查设备读取地址配置是否正确；
notice.description.300206=读取电梯指令失败
notice.solution.300206=1：请检查设备和服务器的网络是否连接正常； 2：请检查设备的电源是否已打开； 3：请在地图编排界面检查设备读取地址配置是否正确；
notice.description.300207=写入自动门指令失败
notice.solution.300207=1：请检查设备和服务器的网络是否连接正常； 2：请检查设备的电源是否已打开； 3：请在地图编排界面检查设写入地址配置是否正确；
notice.description.300208=写入风淋门指令失败
notice.solution.300208=1：请检查设备和服务器的网络是否连接正常； 2：请检查设备的电源是否已打开； 3：请在地图编排界面检查设写入地址配置是否正确；
notice.description.300209=写入电梯指令失败
notice.solution.300209=1：请检查设备和服务器的网络是否连接正常； 2：请检查设备的电源是否已打开； 3：请在地图编排界面检查设写入地址配置是否正确；
notice.description.300210=自动门未绑定路径
notice.solution.300210=请打开地图编辑界面，为该自动门绑定路径
notice.description.300211=风淋门未绑定路径
notice.solution.300211=请打开地图编辑界面，为该风淋门绑定路径
notice.description.300212=电梯未绑定点位
notice.solution.300212=请打开地图编辑界面，为该电梯绑定点位
notice.description.300213=呼叫盒未连接调度系统
notice.solution.300213=1：请检查呼叫盒与服务器的网络是否连接正常； 2：请检查呼叫盒的电源是否已打开； 3：请使用呼叫盒配置工具，检查呼叫盒的服务器地址配置是否正确；
notice.description.300214=呼叫盒未在系统中进行配置
notice.solution.300214=请为该呼叫盒配置事件类型的任务流程
notice.description.300215=呼叫盒关联的任务流程未发布
notice.solution.300215=请发布该呼叫盒绑定的任务流程
notice.description.300216=呼叫盒编号重复
notice.solution.300216=有多个呼叫盒配置了相同的呼叫盒ID，请使用呼叫盒配置工具重新配置呼叫盒
notice.description.300217=呼叫盒软件异常
notice.solution.300217=请使用呼叫盒配置工具连接呼叫盒，并检查呼叫盒程序问题
notice.description.300218=呼叫盒硬件异常
notice.solution.300218=请使用呼叫盒配置工具连接呼叫盒，并检查呼叫盒硬件问题
notice.description.300219=呼叫盒配置异常
notice.solution.300219=请使用呼叫盒配置工具连接呼叫盒，并检查呼叫盒配置问题
notice.description.300220=呼叫盒电量低
notice.solution.300220=请手动为呼叫盒充电

notice.description.300222=充电桩不存在
notice.solution.300222=请在设备管理界面检查充电桩是否存在
notice.description.300223=充电桩不可用
notice.solution.300223=请在设备管理界面检查充电桩状态
notice.description.300224=充电桩已断线
notice.solution.300224=请检查设备和服务器的网络连接是否正常
notice.description.300225=充电桩状态异常
notice.solution.300225=请在设备管理界面检查充电桩状态
notice.description.300226=充电桩放电中
notice.solution.300226=请在设备管理界面检查充电桩状态
notice.description.300227=充电点未绑定充电桩
notice.solution.300227=请在地图编排界面检查充电点配置

notice.description.300301=任务异常
notice.solution.300301=请联系技术支持人员
notice.description.300302=任务节点异常
notice.solution.300302=请联系技术支持人员
notice.description.300303=机器人类型无机器人
notice.solution.300303=请将机器人绑定到该机器人类型上
notice.description.300304=机器人组无机器人
notice.solution.300304=请将机器人绑定到该机器人组上
notice.description.300305=机器人不存在
notice.solution.300305=请打开任务详情弹窗和机器人列表界面，检查创建任务是否使用了不存在的机器人。
notice.description.300306=地图无机器人
notice.solution.300306=请在该地图上线可用的机器人
notice.description.300307=点位不存在
notice.solution.300307=请打开任务详情弹窗和地图列表界面，检查创建任务是否使用了不存在的点位。
notice.description.300308=目标点位不可达
notice.solution.300308=1：请检查机器人所在点和目标点是否在同一个地图上； 2：请检查机器人所在点和目标点之间是否有可用的路径；
notice.description.300309=控制非法的机器人
notice.solution.300309=1：当该节点未指定要控制的机器人时，需在该节点前执行有且只有一个“动态分配机器人”或“指定分配机器人”节点； 2：当该节点已指定要控制的机器人时，该节点指定的机器人需要是“动态分配机器人”或“指定分配机器人”节点的输出参数；
notice.description.300310=发送机器人指令失败
notice.solution.300310=请检查机器人与服务器的网络是否连接正常；
notice.description.300311=机器人无法接收指令
notice.solution.300311=1：请检查机器人是否为自动控制模式； 2：请检查机器人是否被按下急停按钮； 3：请检查机器人是否处于已连接状态； 4：请检查机器人是否处于异常状态；
notice.description.300312=机器人执行指令失败
notice.solution.300312=请联系技术支持人员
notice.description.300313=无可用的点位
notice.solution.300313=1：该节点指定了地图，但在该地图上找不到点位； 2：该节点指定了点位类型，但找不到点位；
notice.description.300314=数据格式转换失败
notice.solution.300314=请在任务详情界面，检查节点参数输入值
notice.description.300315=无法连接Plc
notice.solution.300315=1：请检查Plc与服务器的网络是否连接正常； 2：请检查Plc的电源是否已打开； 3：请检查节点配置的Plc地址端口是否正确；
notice.description.300316=寄存器值不在范围内
notice.solution.300316=请打开任务详情弹窗，检查创建任务是否使用了超出范围的寄存器值。
notice.description.300317=获取点位的属性为空
notice.solution.300317=请打开地图编辑页面，并检查该点位的属性
notice.description.300318=库位不存在或未启用
notice.solution.300318=请打开库位页面, 检查创建任务是否使用了不存在或未启用的库位
notice.description.300319=库区不存在
notice.solution.300319=请打开库区页面, 检查创建任务是否使用了不存在的库区
notice.description.300320=库位类型不存在
notice.solution.300320=请打开库位类型页面, 检查创建任务是否使用了不存在的库位类型
notice.description.300321=无可用库位
notice.solution.300321=请打开库位页面, 检查是否存在任务选择的库位
notice.description.300322=容器条码不存在
notice.solution.300322=请打开库位页面, 检查创建任务是否使用了不存在的容器编码
notice.description.300323=点位的相邻点位不唯一
notice.solution.300323=请检查创建任务所选择的点位是否没有相邻点位或者有多个相邻点位
notice.description.300324=容器条码已存在
notice.solution.300324=请打开库位页面, 检查是否已经存在相同的容器编码
notice.description.300325=库位已被占用
notice.solution.300325=请打开库位页面, 检查该库位是否已经被占用
notice.description.300326=Http请求异常
notice.solution.300326=1：请检查Http请求地址配置是否正确； 2：请检查设备和服务器的网络是否连接正常；
notice.description.300327=该编号下，存在多个符合条件的点位
notice.solution.300327=请检查创建任务所选择的点位是否存在多个
notice.description.300328=无法找到符合要求的库位
notice.solution.300328=请打开库位页面, 检查是否存在任务选择的库位
notice.description.300329=区域不存在
notice.solution.300329=请打开任务流程详情页面，在对应节点填写已存在的禁入区域编码
notice.description.300330=该区域禁止操作
notice.solution.300330=请打开任务流程详情页面，在对应节点填写可操作的区域编码
notice.description.300331=机器人充电停靠偏离角度过大
notice.solution.300331=请打开地图编辑页面，调整对应点位的偏移角度
notice.description.300332=作业任务不能停止
notice.solution.300332=请检查机器人执行的任务是否为作业任务
notice.description.300401=机器人导航冲突, 无可用的避让点
notice.solution.300401=请联系技术支持人员
notice.description.300501=地图文件写入磁盘失败
notice.solution.300501=请重新配置该磁盘目录的访问权限
notice.description.300502=读取磁盘地图文件失败
notice.solution.300502=请重新配置该磁盘目录的访问权限
notice.description.570001=机械臂通用失败
notice.solution.570001=请联系mos开发人员处理
notice.description.570002=机械臂接口参数错误
notice.solution.570002=请联系mos开发人员处理
notice.description.570003=未兼容的指令接口
notice.solution.570003=请联系mos开发人员处理
notice.description.570004=机器人连接失败
notice.solution.570004=请联系mos开发人员处理
notice.description.570005=机械臂socket通讯消息收发异常
notice.solution.570005=请联系mos开发人员处理
notice.description.570006=Socket断开连接
notice.solution.570006=请联系mos开发人员处理
notice.description.570007=创建请求失败
notice.solution.570007=请联系mos开发人员处理
notice.description.570008=请求相关的内部变量出错
notice.solution.570008=请联系mos开发人员处理
notice.description.570009=请求超时
notice.solution.570009=请联系mos开发人员处理
notice.description.570010=发送请求信息失败
notice.solution.570010=请联系mos开发人员处理
notice.description.570011=响应信息为空
notice.solution.570011=请联系mos开发人员处理
notice.description.570012=响应信息header不符
notice.solution.570012=请联系mos开发人员处理
notice.description.570013=解析响应失败
notice.solution.570013=请联系mos开发人员处理
notice.description.570014=正解出错
notice.solution.570014=终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务。
notice.description.570015=逆解出错
notice.solution.570015=终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务。
notice.description.570016=工具标定出错
notice.solution.570016=请联系mos开发人员处理
notice.description.570017=工具标定参数有错
notice.solution.570017=请联系mos开发人员处理
notice.description.570018=坐标系标定失败
notice.solution.570018=请联系mos开发人员处理
notice.description.570019=基坐标系转用户座标失败
notice.solution.570019=请联系mos开发人员处理
notice.description.570020=用户坐标系转基座标失败
notice.solution.570020=请联系mos开发人员处理
notice.description.570021=机器人上电失败
notice.solution.570021=请联系mos开发人员处理
notice.description.570022=机器人断电失败
notice.solution.570022=请联系mos开发人员处理
notice.description.570023=机器人使能失败
notice.solution.570023=请联系mos开发人员处理
notice.description.570024=机器人下使能失败
notice.solution.570024=请联系mos开发人员处理
notice.description.570025=机器人复位失败
notice.solution.570025=再次点击复位按钮进行复位，如果还失败，请取出示教器，清除报错。
notice.description.570026=机器人暂停失败
notice.solution.570026=请联系mos开发人员处理
notice.description.570027=机器人停止失败
notice.solution.570027=拍下急停按钮，取下机械臂抓手上的物料，上电，使能，手动将机械臂回到原点后，恢复任务。
notice.description.570028=机器人状态获取失败
notice.solution.570028=终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务。
notice.description.570029=机器人编码器状态同步失败
notice.solution.570029=请联系mos开发人员处理
notice.description.570030=机器人模式不正确
notice.solution.570030=请联系mos开发人员处理
notice.description.570031=机器人JOG运动失败
notice.solution.570031=请联系mos开发人员处理
notice.description.570032=机器人拖动示教设置失败
notice.solution.570032=请联系mos开发人员处理
notice.description.570033=机器人速度设置失败
notice.solution.570033=请联系mos开发人员处理
notice.description.570034=机器人路点清除失败
notice.solution.570034=请联系mos开发人员处理
notice.description.570035=机器人当前坐标系获取失败
notice.solution.570035=请联系mos开发人员处理
notice.description.570036=机器人坐标系设置失败
notice.solution.570036=请联系mos开发人员处理
notice.description.570037=机器人重量重心设置失败
notice.solution.570037=请联系mos开发人员处理
notice.description.570038=机器人IO设置失败
notice.solution.570038=请联系mos开发人员处理
notice.description.570039=机器人TCP设置失败
notice.solution.570039=请联系mos开发人员处理
notice.description.570040=机器人TCP获取失败
notice.solution.570040=请联系mos开发人员处理
notice.description.570041=move指令阻塞等待超时
notice.solution.570041=终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，检查超时原因。1.如因为路径点轨迹融合半径太大，导致运动不到位，可适当将对应步骤的速度降低。然后单机测试运行该任务，如果不会再报超时，即可恢复任务2.如因为触发防护性停止导致机械臂暂停时间超过1分钟，可以直接恢复任务。
notice.description.570042=运动相关的内部变量出错
notice.solution.570042=请联系mos开发人员处理
notice.description.570043=运动请求失败
notice.solution.570043=请联系mos开发人员处理
notice.description.570044=生成运动请求失败
notice.solution.570044=请联系mos开发人员处理
notice.description.570045=运动被事件中断
notice.solution.570045=终止当前任务，取下机械臂抓手上的物料，点击复位按钮进行复位，如果无法复位，请使用示教器清除错误。清错后重新上电、使能，手动将机械臂回到原点后，恢复任务。
notice.description.570046=运动相关的路点容器的长度不符合规定
notice.solution.570046=请联系mos开发人员处理
notice.description.570047=服务器响应返回错误
notice.solution.570047=请联系mos开发人员处理
notice.description.570048=真实机械臂不存在
notice.solution.570048=请联系mos开发人员处理
notice.description.570049=调用缓停接口失败
notice.solution.570049=请联系mos开发人员处理
notice.description.570050=调用急停接口失败
notice.solution.570050=请联系mos开发人员处理
notice.description.570051=调用暂停接口失败
notice.solution.570051=请联系mos开发人员处理
notice.description.570052=调用继续接口失败
notice.solution.570052=请联系mos开发人员处理
notice.description.570053=运动被条件中断
notice.solution.570053=终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，检查失效条件。如果是非传感器自身原图导致的条件失效，请恢复任务。如果是传感器问题，请联系售后人员解决。
notice.description.570054=运动被手动中断
notice.solution.570054=请联系mos开发人员处理
notice.description.571001=关节运动属性配置错误
notice.solution.571001=请联系mos开发人员处理
notice.description.571002=直线运动属性配置错误
notice.solution.571002=请联系mos开发人员处理
notice.description.571003=轨迹运动属性配置错误
notice.solution.571003=请联系mos开发人员处理
notice.description.571004=无效的运动属性配置
notice.solution.571004=请联系mos开发人员处理
notice.description.571005=等待机器人停止
notice.solution.571005=请联系mos开发人员处理
notice.description.571006=超出关节运动范围
notice.solution.571006=请联系mos开发人员处理
notice.description.571007=请正确设置MODEP第一个路点
notice.solution.571007=请联系mos开发人员处理
notice.description.571008=传送带跟踪配置错误
notice.solution.571008=请联系mos开发人员处理
notice.description.571009=传送带轨迹类型错误
notice.solution.571009=请联系mos开发人员处理
notice.description.571010=相对坐标变换逆解失败
notice.solution.571010=请联系mos开发人员处理
notice.description.571011=示教模式发生碰撞
notice.solution.571011=请联系mos开发人员处理
notice.description.571012=运动属性配置错误
notice.solution.571012=请联系mos开发人员处理
notice.description.571101=轨迹异常
notice.solution.571101=请联系mos开发人员处理
notice.description.571102=轨迹规划错误
notice.solution.571102=请联系mos开发人员处理
notice.description.571103=二型在线轨迹规划失败
notice.solution.571103=请联系mos开发人员处理
notice.description.571104=逆解失败
notice.solution.571104=请联系mos开发人员处理
notice.description.571105=动力学限制保护
notice.solution.571105=请联系mos开发人员处理
notice.description.571106=传送带跟踪失败
notice.solution.571106=请联系mos开发人员处理
notice.description.571107=超出传送带工作范围
notice.solution.571107=请联系mos开发人员处理
notice.description.571108=关节超出范围
notice.solution.571108=终止当前任务，取下机械臂抓手上的物料。检查MOS错误日志，查看是哪个点导致的关节超限，修改该点位后，将机械臂回到原点，重新开始任务。
notice.description.571109=关节超速
notice.solution.571109=请联系mos开发人员处理
notice.description.571110=离线轨迹规划失败
notice.solution.571110=请联系mos开发人员处理
notice.description.571200=控制器异常，逆解失败
notice.solution.571200=请联系mos开发人员处理
notice.description.571201=控制器异常，状态异常
notice.solution.571201=请联系mos开发人员处理
notice.description.571300=运动进入到stop阶段
notice.solution.571300=请联系mos开发人员处理
notice.description.571401=机械臂未定义的失败
notice.solution.571401=请联系mos开发人员处理
notice.description.571501=机械臂ListenNode 未启动
notice.solution.571501=请联系mos开发人员处理
notice.description.572100=PLC客户端异常
notice.solution.572100=请联系mos开发人员处理
notice.description.572101=PLC客户端异常
notice.solution.572101=请联系mos开发人员处理
notice.description.572102=PLC客户端异常
notice.solution.572102=请联系mos开发人员处理
notice.description.572103=PLC客户端异常
notice.solution.572103=请联系mos开发人员处理
notice.description.572104=PLC客户端异常
notice.solution.572104=请联系mos开发人员处理
notice.description.572105=PLC客户端异常
notice.solution.572105=请联系mos开发人员处理
notice.description.572106=PLC客户端异常
notice.solution.572106=请联系mos开发人员处理
notice.description.572107=PLC客户端异常
notice.solution.572107=请联系mos开发人员处理
notice.description.572108=PLC客户端异常
notice.solution.572108=请联系mos开发人员处理
notice.description.572109=PLC客户端异常
notice.solution.572109=请联系mos开发人员处理
notice.description.572110=PLC客户端异常
notice.solution.572110=请联系mos开发人员处理
notice.description.572111=PLC客户端异常
notice.solution.572111=请联系mos开发人员处理
notice.description.572112=PLC客户端异常
notice.solution.572112=请联系mos开发人员处理
notice.description.572113=PLC客户端异常
notice.solution.572113=请联系mos开发人员处理
notice.description.572114=PLC客户端异常
notice.solution.572114=请联系mos开发人员处理
notice.description.572115=PLC客户端异常
notice.solution.572115=请联系mos开发人员处理
notice.description.572116=PLC客户端异常
notice.solution.572116=请联系mos开发人员处理
notice.description.572117=PLC客户端异常
notice.solution.572117=请联系mos开发人员处理
notice.description.572118=PLC客户端异常
notice.solution.572118=请联系mos开发人员处理
notice.description.572119=PLC客户端异常
notice.solution.572119=请联系mos开发人员处理
notice.description.572120=PLC客户端异常
notice.solution.572120=请联系mos开发人员处理
notice.description.572121=PLC客户端异常
notice.solution.572121=请联系mos开发人员处理
notice.description.572122=PLC客户端异常
notice.solution.572122=请联系mos开发人员处理
notice.description.572123=PLC客户端异常
notice.solution.572123=请联系mos开发人员处理
notice.description.572124=PLC客户端异常
notice.solution.572124=请联系mos开发人员处理
notice.description.572125=PLC客户端异常
notice.solution.572125=请联系mos开发人员处理
notice.description.572126=PLC客户端异常
notice.solution.572126=请联系mos开发人员处理
notice.description.572127=PLC客户端异常
notice.solution.572127=请联系mos开发人员处理
notice.description.572128=PLC客户端异常
notice.solution.572128=请联系mos开发人员处理
notice.description.572129=PLC客户端异常
notice.solution.572129=请联系mos开发人员处理
notice.description.572130=PLC客户端异常
notice.solution.572130=请联系mos开发人员处理
notice.description.572131=PLC客户端异常
notice.solution.572131=请联系mos开发人员处理
notice.description.572132=PLC客户端异常
notice.solution.572132=请联系mos开发人员处理
notice.description.572133=PLC客户端异常
notice.solution.572133=请联系mos开发人员处理
notice.description.572134=PLC客户端异常
notice.solution.572134=请联系mos开发人员处理
notice.description.572135=PLC客户端异常
notice.solution.572135=请联系mos开发人员处理
notice.description.572136=PLC客户端异常
notice.solution.572136=请联系mos开发人员处理
notice.description.572137=PLC客户端异常
notice.solution.572137=请联系mos开发人员处理
notice.description.572138=PLC客户端异常
notice.solution.572138=请联系mos开发人员处理
notice.description.572139=PLC客户端异常
notice.solution.572139=请联系mos开发人员处理
notice.description.572150=硬件控制器未初始化
notice.solution.572150=请联系mos开发人员处理
notice.description.572200=PLC硬件异常
notice.solution.572200=请联系mos开发人员处理
notice.description.572201=PLC硬件异常
notice.solution.572201=请联系mos开发人员处理
notice.description.572202=PLC硬件异常
notice.solution.572202=请联系mos开发人员处理
notice.description.572203=PLC硬件异常
notice.solution.572203=请联系mos开发人员处理
notice.description.572204=PLC硬件异常
notice.solution.572204=请联系mos开发人员处理
notice.description.572205=PLC硬件异常
notice.solution.572205=请联系mos开发人员处理
notice.description.572206=PLC硬件异常
notice.solution.572206=请联系mos开发人员处理
notice.description.572207=PLC硬件异常
notice.solution.572207=请联系mos开发人员处理
notice.description.572208=PLC硬件异常
notice.solution.572208=请联系mos开发人员处理
notice.description.572209=PLC硬件异常
notice.solution.572209=请联系mos开发人员处理
notice.description.572210=PLC硬件异常
notice.solution.572210=请联系mos开发人员处理
notice.description.572211=PLC硬件异常
notice.solution.572211=请联系mos开发人员处理
notice.description.572212=PLC硬件异常
notice.solution.572212=请联系mos开发人员处理
notice.description.572213=PLC硬件异常
notice.solution.572213=请联系mos开发人员处理
notice.description.572214=PLC硬件异常
notice.solution.572214=请联系mos开发人员处理
notice.description.572215=PLC硬件异常
notice.solution.572215=请联系mos开发人员处理
notice.description.572216=PLC硬件异常
notice.solution.572216=请联系mos开发人员处理
notice.description.572217=PLC硬件异常
notice.solution.572217=请联系mos开发人员处理
notice.description.572218=PLC硬件异常
notice.solution.572218=请联系mos开发人员处理
notice.description.572219=PLC硬件异常
notice.solution.572219=请联系mos开发人员处理
notice.description.572220=PLC硬件异常
notice.solution.572220=请联系mos开发人员处理
notice.description.572221=PLC硬件异常
notice.solution.572221=请联系mos开发人员处理
notice.description.572222=PLC硬件异常
notice.solution.572222=请联系mos开发人员处理
notice.description.572223=PLC硬件异常
notice.solution.572223=请联系mos开发人员处理
notice.description.572224=PLC硬件异常
notice.solution.572224=请联系mos开发人员处理
notice.description.572225=PLC硬件异常
notice.solution.572225=请联系mos开发人员处理
notice.description.572226=PLC硬件异常
notice.solution.572226=请联系mos开发人员处理
notice.description.572227=PLC硬件异常
notice.solution.572227=请联系mos开发人员处理
notice.description.572228=PLC硬件异常
notice.solution.572228=请联系mos开发人员处理
notice.description.572229=PLC硬件异常
notice.solution.572229=请联系mos开发人员处理
notice.description.572230=PLC硬件异常
notice.solution.572230=请联系mos开发人员处理
notice.description.572231=PLC硬件异常
notice.solution.572231=请联系mos开发人员处理
notice.description.572232=PLC硬件异常
notice.solution.572232=请联系mos开发人员处理
notice.description.572233=PLC硬件异常
notice.solution.572233=请联系mos开发人员处理
notice.description.572234=PLC硬件异常
notice.solution.572234=请联系mos开发人员处理
notice.description.572235=PLC硬件异常
notice.solution.572235=请联系mos开发人员处理
notice.description.572236=PLC硬件异常
notice.solution.572236=请联系mos开发人员处理
notice.description.572237=PLC硬件异常
notice.solution.572237=请联系mos开发人员处理
notice.description.572238=PLC硬件异常
notice.solution.572238=请联系mos开发人员处理
notice.description.572239=PLC硬件异常
notice.solution.572239=请联系mos开发人员处理
notice.description.572240=PLC硬件异常
notice.solution.572240=请联系mos开发人员处理
notice.description.572241=PLC硬件异常
notice.solution.572241=请联系mos开发人员处理
notice.description.572242=PLC硬件异常
notice.solution.572242=请联系mos开发人员处理
notice.description.572243=PLC硬件异常
notice.solution.572243=请联系mos开发人员处理
notice.description.572244=PLC硬件异常
notice.solution.572244=请联系mos开发人员处理
notice.description.572245=PLC硬件异常
notice.solution.572245=请联系mos开发人员处理
notice.description.572246=PLC硬件异常
notice.solution.572246=请联系mos开发人员处理
notice.description.572247=PLC硬件异常
notice.solution.572247=请联系mos开发人员处理
notice.description.572248=PLC硬件异常
notice.solution.572248=请联系mos开发人员处理
notice.description.572249=PLC硬件异常
notice.solution.572249=请联系mos开发人员处理
notice.description.572250=PLC硬件异常
notice.solution.572250=请联系mos开发人员处理
notice.description.572251=PLC硬件异常
notice.solution.572251=请联系mos开发人员处理
notice.description.572252=PLC硬件异常
notice.solution.572252=请联系mos开发人员处理
notice.description.572253=PLC硬件异常
notice.solution.572253=请联系mos开发人员处理
notice.description.572254=PLC硬件异常
notice.solution.572254=请联系mos开发人员处理
notice.description.572255=PLC硬件异常
notice.solution.572255=请联系mos开发人员处理
notice.description.572256=PLC硬件异常
notice.solution.572256=请联系mos开发人员处理
notice.description.572257=PLC硬件异常
notice.solution.572257=请联系mos开发人员处理
notice.description.572258=PLC硬件异常
notice.solution.572258=请联系mos开发人员处理
notice.description.572259=PLC硬件异常
notice.solution.572259=请联系mos开发人员处理
notice.description.572260=PLC硬件异常
notice.solution.572260=请联系mos开发人员处理
notice.description.572261=PLC硬件异常
notice.solution.572261=请联系mos开发人员处理
notice.description.572262=PLC硬件异常
notice.solution.572262=请联系mos开发人员处理
notice.description.572263=PLC硬件异常
notice.solution.572263=请联系mos开发人员处理
notice.description.572264=PLC硬件异常
notice.solution.572264=请联系mos开发人员处理
notice.description.572265=PLC硬件异常
notice.solution.572265=请联系mos开发人员处理
notice.description.572266=PLC硬件异常
notice.solution.572266=请联系mos开发人员处理
notice.description.572267=PLC硬件异常
notice.solution.572267=请联系mos开发人员处理
notice.description.572268=PLC硬件异常
notice.solution.572268=请联系mos开发人员处理
notice.description.572269=PLC硬件异常
notice.solution.572269=请联系mos开发人员处理
notice.description.572270=PLC硬件异常
notice.solution.572270=请联系mos开发人员处理
notice.description.572271=PLC硬件异常
notice.solution.572271=请联系mos开发人员处理
notice.description.572272=PLC硬件异常
notice.solution.572272=请联系mos开发人员处理
notice.description.572273=PLC硬件异常
notice.solution.572273=请联系mos开发人员处理
notice.description.572274=PLC硬件异常
notice.solution.572274=请联系mos开发人员处理
notice.description.572275=PLC硬件异常
notice.solution.572275=请联系mos开发人员处理
notice.description.572276=PLC硬件异常
notice.solution.572276=请联系mos开发人员处理
notice.description.572277=PLC硬件异常
notice.solution.572277=请联系mos开发人员处理
notice.description.572278=PLC硬件异常
notice.solution.572278=请联系mos开发人员处理
notice.description.572279=PLC硬件异常
notice.solution.572279=请联系mos开发人员处理
notice.description.572280=PLC硬件异常
notice.solution.572280=请联系mos开发人员处理
notice.description.572281=PLC硬件异常
notice.solution.572281=请联系mos开发人员处理
notice.description.572282=PLC硬件异常
notice.solution.572282=请联系mos开发人员处理
notice.description.572283=PLC硬件异常
notice.solution.572283=请联系mos开发人员处理
notice.description.572284=PLC硬件异常
notice.solution.572284=请联系mos开发人员处理
notice.description.572285=PLC硬件异常
notice.solution.572285=请联系mos开发人员处理
notice.description.572286=PLC硬件异常
notice.solution.572286=请联系mos开发人员处理
notice.description.572287=PLC硬件异常
notice.solution.572287=请联系mos开发人员处理
notice.description.572288=PLC硬件异常
notice.solution.572288=请联系mos开发人员处理
notice.description.572289=PLC硬件异常
notice.solution.572289=请联系mos开发人员处理
notice.description.572290=PLC硬件异常
notice.solution.572290=请联系mos开发人员处理
notice.description.572291=PLC硬件异常
notice.solution.572291=请联系mos开发人员处理
notice.description.572292=PLC硬件异常
notice.solution.572292=请联系mos开发人员处理
notice.description.572293=PLC硬件异常
notice.solution.572293=请联系mos开发人员处理
notice.description.572294=PLC硬件异常
notice.solution.572294=请联系mos开发人员处理
notice.description.572295=PLC硬件异常
notice.solution.572295=请联系mos开发人员处理
notice.description.572296=PLC硬件异常
notice.solution.572296=请联系mos开发人员处理
notice.description.572297=PLC硬件异常
notice.solution.572297=请联系mos开发人员处理
notice.description.572298=PLC硬件异常
notice.solution.572298=请联系mos开发人员处理
notice.description.572299=PLC硬件异常
notice.solution.572299=请联系mos开发人员处理
notice.description.572300=PLC硬件异常
notice.solution.572300=请联系mos开发人员处理
notice.description.572301=PLC硬件异常
notice.solution.572301=请联系mos开发人员处理
notice.description.572302=PLC硬件异常
notice.solution.572302=请联系mos开发人员处理
notice.description.572303=PLC硬件异常
notice.solution.572303=请联系mos开发人员处理
notice.description.572304=PLC硬件异常
notice.solution.572304=请联系mos开发人员处理
notice.description.572305=PLC硬件异常
notice.solution.572305=请联系mos开发人员处理
notice.description.572306=PLC硬件异常
notice.solution.572306=请联系mos开发人员处理
notice.description.572307=PLC硬件异常
notice.solution.572307=请联系mos开发人员处理
notice.description.572308=PLC硬件异常
notice.solution.572308=请联系mos开发人员处理
notice.description.572309=PLC硬件异常
notice.solution.572309=请联系mos开发人员处理
notice.description.572310=PLC硬件异常
notice.solution.572310=请联系mos开发人员处理
notice.description.572311=PLC硬件异常
notice.solution.572311=请联系mos开发人员处理
notice.description.572312=PLC硬件异常
notice.solution.572312=请联系mos开发人员处理
notice.description.572313=PLC硬件异常
notice.solution.572313=请联系mos开发人员处理
notice.description.572314=PLC硬件异常
notice.solution.572314=请联系mos开发人员处理
notice.description.572315=PLC硬件异常
notice.solution.572315=请联系mos开发人员处理
notice.description.572316=PLC硬件异常
notice.solution.572316=请联系mos开发人员处理
notice.description.572317=PLC硬件异常
notice.solution.572317=请联系mos开发人员处理
notice.description.572318=PLC硬件异常
notice.solution.572318=请联系mos开发人员处理
notice.description.572319=PLC硬件异常
notice.solution.572319=请联系mos开发人员处理
notice.description.572320=PLC硬件异常
notice.solution.572320=请联系mos开发人员处理
notice.description.572321=PLC硬件异常
notice.solution.572321=请联系mos开发人员处理
notice.description.572322=PLC硬件异常
notice.solution.572322=请联系mos开发人员处理
notice.description.572323=PLC硬件异常
notice.solution.572323=请联系mos开发人员处理
notice.description.572324=PLC硬件异常
notice.solution.572324=请联系mos开发人员处理
notice.description.572325=PLC硬件异常
notice.solution.572325=请联系mos开发人员处理
notice.description.572326=PLC硬件异常
notice.solution.572326=请联系mos开发人员处理
notice.description.572327=PLC硬件异常
notice.solution.572327=请联系mos开发人员处理
notice.description.572328=PLC硬件异常
notice.solution.572328=请联系mos开发人员处理
notice.description.572329=PLC硬件异常
notice.solution.572329=请联系mos开发人员处理
notice.description.572330=PLC硬件异常
notice.solution.572330=请联系mos开发人员处理
notice.description.572331=PLC硬件异常
notice.solution.572331=请联系mos开发人员处理
notice.description.572332=PLC硬件异常
notice.solution.572332=请联系mos开发人员处理
notice.description.572333=PLC硬件异常
notice.solution.572333=请联系mos开发人员处理
notice.description.572334=PLC硬件异常
notice.solution.572334=请联系mos开发人员处理
notice.description.572335=PLC硬件异常
notice.solution.572335=请联系mos开发人员处理
notice.description.572336=PLC硬件异常
notice.solution.572336=请联系mos开发人员处理
notice.description.572337=PLC硬件异常
notice.solution.572337=请联系mos开发人员处理
notice.description.572338=PLC硬件异常
notice.solution.572338=请联系mos开发人员处理
notice.description.572339=PLC硬件异常
notice.solution.572339=请联系mos开发人员处理
notice.description.572340=PLC硬件异常
notice.solution.572340=请联系mos开发人员处理
notice.description.572341=PLC硬件异常
notice.solution.572341=请联系mos开发人员处理
notice.description.572342=PLC硬件异常
notice.solution.572342=请联系mos开发人员处理
notice.description.572343=PLC硬件异常
notice.solution.572343=请联系mos开发人员处理
notice.description.572344=PLC硬件异常
notice.solution.572344=请联系mos开发人员处理
notice.description.572345=PLC硬件异常
notice.solution.572345=请联系mos开发人员处理
notice.description.572346=PLC硬件异常
notice.solution.572346=请联系mos开发人员处理
notice.description.572347=PLC硬件异常
notice.solution.572347=请联系mos开发人员处理
notice.description.572348=PLC硬件异常
notice.solution.572348=请联系mos开发人员处理
notice.description.572349=PLC硬件异常
notice.solution.572349=请联系mos开发人员处理
notice.description.572350=PLC硬件异常
notice.solution.572350=请联系mos开发人员处理
notice.description.572351=PLC硬件异常
notice.solution.572351=请联系mos开发人员处理
notice.description.572352=PLC硬件异常
notice.solution.572352=请联系mos开发人员处理
notice.description.572353=PLC硬件异常
notice.solution.572353=请联系mos开发人员处理
notice.description.572354=PLC硬件异常
notice.solution.572354=请联系mos开发人员处理
notice.description.572355=PLC硬件异常
notice.solution.572355=请联系mos开发人员处理
notice.description.572356=PLC硬件异常
notice.solution.572356=请联系mos开发人员处理
notice.description.572357=PLC硬件异常
notice.solution.572357=请联系mos开发人员处理
notice.description.572358=PLC硬件异常
notice.solution.572358=请联系mos开发人员处理
notice.description.572359=PLC硬件异常
notice.solution.572359=请联系mos开发人员处理
notice.description.572360=PLC硬件异常
notice.solution.572360=请联系mos开发人员处理
notice.description.572361=PLC硬件异常
notice.solution.572361=请联系mos开发人员处理
notice.description.572362=PLC硬件异常
notice.solution.572362=请联系mos开发人员处理
notice.description.572363=PLC硬件异常
notice.solution.572363=请联系mos开发人员处理
notice.description.572364=PLC硬件异常
notice.solution.572364=请联系mos开发人员处理
notice.description.572365=PLC硬件异常
notice.solution.572365=请联系mos开发人员处理
notice.description.572366=PLC硬件异常
notice.solution.572366=请联系mos开发人员处理
notice.description.572367=PLC硬件异常
notice.solution.572367=请联系mos开发人员处理
notice.description.572368=PLC硬件异常
notice.solution.572368=请联系mos开发人员处理
notice.description.572369=PLC硬件异常
notice.solution.572369=请联系mos开发人员处理
notice.description.572370=PLC硬件异常
notice.solution.572370=请联系mos开发人员处理
notice.description.572371=PLC硬件异常
notice.solution.572371=请联系mos开发人员处理
notice.description.572372=PLC硬件异常
notice.solution.572372=请联系mos开发人员处理
notice.description.572373=PLC硬件异常
notice.solution.572373=请联系mos开发人员处理
notice.description.572374=PLC硬件异常
notice.solution.572374=请联系mos开发人员处理
notice.description.572375=PLC硬件异常
notice.solution.572375=请联系mos开发人员处理
notice.description.572376=PLC硬件异常
notice.solution.572376=请联系mos开发人员处理
notice.description.572377=PLC硬件异常
notice.solution.572377=请联系mos开发人员处理
notice.description.572378=PLC硬件异常
notice.solution.572378=请联系mos开发人员处理
notice.description.572379=PLC硬件异常
notice.solution.572379=请联系mos开发人员处理
notice.description.572380=PLC硬件异常
notice.solution.572380=请联系mos开发人员处理
notice.description.572381=PLC硬件异常
notice.solution.572381=请联系mos开发人员处理
notice.description.572382=PLC硬件异常
notice.solution.572382=请联系mos开发人员处理
notice.description.572383=PLC硬件异常
notice.solution.572383=请联系mos开发人员处理
notice.description.572384=PLC硬件异常
notice.solution.572384=请联系mos开发人员处理
notice.description.572385=PLC硬件异常
notice.solution.572385=请联系mos开发人员处理
notice.description.572386=PLC硬件异常
notice.solution.572386=请联系mos开发人员处理
notice.description.572387=PLC硬件异常
notice.solution.572387=请联系mos开发人员处理
notice.description.572388=PLC硬件异常
notice.solution.572388=请联系mos开发人员处理
notice.description.572389=PLC硬件异常
notice.solution.572389=请联系mos开发人员处理
notice.description.572390=PLC硬件异常
notice.solution.572390=请联系mos开发人员处理
notice.description.572391=PLC硬件异常
notice.solution.572391=请联系mos开发人员处理
notice.description.572392=PLC硬件异常
notice.solution.572392=请联系mos开发人员处理
notice.description.572393=PLC硬件异常
notice.solution.572393=请联系mos开发人员处理
notice.description.572394=PLC硬件异常
notice.solution.572394=请联系mos开发人员处理
notice.description.572395=PLC硬件异常
notice.solution.572395=请联系mos开发人员处理
notice.description.572396=PLC硬件异常
notice.solution.572396=请联系mos开发人员处理
notice.description.572397=PLC硬件异常
notice.solution.572397=请联系mos开发人员处理
notice.description.572398=PLC硬件异常
notice.solution.572398=请联系mos开发人员处理
notice.description.572399=PLC硬件异常
notice.solution.572399=请联系mos开发人员处理
notice.description.573100=E84 消息等待超时
notice.solution.573100=请联系mos开发人员处理
notice.description.573101=E84不支持的操作类型
notice.solution.573101=请联系mos开发人员处理
notice.description.573102=E84不支持的机器人上下料状态
notice.solution.573102=请联系mos开发人员处理
notice.description.573103=mqtt_client函数调用出错
notice.solution.573103=请联系mos开发人员处理
notice.description.573104=mqtt获取数据超时
notice.solution.573104=请联系mos开发人员处理
notice.description.573105=mqtt发送数据出错
notice.solution.573105=请联系mos开发人员处理
notice.description.573106=mqtt接收数据出错
notice.solution.573106=请联系mos开发人员处理
notice.description.573107=mqtt接收到故障信息
notice.solution.573107=请联系mos开发人员处理
notice.description.573108=雷达区域切换失败
notice.solution.573108=请联系mos开发人员处理
notice.description.574100=夹爪打开失败
notice.solution.574100=请联系mos开发人员处理
notice.description.574101=夹爪关闭失败
notice.solution.574101=请联系mos开发人员处理
notice.description.574102=夹爪复位失败
notice.solution.574102=请联系mos开发人员处理
notice.description.574103=夹爪485协议接收失败
notice.solution.574103=请联系mos开发人员处理
notice.description.574200=距离传感器检测超时
notice.solution.574200=请联系mos开发人员处理
notice.description.574201=抓手有无料检测传感器检测超时
notice.solution.574201=请联系mos开发人员处理
notice.description.574300=相机未连接
notice.solution.574300=请联系mos开发人员处理
notice.description.574301=相机内部错误
notice.solution.574301=请联系mos开发人员处理
notice.description.574302=超时
notice.solution.574302=请联系mos开发人员处理
notice.description.574303=相机未知命令
notice.solution.574303=请联系mos开发人员处理
notice.description.574304=索引超出范围
notice.solution.574304=请联系mos开发人员处理
notice.description.574305=自变量太少
notice.solution.574305=请联系mos开发人员处理
notice.description.574306=无效自变量类型
notice.solution.574306=请联系mos开发人员处理
notice.description.574307=无效自变量
notice.solution.574307=请联系mos开发人员处理
notice.description.574308=不允许的命令
notice.solution.574308=请联系mos开发人员处理
notice.description.574309=不允许的组合
notice.solution.574309=请联系mos开发人员处理
notice.description.574310=相机忙
notice.solution.574310=请联系mos开发人员处理
notice.description.574311=未完全实施
notice.solution.574311=请联系mos开发人员处理
notice.description.574312=不支持
notice.solution.574312=请联系mos开发人员处理
notice.description.574313=结果字符串过⻓
notice.solution.574313=请联系mos开发人员处理
notice.description.574314=⽆效相机 ID
notice.solution.574314=请联系mos开发人员处理
notice.description.574315=⽆效相机特征 ID
notice.solution.574315=请联系mos开发人员处理
notice.description.574316=不同的配⽅名称
notice.solution.574316=请联系mos开发人员处理
notice.description.574317=不同版本
notice.solution.574317=请联系mos开发人员处理
notice.description.574318=没有标定
notice.solution.574318=请联系mos开发人员处理
notice.description.574319=标定失败
notice.solution.574319=请联系mos开发人员处理
notice.description.574320=⽆效标定数据
notice.solution.574320=请联系mos开发人员处理
notice.description.574321=未达到给定的标定位置
notice.solution.574321=请联系mos开发人员处理
notice.description.574322=⽆启动命令
notice.solution.574322=请联系mos开发人员处理
notice.description.574323=特征未经过训练
notice.solution.574323=请联系mos开发人员处理
notice.description.574324=特征未找到
notice.solution.574324=请联系mos开发人员处理
notice.description.574325=特征未映射
notice.solution.574325=请联系mos开发人员处理
notice.description.574326=部件位置未经过训练
notice.solution.574326=请联系mos开发人员处理
notice.description.574327=机器⼈位置未经过训练
notice.solution.574327=请联系mos开发人员处理
notice.description.574328=⽆效部件 ID
notice.solution.574328=请联系mos开发人员处理
notice.description.574329=未定位此部件的所有特征
notice.solution.574329=请联系mos开发人员处理
notice.description.574330=部件⽆有效夹持纠正
notice.solution.574330=请联系mos开发人员处理
notice.description.574331=部件⽆有效夹持纠正
notice.solution.574331=请联系mos开发人员处理
notice.description.574350=相机读取socket错误
notice.solution.574350=请联系mos开发人员处理
notice.description.574351=相机响应信息header不符
notice.solution.574351=请联系mos开发人员处理
notice.description.574352=解析相机响应失败
notice.solution.574352=请联系mos开发人员处理
notice.description.574360=相机内参标定失败
notice.solution.574360=请联系mos开发人员处理
notice.description.574361=相机手眼标定失败
notice.solution.574361=请联系mos开发人员处理
notice.description.574362=没有内参标定数据
notice.solution.574362=请联系mos开发人员处理
notice.description.574363=没有手眼标定数据
notice.solution.574363=请联系mos开发人员处理
notice.description.574364=相机数据获取失败
notice.solution.574364=请联系mos开发人员处理
notice.description.574365=相机数据存储失败
notice.solution.574365=请联系mos开发人员处理
notice.description.574366=特征点坐标获取失败
notice.solution.574366=请联系mos开发人员处理
notice.description.574367=拍照计算的夹取位与示教夹取位之间偏差过大
notice.solution.574367=请联系mos开发人员处理
notice.description.574368=创建模板图像失败
notice.solution.574368=请联系mos开发人员处理
notice.description.574369=获取算法参数失败
notice.solution.574369=请联系mos开发人员处理
notice.description.574370=相机图像处理异常
notice.solution.574370=请联系mos开发人员处理
notice.description.574390=相机拍照失败
notice.solution.574390=请联系mos开发人员处理
notice.description.574391=相机配方设置失败
notice.solution.574391=请联系mos开发人员处理
notice.description.574392=相机触发参数获取失败
notice.solution.574392=请联系mos开发人员处理
notice.description.574393=相机专案保存失败
notice.solution.574393=请联系mos开发人员处理
notice.description.574394=相机未初始化
notice.solution.574394=请联系mos开发人员处理
notice.description.574400=光源设备串口打开失败
notice.solution.574400=请联系mos开发人员处理
notice.description.574401=光源设备串口读写异常
notice.solution.574401=请联系mos开发人员处理
notice.description.574410=光源打开失败
notice.solution.574410=请联系mos开发人员处理
notice.description.574411=光源关闭失败
notice.solution.574411=请联系mos开发人员处理
notice.description.574412=光源亮度获取失败
notice.solution.574412=请联系mos开发人员处理
notice.description.575100=无效的储位ID
notice.solution.575100=请联系mos开发人员处理
notice.description.575101=储位检测超时
notice.solution.575101=请联系mos开发人员处理
notice.description.575200=储位锁锁定超时
notice.solution.575200=请联系mos开发人员处理
notice.description.575201=储位锁解锁超时
notice.solution.575201=请联系mos开发人员处理
notice.description.575300=无物料信息检测sensor
notice.solution.575300=请联系mos开发人员处理
notice.description.575301=smart tag 传感器未连接
notice.solution.575301=请联系mos开发人员处理
notice.description.575302=smart tag 读取失败
notice.solution.575302=请联系mos开发人员处理
notice.description.575303=smart tag 读取超时
notice.solution.575303=请联系mos开发人员处理
notice.description.575304=smart tag 数据无效
notice.solution.575304=请联系mos开发人员处理
notice.description.575401=RFID 传感器未连接
notice.solution.575401=请联系mos开发人员处理
notice.description.575402=RFID 读取失败
notice.solution.575402=请联系mos开发人员处理
notice.description.575403=RFID 读取超时
notice.solution.575403=请联系mos开发人员处理
notice.description.575404=RFID 数据无效
notice.solution.575404=请联系mos开发人员处理
notice.description.575405=RFID 请求数据错误
notice.solution.575405=请联系mos开发人员处理
notice.description.575900=smart tag sensor 故障
notice.solution.575900=请联系mos开发人员处理
notice.description.575901=RFID sensor 故障
notice.solution.575901=请联系mos开发人员处理
notice.description.576100=升降柱超出运动范围
notice.solution.576100=请联系mos开发人员处理
notice.description.576101=无效的控制指令
notice.solution.576101=请联系mos开发人员处理
notice.description.576102=无效的多轴控制模式
notice.solution.576102=请联系mos开发人员处理
notice.description.576103=多轴设备未就绪，或出现异常
notice.solution.576103=请联系mos开发人员处理
notice.description.576104=Can卡未连接
notice.solution.576104=请联系mos开发人员处理
notice.description.576105=步科轴设备超出行程
notice.solution.576105=请联系mos开发人员处理
notice.description.576106=多轴设备无法获取当前位置
notice.solution.576106=请联系mos开发人员处理
notice.description.576107=多轴设备移动失败
notice.solution.576107=请联系mos开发人员处理
notice.description.576108=多轴设备运动被条件中断
notice.solution.576108=请联系mos开发人员处理
notice.description.576109=多轴设备目标点位超出设置的限位
notice.solution.576109=请联系mos开发人员处理
notice.description.576110=多轴设备运动被手动中断
notice.solution.576110=请联系mos开发人员处理
notice.description.576111=单轴设备指令阻塞等待超时,超过600s未停止
notice.solution.576111=请联系mos开发人员处理
notice.description.576201=无效的多轴参数
notice.solution.576201=请联系mos开发人员处理
notice.description.576202=多轴管理初始化失败
notice.solution.576202=请联系mos开发人员处理
notice.description.577100=云台任务JSON解析失败
notice.solution.577100=请联系mos开发人员处理
notice.description.577101=云台执行动作类型错误
notice.solution.577101=请联系mos开发人员处理
notice.description.577102=云台通道错误
notice.solution.577102=请联系mos开发人员处理
notice.description.577103=云台通道类型错误
notice.solution.577103=请联系mos开发人员处理
notice.description.577104=云台相机名字错误
notice.solution.577104=请联系mos开发人员处理
notice.description.577105=云台执行拍照失败
notice.solution.577105=请联系mos开发人员处理
notice.description.577106=云台执行录像失败
notice.solution.577106=请联系mos开发人员处理
notice.description.577107=云台执行参数设置失败
notice.solution.577107=请联系mos开发人员处理
notice.description.577108=云台执行普通测温失败
notice.solution.577108=请联系mos开发人员处理
notice.description.577109=云台图像远程拷贝失败
notice.solution.577109=请联系mos开发人员处理
notice.description.577110=云台获取SGC参数失败，检查SGC下发的相机名是否对应
notice.solution.577110=请联系mos开发人员处理
notice.description.578100=传感器JSON解析失败
notice.solution.578100=请联系mos开发人员处理
notice.description.578101=传感器名字不存在
notice.solution.578101=请联系mos开发人员处理
notice.description.578102=传入的G300M4的模式不对
notice.solution.578102=请联系mos开发人员处理
notice.description.578201=XSLAB声纹传感器初始化失败
notice.solution.578201=请联系mos开发人员处理
notice.description.578202=G300M4局放传感器初始化失败
notice.solution.578202=请联系mos开发人员处理
notice.description.578203=FS00802福申传感器初始化失败
notice.solution.578203=请联系mos开发人员处理
notice.description.578204=接收G300M4数据失败
notice.solution.578204=请联系mos开发人员处理
notice.description.578205=G300M4工作模式错误
notice.solution.578205=请联系mos开发人员处理
notice.description.578206=传感器初始化失败或者运行错误，请检查配置
notice.solution.578206=请联系mos开发人员处理
notice.description.579000=系统未初始化
notice.solution.579000=请联系mos开发人员处理
notice.description.579100=取消任务失败
notice.solution.579100=请联系mos开发人员处理
notice.description.579101=暂停任务失败
notice.solution.579101=请联系mos开发人员处理
notice.description.579102=恢复任务失败
notice.solution.579102=请联系mos开发人员处理
notice.description.579103=buffer解析错误
notice.solution.579103=请联系mos开发人员处理
notice.description.579104=未找到任务
notice.solution.579104=请联系mos开发人员处理
notice.description.579105=任务列表未更新
notice.solution.579105=请联系mos开发人员处理
notice.description.579106=存在未完成的任务
notice.solution.579106=请联系mos开发人员处理
notice.description.579107=任务被手动中断
notice.solution.579107=请联系mos开发人员处理
notice.description.579201=无效步骤类型
notice.solution.579201=请联系mos开发人员处理
notice.description.579202=未找到pose value
notice.solution.579202=请联系mos开发人员处理
notice.description.579203=未找到joint value
notice.solution.579203=请联系mos开发人员处理
notice.description.579204=未找到偏移量
notice.solution.579204=请联系mos开发人员处理
notice.description.579205=无效的feature ID
notice.solution.579205=请联系mos开发人员处理
notice.description.579206=无效的条件类型
notice.solution.579206=请联系mos开发人员处理
notice.description.579207=无效的条件参数
notice.solution.579207=请联系mos开发人员处理
notice.description.579208=动作列表获取失败
notice.solution.579208=请联系mos开发人员处理
notice.description.579209=机械臂不在原点
notice.solution.579209=请联系mos开发人员处理
notice.description.579210=集成锁住,底盘正在运动
notice.solution.579210=请联系mos开发人员处理
notice.description.579211=Socket协议解析失败
notice.solution.579211=请联系mos开发人员处理

notice.description.620001=充电机的输出电压高于预设值
notice.solution.620001=请检查电压设置
notice.description.620002=充电桩的温度高于预设值
notice.solution.620002=请检查刷块
notice.description.620004=充电桩的输入电压高于或低于输入电压范围
notice.solution.620004=请检查输入接线
notice.description.620005=充电桩的输出短路
notice.solution.620005=请检查输出端
notice.description.620006=充电桩的风扇故障
notice.solution.620006=请检查模块风扇
notice.description.620007=充电桩的输出电流高于预设值
notice.solution.620007=请检查电流设置
notice.description.620008=刷块温度过高
notice.solution.620008=请检查散热系统

notice.description.610001=发射端逆变电流超限
notice.solution.610001=将距离控制到合理范围及检查线圈
notice.description.610002=发射端逆变电流突变
notice.solution.610002=如果周期出现，送回返修
notice.description.610003=发射端无线通信掉线
notice.solution.610003=如果周期出现或者频繁出现则检查天线是否松动
notice.description.610004=发射端输入母线电压过高
notice.solution.610004=如果频繁出现，送回返修
notice.description.610005=发射端输入母线电压过低
notice.solution.610005=如果频繁出现，送回返修
notice.description.610006=发射端 FAULT 保护
notice.solution.610006=如果频繁出现，送回返修
notice.description.610007=发射端温度过高
notice.solution.610007=检查风扇是否正常转动
notice.description.610008=发射端外部命令停止使能
notice.solution.610008=无
notice.description.610009=发射端判断电压达到无电流
notice.solution.610009=无
notice.description.610010=发射端判定充满停机
notice.solution.610010=无
notice.description.610011=发射端电源模块故障
notice.solution.610011=如果频繁出现，送回返修
notice.description.610012=发射端耦合度测试不合格
notice.solution.610012=无
notice.description.610013=发射端母线电流硬件保护
notice.solution.610013=如果频繁出现，送回返修
notice.description.610014=发射端母线电压硬件保护
notice.solution.610014=如果频繁出现，送回返修
notice.description.610015=发射端交流接触器异常停止
notice.solution.610015=无
notice.description.610016=发射端线圈/导轨电流异常
notice.solution.610016=检查线圈间距离是否在规定范围内
notice.description.610017=TX导轨/线圈电流过流
notice.solution.610017=检查线圈间距离是否在规定范围内
notice.description.610018=发射端充电超时
notice.solution.610018=无
notice.description.610019=发射端FAULT保护1
notice.solution.610019=如果频繁出现，送回返修
notice.description.610020=发射端FAULT保护2
notice.solution.610020=如果频繁出现，送回返修
notice.description.610021=发射端FAULT保护3
notice.solution.610021=如果频繁出现，送回返修
notice.description.610022=发射端FAULT保护4
notice.solution.610022=如果频繁出现，送回返修
notice.description.610023=发射端FAULT保护5
notice.solution.610023=如果频繁出现，送回返修
notice.description.610024=发射端FAULT保护6
notice.solution.610024=如果频繁出现，送回返修
notice.description.610025=发射端温度保护1
notice.solution.610025=如果频繁出现，送回返修
notice.description.610026=发射端温度保护2
notice.solution.610026=如果频繁出现，送回返修
notice.description.610027=发射端温度保护3
notice.solution.610027=如果频繁出现，送回返修
notice.description.610028=发射端温度保护4
notice.solution.610028=如果频繁出现，送回返修
notice.description.610029=发射端温度保护5
notice.solution.610029=如果频繁出现，送回返修
notice.description.610030=发射端温度保护6
notice.solution.610030=如果频繁出现，送回返修
notice.description.610031=TX导轨/线圈电流电流过低
notice.solution.610031=无
notice.description.610032=调度系统命令停止
notice.solution.610032=请确认调度系统终止放电原因
notice.description.610101=接收端输出过压
notice.solution.610101=检查充电机接收端是否空载
notice.description.610102=接收端输出过流
notice.solution.610102=更换电池重试，排查电池问题，查看上位机设置值
notice.description.610103=接收端短路保护
notice.solution.610103=万用表测量输出两端是否短路
notice.description.610104=接收端判断充满停止
notice.solution.610104=无
notice.description.610105=接收端温度过高
notice.solution.610105=查看风扇是否正常转动
notice.description.610106=接收端输入电压过低
notice.solution.610106=如果频繁出现，送回返修
notice.description.610107=接收端外部命令停止
notice.solution.610107=无
notice.description.610108=接收端电池故障停止
notice.solution.610108=设置开启充电
notice.description.610109=接收端硬件输出过压
notice.solution.610109=检查是否突然断载
notice.description.610110=接收端硬件输出过流
notice.solution.610110=检查电池
notice.description.610111=接收端硬件短路保护
notice.solution.610111=万用表量取是否断路
notice.description.610112=接收端 BMS未使能
notice.solution.610112=设置开启充电
notice.description.610113=接收端风扇故障
notice.solution.610113=检查风扇

notice.description.300108=急停触发时间过长
notice.solution.300108=请检查机器人状态
notice.description.300402=避让计算时间过长
notice.solution.300402=请检查点位配置
notice.description.300109=分配充电点/泊车点时间过长
notice.solution.300109=请检查充电点/泊车点配置
notice.description.300110=申请点位/区域时间过长
notice.solution.300110=请检查点位/区域配置
notice.description.300403=避让次数异常
notice.solution.300403=请联系Fleet开发人员处理


#log
log.export.interfaceLog.excelName=接口日志
log.export.operationLog.excelName=操作日志
log.operation.description.success=成功
log.operation.description.fail=失败
log.third.system.operator=上游系统

log.controller.api.task.create=[API]新建任务
log.controller.api.task.cancel=[API]取消任务
log.controller.api.task.overNode=[API]跳过任务节点
log.controller.api.traffic.occupy=[API]申请交管区域
log.controller.api.traffic.release=[API]释放交管区域
log.controller.api.vehicle.operation=[API]操作机器人
log.controller.api.vehicle.globalPause=[API]全场暂停
log.controller.api.vehicle.globalResume=[API]全场恢复
log.controller.language.delete=删除语言
log.controller.language.import=导入语言
log.controller.language.export=导出语言
log.controller.language.switch=切换语言
log.controller.license.upload=上传证书
log.controller.license.delete=删除证书
log.controller.operationLog.delete=删除操作日志
log.controller.sysLog.delete=删除系统日志
log.controller.airShowerDoor.insert=新增风淋门
log.controller.airShowerDoor.update=修改风淋门
log.controller.airShowerDoor.delete=删除风淋门
log.controller.airShowerDoor.open=打开风淋门
log.controller.airShowerDoor.close=关闭风淋门
log.controller.autoDoor.insert=新增自动门
log.controller.autoDoor.update=修改自动门
log.controller.autoDoor.delete=删除自动门
log.controller.autoDoor.open=打开自动门
log.controller.autoDoor.close=关闭自动门
log.controller.elevator.insert=新增电梯
log.controller.elevator.update=修改电梯
log.controller.elevator.delete=删除电梯
log.controller.elevator.import=导入电梯
log.controller.elevator.export=导出电梯
log.controller.elevator.open=打开电梯门
log.controller.elevator.close=关闭电梯门
log.controller.mapArea.insert=新增区域
log.controller.mapArea.enable=启用区域
log.controller.mapArea.disable=禁用区域
log.controller.mapArea.update=修改元素
log.controller.mapArea.delete=删除元素
log.controller.marker.insert=新增点位
log.controller.marker.transcribe=录制点位
log.controller.marker.update=修改元素
log.controller.marker.delete=删除元素
log.controller.path.insert=新增路径
log.controller.path.update=修改元素
log.controller.path.delete=删除元素
log.controller.vehicleMap.insert=新增地图
log.controller.vehicleMap.update=修改地图
log.controller.vehicleMap.delete=删除地图
log.controller.vehicleMap.batchDelete=批量删除地图
log.controller.vehicleMap.deleteDraft=删除草稿数据
log.controller.vehicleMap.batchGenerateElement=批量生成点位和路径
log.controller.vehicleMap.batchUpdateElement=修改元素
log.controller.vehicleMap.batchDeleteElement=删除元素
log.controller.vehicleMap.import=导入地图
log.controller.vehicleMap.export=导出地图
log.controller.vehicleMap.copy=复制地图
log.controller.vehicleMap.pause=地图全场暂停
log.controller.vehicleMap.recover=地图全场恢复
log.controller.vehicleMap.publish=发布地图
log.controller.vehicleMap.locatingMap.update=修改定位图
log.controller.vehicleMap.locatingMap.import=导入定位图
log.controller.vehicleMap.locatingMap.changeDefault=切换默认定位图
log.controller.vehicleMap.locatingMap.delete=删除定位图
log.controller.noticeConfig.insert=新增通知模板
log.controller.noticeConfig.update=修改通知模板
log.controller.noticeConfig.delete=删除通知模板
log.controller.noticeConfig.export=导出通知模板
log.controller.noticeConfig.import=导入通知模板
log.controller.noticeRecord.insert=创建异常记录
log.controller.noticeRecord.update=更新异常记录
log.controller.noticeRecord.delete=删除异常记录
log.controller.noticeRecord.activation=激活通知
log.controller.noticeRecord.ignore=忽略通知
log.controller.noticeRecord.ignoreVehicle=忽略机器人通知
log.controller.noticeRecord.export=导出通知列表
log.controller.pda.config=(PDA)配置版本信息
log.controller.pda.containerEnter=(PDA)容器进场
log.controller.pda.containerExit=(PDA)容器出场
log.controller.pda.execute=(PDA)创建任务
log.controller.security.login=登录系统
log.controller.security.logout=退出登录系统
log.controller.sys.menu.insert=新增菜单
log.controller.sys.menu.update=修改菜单
log.controller.sys.menu.delete=删除菜单
log.controller.sys.role.insert=新增角色
log.controller.sys.role.update=修改角色
log.controller.sys.role.delete=删除角色
log.controller.sys.property.batchUpdate=批量修改系统配置
log.controller.sys.property.insert=增加系统属性
log.controller.sys.property.update=修改系统配置
log.controller.sys.property.delete=删除系统属性
log.controller.sys.user.password=修改密码
log.controller.sys.user.password.reset=重置密码
log.controller.sys.user.insert=新增用户
log.controller.sys.user.update=修改用户
log.controller.sys.user.delete=删除用户
log.controller.task.nodeConfig.insert=新增节点类型
log.controller.task.nodeConfig.update=修改节点类型
log.controller.task.nodeConfig.delete=删除节点类型
log.controller.task.nodeConfig.batchCommon=设置节点常用
log.controller.task.nodeConfig.export=导出节点类型
log.controller.task.nodeConfig.import=导入节点类型
log.controller.task.insert=新增任务
log.controller.task.cancel=取消任务
log.controller.task.delete=删除任务
log.controller.task.skip=跳过节点
log.controller.task.retry=重试节点
log.controller.task.batchCancel=批量取消任务
log.controller.task.cancelAll=一键取消任务
log.controller.task.export=下载记录
log.controller.task.import=上传记录
log.controller.task.remark=添加任务备注
log.controller.task.type.insert=新增任务流程
log.controller.task.type.update=修改任务流程
log.controller.task.type.copy=复制任务流程
log.controller.task.type.delete=删除任务流程
log.controller.task.type.enable=启用任务流程
log.controller.task.type.disable=禁用任务流程
log.controller.task.type.export=导出任务流程
log.controller.task.type.import=导入任务流程
log.controller.vehicle.stop.open=开启暂停
log.controller.vehicle.stop.close=关闭暂停
log.controller.vehicle.delete=删除机器人
log.controller.vehicle.restart=重启机器人
log.controller.vehicle.shutdown=关闭机器人
log.controller.vehicle.controls.manualMode=切换为手动控制模式
log.controller.vehicle.controls.autoMode=切换为自动控制模式
log.controller.vehicle.scheduler.manualMode=切换为手动调度模式
log.controller.vehicle.scheduler.autoMode=切换为自动调度模式
log.controller.vehicle.update=修改机器人配置
log.controller.vehicle.updateBatch=批量修改机器人配置
log.controller.vehicle.updateGroupBatch=批量修改机器人分组
log.controller.vehicle.updateTypeBatch=批量修改机器人类型
log.controller.vehicle.resource.clear=离场机器人
log.controller.vehicle.reset=复位机器人
log.controller.vehicle.dockingReset=对接复位
log.controller.vehicle.closeSoundLightAlarm=关闭声光报警
log.controller.vehicle.charge=普通充电
log.controller.vehicle.group.insert=新增机器人组
log.controller.vehicle.group.update=修改机器人组
log.controller.vehicle.group.delete=删除机器人组
log.controller.vehicle.group.export=导出机器人组
log.controller.vehicle.group.import=导入机器人组
log.controller.vehicle.type.insert=新增机器人类型
log.controller.vehicle.type.update=修改机器人类型
log.controller.vehicle.type.delete=删除机器人类型
log.controller.vehicle.type.export=导出机器人类型
log.controller.vehicle.type.import=导入机器人类型
log.controller.vehicle.map.appoint=指定地图
log.controller.vehicle.map.relocation=重定位机器人
log.controller.warehouse.area.insert=新增库位区域
log.controller.warehouse.area.update=修改库位区域
log.controller.warehouse.area.delete=删除库位区域
log.controller.warehouse.area.export=导出库位区域
log.controller.warehouse.area.import=导入库位区域
log.controller.warehouse.type.insert=新增库位类型
log.controller.warehouse.type.update=修改库位类型
log.controller.warehouse.type.delete=删除库位类型
log.controller.warehouse.type.export=导出库位类型
log.controller.warehouse.type.import=导入库位类型
log.controller.warehouse.insert=新增库位
log.controller.warehouse.batchInsert=批量新增库位
log.controller.warehouse.update=修改库位
log.controller.warehouse.delete=删除库位
log.controller.warehouse.enable=启用库位
log.controller.warehouse.disable=禁用库位
log.controller.warehouse.export=导出库位
log.controller.warehouse.import=导入库位
log.controller.event.insert=新增事件
log.controller.event.update=修改事件
log.controller.event.copy=复制事件
log.controller.event.delete=删除事件
log.controller.event.enable=启用事件
log.controller.event.disable=禁用事件
log.controller.event.export=导出事件
log.controller.event.import=导入事件
log.controller.charge.station.update=修改充电桩
log.controller.charge.station.delete=删除充电桩
log.controller.charge.station.enable=启用充电桩
log.controller.charge.station.disable=禁用充电桩
log.controller.charge.station.reset=充电桩复位
log.controller.charge.station.stopCharge=充电桩终止放电

log.operation.excel.head.operator=用户
log.operation.excel.head.description=操作
log.operation.excel.head.success=结果
log.operation.excel.head.errorMsg=失败原因
log.operation.excel.head.wasteTime=响应时长
log.operation.excel.head.ip=客户端ip地址
log.operation.excel.head.paramsIn=请求信息
log.operation.excel.head.paramsOut=返回信息
log.operation.excel.head.operationTime=操作时间

log.interface.excel.head.description=详情
log.interface.excel.head.success=结果
log.interface.excel.head.errorMsg=失败原因
log.interface.excel.head.wasteTime=响应时长
log.interface.excel.head.url=URL
log.interface.excel.head.paramsIn=请求信息
log.interface.excel.head.paramsOut=返回信息
log.interface.excel.head.operationTime=创建时间

log.system.module.task=任务
log.system.module.task.allocation=机器人调度
log.system.module.charge.allocation=充电调度
log.system.module.park.allocation=泊车调度
log.system.module.resource.apply=交通资源
log.system.module.traffic.avoid=交通避让
log.system.module.vehicle=机器人
log.system.module.event=事件
log.system.module.system=系统
log.system.module.autodoor=自动门
log.system.module.airshowerdoor=风淋门
log.system.module.elevator=电梯
log.system.module.charge.station=充电桩

log.system.type.Running=运行日志
log.system.type.Warning=告警日志
log.system.type.Error=错误日志

log.system.system.start=调度系统启动成功
log.system.path.plan.is.unreachable=路径规划失败, 目标点不可达
log.system.instruction.status.upload=收到机器人指令状态反馈
log.system.resource.elevator.apply.success=电梯到达申请楼层并已开门
log.system.resource.elevator.ride.success=电梯到达目标楼层并已开门
log.system.resource.vehicle.clear.resource=机器人网络连接超时，自动清理机器人占用的资源

log.system.vehicle.connect=机器人连入调度系统
log.system.vehicle.disconnect=机器人断开连接调度系统
log.system.vehicle.out.of.trace=已脱轨
log.system.vehicle.on.trace=未脱轨
log.system.vehicle.pause.close=关闭暂停
log.system.vehicle.pause.open=开启暂停
log.system.vehicle.close.stop=机器人已被恢复急停按钮
log.system.vehicle.open.stop=机器人已被按下急停按钮
log.system.vehicle.manual.control=机器人已被切换为手动控制模式
log.system.vehicle.auto.control=机器人已被切换为自动控制模式
log.system.vehicle.repair.control=机器人已被切换为检修控制模式
log.system.vehicle.work.status.work=工作
log.system.vehicle.work.status.free=空闲
log.system.vehicle.connect.status.connect=已连接
log.system.vehicle.connect.status.disconnect=已断开
log.system.vehicle.control.status.manual=手动
log.system.vehicle.control.status.auto=自动
log.system.vehicle.control.status.repair=检修
log.system.vehicle.abnormal.status.abnormal=异常
log.system.vehicle.abnormal.status.normal=无异常
log.system.vehicle.position.status.notLocated=未定位
log.system.vehicle.position.status.located=已定位

log.system.charge.scheduler.error=机器人充电分配异常
log.system.charge.create.task.success=机器人创建充电任务成功
log.system.charge.create.task.fail=机器人创建充电任务失败
log.system.charge.vehicle.disable=机器人未启用自动充电
log.system.charge.battery.value.is.null=机器人当前电量是空值
log.system.charge.no.usable.charge.marker=机器人无可用充点电
log.system.charge.get.other.charge.marker=机器人电量过低，抢占其他机器人的充电点
log.system.park.scheduler.error=机器人泊车分配异常
log.system.park.vehicle.disable=机器人未启用自动泊车
log.system.park.no.usable.park.marker=机器人无可用泊车点
log.system.park.create.task.success=机器人创建泊车任务成功
log.system.park.create.task.fail=机器人创建泊车任务失败

log.system.traffic.marker.is.not.avaliable.error=机器人无法前往避让点位，重新规划路径
log.system.traffic.resource.conflict=多个机器人之间路径冲突，机器人重新规划路径到点位
log.system.traffic.detect.obstacle=机器人检测到障碍物，开始重新规划路径
log.system.traffic.detect.vehicle=机器人检测到前方有机器人阻挡, 开始重新规划路径
log.system.traffic.detect.control.area=机器人遇到封控区域，开始重新规划路径
log.system.traffic.detect.map.publish=机器人重新规划路径，用户手动发布了地图
log.system.traffic.detect.vehicle.error=系统检测到机器人前方被阻挡，绕路失败
log.system.traffic.detect.vehicle.drive=机器人检测到前方有机器人阻挡, 驱赶机器人, 驱赶到点位
log.system.traffic.detect.vehicle.drive.error=系统检测到机器人前方被阻挡，驱赶失败

log.system.auto.door.thread.error=自动门程序异常
log.system.auto.door.connect.error=自动门连接失败
log.system.auto.door.no.bind.path.error=自动门未绑定路径
log.system.auto.door.read.error=自动门读取指令失败
log.system.auto.door.write.error=自动门写入指令失败
log.system.auto.door.open.ok=自动门开门成功
log.system.auto.door.close.ok=自动门关门成功

log.system.air.shower.thread.error=风淋门程序异常
log.system.air.shower.no.bind.path.error=风淋门未绑定路径
log.system.air.shower.connect.error=风淋门连接失败
log.system.air.shower.read.error=风淋门读取指令失败
log.system.air.shower.write.error=风淋门写入指令失败
log.system.air.shower.open.ok=风淋门开门成功
log.system.air.shower.close.ok=风淋门关门成功

log.system.elevator.thread.error=电梯程序异常
log.system.elevator.no.bind.path.error=电梯未绑定点位
log.system.elevator.vehicle.leave=机器人离开电梯成功
log.system.elevator.vehicle.apply.run=机器人申请电梯升降
log.system.elevator.connect.error=电梯连接失败
log.system.elevator.read.error=电梯读取指令失败
log.system.elevator.write.error=电梯写入指令失败

log.system.task.allocation.error=机器人任务分配异常
log.system.task.allocation.cancel.old.task.success=原任务取消完成, 新任务绑定机器人
log.system.task.allocation.interrupt.current.task=任务分配机器人, 取消机器人正在执行的任务
log.system.task.allocation.success=任务分配成功，机器人
log.system.task.allocation.fail.vehicle.no.exist=指定机器人分配失败，机器人不存在
log.system.task.allocation.fail.vehicle.state.error=指定机器人分配失败，机器人状态不符合
log.system.task.allocation.fail.vehicle.locked=指定机器人分配失败，机器人已被任务占用
log.system.task.start.run=任务开始执行
log.system.task.run.error=任务执行异常
log.system.task.run.finish=任务执行完成
log.system.task.cancel.run=任务取消执行
log.system.task.status.change.callback.request.param=任务状态变更，触发HTTP回调
log.system.task.status.change.callback.response.param=任务状态变更，HTTP回调完成

log.system.node.start=节点开始执行
log.system.node.end=节点完成执行
log.system.node.cancel=节点取消执行
log.system.node.error=节点执行异常
log.system.node.no.available.marker=分配点位失败，无可用的点位
log.system.node.start.send=节点执行，机器人开始下发指令
log.system.node.send.succeed=节点执行，机器人下发指令成功
log.system.node.send.error=节点执行，机器人下发指令失败
log.system.node.run.succeed=节点执行，机器人完成指令
log.system.node.run.error=节点执行，机器人执行指令失败
log.system.node.start.send.cancel=节点执行，机器人开始下发停止指令
log.system.node.send.cancel.succeed=节点执行，机器人下发停止指令成功
log.system.node.send.cancel.error=节点执行，机器人下发停止指令失败
log.system.node.stop.succeed=节点执行，机器人停止指令成功
log.system.node.stop.error=节点执行，机器人停止指令失败
log.system.node.cancel.timeout=节点执行，机器人停止指令超时，系统强制停止任务
log.system.node.button.release.succeed=放行按钮节点，设备放行成功
log.system.node.button.release.error=放行按钮节点异常，无法连接设备
log.system.node.button.reset.succeed=重置按钮节点执行成功
log.system.node.button.reset.error=重置按钮节点执行异常，无法连接设备
log.system.node.vehicle.no.assign.error=节点执行，未锁定机器人
log.system.node.vehicle.no.exist.error=节点执行，机器人不存在
log.system.node.vehicle.disconnect.error=机器人移动节点， 机器人状态检验失败, 机器人断开连接
log.system.node.vehicle.abnormal.error=机器人移动节点， 机器人状态检验失败, 机器人存在异常
log.system.node.vehicle.state.error=节点执行，机器人状态不符合
log.system.node.vehicle.move.state.change.re.pathplan=机器人移动节点， 机器人状态发生变化，重新规划路径
log.system.node.vehicle.move.send.instruction=机器人移动节点， 机器人下发移动指令
log.system.node.vehicle.move.finish=机器人移动节点， 机器人移动指令完成
log.system.node.vehicle.move.error=机器人移动节点， 机器人移动指令执行失败
log.system.node.vehicle.move.send.cancel=机器人移动节点， 发送取消移动指令
log.system.node.vehicle.move.send.cancel.fail=机器人移动节点， 发送取消移动指令失败
log.system.node.vehicle.move.cancel.success=机器人移动节点， 移动指令取消成功
log.system.node.vehicle.move.cancel.fail=机器人移动节点， 移动指令取消失败
log.system.node.vehicle.move.no.position=机器人移动节点， 机器人没有位置数据
log.system.node.vehicle.move.stay.tartget.marker=机器人移动节点， 机器人已在目标点
log.system.node.vehicle.move.pathplan.start=机器人移动节点，机器人开始路径规划
log.system.node.vehicle.move.pathplan.success=机器人移动节点，机器人路径规划成功
log.system.node.vehicle.move.pathplan.message=机器人移动节点，生成路径规划数据
log.system.node.vehicle.move.pathplan.cancel=机器人移动节点，检测到路径导航撤销
log.system.node.vehicle.move.re.pathplan=机器人移动节点，机器人不在规划的路径上，重新规划路径
log.system.node.vehicle.move.stop.button=机器人移动节点，机器人已被拍下急停按钮
log.system.node.set.variable.param.is.empty=设定变量节点, 找不到设定数字变量的参数
log.system.node.set.variable.param.change.error=设定变量节点, 参数不能转为数字变量
log.system.node.set.variable.param.source.is.unknown=设定变量节点, 找不到参数来源
log.system.node.set.variable.param.is.change=设定变量节点, 设定变量节点将变量名由设置为
log.system.node.finish.request.param=任务结束HTTP回调发出请求
log.system.node.finish.response.param=任务结束HTTP回调接收响应
log.system.node.http.request.param=执行Http请求节点, 发出请求
log.system.node.http.response.param=执行Http请求节点, 接收响应
log.system.node.http.check.param=HTTP校验节点，缺少校验值
log.system.node.http.check.path=HTTP参数校验失败，没有找到对应的字段
log.system.node.http.check.fail=HTTP参数校验失败

log.system.trigger.callbox.success=触发呼叫盒按钮事件，创建任务成功
log.system.trigger.callbox.fail=触发呼叫盒按钮事件，创建任务失败
log.system.trigger.fix.success=触发定时事件，创建任务成功
log.system.trigger.fix.fail=触发定时事件，创建任务失败
log.system.trigger.plc.success=触发寄存器事件，创建任务成功
log.system.trigger.plc.fail=触发寄存器事件，创建任务失败
log.system.trigger.task.cancel.success=触发任务取消事件，创建任务成功
log.system.trigger.task.cancel.fail=触发任务取消事件，创建任务失败
log.system.trigger.task.finish.success=触发任务完成事件，创建任务成功
log.system.trigger.task.finish.fail=触发任务完成事件，创建任务失败
log.system.trigger.vehicle.abnormal.success=触发机器人异常事件，创建任务成功
log.system.trigger.vehicle.abnormal.fail=触发机器人异常事件，创建任务失败
log.system.trigger.vehicle.plc.success=触发机器人寄存器事件，创建任务成功
log.system.trigger.vehicle.plc.fail=触发机器人寄存器事件，创建任务失败

log.system.export.error=单次导出数量不能超过10000条，请重新筛选
log.system.export.name=运行日志
log.system.download.file.not.exist=找不到指定文件，无法下载
log.system.download.file.error=文件下载失败

log.system.excel.head.module=类别
log.system.excel.head.type=等级
log.system.excel.head.content=描述
log.system.excel.head.data=数据
log.system.excel.head.message=报文
log.system.excel.head.vehicleCodes=机器人
log.system.excel.head.taskNos=任务
log.system.excel.head.createDate=创建时间
log.system.excel.head.lastTime=最后更新时间

log.system.charge.station.connect=充电桩连接成功
log.system.charge.station.disconnect=充电桩断开连接
log.system.charge.station.operate=充电桩操作结果
log.system.charge.station.cancel.charge.task=由于充电被强制停止，节点取消执行

#validation
validation.id.require=ID不能为空
validation.id.null=ID必须为空
validation.pid.require=上级ID，不能为空
validation.sort.number=排序值不能小于0
validation.sysparams.paramcode.require=参数编码不能为空
validation.sysparams.paramvalue.require=参数值不能为空
validation.sysuser.username.require=用户名不能为空
validation.sysuser.password.require=密码不能为空
validation.sysuser.realname.require=姓名不能为空
validation.sysuser.email.require=邮箱不能为空
validation.sysuser.email.error=邮箱格式不正确
validation.sysuser.mobile.require=手机号不能为空
validation.sysuser.superadmin.range=超级管理员取值范围0~1
validation.sysuser.status.range=状态取值范围0~1
validation.sysmenu.pid.require=请选择上级菜单
validation.sysmenu.name.require=菜单名称不能为空
validation.sysmenu.type.range=菜单类型取值范围0~1
validation.sysrole.name.require=角色名称不能为空
validation.schedule.status.range=状态取值范围0~1
validation.schedule.cron.require=cron表达式不能为空
validation.schedule.bean.require=bean名称不能为空
validation.news.title.require=标题不能为空
validation.news.content.require=内容不能为空
validation.news.pubdate.require=发布时间不能为空
validation.map.marker.name=点位名称命名规则：为空，或者字母或数字、下划线，字母开头，长度不超过20个字符
validation.map.marker.type.require=点位类型不能为空
validation.map.marker.code.require=点位编号不能为空
validation.map.marker.type=点位类型必须是如下其中之一：ChargingMarker,NavigationMarker,WorkMarker
validation.map.marker.x.require=x坐标不能为空
validation.map.marker.y.require=y坐标不能为空
validation.map.path.type.require=路径类型不能为空
validation.map.path.type=路径类型必须是如下其中之一：Common、普通路径，QR_Down、二维码对接路径，Shelflegs、货架腿对接，Symbol_V、V型板对接，Reflector、反光板对接，LeaveDocking、脱离对接，Pallet、托盘对接
validation.map.path.startMarkerCode.require=开始标记点不能为空
validation.map.path.endMarkerCode.require=结束标记点不能为空
validation.map.path.weightRatio.require=路径权重必须是正数
validation.map.area.areaType.require=区域类型不能为空，枚举值： 单机区域:SingleAgvArea 显示区域: ShowArea 封控区域：ControlArea 通道区域：ChannelArea 禁旋区域：NoRotatingArea 禁停区域：NoParkingArea
validation.map.area.areaType=区域类型必须是如下其中之一：SingleAgvArea,ShowArea,ControlArea,ChannelArea,NoRotatingArea,NoParkingArea
validation.map.area.polygon.require=区域坐标列表不能为空
validation.map.area.operateType.require=构建区域的方式不能为空，枚举值：1、多边形区域方式构建、2、矩形区域方式构建
validation.map.type.require=地图类型不能为空
validation.map.code.require=地图编码不能为空
validation.map.name.require=地图名称不能为空
validation.map.originX.require=地图中心x坐标不能为空
validation.map.originY.require=地图中心y坐标不能为空
validation.map.resolution.require=地图分辨率不能为空
validation.map.height.require=地图像素高度不能为空
validation.map.width.require=地图像素宽度不能为空
validation.door.code.require=自动门编码不能为空
validation.door.ip.require=自动门ip不能为空
validation.door.port.require=自动门port不能为空
validation.door.openAddress.require=自动门开门地址不能为空
validation.door.openStatusAddress.require=自动门开门状态地址不能为空
validation.door.closeAddress.require=自动门关门地址不能为空
validation.door.closeStatusAddress.require=自动门关门状态地址不能为空
validation.door.pathCodes.require=自动门关联的路径编码不能为空
validation.elevator.code.require=电梯编码不能为空
validation.elevator.ip.require=电梯ip不能为空
validation.elevator.port.require=电梯port不能为空
validation.elevator.controlAddress.require=电梯控制地址不能为空
validation.elevator.destAddress.require=电梯目的地址不能为空
validation.elevator.openAddress.require=电梯开门地址不能为空
validation.elevator.readFunctionCode.require=电梯Modbus读功能码不能为空
validation.elevator.writeFunctionCode.require=电梯Modbus写功能码不能为空
validation.property.type.require=系统属性的类型不能为空
validation.property.category.require=系统属性的分类不能为空
validation.property.propertyKey.require=系统属性的键值不能为空
validation.property.valueType.require=系统属性的值类型不能为空


#vehicleMap
vehicleMap.airShowerDoor.not.exist.error=操作失败，风淋门[%s]不存在
vehicleMap.airShowerDoor.add.error=新增风淋门异常，请查看操作日志
vehicleMap.airShowerDoor.update.error=修改风淋门[%s]异常，请查看操作日志
vehicleMap.airShowerDoor.delete.error=删除风淋门[%s]异常，请查看操作日志
vehicleMap.airShowerDoor.bind.path.error=风淋门[%s]绑定路径异常，绑定重复路径

vehicleMap.autoDoor.not.exist.error=操作失败，自动门[%s]不存在
vehicleMap.autoDoor.already.bind.other.device.error=操作失败，路径[%s]已绑定设备
vehicleMap.autoDoor.add.error=新增自动门异常，请查看操作日志
vehicleMap.autoDoor.update.error=修改自动门[%s]异常，请查看操作日志
vehicleMap.autoDoor.delete.error=删除自动门[%s]异常，请查看操作日志

vehicleMap.elevator.add.error=新增电梯异常，请查看操作日志
vehicleMap.elevator.update.error=修改电梯[%s]异常，请查看操作日志
vehicleMap.elevator.delete.error=删除电梯[%s]异常，请查看操作日志
vehicleMap.elevator.not.exist.error=操作失败，电梯[%s]不存在
vehicleMap.elevator.file.format.error=电梯文件格式错误
vehicleMap.elevator.import.already.exist.error=电梯导入异常，电梯[%s]已存在
vehicleMap.elevator.import.error=电梯导入异常，原因：%s
vehicleMap.elevator.export.error=电梯导出异常，原因：%s
vehicleMap.elevator.publish.check.error=地图发布检查，关联电梯正在被使用，是否强制发布
vehicleMap.elevator.import.bind.map.error=电梯[%s]导入失败，请先导入电梯绑定的所有地图
vehicleMap.elevator.bind.multi.marker.error=电梯绑定异常，电梯不能绑定同一地图的多个点位

vehicleMap.mapArea.not.exist.error=操作失败，区域[%s]不存在
vehicleMap.mapArea.add.error=新增区域异常，请查看操作日志
vehicleMap.mapArea.update.error=修改区域[%s]异常，请查看操作日志
vehicleMap.mapArea.update.occupied.error=修改区域[%s]失败，当前资源正在被使用
vehicleMap.mapArea.delete.error=删除区域[%s]异常，请查看操作日志

vehicleMap.marker.add.error=新增点位异常，请查看操作日志
vehicleMap.marker.update.error=修改点位[%s]异常，请查看操作日志
vehicleMap.marker.delete.error=删除点位[%s]异常，请查看操作日志
vehicleMap.marker.not.exist.error=操作失败，点位[%s]不存在
vehicleMap.marker.already.bind.other.device.error=操作失败，点位[%s]已绑定设备
vehicleMap.marker.spacing.error=点位间距小于设定值[%s]

vehicleMap.path.bind.marker.no.exist.error=操作失败，路径绑定的点位[%s]不存在
vehicleMap.path.already.bind.device.error=操作失败，路径[%s]已绑定设备
vehicleMap.path.already.exist.error=操作失败，路径[%s]已存在
vehicleMap.path.not.exist.error=操作失败，路径[%s]不存在
vehicleMap.path.add.error=新增路径异常，请查看操作日志
vehicleMap.path.update.error=修改路径[%s]异常，请查看操作日志
vehicleMap.path.delete.error=删除路径[%s]异常，请查看操作日志

vehicleMap.map.operating.duplicate.error=执行中，请勿重复操作
vehicleMap.map.not.exist.error=地图[%s]不存在，请退出地图编辑页面
vehicleMap.map.file.format.error=导入的文件[%s]格式不正确
vehicleMap.map.add.error=新增地图异常，请查看操作日志
vehicleMap.map.update.error=修改地图[%s]异常，请查看操作日志
vehicleMap.map.delete.error=删除地图[%s]异常，请查看操作日志
vehicleMap.map.roadnet.update.error=修改地图[%s]路网元素异常，请查看操作日志
vehicleMap.map.roadnet.delete.error=删除地图[%s]路网元素异常，请查看操作日志
vehicleMap.map.draft.delete.error=删除地图[%s]路网草稿异常，请查看操作日志
vehicleMap.map.is.not.publish.error=地图[%s]没有正式版本
vehicleMap.map.import.error=地图导入异常，原因：%s
vehicleMap.map.import.structure.error=导入的地图文件格式不正确
vehicleMap.map.import.in.edit.page.error=请在地图编辑页面中导入定位图
vehicleMap.map.import.missing.info.error=导入的地图缺失info文件
vehicleMap.map.import.missing.png.error=导入的地图缺失png文件
vehicleMap.map.import.missing.locating.error=导入的地图文件格式不正确：路网文件没有定位图信息
vehicleMap.map.import.appoint.default.error=导入的地图文件格式不正确：路网文件没有指定默认定位图
vehicleMap.map.export.error=地图导出异常，原因：%s
vehicleMap.map.copy.error=地图复制异常，原因：%s
vehicleMap.map.reset.error=当前地图[%s]没有可撤销的操作
vehicleMap.map.recover.error=当前地图[%s]没有可恢复的操作
vehicleMap.map.global.recover.error=机器人恢复失败：%s
vehicleMap.map.publish.occupied.error=发布地图失败，当前资源[%s]正在被使用

vehicleMap.map.locatingmap.is.empty.error=地图[%s]定位图数据为空!
vehicleMap.map.locatingmap.code.is.empty.error=地图[%s]定位图编码为空!
vehicleMap.map.locatingmap.not.exist.error=定位图[%s]不存在，请查看操作日志
vehicleMap.map.locatingmap.update.error=修改定位图[%s]异常，请查看操作日志
vehicleMap.map.locatingmap.default.error=不能删除[%s]的默认定位图[%s]
vehicleMap.map.locatingmap.import.structure.error=导入的定位图文件目录结构异常
vehicleMap.map.locatingmap.import.file.is.empty.error=导入的定位图文件列表为空
vehicleMap.map.locatingmap.import.file.is.missing.error=导入的定位图[%s]文件缺失
vehicleMap.map.locatingmap.import.file.is.duplicate.error=该定位图%s已存在于地图%s中，请检查后再操作
vehicleMap.map.locatingmap.import.file.is.forbidden.error=该编码定位图%s不允许导入，请检查后再操作
vehicleMap.map.locatingmap.export.error=导出定位图[%s]异常，原因：%s

#device
device.connect.error=操作设备失败，设备[%s]网络未连接
device.open.error=开启设备[%s]异常，请查看操作日志
device.close.error=关闭设备[%s]异常，请查看操作日志
device.is.in.use.error=操作设备失败，设备[%s]正在使用中

#task
task.node.config.export.file.name=节点设置
task.node.config.export.error=节点设置导出异常，原因：%s
task.node.config.import.error=节点设置导入异常，原因：%s
task.node.is.not.exist.error=任务节点[%s]不存在
task.node.is.not.allow.retry.error=任务节点[%s]不允许重试
task.node.is.not.allow.skip.error=任务节点[%s]不允许被跳过
task.type.is.not.published.error=新增任务失败，使用了未发布的任务流程[%s]
task.type.is.not.exist.error=任务流程[%s]不存在
task.type.export.error=任务流程导出异常，原因：%s
task.type.import.error=任务流程导入异常，原因：%s
task.type.node.is.empty.error=启用任务失败，任务流程[%s]缺少可用的节点
task.type.enable.el.parse.error=启用失败，原因为：%s
task.type.enable.param.is.null.error=存在未设定参数的节点：[%s]
task.type.node.while.is.empty.error=存在空循环的节点：[%s]
task.type.prefix.name=任务流程
task.is.not.exist.error=任务[%s]不存在
task.delete.running.error=无法删除执行中的任务
task.cancel.running.error=取消失败，该任务禁止取消
task.export.file.name=任务
task.export.error=任务导出异常，原因：%s
task.import.error=任务导入异常，原因：%s
task.import.code.duplicate.error=上传任务记录失败，重复的任务编码[%s]
task.cancel.timeout.error=取消机器人指令超时，请重启机器人[%s]清理指令
task.insert.vehicle.not.exist.error=新增任务失败，输入的机器人[%s]不存在
task.insert.marker.not.exist.error=新增任务失败，输入的点位[%s]不存在
task.insert.marker.lock.error=该点位[%s]已被其它任务占用
task.insert.map.not.exist.error=新增任务失败，输入的地图[%s]不存在
task.insert.dynamic.param.format.error=传入的动态参数[%s]格式[%s]不正确
task.excel.head.taskNo=编号
task.excel.head.externalTaskNo=外部编码
task.excel.head.name=名称
task.excel.head.status=状态
task.excel.head.priority=优先级
task.excel.head.vehicleCodes=机器人
task.excel.head.source=创建方式
task.excel.head.createDate=创建时间
task.excel.head.startTime=开始时间
task.excel.head.endTime=结束时间
task.excel.head.remark=备注
task.excel.head.callbackUrl=上游回调地址

task.event.not.exist.error=事件[%s]不存在
task.event.bound.taskType.is.null.error=事件[%s]未关联任务流程
task.event.running.duplicate.error=当前事件不允许重复，且有运行中的任务
task.event.plc.condition.check.fail.error=寄存器触发条件校验不通过，请检查
task.event.vehicle.condition.check.fail.error=机器人寄存器触发条件校验不通过，请检查
task.event.fix.interval.time.error=间隔时间设置有误，请检查
task.event.relate.task.contain.cancel.node.error=取消任务节点关联的任务[%s]中不能包含取消任务节点
task.event.relate.task.create.error=取消任务节点关联的任务[%s]创建失败，原因：[%s]

task.event.type.fixedTime=定时事件
task.event.type.button=按钮事件
task.event.type.plc=寄存器事件
task.event.type.vehiclePlc=机器人寄存器事件
task.event.type.vehicleAbnormal=机器人异常事件
task.event.type.taskCancel=任务取消事件
task.event.type.taskFinished=任务完成事件
task.event.status.enable=启用
task.event.status.disable=禁用
task.event.repeat.allow=允许
task.event.repeat.disallow=禁止
task.event.export.file.name=事件

task.event.excel.head.code=事件编码
task.event.excel.head.name=事件名称
task.event.excel.head.type=事件类型
task.event.excel.head.isAllowRepeat=是否允许重复
task.event.excel.head.taskTypeId=任务类型
task.event.excel.head.status=启用状态
task.event.excel.head.param=事件参数
task.event.excel.head.taskParam=任务参数

task.node.name.Wait=等待
task.node.name.DynamicAllocationVehicle=动态分配机器人
task.node.name.AllocationMarker=随机分配点位
task.node.name.ReleaseVehicle=释放机器人
task.node.name.VehicleRotation=机器人旋转
task.node.name.TrayRotation=托盘旋转
task.node.name.TrayLifting=托盘升降
task.node.name.ReadPlc=读寄存器
task.node.name.WritePlc=写寄存器
task.node.name.AssignAllocationVehicle=指定分配机器人
task.node.name.VehicleMove=机器人移动
task.node.name.GetMarkerAttribute=获取点位属性
task.node.name.DockingCharge=对接充电
task.node.name.ButtonRelease=按钮放行
task.node.name.ButtonReset=按钮重置
task.node.name.Alarm=声光报警
task.node.name.ReadVehiclePlc=读机器人寄存器
task.node.name.WriteVehiclePlc=写机器人寄存器
task.node.name.Rotation=组合旋转
task.node.name.RobotArmScript=机械臂控制
task.node.name.CheckPlc=校验寄存器
task.node.name.PlayAudio=播放音频
task.node.name.SwitchSpeedArea=切换速度区域
task.node.name.SwitchDockingArea=切换对接区域
task.node.name.TrayFollowControl=托盘模式切换
task.node.name.CheckVehiclePlc=校验机器人寄存器
task.node.name.ForkArmLifting=叉臂升降
task.node.name.GetTaskAttribute=获取任务属性
task.node.name.FinishTask=结束任务
task.node.name.HttpRequest=Http 请求
task.node.name.NobodyForkCharge=叉车充电
task.node.name.DockingNavigation=对接
task.node.name.SwitchScheduleMode=切换调度模式
task.node.name.GetVehicleAttribute=获取机器人属性
task.node.name.AllocationWarehouse=分配库位
task.node.name.UpdateWarehouse=更新库位
task.node.name.GetWarehouseAttribute=获取库位属性
task.node.name.GetBarcodeAttribute=获取条码属性
task.node.name.GetAdjacentMarker=获取相邻点位
task.node.name.Ajust=微调
task.node.name.LeaveDocking=脱离对接
task.node.name.StopAudio=停止播放音频
task.node.name.ReadQrCode=读取二维码
task.node.name.CheckSensorObstacleStatus=叉车放货检测
task.node.name.SetVariable=设定变量
task.node.name.Notice=通知报警
task.node.name.CancelTask=取消任务
task.node.name.ForbidCancelTask=禁止取消任务
task.node.name.OperateMapArea=操作地图区域
task.node.name.HttpCheck=Http校验
task.node.name.JavaScript=Java脚本
task.node.name.ReadPLCStr=读寄存器-字符串
task.node.name.WritePLCStr=写寄存器-字符串
task.node.name.LockMarker=锁定点位
task.node.name.StopCharge=停止充电

task.node.notice.Wait=等待一段时间后再执行后续节点
task.node.notice.DynamicAllocationVehicle=随机分配并占用机器人，占用成功后执行后续节点
task.node.notice.AllocationMarker=随机分配一个的点位
task.node.notice.ReleaseVehicle=任务释放对该机器人的占用，被释放的机器人可以立即被其它任务占用
task.node.notice.VehicleRotation=控制机器人旋转
task.node.notice.TrayRotation=控制机器人的托盘旋转
task.node.notice.TrayLifting=控制机器人的托盘升降
task.node.notice.ReadPlc=读远程寄存器的值
task.node.notice.WritePlc=向外部寄存器写入值
task.node.notice.AssignAllocationVehicle=任务占用指定的机器人，占用成功后执行后续节点
task.node.notice.VehicleMove=为机器人规划路径并控制机器人前往目标点
task.node.notice.GetMarkerAttribute=获取并输出点位属性给其它节点使用
task.node.notice.DockingCharge=控制机器人自动对接充电桩
task.node.notice.ButtonRelease=按下优艾呼叫盒按钮后，执行后续节点
task.node.notice.ButtonReset=可重置呼叫盒按钮，被重置的按钮可重新触发
task.node.notice.Alarm=控制声光报警器发出报警
task.node.notice.ReadVehiclePlc=读取机器人内部的寄存器值
task.node.notice.WriteVehiclePlc=向机器人内部的寄存器写入值
task.node.notice.Rotation=控制机器人和托盘一起旋转
task.node.notice.RobotArmScript=使用Json脚本控制机械臂运动
task.node.notice.CheckPlc=校验寄存器
task.node.notice.PlayAudio=播放音频
task.node.notice.SwitchSpeedArea=切换速度区域
task.node.notice.SwitchDockingArea=切换对接区域
task.node.notice.TrayFollowControl=托盘模式切换
task.node.notice.CheckVehiclePlc=校验机器人寄存器
task.node.notice.ForkArmLifting=控制无人叉车的叉臂升降
task.node.notice.GetTaskAttribute=获取任务属性
task.node.notice.FinishTask=结束任务
task.node.notice.HttpRequest=向外部系统发送请求，并等待执行结果
task.node.notice.NobodyForkCharge=无人叉车节点
task.node.notice.DockingNavigation=对接
task.node.notice.SwitchScheduleMode=切换调度模式
task.node.notice.GetVehicleAttribute=获取机器人属性
task.node.notice.AllocationWarehouse=分配库位
task.node.notice.UpdateWarehouse=更新库位
task.node.notice.GetWarehouseAttribute=获取库位属性
task.node.notice.GetBarcodeAttribute=获取条码属性
task.node.notice.GetAdjacentMarker=获取相邻点位
task.node.notice.Ajust=微调
task.node.notice.LeaveDocking=脱离对接
task.node.notice.StopAudio=停止播放音频
task.node.notice.ReadQrCode=读取二维码
task.node.notice.CheckSensorObstacleStatus=叉车插尖传感器是否触发避障,是:表示触发避障, false 表示 未触发避障
task.node.notice.SetVariable=该节点可以重新设置变量的值
task.node.notice.Notice=通知报警
task.node.notice.CancelTask=取消任务事件
task.node.notice.ForbidCancelTask=禁止取消任务
task.node.notice.OperateMapArea=操作地图区域
task.node.notice.HttpCheck=HTTP（POST请求）结果校验
task.node.notice.JavaScript=Java脚本节点
task.node.notice.ReadPLCStr=读取PLC的ASCII转为字符串
task.node.notice.WritePLCStr=写入PLC ASCII码
task.node.notice.LockMarker=锁定点位
task.node.notice.StopCharge=停止充电

task.node.param.name.1718202092436357121.vehicleCode.In=机器人
task.node.param.name.1718202092436357121.position.In=目标高度
task.node.param.name.1718202092436357121.offsetHeight.In=偏差高度
task.node.param.name.1718202092436357121.speed.In=速度
task.node.param.name.1738448135527288834.vehicleCode.In=机器人
task.node.param.name.1738448135527288834.markerCode.In=充电点
task.node.param.name.1738448135527288834.chargeTime.In=充电时间
task.node.param.name.1738448135527288834.batteryCharge.In=充电电量
task.node.param.name.1738443040093851650.finishType.In=结束类型
task.node.param.name.1738443040093851650.noticeMsg.In=结束提醒
task.node.param.name.1646764086215663617.vehicleCode.In=机器人
task.node.param.name.1646764086215663617.vehicleAngle1.In=机器人角度1
task.node.param.name.1646764086215663617.vehicleAngle2.In=机器人角度2
task.node.param.name.1646764086215663617.trayRotationSpeed.In=托盘旋转速度
task.node.param.name.1646764086215663617.trayAngle1.In=托盘角度1
task.node.param.name.1646764086215663617.trayAngle2.In=托盘角度2
task.node.param.name.1715183824889581570.vehicleCode.In=机器人
task.node.param.name.1715183824889581570.ladarSwitch.In=速度区域
task.node.param.name.1715184972354686978.vehicleCode.In=机器人
task.node.param.name.1715184972354686978.obstacleArea.In=避障范围
task.node.param.name.1738467719873515521.vehicleCode.In=机器人
task.node.param.name.1738467719873515521.scheduleMode.In=调度模式
task.node.param.name.1715183168871075842.vehicleCode.In=机器人
task.node.param.name.1715183168871075842.audioName.In=音频名称
task.node.param.name.1715183168871075842.audioVolume.In=音量
task.node.param.name.1715183168871075842.playCount.In=播放次数
task.node.param.name.1630863227598438401.markerCode.In=点位
task.node.param.name.1630863227598438401.vehicleGroupCode.In=机器人组
task.node.param.name.1630863227598438401.vehicleTypeCode.In=机器人类型
task.node.param.name.1630863227598438401.vehicleMapCodeList.In=地图
task.node.param.name.1630863227598438401.limitBattery.In=电量
task.node.param.name.1630863227598438401.outVehicleCode.Out=机器人
task.node.param.name.1630863227745239041.vehicleCode.In=机器人
task.node.param.name.1630863227745239041.outVehicleCode.Out=机器人
task.node.param.name.1630863227644575745.vehicleCode.In=机器人
task.node.param.name.1630863227623604225.vehicleMapCodeList.In=地图
task.node.param.name.1630863227623604225.markerType.In=点位类型
task.node.param.name.1630863227623604225.outMarkerCode.Out=选中点位
task.node.param.name.1630863227623604225.vehicleCode.In=机器人
task.node.param.name.1738440272671100929.taskNo.Out=任务编码
task.node.param.name.1738440272671100929.externalTaskNo.Out=外部任务编码
task.node.param.name.1738440272671100929.priority.Out=优先级
task.node.param.name.1630863227707490306.ip.In=IP
task.node.param.name.1630863227707490306.port.In=端口
task.node.param.name.1630863227707490306.code.In=功能码
task.node.param.name.1630863227707490306.slaveId.In=从站ID
task.node.param.name.1630863227707490306.address.In=读取地址
task.node.param.name.1630863227707490306.executeMode.In=执行方式
task.node.param.name.1630863227707490306.vehicleCode.In=机器人
task.node.param.name.1630863227707490306.outValue.Out=输出值
task.node.param.name.1645676364679905282.vehicleCode.In=机器人
task.node.param.name.1645676364679905282.code.In=功能码
task.node.param.name.1645676364679905282.slaveId.In=从站ID
task.node.param.name.1645676364679905282.address.In=读取地址
task.node.param.name.1645676364679905282.outValue.Out=寄存器值
task.node.param.name.1630863227724267521.ip.In=IP
task.node.param.name.1630863227724267521.port.In=端口
task.node.param.name.1630863227724267521.code.In=功能码
task.node.param.name.1630863227724267521.slaveId.In=从站ID
task.node.param.name.1630863227724267521.address.In=写入地址
task.node.param.name.1630863227724267521.value.In=写入值
task.node.param.name.1630863227724267521.executeMode.In=执行方式
task.node.param.name.1630863227724267521.vehicleCode.In=机器人
task.node.param.name.1645678201743114241.vehicleCode.In=机器人
task.node.param.name.1645678201743114241.code.In=功能码
task.node.param.name.1645678201743114241.slaveId.In=从站ID
task.node.param.name.1645678201743114241.address.In=写入地址
task.node.param.name.1645678201743114241.value.In=写入值
task.node.param.name.1715188504537468930.vehicleCode.In=机器人
task.node.param.name.1715188504537468930.code.In=功能码
task.node.param.name.1715188504537468930.slaveId.In=从站ID
task.node.param.name.1715188504537468930.address.In=寄存器地址
task.node.param.name.1715188504537468930.successVal.In=正常退出
task.node.param.name.1715188504537468930.failVal.In=失败退出
task.node.param.name.1715188504537468930.timeout.In=超时时间
task.node.param.name.1715188504537468930.outValue.Out=输出值
task.node.param.name.1641376178617024513.callBoxCode.In=呼叫盒编号
task.node.param.name.1641376178617024513.buttonCode.In=按钮编号
task.node.param.name.1641376178617024513.timeout.In=超时放行（秒）
task.node.param.name.1641376553134817282.callBoxCode.In=呼叫盒编号
task.node.param.name.1641376553134817282.buttonCode.In=按钮编号
task.node.param.name.1641377688272863233.ip.In=IP
task.node.param.name.1641377688272863233.port.In=端口
task.node.param.name.1641377688272863233.type.In=报警类型
task.node.param.name.1641377688272863233.time.In=持续时间（秒）
task.node.param.name.1742437277025456130.warehouseCode.In=库位
task.node.param.name.1742437277025456130.containerBarcode.In=容器
task.node.param.name.1742437277025456130.dispatchPolicy.In=分配策略
task.node.param.name.1742437277025456130.occupyStatus.In=库位状态
task.node.param.name.1742437277025456130.warehouseTypeCode.In=库位类型
task.node.param.name.1742437277025456130.warehouseAreaCode.In=库区
task.node.param.name.1742437277025456130.extendParam1.In=扩展属性1
task.node.param.name.1742437277025456130.extendParam2.In=扩展属性2
task.node.param.name.1742437277025456130.extendParam3.In=扩展属性3
task.node.param.name.1742437277025456130.extendParam4.In=扩展属性4
task.node.param.name.1742437277025456130.extendParam5.In=扩展属性5
task.node.param.name.1742437277025456130.warehouseCode.Out=库位编码
task.node.param.name.1742441148997193730.containerBarCode.In=容器条码
task.node.param.name.1742441148997193730.warehouseCode.Out=库区编码
task.node.param.name.1742441148997193730.workMarkerCode.Out=作业点位编码
task.node.param.name.1742441148997193730.workHeight.Out=作业高度
task.node.param.name.1742441148997193730.warehouseTypeCode.Out=库位类型编码
task.node.param.name.1742441148997193730.warehouseAreaCode.Out=库位区域编码
task.node.param.name.1742440444115046401.warehouseCode.In=库位
task.node.param.name.1742440444115046401.containerBarcode.Out=容器条码
task.node.param.name.1742440444115046401.workMarkerCode.Out=作业点位编码
task.node.param.name.1742440444115046401.workHeight.Out=作业高度
task.node.param.name.1742440444115046401.warehouseTypeCode.Out=库位类型编码
task.node.param.name.1742440444115046401.warehouseAreaCode.Out=库位区域编码
task.node.param.name.1742441529047273474.markerCode.In=点位
task.node.param.name.1742441529047273474.markerCode.Out=相邻点位编码
task.node.param.name.1630863227652964354.vehicleCode.In=机器人
task.node.param.name.1630863227652964354.angle.In=旋转角度
task.node.param.name.1630863227652964354.rateType.In=旋转类型
task.node.param.name.1630863227673935874.vehicleCode.In=机器人
task.node.param.name.1630863227673935874.angle1.In=旋转角度1
task.node.param.name.1630863227673935874.angle2.In=旋转角度2
task.node.param.name.1630863227673935874.speed.In=旋转速度
task.node.param.name.1630863227673935874.rateType.In=旋转类型
task.node.param.name.1750822156822622210.vehicleCode.In=机器人
task.node.param.name.1750822156822622210.offsetX.In=偏差 X
task.node.param.name.1750822156822622210.offsetY.In=偏差 Y
task.node.param.name.1750822156822622210.offsetAngle.In=偏差角度
task.node.param.name.1750822156822622210.rotationAngleRange.In=旋转范围
task.node.param.name.1750822156822622210.moveDistanceRange.In=移动范围
task.node.param.name.1750822156822622210.obstacleRegion.In=避障区域
task.node.param.name.1738450017482133505.vehicleCode.In=机器人
task.node.param.name.1738450017482133505.dockingType.In=类型
task.node.param.name.1738450017482133505.startX.In=起点 X 坐标
task.node.param.name.1738450017482133505.startY.In=起点 Y 坐标
task.node.param.name.1738450017482133505.startAngle.In=起点角度
task.node.param.name.1738450017482133505.endX.In=终点 X 坐标
task.node.param.name.1738450017482133505.endY.In=终点 Y 坐标
task.node.param.name.1738450017482133505.endAngle.In=终点角度
task.node.param.name.1738450017482133505.offsetX.In=偏差 X
task.node.param.name.1738450017482133505.offsetY.In=偏差 Y
task.node.param.name.1738450017482133505.offsetAngle.In=偏差角度
task.node.param.name.1738450017482133505.workStationCode.In=工位编码
task.node.param.name.1738450017482133505.templateNo.In=模板编号
task.node.param.name.1738450017482133505.cameraObstacle.In=相机避障
task.node.param.name.1738450017482133505.obstacleRegion.In=避障区域
task.node.param.name.1751081370463637506.vehicleCode.In=机器人
task.node.param.name.1738450017482133505.dockingDirection.In=对接方向
task.node.param.name.1715186203621986306.vehicleCode.In=机器人
task.node.param.name.1715186203621986306.type.In=旋转模式
task.node.param.name.1630863227694907394.vehicleCode.In=机器人
task.node.param.name.1630863227694907394.speed.In=升降速度
task.node.param.name.1630863227694907394.targetTicks.In=升降高度
task.node.param.name.1751878094551769090.vehicleCode.In=机器人
task.node.param.name.1751878094551769090.outValue.Out=二维码信息
task.node.param.name.1751825844022235138.vehicleCode.In=机器人
task.node.param.name.1630863227787182081.markerCode.In=点位
task.node.param.name.1630863227787182081.markerName.In=自定义编码
task.node.param.name.1630863227787182081.code.Out=点位编码
task.node.param.name.1630863227787182081.name.Out=自定义编码
task.node.param.name.1630863227787182081.type.Out=点位类型
task.node.param.name.1630863227787182081.isPark.Out=是否泊车
task.node.param.name.1630863227787182081.angle.Out=点位角度
task.node.param.name.1630863227787182081.chargeEnable.Out=是否允许工作时充电
task.node.param.name.1790203373283213314.oriValue.In=变量名
task.node.param.name.1790203373283213314.newValue.In=期望的变量值
task.node.param.name.1738444988633272322.url.In=Url 地址
task.node.param.name.1738444988633272322.customParams.In=自定义入参
task.node.param.name.1738444988633272322.customValues.Out=自定义出参
task.node.param.name.1630863227757821953.vehicleCode.In=机器人
task.node.param.name.1630863227757821953.markerCode.In=目标点
task.node.param.name.1630863227757821953.dockingMove.In=对接导航
task.node.param.name.1630863227757821953.accurate.In=精定位
task.node.param.name.1630863227757821953.fallPrevent.In=防跌落
task.node.param.name.1630863227757821953.safety3D.In=3D避障
task.node.param.name.1630863227757821953.featureFusion.In=融合特征
task.node.param.name.1630863227757821953.movingSpeed.In=移动速度
task.node.param.name.1630863227757821953.rotationSpeed.In=旋转速度
task.node.param.name.1630863227757821953.moveObstacleRegion.In=移动避障区域
task.node.param.name.1630863227757821953.rotationObstacleRegion.In=旋转避障区域
task.node.param.name.1630863227757821953.obstacleAvoidance.In=自主绕障
task.node.param.name.1630863227757821953.agvDirection.In=车头角度
task.node.param.name.1630863227757821953.extendParams.In=扩展参数
task.node.param.name.1630863227577466882.time.In=等待时间（秒）
task.node.param.name.1714957947186581506.ip.In=IP
task.node.param.name.1714957947186581506.port.In=端口
task.node.param.name.1714957947186581506.code.In=功能码
task.node.param.name.1714957947186581506.slaveId.In=从站ID
task.node.param.name.1714957947186581506.address.In=寄存器地址
task.node.param.name.1714957947186581506.successVal.In=正常退出
task.node.param.name.1714957947186581506.failVal.In=失败退出
task.node.param.name.1714957947186581506.timeout.In=超时时间
task.node.param.name.1714957947186581506.executeMode.In=执行方式
task.node.param.name.1714957947186581506.vehicleCode.In=机器人
task.node.param.name.1714957947186581506.outValue.Out=输出值
task.node.param.name.1788855369901137921.result_code.Out=返回码
task.node.param.name.1788855369901137921.error_message.Out=返回消息
task.node.param.name.1630863227787182081.extendParam1.Out=扩展属性1
task.node.param.name.1630863227787182081.extendParam2.Out=扩展属性2
task.node.param.name.1630863227787182081.extendParam3.Out=扩展属性3
task.node.param.name.1630863227787182081.extendParam4.Out=扩展属性4
task.node.param.name.1630863227787182081.extendParam5.Out=扩展属性5
task.node.param.name.1742440444115046401.extendParam1.Out=扩展属性1
task.node.param.name.1742440444115046401.extendParam2.Out=扩展属性2
task.node.param.name.1742440444115046401.extendParam3.Out=扩展属性3
task.node.param.name.1742440444115046401.extendParam4.Out=扩展属性4
task.node.param.name.1742440444115046401.extendParam5.Out=扩展属性5
task.node.param.name.1630863227799764993.vehicleCode.In=机器人
task.node.param.name.1630863227799764993.markerCode.In=充电点
task.node.param.name.1630863227799764993.chargeTime.In=充电时长（分）
task.node.param.name.1630863227799764993.batteryCharge.In=充电电量
task.node.param.name.1630863227799764993.correctChargeCycle.In=校正充电周期（天）
task.node.param.name.1821375525306884097.message.In=报错信息
task.node.param.name.1742439427101184002.warehouseCode.In=库位
task.node.param.name.1742439427101184002.occupyStatus.In=占用状态
task.node.param.name.1742439427101184002.containerBarcode.In=容器条码
task.node.param.name.1742439427101184002.extendParam1.In=扩展属性1
task.node.param.name.1742439427101184002.extendParam2.In=扩展属性2
task.node.param.name.1742439427101184002.extendParam3.In=扩展属性3
task.node.param.name.1742439427101184002.extendParam4.In=扩展属性4
task.node.param.name.1742439427101184002.extendParam5.In=扩展属性5
task.node.param.name.1831609346757263362.taskTypeId.In=执行任务
task.node.param.name.1831620038075908097.taskTypeId.In=任务类型
task.node.param.name.1831620038075908097.executionMode.In=执行模式
task.node.param.name.1831620038075908097.outValue.Out=任务编号
task.node.param.name.1750822156822622210.QR_dock_id.In=工位ID
task.node.param.name.1750822156822622210.dockingType.In=类型
task.node.param.name.1852196093673111553.url.In=URL地址
task.node.param.name.1852196093673111553.request.In=自定义请求参数
task.node.param.name.1852196093673111553.response.Out=输出结果
task.node.param.name.1852196093673111553.checkParam.Out=校验参数
task.node.param.name.1738470004770951170.vehicleCode.In=机器人
task.node.param.name.1738470004770951170.vehicleCode.Out=机器人编码
task.node.param.name.1738470004770951170.batteryValue.Out=电量
task.node.param.name.1738470004770951170.vehicleTypeCode.Out=机器人类型
task.node.param.name.1738470004770951170.vehicleGroupCode.Out=机器人组
task.node.param.name.1738470004770951170.vehicleMapCode.Out=当前地图
task.node.param.name.1738470004770951170.markerCode.Out=当前点位
task.node.param.name.1738470004770951170.storageInfoList.Out=储位信息
task.node.param.name.1654731794802663426.vehicleCode.In=机器人
task.node.param.name.1654731794802663426.scriptData.In=控制脚本
task.node.param.name.1654731794802663426.HAND_VISION.Out=扫描结果
task.node.param.name.1654731794802663426.OPERATE.Out=操作数据
task.node.param.name.1654731794802663426.STATUS.Out=执行结果
task.node.param.name.1856960932295598081.ip.In=IP
task.node.param.name.1856960932295598081.port.In=端口
task.node.param.name.1856960932295598081.slaveId.In=从站ID
task.node.param.name.1856960932295598081.address.In=开始地址
task.node.param.name.1856960932295598081.value.In=写入值
task.node.param.name.1856959739322294274.ip.In=IP
task.node.param.name.1856959739322294274.port.In=端口
task.node.param.name.1856959739322294274.slaveId.In=从站ID
task.node.param.name.1856959739322294274.address.In=开始地址
task.node.param.name.1856959739322294274.length.In=读取长度
task.node.param.name.1856959739322294274.result.Out=输出值
task.node.param.name.1851551331579158530.mapAreaCode.In=区域编码
task.node.param.name.1851551331579158530.operation.In=操作
task.node.param.name.1856960932295598081.executeMode.In=执行方式
task.node.param.name.1856960932295598081.vehicleCode.In=机器人
task.node.param.name.1856959739322294274.executeMode.In=执行方式
task.node.param.name.1856959739322294274.vehicleCode.In=机器人
task.node.param.name.1856959739322294274.code.In=功能码
task.node.param.name.1856960932295598081.code.In=功能码
task.node.param.name.1856613384389165058.script.In=脚本
task.node.param.name.1856613384389165058.param1.In=参数1
task.node.param.name.1856613384389165058.param2.In=参数2
task.node.param.name.1856613384389165058.param3.In=参数3
task.node.param.name.1856613384389165058.param4.In=参数4
task.node.param.name.1856613384389165058.param5.In=参数5
task.node.param.name.1856613384389165058.param1.Out=参数1
task.node.param.name.1856613384389165058.param2.Out=参数2
task.node.param.name.1856613384389165058.param3.Out=参数3
task.node.param.name.1856613384389165058.param4.Out=参数4
task.node.param.name.1856613384389165058.param5.Out=参数5
task.node.param.name.1912414958361030657.markerCode.In=点位
task.node.param.name.1912414958361030657.vehicleCode.In=机器人
task.node.param.name.1912415217493520385.vehicleCode.In=机器人

task.node.param.notice.1718202092436357121.vehicleCode.In=机器人
task.node.param.notice.1718202092436357121.position.In=单位为毫米
task.node.param.notice.1718202092436357121.offsetHeight.In=单位为毫米
task.node.param.notice.1718202092436357121.speed.In=单位为毫米/秒
task.node.param.notice.1738448135527288834.vehicleCode.In=机器人
task.node.param.notice.1738448135527288834.markerCode.In=充电点
task.node.param.notice.1738448135527288834.chargeTime.In=大于该充电时间（分钟）可打断充电并执行作业
task.node.param.notice.1738448135527288834.batteryCharge.In=超过该电量可打断充电并执行作业
task.node.param.notice.1738443040093851650.finishType.In=结束类型
task.node.param.notice.1738443040093851650.noticeMsg.In=任务结束时，上报该提醒消息到上游系统
task.node.param.notice.1646764086215663617.vehicleCode.In=值为空时默认使用任务占用的机器人
task.node.param.notice.1646764086215663617.vehicleAngle1.In=机器人相对地图的目标角度
task.node.param.notice.1646764086215663617.vehicleAngle2.In=机器人相对地图的目标角度
task.node.param.notice.1646764086215663617.trayRotationSpeed.In=托盘旋转速度
task.node.param.notice.1646764086215663617.trayAngle1.In=托盘相对地图的目标角度
task.node.param.notice.1646764086215663617.trayAngle2.In=托盘相对地图的目标角度
task.node.param.notice.1715183824889581570.vehicleCode.In=机器人
task.node.param.notice.1715183824889581570.ladarSwitch.In=速度区域
task.node.param.notice.1715184972354686978.vehicleCode.In=机器人
task.node.param.notice.1715184972354686978.obstacleArea.In=避障范围
task.node.param.notice.1738467719873515521.vehicleCode.In=机器人
task.node.param.notice.1738467719873515521.scheduleMode.In=调度模式
task.node.param.notice.1715183168871075842.vehicleCode.In=机器人
task.node.param.notice.1715183168871075842.audioName.In=音频名称
task.node.param.notice.1715183168871075842.audioVolume.In=音量
task.node.param.notice.1715183168871075842.playCount.In=播放次数
task.node.param.notice.1630863227598438401.markerCode.In=分配点位附近的机器人，该值为空时随机分配机器人
task.node.param.notice.1630863227598438401.vehicleGroupCode.In=仅分配属于该机器人组的机器人
task.node.param.notice.1630863227598438401.vehicleTypeCode.In=仅分配属于该机器人类型的机器人
task.node.param.notice.1630863227598438401.vehicleMapCodeList.In=仅分配该地图内的机器人
task.node.param.notice.1630863227598438401.limitBattery.In=仅分配高于该电量、高于充电策略电量的机器人
task.node.param.notice.1630863227598438401.outVehicleCode.Out=机器人
task.node.param.notice.1630863227745239041.vehicleCode.In=机器人
task.node.param.notice.1630863227745239041.outVehicleCode.Out=机器人
task.node.param.notice.1630863227644575745.vehicleCode.In=机器人
task.node.param.notice.1630863227623604225.vehicleMapCodeList.In=仅分配该地图内的点位
task.node.param.notice.1630863227623604225.markerType.In=仅分配该类型的点位
task.node.param.notice.1630863227623604225.outMarkerCode.Out=选中点位
task.node.param.notice.1630863227623604225.vehicleCode.In=机器人
task.node.param.notice.1738440272671100929.taskNo.Out=该任务的唯一编码
task.node.param.notice.1738440272671100929.externalTaskNo.Out=该任务的外部任务编码
task.node.param.notice.1738440272671100929.priority.Out=该任务的优先级
task.node.param.notice.1630863227707490306.ip.In=PLC的IP地址(与本系统能够进行网络通讯的IP)
task.node.param.notice.1630863227707490306.port.In=PLC的端口地址
task.node.param.notice.1630863227707490306.code.In=功能码
task.node.param.notice.1630863227707490306.slaveId.In=从站ID
task.node.param.notice.1630863227707490306.address.In=读取的寄存器地址
task.node.param.notice.1630863227707490306.executeMode.In=执行方式
task.node.param.notice.1630863227707490306.vehicleCode.In=机器人
task.node.param.notice.1630863227707490306.outValue.Out=输出值
task.node.param.notice.1645676364679905282.vehicleCode.In=值为空时默认使用任务占用的机器人
task.node.param.notice.1645676364679905282.code.In=读线圈寄存器（01），值范围[0-1] 。读保持寄存器（03），值范围[0-30000]
task.node.param.notice.1645676364679905282.slaveId.In=从站ID
task.node.param.notice.1645676364679905282.address.In=读机器人寄存器的地址
task.node.param.notice.1645676364679905282.outValue.Out=输出值
task.node.param.notice.1630863227724267521.ip.In=PLC的IP地址(与本系统能够进行网络通讯的IP)
task.node.param.notice.1630863227724267521.port.In=PLC的端口号
task.node.param.notice.1630863227724267521.code.In=功能码
task.node.param.notice.1630863227724267521.slaveId.In=从站ID
task.node.param.notice.1630863227724267521.address.In=写入的寄存器地址
task.node.param.notice.1630863227724267521.value.In=写入寄存器的值
task.node.param.notice.1630863227724267521.executeMode.In=执行方式
task.node.param.notice.1630863227724267521.vehicleCode.In=机器人
task.node.param.notice.1645678201743114241.vehicleCode.In=值为空时使用任务占用的机器人
task.node.param.notice.1645678201743114241.code.In= 写单个线圈寄存器（05），值范围[0-1] 写单个保持寄存器（06），值范围[0-30000]
task.node.param.notice.1645678201743114241.slaveId.In=从站ID
task.node.param.notice.1645678201743114241.address.In=要写入的寄存器地址
task.node.param.notice.1645678201743114241.value.In=写入寄存器的值
task.node.param.notice.1715188504537468930.vehicleCode.In=机器人
task.node.param.notice.1715188504537468930.code.In=功能码
task.node.param.notice.1715188504537468930.slaveId.In=从站ID
task.node.param.notice.1715188504537468930.address.In=寄存器地址
task.node.param.notice.1715188504537468930.successVal.In=正常退出
task.node.param.notice.1715188504537468930.failVal.In=失败退出
task.node.param.notice.1715188504537468930.timeout.In=超时时间
task.node.param.notice.1715188504537468930.outValue.Out=输出值
task.node.param.notice.1641376178617024513.callBoxCode.In=呼叫盒的出厂编号，可使用呼叫盒配置工具查看
task.node.param.notice.1641376178617024513.buttonCode.In=呼叫盒按钮的编号
task.node.param.notice.1641376178617024513.timeout.In=超过该值后，节点自动被完成
task.node.param.notice.1641376553134817282.callBoxCode.In=呼叫盒的出厂编号，可使用呼叫盒配置工具查看
task.node.param.notice.1641376553134817282.buttonCode.In=呼叫盒按钮的编号
task.node.param.notice.1641377688272863233.ip.In=声光报警器的IP地址
task.node.param.notice.1641377688272863233.port.In=声光报警器的端口号
task.node.param.notice.1641377688272863233.type.In=报警类型
task.node.param.notice.1641377688272863233.time.In=报警器持续报警时间
task.node.param.notice.1742437277025456130.warehouseCode.In=库位
task.node.param.notice.1742437277025456130.containerBarcode.In=容器
task.node.param.notice.1742437277025456130.dispatchPolicy.In=分配策略：随机分配RANDOM、先进先出FIFO
task.node.param.notice.1742437277025456130.occupyStatus.In=库位状态
task.node.param.notice.1742437277025456130.warehouseTypeCode.In=库位类型
task.node.param.notice.1742437277025456130.warehouseAreaCode.In=库区
task.node.param.notice.1742437277025456130.extendParam1.In=扩展属性1
task.node.param.notice.1742437277025456130.extendParam2.In=扩展属性2
task.node.param.notice.1742437277025456130.extendParam3.In=扩展属性3
task.node.param.notice.1742437277025456130.extendParam4.In=扩展属性4
task.node.param.notice.1742437277025456130.extendParam5.In=扩展属性5
task.node.param.notice.1742437277025456130.warehouseCode.Out=库位编码
task.node.param.notice.1742441148997193730.containerBarCode.In=容器条码
task.node.param.notice.1742441148997193730.warehouseCode.Out=库区编码
task.node.param.notice.1742441148997193730.workMarkerCode.Out=作业点位编码
task.node.param.notice.1742441148997193730.workHeight.Out=作业高度
task.node.param.notice.1742441148997193730.warehouseTypeCode.Out=库位类型编码
task.node.param.notice.1742441148997193730.warehouseAreaCode.Out=库位区域编码
task.node.param.notice.1742440444115046401.warehouseCode.In=库位
task.node.param.notice.1742440444115046401.containerBarcode.Out=容器条码
task.node.param.notice.1742440444115046401.workMarkerCode.Out=作业点位编码
task.node.param.notice.1742440444115046401.workHeight.Out=作业高度
task.node.param.notice.1742440444115046401.warehouseTypeCode.Out=库位类型编码
task.node.param.notice.1742440444115046401.warehouseAreaCode.Out=库位区域编码
task.node.param.notice.1742441529047273474.markerCode.In=点位
task.node.param.notice.1742441529047273474.markerCode.Out=相邻点位编码
task.node.param.notice.1630863227652964354.vehicleCode.In=值为空时默认使用任务占用的机器人
task.node.param.notice.1630863227652964354.angle.In=机器人相对地图的目标角度
task.node.param.notice.1630863227652964354.rateType.In=旋转类型
task.node.param.notice.1630863227673935874.vehicleCode.In=值为空时默认使用任务占用的机器人
task.node.param.notice.1630863227673935874.angle1.In=托盘相对地图的目标角度
task.node.param.notice.1630863227673935874.angle2.In=托盘相对地图的目标角度
task.node.param.notice.1630863227673935874.speed.In=旋转速度
task.node.param.notice.1630863227673935874.rateType.In=旋转类型
task.node.param.notice.1750822156822622210.vehicleCode.In=机器人
task.node.param.notice.1750822156822622210.offsetX.In=单位为米
task.node.param.notice.1750822156822622210.offsetY.In=单位为米
task.node.param.notice.1750822156822622210.offsetAngle.In=偏差角度
task.node.param.notice.1750822156822622210.rotationAngleRange.In=值为 0 时使用底盘默认配置
task.node.param.notice.1750822156822622210.moveDistanceRange.In=值为 0 时使用底盘默认配置
task.node.param.notice.1750822156822622210.obstacleRegion.In=避障区域
task.node.param.notice.1738450017482133505.vehicleCode.In=机器人
task.node.param.notice.1738450017482133505.dockingType.In=类型
task.node.param.notice.1738450017482133505.startX.In=单位为米
task.node.param.notice.1738450017482133505.startY.In=单位为米
task.node.param.notice.1738450017482133505.startAngle.In=起点角度
task.node.param.notice.1738450017482133505.endX.In=单位为米
task.node.param.notice.1738450017482133505.endY.In=单位为米
task.node.param.notice.1738450017482133505.endAngle.In=终点角度
task.node.param.notice.1738450017482133505.offsetX.In=偏差 X
task.node.param.notice.1738450017482133505.offsetY.In=偏差 Y
task.node.param.notice.1738450017482133505.offsetAngle.In=偏差角度
task.node.param.notice.1738450017482133505.workStationCode.In=只有在二维码对接时才用
task.node.param.notice.1738450017482133505.templateNo.In=模板编号
task.node.param.notice.1738450017482133505.cameraObstacle.In=相机避障
task.node.param.notice.1738450017482133505.obstacleRegion.In=避障区域
task.node.param.notice.1751081370463637506.vehicleCode.In=机器人
task.node.param.notice.1738450017482133505.dockingDirection.In=对接方向
task.node.param.notice.1715186203621986306.vehicleCode.In=机器人
task.node.param.notice.1715186203621986306.type.In=1、相对机器人静止：托盘跟随底盘一起动 2、相对地图静止：底盘动时托盘不动
task.node.param.notice.1630863227694907394.vehicleCode.In=值为空时默认使用任务占用的机器人
task.node.param.notice.1630863227694907394.speed.In=升降速度
task.node.param.notice.1630863227694907394.targetTicks.In=升降高度
task.node.param.notice.1751878094551769090.vehicleCode.In=机器人
task.node.param.notice.1751878094551769090.outValue.Out=二维码信息
task.node.param.notice.1751825844022235138.vehicleCode.In=机器人
task.node.param.notice.1630863227787182081.markerCode.In=点位
task.node.param.notice.1630863227787182081.markerName.In=自定义编码
task.node.param.notice.1630863227787182081.code.Out=点位唯一编码
task.node.param.notice.1630863227787182081.name.Out=用户自定义的点位编码，可为空值
task.node.param.notice.1630863227787182081.type.Out=枚举值： ChargingMarker:充电点, NavigationMarker:导航点, WorkMarker:工作点
task.node.param.notice.1630863227787182081.isPark.Out=枚举值：true、false
task.node.param.notice.1630863227787182081.angle.Out=点位角度
task.node.param.notice.1630863227787182081.chargeEnable.Out=是否允许工作时充电
task.node.param.notice.1790203373283213314.oriValue.In=变量名
task.node.param.notice.1790203373283213314.newValue.In=期望的变量值
task.node.param.notice.1738444988633272322.url.In=要请求的 Url 地址
task.node.param.notice.1738444988633272322.customParams.In=自定义入参
task.node.param.notice.1738444988633272322.customValues.Out=自定义出参
task.node.param.notice.1630863227757821953.vehicleCode.In=值为空时默认使用任务占用的机器人
task.node.param.notice.1630863227757821953.markerCode.In=目标点
task.node.param.notice.1630863227757821953.dockingMove.In=关闭该项配置，对接类型路径不再生效
task.node.param.notice.1630863227757821953.accurate.In=精定位
task.node.param.notice.1630863227757821953.fallPrevent.In=防跌落
task.node.param.notice.1630863227757821953.safety3D.In=3D避障
task.node.param.notice.1630863227757821953.featureFusion.In=融合特征
task.node.param.notice.1630863227757821953.movingSpeed.In=移动速度
task.node.param.notice.1630863227757821953.rotationSpeed.In=旋转速度
task.node.param.notice.1630863227757821953.moveObstacleRegion.In=移动避障区域
task.node.param.notice.1630863227757821953.rotationObstacleRegion.In=旋转避障区域
task.node.param.notice.1630863227757821953.obstacleAvoidance.In=设置是否开启自主绕障
task.node.param.notice.1630863227757821953.agvDirection.In=车头角度
task.node.param.notice.1630863227757821953.extendParams.In=扩展参数
task.node.param.notice.1630863227577466882.time.In=等待时间（秒）
task.node.param.notice.1714957947186581506.ip.In=PLC的IP地址(与本系统能够进行网络通讯的IP)
task.node.param.notice.1714957947186581506.port.In=PLC的端口地址
task.node.param.notice.1714957947186581506.code.In=功能码
task.node.param.notice.1714957947186581506.slaveId.In=从站ID
task.node.param.notice.1714957947186581506.address.In=读取的寄存器地址
task.node.param.notice.1714957947186581506.successVal.In=正常退出
task.node.param.notice.1714957947186581506.failVal.In=失败退出
task.node.param.notice.1714957947186581506.timeout.In=超时时间
task.node.param.notice.1714957947186581506.executeMode.In=执行方式
task.node.param.notice.1714957947186581506.vehicleCode.In=机器人
task.node.param.notice.1714957947186581506.outValue.Out=输出值
task.node.param.notice.1788855369901137921.result_code.Out=1001: 未触发避障, 1002 :触发避障
task.node.param.notice.1788855369901137921.error_message.Out=返回传感器的避障区域(急停,停止 减速 未触发)
task.node.param.notice.1630863227787182081.extendParam1.Out=扩展属性1
task.node.param.notice.1630863227787182081.extendParam2.Out=扩展属性2
task.node.param.notice.1630863227787182081.extendParam3.Out=扩展属性3
task.node.param.notice.1630863227787182081.extendParam4.Out=扩展属性4
task.node.param.notice.1630863227787182081.extendParam5.Out=扩展属性5
task.node.param.notice.1742440444115046401.extendParam1.Out=扩展属性1
task.node.param.notice.1742440444115046401.extendParam2.Out=扩展属性2
task.node.param.notice.1742440444115046401.extendParam3.Out=扩展属性3
task.node.param.notice.1742440444115046401.extendParam4.Out=扩展属性4
task.node.param.notice.1742440444115046401.extendParam5.Out=扩展属性5
task.node.param.notice.1630863227799764993.vehicleCode.In=值为空时默认使用任务占用的机器人
task.node.param.notice.1630863227799764993.markerCode.In=充电点
task.node.param.notice.1630863227799764993.chargeTime.In=机器人已充电时长超过该值后才可以被中断
task.node.param.notice.1630863227799764993.batteryCharge.In=机器人当前电量超过该值时才可以被中断
task.node.param.notice.1630863227799764993.correctChargeCycle.In=机器人处于矫正充电时不可以被中断
task.node.param.notice.1821375525306884097.message.In=报错信息
task.node.param.notice.1742439427101184002.warehouseCode.In=库位
task.node.param.notice.1742439427101184002.occupyStatus.In=占用状态
task.node.param.notice.1742439427101184002.containerBarcode.In=容器条码
task.node.param.notice.1742439427101184002.extendParam1.In=扩展属性1
task.node.param.notice.1742439427101184002.extendParam2.In=扩展属性2
task.node.param.notice.1742439427101184002.extendParam3.In=扩展属性3
task.node.param.notice.1742439427101184002.extendParam4.In=扩展属性4
task.node.param.notice.1742439427101184002.extendParam5.In=扩展属性5
task.node.param.notice.1831609346757263362.taskTypeId.In=执行任务
task.node.param.notice.1831620038075908097.taskTypeId.In=任务类型
task.node.param.notice.1831620038075908097.executionMode.In=执行模式
task.node.param.notice.1831620038075908097.outValue.Out=任务编号
task.node.param.notice.1750822156822622210.QR_dock_id.In=工位id
task.node.param.notice.1750822156822622210.dockingType.In=类型
task.node.param.notice.1852196093673111553.url.In=请求的地址
task.node.param.notice.1852196093673111553.request.In=自定义请求参数
task.node.param.notice.1852196093673111553.response.Out=输出结果
task.node.param.notice.1852196093673111553.checkParam.Out=校验参数
task.node.param.notice.1738470004770951170.vehicleCode.In=机器人
task.node.param.notice.1738470004770951170.vehicleCode.Out=机器人
task.node.param.notice.1738470004770951170.batteryValue.Out=电量
task.node.param.notice.1738470004770951170.vehicleTypeCode.Out=机器人类型
task.node.param.notice.1738470004770951170.vehicleGroupCode.Out=机器人组
task.node.param.notice.1738470004770951170.vehicleMapCode.Out=当前地图
task.node.param.notice.1738470004770951170.markerCode.Out=当前点位
task.node.param.notice.1738470004770951170.storageInfoList.Out=储位信息
task.node.param.notice.1654731794802663426.vehicleCode.In=值为空时默认使用任务占用的机器人
task.node.param.notice.1654731794802663426.scriptData.In=Mos系统可执行的Json脚本
task.node.param.notice.1654731794802663426.HAND_VISION.Out=扫描结果
task.node.param.notice.1654731794802663426.OPERATE.Out=操作数据
task.node.param.notice.1654731794802663426.STATUS.Out=1:执行中 ; 2:执行异常(未回原); 3:执行完成; 4:执行异常(已回原)
task.node.param.notice.1856960932295598081.ip.In=PLC的IP地址(与本系统能够进行网络通讯的IP)
task.node.param.notice.1856960932295598081.port.In=端口
task.node.param.notice.1856960932295598081.slaveId.In=从站ID
task.node.param.notice.1856960932295598081.address.In=开始地址
task.node.param.notice.1856960932295598081.value.In=写入值
task.node.param.notice.1856959739322294274.ip.In=PLC的IP地址(与本系统能够进行网络通讯的IP)
task.node.param.notice.1856959739322294274.port.In=端口
task.node.param.notice.1856959739322294274.slaveId.In=从站ID
task.node.param.notice.1856959739322294274.address.In=开始地址
task.node.param.notice.1856959739322294274.length.In=读取长度
task.node.param.notice.1856959739322294274.result.Out=输出值
task.node.param.notice.1851551331579158530.mapAreaCode.In=区域编码
task.node.param.notice.1851551331579158530.operation.In=操作
task.node.param.notice.1856960932295598081.executeMode.In=执行方式
task.node.param.notice.1856960932295598081.vehicleCode.In=机器人
task.node.param.notice.1856959739322294274.executeMode.In=执行方式
task.node.param.notice.1856959739322294274.vehicleCode.In=机器人
task.node.param.notice.1856959739322294274.code.In=功能码
task.node.param.notice.1856960932295598081.code.In=功能码
task.node.param.notice.1856613384389165058.script.In=脚本
task.node.param.notice.1856613384389165058.param1.In=参数1
task.node.param.notice.1856613384389165058.param2.In=参数2
task.node.param.notice.1856613384389165058.param3.In=参数3
task.node.param.notice.1856613384389165058.param4.In=参数4
task.node.param.notice.1856613384389165058.param5.In=参数5
task.node.param.notice.1856613384389165058.param1.Out=参数1
task.node.param.notice.1856613384389165058.param2.Out=参数2
task.node.param.notice.1856613384389165058.param3.Out=参数3
task.node.param.notice.1856613384389165058.param4.Out=参数4
task.node.param.notice.1856613384389165058.param5.Out=参数5
task.node.param.notice.1912414958361030657.markerCode.In=点位
task.node.param.notice.1912414958361030657.vehicleCode.In=机器人
task.node.param.notice.1912415217493520385.vehicleCode.In=机器人

task.node.param.value.desc.1738443040093851650.finishType.In.Finished=完成任务
task.node.param.value.desc.1738443040093851650.finishType.In.Cancel=取消任务
task.node.param.value.desc.1715183824889581570.ladarSwitch.In.0=区域0
task.node.param.value.desc.1715183824889581570.ladarSwitch.In.1=区域1
task.node.param.value.desc.1715184972354686978.obstacleArea.In.1=避障区域 1
task.node.param.value.desc.1715184972354686978.obstacleArea.In.2=避障区域 2
task.node.param.value.desc.1715184972354686978.obstacleArea.In.3=避障区域 3
task.node.param.value.desc.1715184972354686978.obstacleArea.In.4=避障区域 4
task.node.param.value.desc.1715184972354686978.obstacleArea.In.5=避障区域 5
task.node.param.value.desc.1715184972354686978.obstacleArea.In.6=避障区域 6
task.node.param.value.desc.1715184972354686978.obstacleArea.In.7=避障区域 7
task.node.param.value.desc.1715184972354686978.obstacleArea.In.8=避障区域 8
task.node.param.value.desc.1715184972354686978.obstacleArea.In.9=避障区域 9
task.node.param.value.desc.1715184972354686978.obstacleArea.In.10=避障区域 10
task.node.param.value.desc.1715184972354686978.obstacleArea.In.11=避障区域 11
task.node.param.value.desc.1715184972354686978.obstacleArea.In.12=避障区域 12
task.node.param.value.desc.1715184972354686978.obstacleArea.In.13=避障区域 13
task.node.param.value.desc.1715184972354686978.obstacleArea.In.14=避障区域 14
task.node.param.value.desc.1715184972354686978.obstacleArea.In.15=避障区域 15
task.node.param.value.desc.1715184972354686978.obstacleArea.In.16=避障区域 16
task.node.param.value.desc.1738467719873515521.scheduleMode.In.AutoSchedule=自动调度
task.node.param.value.desc.1738467719873515521.scheduleMode.In.ManualSchedule=手动调度
task.node.param.value.desc.1630863227623604225.markerType.In.NavigationMarker=导航点
task.node.param.value.desc.1630863227623604225.markerType.In.WorkMarker=工作点
task.node.param.value.desc.1630863227623604225.markerType.In.ChargingMarker=充电点
task.node.param.value.desc.1630863227707490306.code.In.01=读线圈寄存器（01）
task.node.param.value.desc.1630863227707490306.code.In.02=读离散输入寄存器（02）
task.node.param.value.desc.1630863227707490306.code.In.03=读保持寄存器（03）
task.node.param.value.desc.1630863227707490306.code.In.04=读输入寄存器（04）
task.node.param.value.desc.1630863227707490306.executeMode.In.Server=服务器
task.node.param.value.desc.1630863227707490306.executeMode.In.Vehicle=机器人
task.node.param.value.desc.1645676364679905282.code.In.01=读线圈寄存器（01）
task.node.param.value.desc.1645676364679905282.code.In.03=读保持寄存器（03）
task.node.param.value.desc.1630863227724267521.code.In.05=写单个线圈寄存器（05）
task.node.param.value.desc.1630863227724267521.code.In.06=写单个离散输入寄存器（06）
task.node.param.value.desc.1630863227724267521.code.In.15=写多个线圈寄存器（15）
task.node.param.value.desc.1630863227724267521.code.In.16=写多个离散输入寄存器（16）
task.node.param.value.desc.1630863227724267521.executeMode.In.Server=服务器
task.node.param.value.desc.1630863227724267521.executeMode.In.Vehicle=机器人
task.node.param.value.desc.1645678201743114241.code.In.05=写单个线圈寄存器（05）
task.node.param.value.desc.1645678201743114241.code.In.06=写单个保持寄存器（06）
task.node.param.value.desc.1715188504537468930.code.In.03=读保持寄存器（03）
task.node.param.value.desc.1641377688272863233.type.In.SoundAndLight=声光
task.node.param.value.desc.1641377688272863233.type.In.Sound=声
task.node.param.value.desc.1641377688272863233.type.In.Light=光
task.node.param.value.desc.1742437277025456130.dispatchPolicy.In.FIFO=先进先出
task.node.param.value.desc.1742437277025456130.dispatchPolicy.In.RANDOM=随机分配
task.node.param.value.desc.1742437277025456130.occupyStatus.In.Store=存储
task.node.param.value.desc.1742437277025456130.occupyStatus.In.Free=空闲
task.node.param.value.desc.1630863227652964354.rateType.In.Relative=相对机器人旋转
task.node.param.value.desc.1630863227652964354.rateType.In.Absolute=相对地图旋转
task.node.param.value.desc.1630863227673935874.rateType.In.Relative=相对机器人
task.node.param.value.desc.1630863227673935874.rateType.In.Absolute=相对地图
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.1=避障区域 1
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.2=避障区域 2
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.3=避障区域 3
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.4=避障区域 4
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.5=避障区域 5
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.6=避障区域 6
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.7=避障区域 7
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.8=避障区域 8
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.9=避障区域 9
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.10=避障区域 10
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.11=避障区域 11
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.12=避障区域 12
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.13=避障区域 13
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.14=避障区域 14
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.15=避障区域 15
task.node.param.value.desc.1750822156822622210.obstacleRegion.In.16=避障区域 16
task.node.param.value.desc.1738450017482133505.dockingType.In.QR_Down=二维码对接
task.node.param.value.desc.1738450017482133505.dockingType.In.Reflector=反光板对接
task.node.param.value.desc.1738450017482133505.dockingType.In.Symbol_V=V 型板对接
task.node.param.value.desc.1738450017482133505.dockingType.In.Shelflegs=货架腿对接
task.node.param.value.desc.1738450017482133505.dockingType.In.Pallet=托盘对接
task.node.param.value.desc.1738450017482133505.dockingType.In.LeaveDocking=脱离对接
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.1=避障区域 1
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.2=避障区域 2
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.3=避障区域 3
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.4=避障区域 4
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.5=避障区域 5
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.6=避障区域 6
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.7=避障区域 7
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.8=避障区域 8
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.9=避障区域 9
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.10=避障区域 10
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.11=避障区域 11
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.12=避障区域 12
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.13=避障区域 13
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.14=避障区域 14
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.15=避障区域 15
task.node.param.value.desc.1738450017482133505.obstacleRegion.In.16=避障区域 16
task.node.param.value.desc.1738450017482133505.dockingDirection.In.Head=车头
task.node.param.value.desc.1738450017482133505.dockingDirection.In.Tail=车尾
task.node.param.value.desc.1715186203621986306.type.In.Open=相对机器人静止
task.node.param.value.desc.1715186203621986306.type.In.Close=相对地图静止
task.node.param.value.desc.1630863227757821953.accurate.In.=默认
task.node.param.value.desc.1630863227757821953.accurate.In.true=开启
task.node.param.value.desc.1630863227757821953.accurate.In.false=关闭
task.node.param.value.desc.1630863227757821953.fallPrevent.In.=默认
task.node.param.value.desc.1630863227757821953.fallPrevent.In.true=开启
task.node.param.value.desc.1630863227757821953.fallPrevent.In.false=关闭
task.node.param.value.desc.1630863227757821953.safety3D.In.=默认
task.node.param.value.desc.1630863227757821953.safety3D.In.true=开启
task.node.param.value.desc.1630863227757821953.safety3D.In.false=关闭
task.node.param.value.desc.1630863227757821953.featureFusion.In.=默认
task.node.param.value.desc.1630863227757821953.featureFusion.In.true=开启
task.node.param.value.desc.1630863227757821953.featureFusion.In.false=关闭
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.=默认
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.1=避障区域1
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.2=避障区域2
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.3=避障区域3
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.4=避障区域4
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.5=避障区域5
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.6=避障区域6
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.7=避障区域7
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.8=避障区域8
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.9=避障区域9
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.10=避障区域10
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.11=避障区域11
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.12=避障区域12
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.13=避障区域13
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.14=避障区域14
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.15=避障区域15
task.node.param.value.desc.1630863227757821953.moveObstacleRegion.In.16=避障区域16
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In	=默认
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.1=避障区域1
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.2=避障区域2
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.3=避障区域3
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.4=避障区域4
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.5=避障区域5
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.6=避障区域6
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.7=避障区域7
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.8=避障区域8
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.9=避障区域9
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.10=避障区域10
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.11=避障区域11
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.12=避障区域12
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.13=避障区域13
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.14=避障区域14
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.15=避障区域15
task.node.param.value.desc.1630863227757821953.rotationObstacleRegion.In.16=避障区域16
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.=默认
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.true=开启
task.node.param.value.desc.1630863227757821953.obstacleAvoidance.In.false=关闭
task.node.param.value.desc.1630863227757821953.agvDirection.In.0=0
task.node.param.value.desc.1630863227757821953.agvDirection.In.90=90
task.node.param.value.desc.1630863227757821953.agvDirection.In.-90=-90
task.node.param.value.desc.1630863227757821953.agvDirection.In.180=180
task.node.param.value.desc.1714957947186581506.code.In.01=读线圈寄存器（01）
task.node.param.value.desc.1714957947186581506.code.In.02=读离散输入寄存器（02）
task.node.param.value.desc.1714957947186581506.code.In.03=读保持寄存器（03）
task.node.param.value.desc.1714957947186581506.code.In.04=读输入寄存器（04）
task.node.param.value.desc.1714957947186581506.executeMode.In.Server=服务器
task.node.param.value.desc.1714957947186581506.executeMode.In.Vehicle=机器人
task.node.param.value.desc.1742439427101184002.occupyStatus.In.Free=空闲
task.node.param.value.desc.1742439427101184002.occupyStatus.In.Store=存储
task.node.param.value.desc.1831620038075908097.executionMode.In.Independent=独立执行
task.node.param.value.desc.1831620038075908097.executionMode.In.Embedded=嵌入执行
task.node.param.value.desc.1750822156822622210.dockingType.In.UP=上二维码（低速）
task.node.param.value.desc.1750822156822622210.dockingType.In.QR_Up=上二维码（高速）
task.node.param.value.desc.1750822156822622210.dockingType.In.DOWN=下二维码
task.node.param.value.desc.1750822156822622210.dockingType.In.LEFT=左二维码
task.node.param.value.desc.1750822156822622210.dockingType.In.RIGHT=右二维码
task.node.param.value.desc.1750822156822622210.dockingType.In.Laser_Side=侧面激光
task.node.param.value.desc.1750822156822622210.dockingType.In.Line_Straight=单悬臂
task.node.param.value.desc.1750822156822622210.dockingType.In.Reflector_Adjust=反光贴精调
task.node.param.value.desc.1851551331579158530.operation.In.Enable=启用
task.node.param.value.desc.1851551331579158530.operation.In.Disable=禁用
task.node.param.value.desc.1856960932295598081.executeMode.In.Server=服务器
task.node.param.value.desc.1856960932295598081.executeMode.In.Vehicle=机器人
task.node.param.value.desc.1856959739322294274.executeMode.In.Server=服务器
task.node.param.value.desc.1856959739322294274.executeMode.In.Vehicle=机器人
task.node.param.value.desc.1856959739322294274.code.In.03=读保持寄存器（03）
task.node.param.value.desc.1856960932295598081.code.In.16=写多个寄存器（16）