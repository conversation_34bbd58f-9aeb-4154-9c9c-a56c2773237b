package com.youibot.vehicle.scheduler.modules.map.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.youibot.vehicle.scheduler.common.algorithm.MathematicalGraphicsAlgorithm;
import com.youibot.vehicle.scheduler.common.constant.RegexConstant;
import com.youibot.vehicle.scheduler.common.dao.BaseDao;
import com.youibot.vehicle.scheduler.common.entity.Point;
import com.youibot.vehicle.scheduler.common.entity.QueryCol;
import com.youibot.vehicle.scheduler.common.exception.FleetException;
import com.youibot.vehicle.scheduler.common.page.PageData;
import com.youibot.vehicle.scheduler.common.thread.ThreadUtils;
import com.youibot.vehicle.scheduler.common.utils.BeanUtils;
import com.youibot.vehicle.scheduler.common.utils.ConvertUtils;
import com.youibot.vehicle.scheduler.common.utils.MapCompressUtil;
import com.youibot.vehicle.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.entity.ThirdSystemTrafficAreaResource;
import com.youibot.vehicle.scheduler.engine.pathplan.resource.pool.ThirdSystemTrafficAreaResourcePool;
import com.youibot.vehicle.scheduler.engine.pathplan.util.ConversionUtils;
import com.youibot.vehicle.scheduler.modules.device.utils.AutoDoorUtils;
import com.youibot.vehicle.scheduler.modules.language.util.I18nMessageUtils;
import com.youibot.vehicle.scheduler.modules.map.cache.MapGraphInfo;
import com.youibot.vehicle.scheduler.modules.map.constant.MapConstant;
import com.youibot.vehicle.scheduler.modules.map.constant.MapFileSuffixConstant;
import com.youibot.vehicle.scheduler.modules.map.dao.*;
import com.youibot.vehicle.scheduler.modules.map.dto.*;
import com.youibot.vehicle.scheduler.modules.map.entity.*;
import com.youibot.vehicle.scheduler.modules.map.pool.PublishMapHandleMatrixQueue;
import com.youibot.vehicle.scheduler.modules.map.pool.PublishMapHandleResourceQueue;
import com.youibot.vehicle.scheduler.modules.map.service.*;
import com.youibot.vehicle.scheduler.modules.map.utils.*;
import com.youibot.vehicle.scheduler.modules.map.webSocket.controller.MapUpdateSocketController;
import com.youibot.vehicle.scheduler.modules.map.webSocket.module.SocketMessageModel;
import com.youibot.vehicle.scheduler.modules.monitor.dto.ElementsQueryDTO;
import com.youibot.vehicle.scheduler.modules.security.user.SecurityUser;
import com.youibot.vehicle.scheduler.modules.sys.entity.SystemConfigEntity;
import com.youibot.vehicle.scheduler.modules.sys.service.SystemPropertyService;
import com.youibot.vehicle.scheduler.modules.task.service.TaskService;
import com.youibot.vehicle.scheduler.modules.vehicle.Vehicle;
import com.youibot.vehicle.scheduler.modules.vehicle.dto.VehicleDetailDTO;
import com.youibot.vehicle.scheduler.modules.vehicle.dto.VehicleTypeDTO;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.VehiclePoolService;
import com.youibot.vehicle.scheduler.modules.vehicle.pool.impl.DefaultVehiclePool;
import com.youibot.vehicle.scheduler.modules.vehicle.service.VehicleTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.youibot.vehicle.scheduler.modules.map.constant.MapConstant.*;
import static com.youibot.vehicle.scheduler.modules.map.utils.MapFileUtils.*;
import static com.youibot.vehicle.scheduler.modules.vehicle.constant.VehicleConstant.CONNECT_STATUS;
import static com.youibot.vehicle.scheduler.modules.vehicle.constant.VehicleConstant.SOFTSTOP_STATUS_CLOSE;

@Slf4j
@Service
public class VehicleMapServiceImpl implements VehicleMapService,MapElementService {
    private static final Logger LOGGER = LoggerFactory.getLogger(VehicleMapServiceImpl.class);

    @Autowired
    public PathService pathService;
    @Autowired
    public MarkerService markerService;
    @Autowired
    private MapAreaService mapAreaService;
    @Autowired
    private AutoDoorService autoDoorService;
    @Autowired
    private AirShowerDoorService airShowerDoorService;
    @Autowired
    private ElevatorService elevatorService;
    @Autowired
    private ElevatorToMapDao elevatorToMapDao;
    @Autowired
    public DefaultVehiclePool defaultVehiclePool;
    @Autowired
    private PublishMapHandleResourceQueue publishMapHandleResourceQueue;
    @Autowired
    private PublishMapHandleMatrixQueue publishMapHandleMatrixQueue;
    @Autowired
    private SystemPropertyService systemPropertyService;
    @Autowired
    private VehicleMapDao vehicleMapDao;
    @Autowired
    private MapInfoDao mapInfoDao;
    @Autowired
    private MapInfoDraftDao mapInfoDraftDao;
    @Autowired
    private MarkerDraftDao markerDraftDao;
    @Autowired
    private PathDraftDao pathDraftDao;
    @Autowired
    private MapAreaDraftDao mapAreaDraftDao;
    @Autowired
    private MapAreaDao mapAreaDao;
    @Autowired
    private AutoDoorDraftDao autoDoorDraftDao;
    @Autowired
    private MarkerDao markerDao;
    @Autowired
    private PathDao pathDao;
    @Autowired
    private AutoDoorDao autoDoorDao;
    @Autowired
    private AirShowerDoorDraftDao airShowerDoorDraftDao;
    @Autowired
    private AirShowerDoorDao airShowerDoorDao;
    @Autowired
    private DbUtils dbUtils;
    @Autowired
    private VehicleTypeService vehicleTypeService;
    @Autowired
    private UndoManageService undoManageService;
    @Autowired
    private ThreadPoolTaskExecutor asyncExecutor;
    @Autowired
    private VehiclePoolService vehiclePoolService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private ThirdSystemTrafficAreaResourcePool thirdSystemTrafficAreaResourcePool;

    @Override
    public PageData<VehicleMap> page(Map<String, Object> searchMap) {
        QueryCol build = QueryCol.builder().eqCol("creator,isProd").likeCol("code,name,creatorName").timeCol("createTime,publishTime").build();
        QueryWrapper<VehicleMap> queryWrapper = getWrapper(searchMap, build);
        IPage<VehicleMap> page = vehicleMapDao.selectPage(getPage(searchMap, "create_time", false), queryWrapper);
        PageData<VehicleMap> pageData = getPageData(page, VehicleMap.class);
        return pageData;
    }

    @Override
    public List<VehicleMap> getAllVehicleMap(List<String> vehicleMapCodes) {
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("create_time");
        List<VehicleMap> vehicleMaps = vehicleMapDao.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(vehicleMaps)){
            return new ArrayList<>();
        }
        if (CollUtil.isNotEmpty(vehicleMapCodes)) {
            vehicleMaps = vehicleMaps.stream().filter(m -> vehicleMapCodes.contains(m.getCode())).collect(Collectors.toList());
        }
        return vehicleMaps;
    }

    @Override
    public List<VehicleMapDetailDTO> findAllWithMapInfo(boolean isDraft) {
        List<VehicleMap> vehicleMaps = getAllVehicleMap(new ArrayList<>());
        if(CollectionUtils.isEmpty(vehicleMaps)){
            return new ArrayList<>();
        }

        Map<String, List<MapInfoDTO>> mapInfoMap;
        if(isDraft){
            List<MapInfoDraft> collect = mapInfoDraftDao.selectList(new QueryWrapper<>());
            List<MapInfoDTO> list = ConvertUtils.sourceToTarget(collect, MapInfoDTO.class);
            mapInfoMap = list.stream().collect(Collectors.groupingBy(MapInfoDTO::getVehicleMapCode));
        }else {
            List<MapInfo> collect = mapInfoDao.selectList(new QueryWrapper<>());
            List<MapInfoDTO> list = ConvertUtils.sourceToTarget(collect, MapInfoDTO.class);
            mapInfoMap = list.stream().collect(Collectors.groupingBy(MapInfoDTO::getVehicleMapCode));
        }

        //按创建时间排序
        for(Map.Entry<String, List<MapInfoDTO>> entry : mapInfoMap.entrySet()){
            List<MapInfoDTO> value = entry.getValue();
            if(!CollectionUtils.isEmpty(value)){
                value.sort(Comparator.comparing(MapInfoDTO::getCreateDate,Comparator.nullsLast(Comparator.naturalOrder())));
            }
        }

        List<VehicleMapDetailDTO> dtoList = ConvertUtils.sourceToTarget(vehicleMaps, VehicleMapDetailDTO.class);
        dtoList.forEach(dto->{
            String mapCode = dto.getCode();
            List<MapInfoDTO> mapInfoList = mapInfoMap.get(mapCode);
            String defaultLocatingCode = null;
            if(!CollectionUtils.isEmpty(mapInfoList)){
                dto.setMapInfoList(mapInfoList);
                defaultLocatingCode = mapInfoList.stream().filter(MapInfoDTO::isDefault).findFirst().map(MapInfoDTO::getLocatingCode).orElse(null);
            }
            dto.setDefaultLocatingCode(defaultLocatingCode);
        });
        return dtoList;
    }

    @Override
    public List<VehicleMapDetailDTO> searchAll(Map<String, String> searchMap) throws Exception {
        boolean isDraft = Boolean.parseBoolean(searchMap.remove("isDraft"));
        List<VehicleMapDetailDTO> vehicleMapDTOS = findAllWithMapInfo(isDraft);
        if (CollectionUtils.isEmpty(searchMap)) {
            return vehicleMapDTOS;
        }
        Set<VehicleMapDetailDTO> result = new HashSet<>();
        for (String attribute : searchMap.keySet()) {
            for (VehicleMapDetailDTO vehicleMapDTO : vehicleMapDTOS) {
                if (MapFileUtils.getGetMethod(vehicleMapDTO, attribute, searchMap.get(attribute))) {
                    result.add(vehicleMapDTO);
                }
            }
        }
        return new ArrayList<>(result);
    }

    @Override
    public VehicleMapDetailDTO selectByCode(String mapCode) {
        return selectByCode(mapCode, false);
    }

    @Override
    public VehicleMapDetailDTO selectByCode(String mapCode, Boolean isDraft) {
        VehicleMap vehicleMap = vehicleMapDao.selectById(mapCode);
        if (Objects.isNull(vehicleMap)) {
            return null;
        }
        VehicleMapDetailDTO vehicleMapDTO = new VehicleMapDetailDTO();
        BeanUtils.copyPropertiesIgnoreNull(vehicleMap, vehicleMapDTO);
        List<MapInfoDTO> mapInfoList = getMapInfoList(mapCode, isDraft);
        vehicleMapDTO.setMapInfoList(mapInfoList);
        String defaultLocatingCode = null;
        if(!CollectionUtils.isEmpty(mapInfoList)){
            vehicleMapDTO.setMapInfoList(mapInfoList);
            defaultLocatingCode = mapInfoList.stream().filter(MapInfoDTO::isDefault).findFirst().map(MapInfoDTO::getLocatingCode).orElse(null);
        }
        vehicleMapDTO.setDefaultLocatingCode(defaultLocatingCode);
        return vehicleMapDTO;
    }

    @Override
    public List<MapInfoDTO> getMapInfoList(String mapCode, boolean isDraft) {
        List<MapInfoDTO> dtoList;
        if (isDraft) {
            List<MapInfoDraft> draftList = getListByVehicleMapCode(mapCode,mapInfoDraftDao);
            dtoList = ConvertUtils.sourceToTarget(draftList, MapInfoDTO.class);
        } else {
            List<MapInfo> mapInfoList = getListByVehicleMapCode(mapCode,mapInfoDao);
            dtoList = ConvertUtils.sourceToTarget(mapInfoList, MapInfoDTO.class);
        }
        //填充机器人类型名称
        fillVehicleTypeNames(dtoList);
        //按创建时间排序
        dtoList.sort(Comparator.comparing(MapInfoDTO::getCreateDate,Comparator.nullsLast(Comparator.naturalOrder())));
        return dtoList;
    }

    private void fillVehicleTypeNames(List<MapInfoDTO> dtoList){
        List<VehicleTypeDTO> typeDTOList = vehicleTypeService.findAll();
        if(CollectionUtils.isEmpty(dtoList) || CollectionUtils.isEmpty(typeDTOList)){
            return;
        }
        Map<String,String> nameMap = typeDTOList.stream().collect(Collectors.toMap(VehicleTypeDTO::getCode, VehicleTypeDTO::getName,(k1,k2)->k1));
        dtoList.forEach(info->{
            if(StringUtils.isEmpty(info.getVehicleTypeCodes())){
                return;
            }
            String[] split = info.getVehicleTypeCodes().split(",");
            List<String> typeNamesList = Stream.of(split).map(nameMap::get).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(typeNamesList)){
                info.setVehicleTypeNames(String.join(",",typeNamesList));
            }
        });
    }

    private static final Map<String, ReentrantLock> CREATE_LOCKS = new ConcurrentHashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VehicleMapDTO insert(VehicleMapDTO vehicleMapDTO) {
        String mapCode = vehicleMapDTO.getCode();
        //校验地图名称是否合法
        boolean mapCodeValid = MapFileUtils.checkMapCodeValid(mapCode);
        if (!mapCodeValid) {
            String message = I18nMessageUtils.getMessage("system.code.format.error", mapCode);
            throw new FleetException(message);
        }
        if (Objects.nonNull(selectByCode(mapCode))) {
            String message = I18nMessageUtils.getMessage("system.code.duplicate.error", mapCode);
            throw new FleetException(message);
        }

        CREATE_LOCKS.putIfAbsent(vehicleMapDTO.getCode(), new ReentrantLock());
        if (!CREATE_LOCKS.get(vehicleMapDTO.getCode()).tryLock()) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            if (vehicleMapDTO.getOriginYaw() == null) {
                vehicleMapDTO.setOriginYaw(0.0);
            }
            if (vehicleMapDTO.getAngle() == null) {
                vehicleMapDTO.setAngle(0.0);
            }
            // 生成对应物理像素的白色底图
            String base64 = MapCompressUtil.generatePic(vehicleMapDTO.getHeight(), vehicleMapDTO.getWidth());
            String mapRootPath = LOCATING_DRAFT_PATH + mapCode + File.separator;
            //默认空白图的编码格式调整为：Default-White-Map-mapCode.png
            String defaultLocatingCode = MapConstant.DEFAULT_LOCATING_MAP_CODE + "-" + mapCode;
            String imageName = defaultLocatingCode + ".png";
            MapFileUtils.base64ToImage(base64, mapRootPath, imageName);
            vehicleMapDTO.setImage(imageName);
            vehicleMapDTO.setCreator(SecurityUser.getUser().getId());
            vehicleMapDTO.setCreatorName(SecurityUser.getUser().getRealName());
            vehicleMapDTO.setCreateDate(new Date());

            VehicleMap vehicleMap = VehicleMap.builder().createTime(new Date()).isDraft(0).isProd(0).build();
            BeanUtils.copyPropertiesIgnoreNull(vehicleMapDTO, vehicleMap);
            vehicleMapDao.insert(vehicleMap);
            MapInfoDraft mapInfoDraft = new MapInfoDraft();
            BeanUtils.copyPropertiesIgnoreNull(vehicleMapDTO, mapInfoDraft);
            mapInfoDraft.setLocatingCode(defaultLocatingCode);
            mapInfoDraft.setIsDefault(MapConstant.LOCATING_IS_DEFAULT);
            mapInfoDraft.setVehicleMapCode(mapCode);
            mapInfoDraftDao.insert(mapInfoDraft);

            //新增一个默认info文件
            String infoFilePath = LOCATING_DRAFT_PATH + mapCode + File.separator + defaultLocatingCode + ".info";
            FormatJsonUtils.writeFile(JSONObject.toJSONString(mapInfoDraft), infoFilePath);

            // 如果没有zip,则生成
            generateZip(mapRootPath);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("add map failed ： {}", e);
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.add.error"));
        } finally {
            CREATE_LOCKS.get(vehicleMapDTO.getCode()).unlock();
            CREATE_LOCKS.remove(vehicleMapDTO.getCode());
        }
        return vehicleMapDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VehicleMapDTO update(VehicleMapDTO vehicleMapDTO) {
        String vehicleMapCode = vehicleMapDTO.getCode();
        if (StringUtils.isEmpty(vehicleMapCode)) {
            String message = I18nMessageUtils.getMessage("system.code.is.empty.error", "code");
            throw new FleetException(message);
        }
        VehicleMapDetailDTO vehicleMapDetailDTO = selectByCode(vehicleMapCode);
        if (Objects.isNull(vehicleMapDetailDTO)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.not.exist.error", vehicleMapCode);
            throw new FleetException(message);
        }

        boolean applyLockFlag = MapPublishUtil.applyLockForMap(vehicleMapCode);
        if (!applyLockFlag) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            VehicleMap vehicleMap = vehicleMapDao.selectById(vehicleMapCode);
            vehicleMap.setEditTime(new Date());
            vehicleMap.setIsDraft(1);
            vehicleMap.setName(vehicleMapDTO.getName());
            vehicleMapDao.updateById(vehicleMap);

            vehicleMapDetailDTO.setName(vehicleMapDTO.getName());
            vehicleMapDetailDTO.setEditTime(vehicleMap.getEditTime());
            vehicleMapDetailDTO.setIsDraft(1);
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfUpdate(vehicleMapCode, "Map", Arrays.asList(vehicleMapDetailDTO));
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("update map failed ： {}", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.map.update.error", vehicleMapCode);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(vehicleMapCode);
        }
        return vehicleMapDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String mapCode) {
        boolean applyLockFlag = MapPublishUtil.applyLockForMap(mapCode);
        if (!applyLockFlag) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            VehicleMapDetailDTO vehicleMapDTO = selectByCode(mapCode);
            if (Objects.isNull(vehicleMapDTO)) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.not.exist.error", mapCode);
                throw new FleetException(message);
            }

            FileUtils.deleteFile(MapFileUtils.LOCATING_PUBLISH_PATH + mapCode + "/");
            FileUtils.deleteFile(MapFileUtils.LOCATING_DRAFT_PATH + mapCode + "/");
            FileUtils.deleteFile(MapFileUtils.LOCATING_CURRENT_PATH + mapCode + "/");

            vehicleMapDao.deleteById(mapCode);
            mapInfoDao.delete(new QueryWrapper<MapInfo>().eq("vehicle_map_code", mapCode));
            mapInfoDraftDao.delete(new QueryWrapper<MapInfoDraft>().eq("vehicle_map_code", mapCode));

            markerService.deleteByVehicleMapCode(mapCode);
            mapAreaService.deleteByVehicleMapCode(mapCode);
            pathService.deleteByVehicleMapCode(mapCode);
            autoDoorService.deleteByVehicleMapCode(mapCode);
            airShowerDoorService.deleteByVehicleMapCode(mapCode);
            elevatorToMapDao.delete(new QueryWrapper<ElevatorToMap>().eq("vehicle_map_code", mapCode));
            MapGraphUtil.removeAGVMap(mapCode);
            MapFileUtils.removeCounter(mapCode);

            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfDelete(mapCode, "Map", Collections.EMPTY_LIST);
            publishMapHandleMatrixQueue.add(PublishMap.builder().code(mapCode).build());
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("删除地图异常：", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.map.delete.error", mapCode);
            throw new FleetException(message);
        } finally {
            //释放地图同步磁盘锁
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    @Override
    public boolean existDraft(String mapCode) {
        return Optional.ofNullable(vehicleMapDao.selectById(mapCode)).map(m -> BooleanUtils.toBoolean(m.getIsDraft())).orElse(false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDraftFile(String mapCode) {
        boolean applyLockFlag = MapPublishUtil.applyLockForMap(mapCode);
        if (!applyLockFlag) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            List<MapInfo> mapInfoList = getListByVehicleMapCode(mapCode,mapInfoDao);
            if (CollectionUtils.isEmpty(mapInfoList)) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.is.not.publish.error", mapCode);
                throw new FleetException(message);
            }

            VehicleMap vehicleMap = vehicleMapDao.selectById(mapCode);
            vehicleMap.setIsDraft(0);
            vehicleMap.setEditTime(new Date());
            vehicleMapDao.updateById(vehicleMap);
            mapInfoDraftDao.delete(new QueryWrapper<MapInfoDraft>().eq("vehicle_map_code", mapCode));

            markerService.deleteDraftByVehicleMapCode(mapCode);
            pathService.deleteDraftByVehicleMapCode(mapCode);
            mapAreaService.deleteDraftByVehicleMapCode(mapCode);
            autoDoorService.deleteDraftByVehicleMapCode(mapCode);
            airShowerDoorService.deleteDraftByVehicleMapCode(mapCode);

            MapFileUtils.removeCounter(mapCode);
            undoManageService.clear(mapCode);
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfDiscard(mapCode, "Map", Collections.EMPTY_LIST);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("delete draft file failed:", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.map.draft.delete.error", mapCode);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    @Override
    public void batchGenerate(MarkerAndPathBatchGenerateDTO generateDTO) {
        SystemConfigEntity systemConfig = systemPropertyService.getSystemConfig();
        if (BooleanUtils.toBoolean(systemConfig.getMarkerSpacingCheck())) {
            if (Math.abs(generateDTO.getRowSpacing()) < systemConfig.getMarkerSpacing() || Math.abs(generateDTO.getColumnSpacing()) < systemConfig.getMarkerSpacing()) {
                throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.marker.spacing.error", systemConfig.getMarkerSpacing()));
            }
        }

        String vehicleMapCode = generateDTO.getVehicleMapCode();
        if (!MapPublishUtil.applyLockForMap(vehicleMapCode)) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            List<Marker> existMarkers = markerService.selectByVehicleMapCode(generateDTO.getVehicleMapCode(), true);
            //所有生成点进行点位间距判断
            for (int i = 0; i < generateDTO.getRows(); i++) {
                for (int j = 0; j < generateDTO.getColumns(); j++) {
                    MarkerDTO marker = new MarkerDTO();
                    marker.setX(generateDTO.getX() + Double.valueOf(generateDTO.getColumnSpacing()) / 1000 * j);
                    marker.setY(generateDTO.getY() + Double.valueOf(generateDTO.getRowSpacing()) / 1000 * i);
                    marker.setLocatingCode(generateDTO.getLocatingCode());
                    markerService.checkMarkerSpacing(marker, existMarkers);
                }
            }

            List<MapInfoDTO> mapInfoDTOList = this.getMapInfoList(vehicleMapCode, true);
            Map<String, Marker> allMarkerMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(existMarkers)) {
                existMarkers.forEach(it -> allMarkerMap.put(it.getCode(), it));
            }
            List<List<Marker>> markerList = new ArrayList<>(generateDTO.getRows());
            //生成点
            for (int i = 0; i < generateDTO.getRows(); i++) {
                List<Marker> markers = new ArrayList<>(generateDTO.getColumns());
                for (int j = 0; j < generateDTO.getColumns(); j++) {
                    double x = generateDTO.getX() + Double.valueOf(generateDTO.getColumnSpacing()) / 1000 * j;
                    double y = generateDTO.getY() + Double.valueOf(generateDTO.getRowSpacing()) / 1000 * i;
                    Marker marker = buildMarker(generateDTO, allMarkerMap, x, y, mapInfoDTOList);
                    markers.add(marker);
                    allMarkerMap.put(marker.getCode(), marker);
                }
                markerList.add(markers);
            }

            //生成边
            List<Path> paths = new ArrayList<>();
            for (int i = 0; i < markerList.size(); i++) {
                for (int j = 0; j < markerList.get(i).size(); j++) {
                    Marker now = markerList.get(i).get(j);
                    //和右边的点位连线
                    if (markerList.get(i).size() > (j + 1)) {
                        Marker right = markerList.get(i).get(j + 1);

                        Path p = buildPath(generateDTO, now, right);
                        paths.add(p);
                        //反向路径
                        p = buildPath(generateDTO, right, now);
                        paths.add(p);
                    }
                    //和下边的点位连线
                    if (markerList.size() > (i + 1)) {
                        Marker down = markerList.get(i + 1).get(j);

                        Path p = buildPath(generateDTO, now, down);
                        paths.add(p);
                        //反向路径
                        p = buildPath(generateDTO, down, now);
                        paths.add(p);
                    }
                }
            }

            List<Marker> markers = markerList.stream().flatMap(Collection::stream).collect(Collectors.toList());
            List<MarkerDraft> markerDrafts = ConvertUtils.sourceToTarget(markers, MarkerDraft.class);
            dbUtils.insertBatchById(MarkerDraft.class, markerDrafts, 500);
            List<PathDraft> pathDrafts = ConvertUtils.sourceToTarget(paths, PathDraft.class);
            dbUtils.insertBatchById(PathDraft.class, pathDrafts, 500);

            //直接返回前端，服务端异步发送消息
            sendWsMessageAsync(generateDTO.getVehicleMapCode(), markers, paths,null);
            undoManageService.pushToUndoPool(vehicleMapCode,MapConstant.CREATE_ELEMENTS,null,null,null,markerDrafts,pathDrafts,null);
        }catch (Exception e){
            MapPublishUtil.releaseMap(vehicleMapCode);
            throw e;
        }
    }

    private void sendWsMessageAsync(String vehicleMapCode, List<Marker> markers,List<Path> paths,List<MapArea> mapAreas) {
        Runnable runnable = () -> {
            try {
                //发送消息到打开当前地图的窗口
                if (!CollectionUtils.isEmpty(markers)) {
                    List<List<Marker>> markerPartition = Lists.partition(markers, 1000);
                    markerPartition.forEach(it->{
                        SocketMessageModel messageModel1 = new SocketMessageModel().add();
                        messageModel1.setMarkers(it);
                        MapUpdateSocketController.sendMessage(vehicleMapCode, messageModel1);
                    });
                }
                if (!CollectionUtils.isEmpty(paths)) {
                    List<List<Path>> pathPartition = Lists.partition(paths, 2000);
                    pathPartition.forEach(it->{
                        SocketMessageModel messageModel2 = new SocketMessageModel().add();
                        messageModel2.setPaths(it);
                        MapUpdateSocketController.sendMessage(vehicleMapCode, messageModel2);
                    });
                }
                if (!CollectionUtils.isEmpty(mapAreas)) {
                    SocketMessageModel messageModel3 = new SocketMessageModel().add();
                    messageModel3.setMapAreas(mapAreas);
                    MapUpdateSocketController.sendMessage(vehicleMapCode, messageModel3);
                }
            } catch (FleetException e) {
                LOGGER.error("操作异常", e);
                throw e;
            } catch (Exception e) {
                LOGGER.error("batchGenerate failed:{}", e);
                throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.marker.add.error"));
            } finally {
                MapPublishUtil.releaseMap(vehicleMapCode);
            }
        };
        ThreadUtils.newThread("map-edit-ws-send-thread", runnable).start();
    }

    private Marker buildMarker(MarkerAndPathBatchGenerateDTO generateDTO,Map<String, Marker> allMarkerMap,double x,double y, List<MapInfoDTO> mapInfoDTOList) {
        Marker marker = new Marker();
        String vehicleMapCode = generateDTO.getVehicleMapCode();
        marker.setAngle(0.0);
        marker.setType(MARKER_TYPE_NAVIGATION);
        //默认不是通道点
        marker.setNetworkMarkerType(MapConstant.MARKER_COMMON_TYPE);
        marker.setIsPark(0);
        marker.setVehicleMapCode(vehicleMapCode);
        marker.setParams(new MarkerParam());
        marker.setChargeProp(new MarkerChargeProp());

        marker.setMarkInfos(new ArrayList<>());
        if(!CollectionUtils.isEmpty(mapInfoDTOList)){
            mapInfoDTOList.forEach(it->{
                MarkerInfo markerInfo = MarkerInfo.builder().x(x).y(y).locatingCode(it.getLocatingCode()).build();
                marker.getMarkInfos().add(markerInfo);
            });
        }
        //旋转点位
        rotationMarker(marker,generateDTO);

        List<Marker> markers = new ArrayList<>(allMarkerMap.values());
        //计算code
        AtomicLong markerCounter = MapFileUtils.getCounterOfMap(vehicleMapCode, markers);
        String newCodeNum = String.valueOf(markerCounter.incrementAndGet());
        marker.setCode(CodeFormatUtils.getFormatCode(vehicleMapCode, newCodeNum, CodeFormatUtils.MARKER_CODE_PREFIX));

        return marker;
    }

    private Path buildPath(MarkerAndPathBatchGenerateDTO generateDTO,Marker start,Marker end) {
        Path p = new Path();
        p.setParams(new PathParam());
        p.setExtendParamList(new ArrayList<>());
        p.setVehicleMapCode(generateDTO.getVehicleMapCode());
        p.setStartMarkerCode(start.getCode());
        p.setEndMarkerCode(end.getCode());
        p.setAgvDirection(0);
        p.setPathType("Common");
        if (p.getWeightRatio() == null || p.getWeightRatio() < 0) {
            p.setWeightRatio(1.0);
        }

        p.setPathInfos(new ArrayList<>());
        List<MarkerInfo> markInfos = start.getMarkInfos();
        List<String> locatingCodes = markInfos.stream().map(MarkerInfo::getLocatingCode).collect(Collectors.toList());
        for (String locatingCode : locatingCodes) {
            MarkerInfo startMarkerInfo = start.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
            MarkerInfo endMarkerInfo = end.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);

            PathInfo pathInfo = new PathInfo();
            pathInfo.setLocatingCode(locatingCode);
            pathInfo.setStartControl(PathUtils.getControl(startMarkerInfo.getX(), startMarkerInfo.getY(), endMarkerInfo.getX(), endMarkerInfo.getY(), null));
            pathInfo.setEndControl(PathUtils.getControl(endMarkerInfo.getX(), endMarkerInfo.getY(), startMarkerInfo.getX(), startMarkerInfo.getY(), null));

            pathInfo.setLength(PathUtils.getLength(pathInfo, startMarkerInfo, endMarkerInfo));
            Double[] inOutAngle = PathUtils.getInOutAngle2(pathInfo, startMarkerInfo, endMarkerInfo);
            pathInfo.setInRadian(inOutAngle[0]);
            pathInfo.setOutRadian(inOutAngle[1]);
            p.getPathInfos().add(pathInfo);
        }

        //形式类似： mapCode_L_1_2
        String newCodeNum = CodeFormatUtils.getIntegerCode(p.getStartMarkerCode()) + CodeFormatUtils.SPLIT_CHAR + CodeFormatUtils.getIntegerCode(p.getEndMarkerCode());
        p.setCode(CodeFormatUtils.getFormatCode(generateDTO.getVehicleMapCode(), newCodeNum, CodeFormatUtils.PATH_CODE_PREFIX));
        return p;
    }

    private void rotationMarker(Marker marker,MarkerAndPathBatchGenerateDTO generateDTO){
        //0不旋转
        double angle = generateDTO.getAngle() % 360;
        if(BigDecimal.ZERO.compareTo(new BigDecimal(angle)) == 0){
            return;
        }

        //绕着generateDTO的点旋转
        double radians = Math.toRadians(angle);
        double cos = Math.cos(radians);
        double sin = Math.sin(radians);
        if(!CollectionUtils.isEmpty(marker.getMarkInfos())){
            marker.getMarkInfos().forEach(it->{
                double newX = (it.getX() - generateDTO.getX()) * cos - (it.getY() - generateDTO.getY()) * sin + generateDTO.getX();
                double newY = (it.getX() - generateDTO.getX()) * sin + (it.getY() - generateDTO.getY()) * cos + generateDTO.getY();
                it.setX(newX);
                it.setY(newY);
            });
        }
    }

    @Override
    public void batchUpdate(BatchUpdateElementData elementData, String mapCode, String locatingCode) {
        List<MarkerDTO> markers = elementData.getMarkers();
        List<BatchUpdatePathDTO> paths = elementData.getPaths();
        List<MapArea> mapAreas = elementData.getMapAreas();
        //都为空就退出
        if (CollectionUtils.isEmpty(markers) && CollectionUtils.isEmpty(paths) && CollectionUtils.isEmpty(mapAreas)) {
            return;
        }
        //获取元素更新前的基本数据，用于存储做回退使用
        List<MarkerDraft> oriMarkers = null;
        if (!CollectionUtils.isEmpty(markers)) {
            oriMarkers = markerDraftDao.selectBatchIds(markers.stream().map(MarkerDTO::getCode).collect(Collectors.toList()));
        }
        List<PathDraft> oriPaths = null;
        if (!CollectionUtils.isEmpty(paths)) {
            oriPaths = pathDraftDao.selectBatchIds(paths.stream().map(BatchUpdatePathDTO::getCode).collect(Collectors.toList()));
        }
        List<MapAreaDraft> oriMapAreas = null;
        if (!CollectionUtils.isEmpty(mapAreas)) {
            oriMapAreas = mapAreaDraftDao.selectBatchIds(mapAreas.stream().map(MapArea::getCode).collect(Collectors.toList()));
        }
        boolean applyLockFlag = MapPublishUtil.applyLockForMap(mapCode);
        if (!applyLockFlag) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            //间距判断
            markerService.checkMarkerSpacingByUpdate(mapCode, locatingCode, markers);

            Map<String, Marker> markerOfDbMap = markerService.selectByVehicleMapCode(mapCode, true).stream().collect(Collectors.toMap(Marker::getCode, Function.identity()));
            List<MarkerDraft> markerDraftList = resvoleMarkers(elementData, locatingCode, markerOfDbMap);
            List<PathDraft> pathDraftList = resvolePaths(elementData, mapCode, locatingCode, markerOfDbMap);

            if (!CollectionUtils.isEmpty(markerDraftList)) {
                dbUtils.updateBatchById(MarkerDraft.class, markerDraftList, 50);
            }
            if (!CollectionUtils.isEmpty(pathDraftList)) {
                dbUtils.updateBatchById(PathDraft.class, pathDraftList, 50);
            }
            if (!CollectionUtils.isEmpty(mapAreas)) {
                List<MapAreaDraft> mapAreaDrafts = ConvertUtils.sourceToTarget(mapAreas, MapAreaDraft.class);
                dbUtils.updateBatchById(MapAreaDraft.class, mapAreaDrafts, 50);
            }

            //发送消息到打开当前地图的窗口
            SocketMessageModel messageModel = new SocketMessageModel().update();
            List<Marker> markerDTOS = ConvertUtils.sourceToTarget(markerDraftList, Marker.class);
            messageModel.setMarkers(markerDTOS);
            List<Path> pathDTOS = ConvertUtils.sourceToTarget(pathDraftList, Path.class);
            messageModel.setPaths(pathDTOS);
            messageModel.setMapAreas(mapAreas);
            MapUpdateSocketController.sendMessage(mapCode, messageModel);
            undoManageService.pushToUndoPool(mapCode,MapConstant.UPDATE_ELEMENTS,oriMarkers,oriPaths,oriMapAreas,markerDraftList,pathDraftList,ConvertUtils.sourceToTarget(mapAreas, MapAreaDraft.class));
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("Failed to execute batchUpdatePathInfo method e:{}", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.map.roadnet.update.error", mapCode);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    private List<MarkerDraft> resvoleMarkers(BatchUpdateElementData elementData, String locatingCode, Map<String, Marker> markerOfDbMap){
        List<MarkerDraft> markerDraftList = new ArrayList<>();
        List<MarkerDTO> markers = elementData.getMarkers();
        if(!CollectionUtils.isEmpty(markers)){
            markers.forEach(it->{
                MarkerDraft markerDraft = ConvertUtils.sourceToTarget(it, MarkerDraft.class);
                Marker markerOfDb = markerOfDbMap.get(markerDraft.getCode());
                markerDraft.setMarkInfos(markerOfDb.getMarkInfos());
                //只改locatingCode对应的路径
                markerDraft.getMarkInfos().forEach(pathInfo -> {
                    if (pathInfo.getLocatingCode().equals(locatingCode)) {
                        pathInfo.setX(it.getX());
                        pathInfo.setY(it.getY());
                    }
                });
                markerDraftList.add(markerDraft);
            });
        }
        return markerDraftList;
    }

    private List<PathDraft> resvolePaths(BatchUpdateElementData elementData, String mapCode, String locatingCode, Map<String, Marker> markerOfDbMap) {
        List<BatchUpdatePathDTO> paths = elementData.getPaths();
        List<MarkerDTO> markers = elementData.getMarkers();

        Map<String, PathDTO> allPathMap = new HashMap<>();
        Set<String> pathCodeSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(paths)) {
            pathCodeSet.addAll(paths.stream().map(BatchUpdatePathDTO::getCode).collect(Collectors.toSet()));
        }
        List<Path> pathsOfDb = pathService.selectByVehicleMapCode(mapCode, true);
        List<Path> paramRelatePathList = pathsOfDb.stream().filter(p -> pathCodeSet.contains(p.getCode())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(paramRelatePathList)) {
            paramRelatePathList.forEach(p -> allPathMap.put(p.getCode(), MapUtils.convertToPathDTO(locatingCode, p)));
        }

        //计算点位关联的路径
        if (!CollectionUtils.isEmpty(markers)) {
            Set<Path> relatePaths = new HashSet<>();
            markers.forEach(id -> {
                List<Path> updatePath = pathsOfDb.stream().filter(path -> path.getEndMarkerCode().equals(id) || path.getStartMarkerCode().equals(id)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(updatePath)) {
                    relatePaths.addAll(updatePath);
                }
            });
            Map<String, Path> relatePathMap = relatePaths.stream().collect(Collectors.toMap(Path::getCode, path -> path, (k1, k2) -> k2));
            if (!CollectionUtils.isEmpty(relatePathMap)) {
                relatePathMap.forEach((key, item) -> {
                    if (!allPathMap.containsKey(key)) {
                        allPathMap.put(key, MapUtils.convertToPathDTO(locatingCode, item));
                    }
                });
            }
        }

        Map<String, BatchUpdatePathDTO> paramMap = paths.stream().collect(Collectors.toMap(BatchUpdatePathDTO::getCode, Function.identity()));
        Map<String, MarkerDTO> markerOfParamMap = markers.stream().collect(Collectors.toMap(MarkerDTO::getCode, k1 -> k1));
        //重新计算路径相关信息
        List<PathDraft> pathDraftList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allPathMap)) {
            Map<String, Path> pathOfDbMap = pathsOfDb.stream().collect(Collectors.toMap(Path::getCode, k1 -> k1));
            for (PathDTO pathDTO : allPathMap.values()) {
                PathDraft pathDraftOfParam = ConvertUtils.sourceToTarget(pathDTO, PathDraft.class);
                BatchUpdatePathDTO batchUpdatePathDTO = paramMap.get(pathDTO.getCode());
                if (batchUpdatePathDTO != null) {
                    BeanUtils.copyProperties(batchUpdatePathDTO, pathDraftOfParam);
                }
                if (pathDraftOfParam.getWeightRatio() == null || pathDraftOfParam.getWeightRatio() <= 0) {
                    pathDraftOfParam.setWeightRatio(1.0);
                }
                Path path = pathOfDbMap.get(pathDraftOfParam.getCode());
                pathDraftOfParam.setPathInfos(path.getPathInfos());

                //只改locatingCode对应的路径
                pathDraftOfParam.getPathInfos().forEach(pathInfo -> {
                    if (pathInfo.getLocatingCode().equals(locatingCode)) {
                        MarkerInfo startMarkerInfo;
                        MarkerDTO startMarker = markerOfParamMap.get(path.getStartMarkerCode());
                        if (startMarker != null) {
                            startMarkerInfo = MarkerInfo.builder().x(startMarker.getX()).y(startMarker.getY()).locatingCode(startMarker.getLocatingCode()).build();
                        } else {
                            Marker marker = markerOfDbMap.get(path.getStartMarkerCode());
                            startMarkerInfo = marker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                        }
                        MarkerInfo endMarkerInfo;
                        MarkerDTO endMarker = markerOfParamMap.get(path.getEndMarkerCode());
                        if (endMarker != null) {
                            endMarkerInfo = MarkerInfo.builder().x(endMarker.getX()).y(endMarker.getY()).locatingCode(endMarker.getLocatingCode()).build();
                        } else {
                            Marker marker = markerOfDbMap.get(path.getEndMarkerCode());
                            endMarkerInfo = marker.getMarkInfos().stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null);
                        }
                        pathInfo.init(pathDTO);
                        pathInfo.setStartControl(batchUpdatePathDTO.getStartControl());
                        pathInfo.setEndControl(batchUpdatePathDTO.getEndControl());
                        pathInfo.setLength(PathUtils.getLength(pathInfo, startMarkerInfo, endMarkerInfo));
                        Double[] inOutAngle = PathUtils.getInOutAngle2(pathInfo, startMarkerInfo, endMarkerInfo);
                        pathInfo.setInRadian(inOutAngle[0]);
                        pathInfo.setOutRadian(inOutAngle[1]);
                    }
                });
                pathDraftList.add(pathDraftOfParam);
            }
        }
        return pathDraftList;
    }

    @Override
    public void batchDelete(BatchDeleteElementDTO pathResult, String mapCode) {
        if(CollectionUtils.isEmpty(pathResult.getMarkerCodes())
                && CollectionUtils.isEmpty(pathResult.getPathCodes())
                && CollectionUtils.isEmpty(pathResult.getMapAreaCodes())){
            return;
        }

        //获取元素更新前的基本数据，用于存储做回退使用
        List<MarkerDraft> oriMarkers = null;
        if (!CollectionUtils.isEmpty(pathResult.getMarkerCodes())) {
            oriMarkers = markerDraftDao.selectBatchIds(pathResult.getMarkerCodes());
        }
        List<MapAreaDraft> oriMapAreas = null;
        if (!CollectionUtils.isEmpty(pathResult.getMapAreaCodes())) {
            oriMapAreas = mapAreaDraftDao.selectBatchIds(pathResult.getMapAreaCodes());
        }

        boolean applyLockFlag = MapPublishUtil.applyLockForMap(mapCode);
        if (!applyLockFlag) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            List<String> markerCodes = Optional.ofNullable(pathResult.getMarkerCodes()).orElse(new ArrayList<>());
            List<String> pathCodes = Optional.ofNullable(pathResult.getPathCodes()).orElse(new ArrayList<>());
            List<String> mapAreaCodes = Optional.ofNullable(pathResult.getMapAreaCodes()).orElse(new ArrayList<>());
            if (!CollectionUtils.isEmpty(markerCodes)) {
                List<Path> allPaths = pathService.selectCodeByVehicleMapCode(mapCode, true);
                List<Path> delPaths = allPaths.stream().filter(p -> markerCodes.contains(p.getStartMarkerCode()) || markerCodes.contains(p.getEndMarkerCode())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(delPaths)) {
                    Set<String> pathSet = new HashSet<>();
                    pathSet.addAll(pathCodes);
                    pathSet.addAll(delPaths.stream().map(Path::getCode).collect(Collectors.toList()));
                    pathCodes = new ArrayList<>(pathSet);
                }
            }
            //校验点位是否绑定了电梯点
            List<Marker> markers = markerCodes.stream().map(code -> {
                Marker marker = new Marker();
                marker.setCode(code);
                marker.setVehicleMapCode(CodeFormatUtils.getMapCode(code));
                return marker;
            }).collect(Collectors.toList());
            markerService.checkBindElevator(markers);
            //校验路径是否关联了自动门、风淋门
            pathService.checkBindDoorsWithCodes(pathCodes);

            //路径要从pathCodes获取，pathCodes包含前端传入的路径、点位关联的路径
            List<PathDraft> oriPaths = null;
            if (!CollectionUtils.isEmpty(pathCodes)) {
                oriPaths = pathDraftDao.selectBatchIds(pathCodes);
            }
            if (!CollectionUtils.isEmpty(pathCodes)) {
                pathDraftDao.deleteBatchIds(pathCodes);
            }
            if (!CollectionUtils.isEmpty(markerCodes)) {
                markerDraftDao.deleteBatchIds(markerCodes);
            }
            if (!CollectionUtils.isEmpty(mapAreaCodes)) {
                mapAreaDraftDao.deleteBatchIds(mapAreaCodes);
            }
            //此处接口提前返回，ws消息慢慢发送
            sendWebSocketMessageAsync(mapCode, oriMarkers, oriPaths, oriMapAreas);
            undoManageService.pushToUndoPool(mapCode,MapConstant.DELETE_ELEMENTS,oriMarkers,oriPaths,oriMapAreas,null,null,null);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.roadnet.delete.error", mapCode);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    private void sendWebSocketMessageAsync(String mapCode, List<MarkerDraft> markers, List<PathDraft> paths, List<MapAreaDraft> mapAreas) {
        Runnable runnable = () -> {
            //发送消息到打开当前地图的窗口
            if (!CollectionUtils.isEmpty(paths)) {
                //为了避免双向路径被分到两个批次下发给前端，先将双向路径合并
                Map<String, List<Path>> map = new HashMap<>();
                paths.forEach(p -> {
                    Path path = new Path();
                    path.setCode(p.getCode());
                    path.setVehicleMapCode(p.getVehicleMapCode());
                    path.setStartMarkerCode(p.getStartMarkerCode());
                    path.setEndMarkerCode(p.getEndMarkerCode());
                    String pathCode = CodeFormatUtils.getPathCode(p.getVehicleMapCode(), p.getStartMarkerCode(), p.getEndMarkerCode());
                    List<Path> pathList = map.computeIfAbsent(pathCode, k -> new ArrayList<>());
                    pathList.add(path);
                });
                List<List<Path>> pairList = map.values().stream().collect(Collectors.toList());
                List<List<List<Path>>> partition = Lists.partition(pairList, 1500);
                for (int i = 0; i < partition.size(); i++) {
                    List<List<Path>> it = partition.get(i);
                    List<Path> list = it.stream().flatMap(e -> e.stream()).collect(Collectors.toList());
                    SocketMessageModel messageModel = new SocketMessageModel().delete();
                    messageModel.setPaths(list);
                    MapUpdateSocketController.sendMessage(mapCode, messageModel);
                    //最后一批不用等
                    if (i < partition.size() - 1) ThreadUtils.sleep(1000);
                }
            }
            if (!CollectionUtils.isEmpty(markers)) {
                List<Marker> wsMarkerDTOS = markers.stream().map(m -> {
                    Marker marker = new Marker();
                    marker.setCode(m.getCode());
                    marker.setVehicleMapCode(m.getVehicleMapCode());
                    return marker;
                }).collect(Collectors.toList());
                List<List<Marker>> markerPartition = Lists.partition(wsMarkerDTOS, 1000);
                for (int i = 0; i < markerPartition.size(); i++) {
                    List<Marker> it = markerPartition.get(i);
                    SocketMessageModel messageModel = new SocketMessageModel().delete();
                    messageModel.setMarkers(it);
                    MapUpdateSocketController.sendMessage(mapCode, messageModel);
                    //最后一批不用等
                    if (i < markerPartition.size() - 1) ThreadUtils.sleep(1000);
                }
            }
            if (!CollectionUtils.isEmpty(mapAreas)) {
                List<MapArea> mapAreaList = mapAreas.stream().map(a -> {
                    MapArea mapArea = new MapArea();
                    mapArea.setCode(a.getCode());
                    mapArea.setVehicleMapCode(a.getVehicleMapCode());
                    return mapArea;
                }).collect(Collectors.toList());
                List<List<MapArea>> mapAreaPartition = Lists.partition(mapAreaList, 200);
                for (int i = 0; i < mapAreaPartition.size(); i++) {
                    List<MapArea> it = mapAreaPartition.get(i);
                    SocketMessageModel messageModel = new SocketMessageModel().delete();
                    messageModel.setMapAreas(it);
                    MapUpdateSocketController.sendMessage(mapCode, messageModel);
                    //最后一批不用等
                    if (i < mapAreaPartition.size() - 1) ThreadUtils.sleep(1000);
                }
            }
        };
        ThreadUtils.newThread("map-edit-ws-send-thread", runnable).start();
    }

    @Override
    public MapInfoDTO getLocatingMap(String vehicleMapCode, String locatingCode, Boolean isDraft){
        List<MapInfoDTO> mapInfoList = getMapInfoList(vehicleMapCode, isDraft);
        return Optional.ofNullable(mapInfoList)
                .map(it -> it.stream().filter(info -> info.getLocatingCode().equals(locatingCode)).findFirst().orElse(null))
                .orElse(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MapInfoDTO updateLocatingMap(MapInfoUpdateDTO mapInfoUpdateDTO){
        String vehicleMapCode = mapInfoUpdateDTO.getVehicleMapCode();
        String locatingCode = mapInfoUpdateDTO.getLocatingCode();

        boolean applyLockFlag = MapPublishUtil.applyLockForMap(vehicleMapCode);
        if (!applyLockFlag) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        MapInfoDTO mapInfoDtoOfDb = null;
        try {
            VehicleMapDetailDTO vehicleMapDetailDTO = selectByCode(vehicleMapCode, true);
            if (vehicleMapDetailDTO == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.not.exist.error", vehicleMapCode);
                throw new FleetException(message);
            }
            mapInfoDtoOfDb = Optional.ofNullable(vehicleMapDetailDTO.getMapInfoList())
                    .map(list -> list.stream().filter(it -> it.getLocatingCode().equals(locatingCode)).findFirst().orElse(null))
                    .orElseThrow(()-> {
                        String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.not.exist.error", locatingCode);
                        return new FleetException(message);
                    });

            //校验类型编码绑定情况
            checkVehicleTypeCodesBinds(mapInfoUpdateDTO,vehicleMapDetailDTO.getMapInfoList());
            //如果机器人类型编码不为空，则去重
            String vehicleTypeCodes = mapInfoUpdateDTO.getVehicleTypeCodes();
            if(!StringUtils.isEmpty(vehicleTypeCodes)){
                mapInfoUpdateDTO.setVehicleTypeCodes(String.join(",", Stream.of(vehicleTypeCodes.split(",")).collect(Collectors.toSet())));
            }

            mapInfoDtoOfDb.setAngle(mapInfoUpdateDTO.getAngle());
            mapInfoDtoOfDb.setVehicleTypeCodes(mapInfoUpdateDTO.getVehicleTypeCodes());
            MapInfoDraft param = ConvertUtils.sourceToTarget(mapInfoDtoOfDb, MapInfoDraft.class);
            updateMapInfo(param, mapInfoDraftDao, param.getVehicleMapCode(), param.getLocatingCode());

            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfUpdate(vehicleMapCode, "LocatingMap", Arrays.asList(mapInfoDtoOfDb));
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("update locating map failed ： {}", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.update.error", vehicleMapCode);
            throw new FleetException(message);
        } finally {
            MapPublishUtil.releaseMap(vehicleMapCode);
        }
        return mapInfoDtoOfDb;
    }

    /**
     * 校验机器人类型的绑定情况
     * 一种类型，只能在一个定位图中存在
     */
    private void checkVehicleTypeCodesBinds(MapInfoUpdateDTO mapInfoUpdateDTO, List<MapInfoDTO> mapInfoList){
        String vehicleTypeCodes = mapInfoUpdateDTO.getVehicleTypeCodes();
        if(CollectionUtils.isEmpty(mapInfoList) || StringUtils.isEmpty(vehicleTypeCodes)){
            return;
        }
        List<VehicleTypeDTO> typeDTOList = vehicleTypeService.findAll();
        if(CollectionUtils.isEmpty(typeDTOList)){
            String message = I18nMessageUtils.getMessage("vehicle.type.empty", vehicleTypeCodes);
            throw new FleetException(message);
        }
        Set<String> typeCodeSetOfDb = typeDTOList.stream().map(VehicleTypeDTO::getCode).collect(Collectors.toSet());
        List<String> codesOfParam = Arrays.asList(vehicleTypeCodes.split(","));
        codesOfParam.forEach(code->{
            if(!typeCodeSetOfDb.contains(code)){
                String message = I18nMessageUtils.getMessage("vehicle.type.empty", vehicleTypeCodes);
                throw new FleetException(message);
            }
        });

        String locatingCode = mapInfoUpdateDTO.getLocatingCode();
        for(MapInfoDTO mapInfoDTO : mapInfoList){
            if(mapInfoDTO.getLocatingCode().equals(locatingCode)) continue;
            String otherLocatingVehicleTypeCodes = mapInfoDTO.getVehicleTypeCodes();
            if(StringUtils.isEmpty(otherLocatingVehicleTypeCodes)) continue;
            Set<String> otherCodes = Stream.of(otherLocatingVehicleTypeCodes.split(",")).collect(Collectors.toSet());
            codesOfParam.forEach(code->{
                if(otherCodes.contains(code)){
                    String message = I18nMessageUtils.getMessage("vehicle.type.bind.duplicate", code);
                    throw new FleetException(message);
                }
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MapInfoDTO importLocatingMap(String vehicleMapCode, MultipartFile multiPartFile) throws Exception {
        if (multiPartFile == null) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        byte[] fileByte = FileUtils.getByteArray(multiPartFile.getInputStream());
        if (fileByte == null || fileByte.length <= 0) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        String originalFilename = multiPartFile.getOriginalFilename();
        if (!originalFilename.endsWith(MapFileSuffixConstant.MAP_PACKAGE_SUFFIX)) {
            String message = I18nMessageUtils.getMessage("system.code.format.error", originalFilename);
            throw new FleetException(message);
        }

        MapInfoDTO mapInfoDTO = null;
        originalFilename = originalFilename.lastIndexOf(".") >= 0 ? originalFilename.substring(0, originalFilename.lastIndexOf(".")) : originalFilename;
        originalFilename += System.currentTimeMillis();
        String tmpDir = LOCATING_DRAFT_PATH + "tmp/" + originalFilename + "/";
        try {
            FileUtils.writeZipDataToLocal(tmpDir, new ByteArrayInputStream(fileByte, 0, fileByte.length));
            File tmpDirFile = new File(tmpDir);
            File[] files = tmpDirFile.listFiles();
            if (files == null || files.length == 0) {
                String message = I18nMessageUtils.getMessage("system.directory.is.empty", tmpDirFile.getPath());
                throw new FleetException(message);
            }
            //tmpDir / directory / xxx.info、xxx.png、xxx.json ...
            File firstDirectory = Stream.of(files).filter(f -> f.isDirectory()).findFirst().orElse(null);
            if (firstDirectory == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.import.structure.error");
                throw new FleetException(message);
            }

            //获取 / xxx.info、xxx.png、xxx.json ...
            //获取当前目录下的所有文件列表
            files = firstDirectory.listFiles();
            if (files == null || files.length == 0) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.import.file.is.empty.error");
                throw new FleetException(message);
            }
            File mapInfoFile = Stream.of(files).filter(f -> f.getName().endsWith(".info")).findFirst().orElse(null);
            if (mapInfoFile == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.import.file.is.missing.error", "info");
                throw new FleetException(message);
            }
            File pngFile = Stream.of(files).filter(f -> f.getName().endsWith(".png")).findFirst().orElse(null);
            if (pngFile == null) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.import.file.is.missing.error", "png");
                throw new FleetException(message);
            }

            String infoString = FileUtils.readFileData(mapInfoFile.getPath());
            mapInfoDTO = JSON.parseObject(infoString, MapInfoDTO.class);
            //有可能底盘传递的是code、id,不是locatingCode
            JSONObject object = JSON.parseObject(infoString);
            if (StringUtils.isEmpty(mapInfoDTO.getLocatingCode())) {
                mapInfoDTO.setLocatingCode(object.getString("code"));
            }
            if (StringUtils.isEmpty(mapInfoDTO.getLocatingCode())) {
                mapInfoDTO.setLocatingCode(object.getString("id"));
            }

            //不允许导入Default-White-Map定位图
            String locatingCode = mapInfoDTO.getLocatingCode();
            if (MapConstant.DEFAULT_LOCATING_MAP_CODE.equals(locatingCode)) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.import.file.is.forbidden.error", MapConstant.DEFAULT_LOCATING_MAP_CODE);
                throw new FleetException(message);
            }

            //检验定位图是否重复
            checkLocatingDuplicate(vehicleMapCode, locatingCode);

            //筛选出定位图该有的文件
            Set<String> fileNameSet = locatingFileSuffixs.stream().filter(s -> !s.equals(".zip")).map(suffix -> locatingCode + suffix).collect(Collectors.toSet());
            List<File> fileList = Stream.of(files).filter(f -> fileNameSet.contains(f.getName())).collect(Collectors.toList());
            File[] tmpFileArr = new File[fileList.size()];
            for (int i = 0; i < fileList.size(); i++) {
                tmpFileArr[i] = fileList.get(i);
            }
            files = tmpFileArr;
            resolveLocatingImportLogic(vehicleMapCode, mapInfoDTO, files);
            //将zip文件转移到vehicleMapCode的草稿目录
            String locatingDraftPath = LOCATING_DRAFT_PATH + vehicleMapCode + "/" + mapInfoDTO.getLocatingCode() + ".zip";
            FileUtils.writeFile(fileByte, locatingDraftPath);

            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfAdd(vehicleMapCode, "LocatingMap", Arrays.asList(mapInfoDTO));
        } finally {
            FileUtils.deleteFile(tmpDir);
        }
        return mapInfoDTO;
    }

    /**
     * 导入定位图时，检验定位图是否重复：包含草稿、正式
     * @param currentMapCode
     * @param locatingCode
     */
    private void checkLocatingDuplicate(String currentMapCode, String locatingCode) {
        List<MapInfo> mapInfos = mapInfoDao.selectList(new QueryWrapper<>());
        List<MapInfoDraft> mapInfoDrafts = mapInfoDraftDao.selectList(new QueryWrapper<>());
        Map<String, String> locatingCodeMap = new HashMap<>();
        Optional.ofNullable(mapInfos).orElse(new ArrayList<>()).forEach(it -> locatingCodeMap.put(it.getLocatingCode(), it.getVehicleMapCode()));
        Optional.ofNullable(mapInfoDrafts).orElse(new ArrayList<>()).forEach(it -> locatingCodeMap.put(it.getLocatingCode(), it.getVehicleMapCode()));
        if (locatingCodeMap.containsKey(locatingCode)) {
            String vehicleMapCode = locatingCodeMap.get(locatingCode);
            if (!vehicleMapCode.equals(currentMapCode)) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.import.file.is.duplicate.error", locatingCode, vehicleMapCode);
                throw new FleetException(message);
            }
        }
    }

    /**
     * 1、如果地图只有一个定位图且是默认，删除默认定位图，新增一个新的
     * 2、否则，是否重复了，重复则更新
     * 3、不重复，则新增（数据库表新增，磁盘目录新增，点位增加数据、路径增加数据）
     */
    private void resolveLocatingImportLogic(String mapCode, MapInfoDTO mapInfoDTO, File[] files) throws IOException {
        if(StringUtils.isEmpty(mapInfoDTO.getLocatingCode())){
            String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.code.is.empty.error", mapCode);
            throw new FleetException(message);
        }
        if(!RegexConstant.isMatched(mapInfoDTO.getLocatingCode(), RegexConstant.NAME_REG)){
            String message = I18nMessageUtils.getMessage("system.code.format.error", mapInfoDTO.getLocatingCode());
            throw new FleetException(message);
        }
        List<MapInfoDTO> mapInfoDTOList = getMapInfoList(mapCode, true);
        if(CollectionUtils.isEmpty(mapInfoDTOList)){
            String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.is.empty.error", mapCode);
            throw new FleetException(message);
        }

        String defaultLocatingCode = MapConstant.DEFAULT_LOCATING_MAP_CODE + "-" + mapCode;
        //只有一个默认的
        if(mapInfoDTOList.size() == 1
                && (defaultLocatingCode.equals(mapInfoDTOList.get(0).getLocatingCode()) || MapConstant.DEFAULT_LOCATING_MAP_CODE.equals(mapInfoDTOList.get(0).getLocatingCode()))){
            defaultLocatingCode = mapInfoDTOList.get(0).getLocatingCode();
            replaceDefaultLocating(mapCode, defaultLocatingCode, mapInfoDTO);
        }else {
            MapInfoDTO exit = mapInfoDTOList.stream().filter(it -> it.getLocatingCode().equals(mapInfoDTO.getLocatingCode())).findFirst().orElse(null);
            if (exit == null) {
                defaultLocatingCode = mapInfoDTOList.stream().filter(MapInfoDTO::isDefault).findFirst()
                        .map(MapInfoDTO::getLocatingCode)
                        .orElse(mapInfoDTOList.get(0).getLocatingCode());
                addLocating(mapCode, defaultLocatingCode, mapInfoDTO);
            } else {
                updateLocating(mapInfoDTO, exit);
            }
        }

        //将临时文件转移到草稿文件夹目录
        String locatingDraftPath = LOCATING_DRAFT_PATH + mapCode + "/";
        for(File file : files){
            FileUtils.copyFolder(file.getPath(),locatingDraftPath);
        }
    }

    /**
     * 替换默认定位图：
     * 1、删除默认草稿定位图数据库记录
     * 2、生成新的草稿定位图数据库记录
     * 3、替换点位中的locatingCode
     * 4、替换路径中的locatingCode
     * 5、删除默认草稿定位图文件夹
     */
    private void replaceDefaultLocating(String mapCode, String defaultLocatingCode, MapInfoDTO mapInfoDTO) {
        deleteMapInfo(mapInfoDraftDao, mapCode, defaultLocatingCode);

        mapInfoDTO.setIsDefault(MapConstant.LOCATING_IS_DEFAULT);
        mapInfoDTO.setVehicleMapCode(mapCode);
        MapInfoDraft mapInfoDraft = new MapInfoDraft();
        BeanUtils.copyPropertiesIgnoreNull(mapInfoDTO, mapInfoDraft);
        mapInfoDraftDao.insert(mapInfoDraft);

        List<MarkerDraft> markerDraftList = getListByVehicleMapCode(mapCode, markerDraftDao);
        if (!CollectionUtils.isEmpty(markerDraftList)) {
            markerDraftList.forEach(it -> {
                if (!CollectionUtils.isEmpty(it.getMarkInfos())) {
                    it.getMarkInfos().forEach(info -> {
                        if (defaultLocatingCode.equals(info.getLocatingCode())) {
                            info.setLocatingCode(mapInfoDTO.getLocatingCode());
                        }
                    });
                }
            });
            dbUtils.updateBatchById(MarkerDraft.class, markerDraftList, 50);
        }
        List<PathDraft> pathDraftList = getListByVehicleMapCode(mapCode, pathDraftDao);
        if (!CollectionUtils.isEmpty(pathDraftList)) {
            pathDraftList.forEach(it -> {
                if (!CollectionUtils.isEmpty(it.getPathInfos())) {
                    it.getPathInfos().forEach(info -> {
                        if (defaultLocatingCode.equals(info.getLocatingCode())) {
                            info.setLocatingCode(mapInfoDTO.getLocatingCode());
                        }
                    });
                }
            });
            dbUtils.updateBatchById(PathDraft.class, pathDraftList, 50);
        }

        deleteLocatingFileOfMap(LOCATING_DRAFT_PATH, mapCode, defaultLocatingCode);
    }

    /**
     * 添加定位图：
     * 1、生成新的定位图记录
     * 2、点位中生成新的坐标
     * 3、路径中生成新的参数
     */
    private void addLocating(String mapCode, String defaultLocatingCode, MapInfoDTO mapInfoDTO) {
        //新增的定位图，点位、路径，需要扩增
        mapInfoDTO.setVehicleMapCode(mapCode);
        mapInfoDTO.setIsDefault(MapConstant.LOCATING_IS_NOT_DEFAULT);
        MapInfoDraft mapInfoDraft = new MapInfoDraft();
        BeanUtils.copyPropertiesIgnoreNull(mapInfoDTO, mapInfoDraft);
        mapInfoDraftDao.insert(mapInfoDraft);

        List<MarkerDraft> markerDraftList = getListByVehicleMapCode(mapCode, markerDraftDao);
        if (!CollectionUtils.isEmpty(markerDraftList)) {
            markerDraftList.forEach(it -> {
                if (!CollectionUtils.isEmpty(it.getMarkInfos())) {
                    MarkerInfo markerInfo = it.getMarkInfos().stream().filter(info -> info.getLocatingCode().equals(defaultLocatingCode)).findFirst().orElse(it.getMarkInfos().get(0));
                    MarkerInfo newInfo = MarkerInfo.builder().x(markerInfo.getX()).y(markerInfo.getY()).locatingCode(mapInfoDTO.getLocatingCode()).build();
                    it.getMarkInfos().add(newInfo);
                }
            });
            dbUtils.updateBatchById(MarkerDraft.class, markerDraftList, 50);
        }
        List<PathDraft> pathDraftList = getListByVehicleMapCode(mapCode, pathDraftDao);
        if (!CollectionUtils.isEmpty(pathDraftList)) {
            pathDraftList.forEach(it -> {
                if (!CollectionUtils.isEmpty(it.getPathInfos())) {
                    PathInfo pathInfo = it.getPathInfos().stream().filter(info -> info.getLocatingCode().equals(defaultLocatingCode)).findFirst().orElse(it.getPathInfos().get(0));
                    PathInfo newInfo = new PathInfo();
                    BeanUtils.copyPropertiesIgnoreNull(pathInfo, newInfo);
                    newInfo.setLocatingCode(mapInfoDTO.getLocatingCode());
                    it.getPathInfos().add(newInfo);
                }
            });
            dbUtils.updateBatchById(PathDraft.class, pathDraftList, 50);
        }
    }

    /**
     * 1、删除旧的定位图记录
     * 2、插入新的定位图记录
     * 3、删除原定位图草稿文件夹数据
     * 4、导入新的草稿定位图文件夹数据
     */
    private void updateLocating(MapInfoDTO mapInfoDTO, MapInfoDTO exit) {
        deleteMapInfo(mapInfoDraftDao, exit.getVehicleMapCode(), exit.getLocatingCode());

        mapInfoDTO.setIsDefault(exit.getIsDefault());
        mapInfoDTO.setVehicleMapCode(exit.getVehicleMapCode());
        MapInfoDraft mapInfoDraft = ConvertUtils.sourceToTarget(mapInfoDTO, MapInfoDraft.class);
        mapInfoDraftDao.insert(mapInfoDraft);

        deleteLocatingFileOfMap(LOCATING_DRAFT_PATH, mapInfoDTO.getVehicleMapCode(), mapInfoDTO.getLocatingCode());
    }

    /**
     * 导出定位图数据
     */
    @Override
    public void exportLocationMap(String mapCode, String locatingCode, Boolean isDraft, HttpServletResponse response) {
        String local_map_root_path = isDraft ? LOCATING_DRAFT_PATH : LOCATING_CURRENT_PATH;
        File rootMapDir = new File(local_map_root_path);
        if (!rootMapDir.exists() || !rootMapDir.isDirectory()) {
            LOGGER.error("地图目录{}不存在", local_map_root_path);
            String message = I18nMessageUtils.getMessage("system.directory.is.not.exists", local_map_root_path);
            throw new FleetException(message);
        }
        File[] mapFileDirs = rootMapDir.listFiles();
        if (mapFileDirs == null || mapFileDirs.length == 0) {
            LOGGER.error("地图目录{}下没有任何地图文件", local_map_root_path);
            String message = I18nMessageUtils.getMessage("system.directory.is.empty", local_map_root_path);
            throw new FleetException(message);
        }
        File mapFileDir = Arrays.stream(mapFileDirs).filter(file -> file.getName().equals(mapCode)).findFirst().orElse(null);
        if (mapFileDir == null) {
            LOGGER.error("地图目录{}下不存在编码为{}的地图", local_map_root_path, mapCode);
            String message = I18nMessageUtils.getMessage("vehicleMap.map.not.exist.error", mapCode);
            throw new FleetException(message);
        }

        File locatingFileDir = new File(local_map_root_path + mapCode);
        File[] locatingFiles = locatingFileDir.listFiles();
        if (locatingFiles == null || locatingFiles.length <= 0) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.not.exist.error", locatingCode);
            throw new FleetException(message);
        }

        java.nio.file.Path tempPath = null;
        OutputStream os = null;
        try {

            //如果有zip文件，直接导出zip文件，没有的话，将文件压缩后导出
            byte[] bytes;
            File zipFile = Arrays.stream(locatingFiles).filter(file -> file.getName().equals(locatingCode + ".zip")).findFirst().orElse(null);
            if (zipFile != null) {
                bytes = Files.readAllBytes(Paths.get(zipFile.getPath()));
            }else{
                tempPath = Files.createDirectories(Paths.get(local_map_root_path + System.currentTimeMillis() + "/"+ locatingCode));
                Set<String> fileNameSet = locatingFileSuffixs.stream().map(suffix -> locatingCode + suffix).collect(Collectors.toSet());
                for (File f : locatingFiles) {
                    if (!fileNameSet.contains(f.getName())) continue;
                    Files.copy(Paths.get(f.getPath()), Paths.get(tempPath + "/" + f.getName()));
                }
                String zipPath = local_map_root_path + mapCode + "/" + locatingCode + ".zip";
                File file = ZipUtil.zip(tempPath.toString(), zipPath, true);
                bytes = Files.readAllBytes(Paths.get(file.getPath()));
            }

            response.addHeader("Content-Disposition", "attachment;filename=" + new String(locatingCode.getBytes(), StandardCharsets.ISO_8859_1) + ".zip");
            response.addHeader("Content-Length", "" + bytes.length);
            os = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            os.write(bytes);// 输出文件
            os.flush();
            os.close();
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("",e);
            String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.export.error", locatingCode, e.getMessage());
            throw new FleetException(message);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (tempPath != null) {
                FileUtils.deleteFile(tempPath.getParent().toFile());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeDefaultLocatingMap(MapLocationDTO mapLocationDTO) {
        String mapCode = mapLocationDTO.getMapCode();
        String locatingCode = mapLocationDTO.getLocatingCode();

        boolean applyLockFlag = MapPublishUtil.applyLockForMap(mapCode);
        if (!applyLockFlag) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }

        try {
            List<MapInfoDraft> mapInfoDraftList = getListByVehicleMapCode(mapCode, mapInfoDraftDao);
            if (CollectionUtils.isEmpty(mapInfoDraftList)) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.is.empty.error", mapCode);
                throw new FleetException(message);
            }
            //如果只有一个，则直接指定
            if (mapInfoDraftList.size() == 1) {
                MapInfoDraft mapInfoDraft = mapInfoDraftList.get(0);
                mapInfoDraft.setIsDefault(MapConstant.LOCATING_IS_DEFAULT);
                mapInfoDraft.setVehicleTypeCodes(null);
                updateMapInfo(mapInfoDraft, mapInfoDraftDao, mapInfoDraft.getVehicleMapCode(), mapInfoDraft.getLocatingCode());
                return;
            }
            MapInfoDraft target = mapInfoDraftList.stream().filter(it -> it.getLocatingCode().equals(locatingCode))
                    .findFirst()
                    .orElseThrow(() -> {
                        String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.not.exist.error", locatingCode);
                        return new FleetException(message);
                    });
            if (target.isDefault()) {
                return;
            }

            MapInfoDraft defaultMap = mapInfoDraftList.stream().filter(it -> it.isDefault()).findFirst().orElse(null);
            if (defaultMap != null) {
                defaultMap.setIsDefault(MapConstant.LOCATING_IS_NOT_DEFAULT);
                updateMapInfo(defaultMap, mapInfoDraftDao, defaultMap.getVehicleMapCode(), defaultMap.getLocatingCode());
            }

            target.setIsDefault(MapConstant.LOCATING_IS_DEFAULT);
            target.setVehicleTypeCodes(null);
            updateMapInfo(target, mapInfoDraftDao, target.getVehicleMapCode(), target.getLocatingCode());

            //发送消息到打开当前地图的窗口
            MapInfoDTO mapInfoDTO = ConvertUtils.sourceToTarget(target, MapInfoDTO.class);
            MapUpdateSocketController.sendMessageOfUpdate(mapCode, "LocatingMap", Arrays.asList(mapInfoDTO));

            //切换了默认定位图，推送完整地图信息，通知页面更新
            VehicleMapDetailDTO vehicleMapDetailDTO = selectByCode(mapCode, true);
            MapUpdateSocketController.sendMessageOfUpdate(mapCode, "Map", Arrays.asList(vehicleMapDetailDTO));
        }finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLocatingMap(MapLocationDTO mapLocationDTO) {
        String mapCode = mapLocationDTO.getMapCode();
        String locatingCode = mapLocationDTO.getLocatingCode();

        boolean applyLockFlag = MapPublishUtil.applyLockForMap(mapCode);
        if (!applyLockFlag) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        try {
            List<MapInfoDraft> mapInfoDraftList = getListByVehicleMapCode(mapCode, mapInfoDraftDao);
            if (CollectionUtils.isEmpty(mapInfoDraftList)) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.is.empty.error", mapCode);
                throw new FleetException(message);
            }
            MapInfoDraft target = mapInfoDraftList.stream().filter(it -> it.getLocatingCode().equals(locatingCode))
                    .findFirst()
                    .orElseThrow(() -> {
                        String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.not.exist.error", locatingCode);
                        return new FleetException(message);
                    });
            if (target.isDefault()) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.default.error", mapCode, locatingCode);
                throw new FleetException(message);
            }
            doDeleteLocating(mapCode, locatingCode);
            //发送消息到打开当前地图的窗口
            MapInfoDTO mapInfoDTO = ConvertUtils.sourceToTarget(target, MapInfoDTO.class);
            MapUpdateSocketController.sendMessageOfDelete(mapCode, "LocatingMap", Arrays.asList(mapInfoDTO));
        } finally {
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    /**
     * 删除定位图：
     * 1、删除定位图记录
     * 2、删除点位中对应的坐标
     * 3、删除路径中对应的参数
     * 4、删除定位图草稿文件夹
     */
    private void doDeleteLocating(String mapCode, String locatingCode) {
        deleteMapInfo(mapInfoDraftDao, mapCode, locatingCode);

        List<MarkerDraft> markerDraftList = getListByVehicleMapCode(mapCode, markerDraftDao);
        if (!CollectionUtils.isEmpty(markerDraftList)) {
            markerDraftList.forEach(it -> {
                if (!CollectionUtils.isEmpty(it.getMarkInfos())) {
                    it.getMarkInfos().removeIf(info -> info.getLocatingCode().equals(locatingCode));
                }
            });
            dbUtils.updateBatchById(MarkerDraft.class, markerDraftList, 50);
        }

        List<PathDraft> pathDraftList = getListByVehicleMapCode(mapCode, pathDraftDao);
        if (!CollectionUtils.isEmpty(pathDraftList)) {
            pathDraftList.forEach(it -> {
                if (!CollectionUtils.isEmpty(it.getPathInfos())) {
                    it.getPathInfos().removeIf(info -> info.getLocatingCode().equals(locatingCode));
                }
            });
            dbUtils.updateBatchById(PathDraft.class, pathDraftList, 50);
        }

        deleteLocatingFileOfMap(LOCATING_DRAFT_PATH, mapCode, locatingCode);
    }

    /**
     * 删除某个地图的某个定位图
     */
    private List<String> locatingFileSuffixs = Lists.newArrayList(".info",".pcd",".zip"
            ,".pgd",".png",".qr",".feature",".yaml", "_graph.g2o","_graph.g2o.kernels","_og.png","_og.yaml");
    private void deleteLocatingFileOfMap(String path, String mapCode, String locatingCode){
        String locatingDraftPath = path + mapCode + "/";
        File locatingFileDir = new File(locatingDraftPath);
        File[] files = locatingFileDir.listFiles();

        List<File> locatingFileList = null;
        if (files != null && files.length > 0) {
            //不能用startsWith，会出现删除map1,结果把map1、map12都删除的情况
            Set<String> fileNameSet = locatingFileSuffixs.stream().map(suffix -> locatingCode + suffix).collect(Collectors.toSet());
            locatingFileList = Stream.of(files).filter(it -> fileNameSet.contains(it.getName())).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(locatingFileList)) {
            locatingFileList.forEach(FileUtils::deleteFile);
        }
    }

    //更新地图信息
    private <T> void updateMapInfo(T entity, BaseDao<T> baseDao, String mapCode, String locatingCode){
        QueryWrapper<T> wrapper = new QueryWrapper();
        wrapper.eq("vehicle_map_code",mapCode);
        wrapper.eq("locating_code",locatingCode);
        baseDao.update(entity,wrapper);
    }

    //删除地图信息
    private <T> void deleteMapInfo(BaseDao<T> baseDao, String mapCode, String locatingCode){
        QueryWrapper<T> wrapper = new QueryWrapper();
        wrapper.eq("vehicle_map_code",mapCode);
        wrapper.eq("locating_code",locatingCode);
        baseDao.delete(wrapper);
    }

    /**
     * 发布地图
     * 1、删除地图、发布地图、丢弃草稿、同步草稿数据到磁盘，需要加锁，保证同时只有一个操作在执行
     * 2、删除地图、发布地图、丢弃草稿，这三个操作执行时，不能进行地图元素的编辑操作
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(String mapCode) throws Exception {
        boolean applyLockFlag = MapPublishUtil.applyLockForMap(mapCode);
        if (!applyLockFlag) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }

        PublishMap publishMap = PublishMap.builder().code(mapCode).build();
        publishMapHandleResourceQueue.add(publishMap);
        try {
            LOGGER.debug("发布地图, 等待资源处理线程确认, mapCode:{}", mapCode);
            //等待资源处理线程确认
            while (PublishMap.STATUS_TO_BE_CONFIRMED.equals(publishMap.getStatus())) {
                TimeUnit.MILLISECONDS.sleep(10);
            }
            LOGGER.debug("发布地图, 资源处理线程已确认, mapCode:{}", mapCode);

            /**
             * 地图发布前校验
             */
            checkBeforePublish(mapCode);

            /**
             * 草稿数据替换正式数据
             */
            this.publishDraftToCurrent(mapCode);
            //清理撤销恢复缓存
            undoManageService.clear(mapCode);
            String parentPath = LOCATING_PUBLISH_PATH + mapCode;
            File parentDirectory = new File(parentPath);
            if(!parentDirectory.exists()){
                parentDirectory.mkdirs();
            }
            /**
             * 压缩路网文件，修改正式路网文件md5
             */
            zipRoadNetFile(parentPath, mapCode);

            /**
             * 压缩定位文件，修改正式定位文件md5
             */
            String locatingFilePath = LOCATING_CURRENT_PATH + mapCode + "/";
            File locatingList = new File(locatingFilePath);
            File[] locatingFileList = null;
            if (locatingList.exists() && locatingList.isDirectory()) {
                locatingFileList = locatingList.listFiles();
            }
            List<MapInfo> mapInfoList = getListByVehicleMapCode(mapCode, mapInfoDao);
            if (locatingFileList != null && locatingFileList.length > 0 && !CollectionUtils.isEmpty(mapInfoList)) {
                if(mapInfoList.size() == 1){
                    //如果只有一个定位图，直接压缩
                    MapInfo mapInfo = mapInfoList.get(0);
                    String locatingCode = mapInfo.getLocatingCode();
                    List<File> currentLocatingFileList = Stream.of(locatingFileList).collect(Collectors.toList());
                    zipSingleLocatingFile(currentLocatingFileList, locatingCode, mapInfo);
                }else {
                    for (MapInfo mapInfo : mapInfoList) {
                        String locatingCode = mapInfo.getLocatingCode();
                        List<File> currentLocatingFileList = Stream.of(locatingFileList).filter(f -> f.getName().startsWith(locatingCode)).collect(Collectors.toList());
                        zipSingleLocatingFile(currentLocatingFileList, locatingCode, mapInfo);
                    }
                }
            }

            /**
             * 加载到地图路径导航缓存
             */
            MapGraphUtil.addAGVMap(mapCode);
            //发布地图完成, 通知距离矩阵更新
            publishMapHandleMatrixQueue.add(publishMap);
            //发送消息到打开当前地图的窗口
            MapUpdateSocketController.sendMessageOfPublish(mapCode, "Map", Collections.EMPTY_LIST);
        } catch (IOException e) {
            LOGGER.error("发布地图io异常",e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("发布地图异常",e);
            throw e;
        } finally {
            publishMap.setStatus(PublishMap.STATUS_PUBLISHED);
            MapPublishUtil.releaseMap(mapCode);
        }
    }

    private void checkBeforePublish(String mapCode){
        checkAreaBeforePublish(mapCode);
    }

    /**
     * 如果正在被使用的第三方交管区域被删除，或区域类型被变更，不能发布地图
     */
    private void checkAreaBeforePublish(String mapCode){
        List<MapArea> draftList = mapAreaService.selectByVehicleMapCode(mapCode, true);
        List<MapArea> currentList = mapAreaService.selectByVehicleMapCode(mapCode, false);
        if(CollUtil.isEmpty(currentList)){
            //地图原本没有区域
            return;
        }
        List<MapArea> thirdTrafficAreaList = currentList.stream().filter(a -> MapConstant.MAP_AREA_TYPE_THIRD_SYSTEM_TRAFFIC.equals(a.getAreaType())).collect(Collectors.toList());
        if(CollUtil.isEmpty(thirdTrafficAreaList)){
            //地图原本没有交管区域
            return;
        }
        List<MapArea> changedAreaList = new ArrayList<>();
        if (CollUtil.isEmpty(draftList)) {
            //原本的交管区域被删除了
            changedAreaList.addAll(thirdTrafficAreaList);
        } else {
            Map<String, MapArea> draftMap = draftList.stream().collect(Collectors.toMap(MapArea::getCode, Function.identity(), (k1, k2) -> k1));
            List<MapArea> areas = thirdTrafficAreaList.stream().filter(area -> draftMap.get(area.getCode()) == null || !area.getAreaType().equals(draftMap.get(area.getCode()).getAreaType())).collect(Collectors.toList());
            changedAreaList.addAll(areas);
        }
        if(CollUtil.isEmpty(changedAreaList)){
            //交管区域没有被变更或删除
            return;
        }
        changedAreaList.forEach(mapArea -> {
            ThirdSystemTrafficAreaResource resource = thirdSystemTrafficAreaResourcePool.get(mapArea.getCode());
            if (resource != null && org.apache.commons.lang3.StringUtils.isNotBlank(resource.getOccupyVehicleCode())) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.publish.occupied.error", mapArea.getCode());
                throw new FleetException(message);
            }
        });
    }

    private void publishDraftToCurrent(String mapCode) throws IOException {
        /**
         * 数据表更新
         */
        List<MapInfoDraft> mapInfoDraftList = getListByVehicleMapCode(mapCode,mapInfoDraftDao);
        mapInfoDao.delete(new QueryWrapper<MapInfo>().eq("vehicle_map_code", mapCode));
        if (!CollectionUtils.isEmpty(mapInfoDraftList)) {
            mapInfoDraftDao.delete(new QueryWrapper<MapInfoDraft>().eq("vehicle_map_code", mapCode));
            List<MapInfo> mapInfoList = ConvertUtils.sourceToTarget(mapInfoDraftList, MapInfo.class);
            dbUtils.insertBatchById(MapInfo.class,mapInfoList,200);
        }

        List<MapArea> mapAreas = mapAreaService.selectByVehicleMapCode(mapCode, true);
        List<MapArea> formalMapAreas = mapAreaService.selectByVehicleMapCode(mapCode, false);
        mapAreaService.deleteByVehicleMapCode(mapCode);
        if (!CollectionUtils.isEmpty(mapAreas)) {
            //正式数据
            Map<String, MapArea> mapAreaMap = formalMapAreas.stream().collect(Collectors.toMap(MapArea::getCode, area -> area));
            //设置启用状态
            mapAreas.forEach(mapArea -> {
                MapArea formalArea = mapAreaMap.get(mapArea.getCode());
                if(CannotOperateAreaType.contains(mapArea.getAreaType())){
                    mapArea.setEnable(true);
                }else if (formalArea != null) {//正式地图有该区域，沿用原本的启用状态
                    mapArea.setEnable(formalArea.getEnable());
                }
            });
            dbUtils.insertBatchById(MapArea.class,mapAreas,200);
        }

        List<Marker> markers = markerService.selectByVehicleMapCode(mapCode, true);
        List<Path> paths = pathService.selectByVehicleMapCode(mapCode, true);
        markerService.deleteByVehicleMapCode(mapCode);
        if (!CollectionUtils.isEmpty(markers)) {
            //默认定位编码
            String defaultLocatingCode = mapInfoDraftList.stream().filter(MapInfoDraft::isDefault).map(MapInfoDraft::getLocatingCode).findFirst().orElse(null);
            //获取通道区域内的点位
            List<String> channelMarkerCodes = new ArrayList<>();
            List<MapArea> channelAreas = mapAreas.stream().filter(mapArea -> MAP_AREA_TYPE_CHANNEL.equals(mapArea.getAreaType())).collect(Collectors.toList());
            channelAreas.forEach(channelArea -> Optional.ofNullable(channelArea.getPolygon())
                    .map(ConversionUtils::conversion)
                    .map(points -> findAreaMarkers(points, markers, defaultLocatingCode))
                    .ifPresent(channelMarkers -> channelMarkerCodes.addAll(channelMarkers.stream().map(Marker::getCode).collect(Collectors.toList()))));
            markers.forEach(marker -> {
                String markerCode = marker.getCode();
                //如果是属于通道区域内的点位, 直接设置为普通路网点(通道点)
                if (channelMarkerCodes.contains(markerCode)) {
                    marker.setNetworkMarkerType(MapConstant.MARKER_COMMON_TYPE);//相连点位小于等于2为普通点
                    return;
                }
                List<Path> relatePaths = paths.stream().filter(path -> path.getStartMarkerCode().equals(markerCode) || path.getEndMarkerCode().equals(markerCode)).collect(Collectors.toList());
                Set<String> adjacentMarkerCodes = new HashSet<>();
                relatePaths.forEach(path -> {
                    adjacentMarkerCodes.add(path.getStartMarkerCode());
                    adjacentMarkerCodes.add(path.getEndMarkerCode());
                });
                adjacentMarkerCodes.remove(markerCode);
                if (!CollectionUtils.isEmpty(adjacentMarkerCodes) && adjacentMarkerCodes.size() > 2) {
                    marker.setNetworkMarkerType(MapConstant.MARKER_CROSS_TYPE);//相连点位大于2为交叉点
                } else {
                    marker.setNetworkMarkerType(MapConstant.MARKER_COMMON_TYPE);//相连点位小于等于2为普通点
                }
            });
            //设置默认值
            markers.stream().filter(m -> m.getParams() == null).forEach(marker -> marker.setParams(new MarkerParam()));
            markers.stream().filter(m -> m.getChargeProp() == null).forEach(marker -> marker.setChargeProp(new MarkerChargeProp()));
            dbUtils.insertBatchById(Marker.class, markers, 200);
        }
        pathService.deleteByVehicleMapCode(mapCode);
        if (!CollectionUtils.isEmpty(paths)) {
            paths.stream().filter(p -> p.getExtendParamList() == null).forEach(p -> p.setExtendParamList(new ArrayList<>()));
            dbUtils.insertBatchById(Path.class, paths, 200);
        }

        List<AutoDoorDTO> autoDoorDTOS = autoDoorService.selectByVehicleMapCode(mapCode, true);
        autoDoorService.deleteByVehicleMapCode(mapCode);
        if (!CollectionUtils.isEmpty(autoDoorDTOS)) {
            List<AutoDoor> autoDoors = AutoDoorUtils.autoDoorDTOToAutoDoor(autoDoorDTOS, null);
            dbUtils.insertBatchById(AutoDoor.class,autoDoors,200);
        }

        List<AirShowerDoorDTO> airShowerDoorDTOS = airShowerDoorService.selectByVehicleMapCode(mapCode, true);
        airShowerDoorService.deleteByVehicleMapCode(mapCode);
        if (!CollectionUtils.isEmpty(airShowerDoorDTOS)) {
            airShowerDoorDTOS.stream().forEach(airShowerDoorDTO -> {
                AirShowerDoor airShowerDoor = ConvertUtils.sourceToTarget(airShowerDoorDTO, AirShowerDoor.class);
                List<AutoDoorDTO> autoDoorDTOS1 = airShowerDoorDTO.getDoors();
                if (!CollectionUtils.isEmpty(autoDoorDTOS1)) {
                    List<AutoDoor> autoDoors = AutoDoorUtils.autoDoorDTOToAutoDoor(autoDoorDTOS1, airShowerDoorDTO.getCode());
                    autoDoors.stream().forEach(autoDoor -> {
                        autoDoorDao.insert(autoDoor);
                    });
                }
                airShowerDoorDao.insert(airShowerDoor);
            });
        }
        /**
         * 防止草稿目录里面没有定位图压缩包，此处先生成一下
         */
        String path = LOCATING_DRAFT_PATH + mapCode + File.separator;
        generateZip(path);
        /**
         * 激光文件更新
         */
        MapFileUtils.locatingDraftToCurrent(mapCode);
    }

    private static List<Marker> findAreaMarkers(Point[] poly, Collection<Marker> markers, String defaultLocatingCode) {
        List<Marker> singleMarkers = new ArrayList<>();
        for (Marker marker : markers) {
            MarkerInfo markerInfo = marker.getMarkInfos().stream().filter(info -> info.getLocatingCode().equals(defaultLocatingCode)).findFirst().orElse(null);
            Double x = Optional.ofNullable(markerInfo).map(MarkerInfo::getX).orElse(0.0);
            Double y = Optional.ofNullable(markerInfo).map(MarkerInfo::getY).orElse(0.0);
            if (MathematicalGraphicsAlgorithm.isPtInPoly(x, y, poly)) {
                singleMarkers.add(marker);
            }
        }
        return singleMarkers;
    }

    private void zipRoadNetFile(String parentPath, String mapCode) throws IOException {
        RoadNetData roadNetData = this.getMapRoadNetData(mapCode, false);
        String roadNetFilePath = parentPath + "/" + mapCode + ".roadnet";
        String roadNetZipPath = parentPath + "/" + mapCode + "_roadnet.zip";
        File roadNetFile = new File(roadNetFilePath);
        roadNetFile.createNewFile();
        FormatJsonUtils.writeFile(JSON.toJSONString(roadNetData), roadNetFile);

        File roadNetZipPathFile = new File(roadNetZipPath);
        FileUtils.deleteFile(roadNetZipPathFile);
        ZipUtils.zipCompress(roadNetFile, new File(roadNetZipPath), roadNetZipPathFile.lastModified());
        FileInputStream roadNetStream = new FileInputStream(new File(roadNetZipPath));
        String roadNetMd5 = DigestUtils.md5DigestAsHex(roadNetStream);
        roadNetStream.close();
        FileUtils.deleteFile(roadNetFile);

        VehicleMap vehicleMap = vehicleMapDao.selectById(mapCode);
        vehicleMap.setIsDraft(0);
        vehicleMap.setPublishTime(new Date());
        vehicleMap.setIsProd(1);
        vehicleMap.setPathMd5(roadNetMd5);
        vehicleMapDao.updateById(vehicleMap);
    }

    private void zipSingleLocatingFile(List<File> currentLocatingFileList, String locatingCode, MapInfo mapInfo) throws IOException {
        String mapCode = mapInfo.getVehicleMapCode();
        String parentPath = LOCATING_PUBLISH_PATH + mapCode;
        String locatingZipPath = parentPath + "/" + locatingCode + ".zip";
        String locatingMd5 = null;

        File zipFile = currentLocatingFileList.stream().filter(f -> f.getName().equals(locatingCode + ".zip")).findFirst().orElse(null);
        if (zipFile != null) {
            //删除发布目录里的zip文件
            File publishZipFile = new File(locatingZipPath);
            if(publishZipFile.exists() && publishZipFile.isFile()){
                publishZipFile.delete();
            }
            //新写入一个
            byte[] zipBytes = Files.readAllBytes(Paths.get(zipFile.getPath()));
            FileUtils.writeFile(zipBytes, locatingZipPath);
            //计算新zip文件的md5值
            FileInputStream locatingStream = new FileInputStream(new File(locatingZipPath));
            locatingMd5 = DigestUtils.md5DigestAsHex(locatingStream);
            locatingStream.close();
        }else {
            //1、压缩定位图成zip格式
            FileUtils.deleteFile(new File(locatingZipPath));
            File tmpLocationDirectory = new File(parentPath + "/" + locatingCode);
            if (!tmpLocationDirectory.exists()) {
                tmpLocationDirectory.mkdirs();
            }
            if (!CollectionUtils.isEmpty(currentLocatingFileList)) {
                //筛选出定位图该有的文件
                Set<String> fileNameSet = locatingFileSuffixs.stream().filter(s -> !s.equals(".zip")).map(suffix -> locatingCode + suffix).collect(Collectors.toSet());
                currentLocatingFileList = currentLocatingFileList.stream().filter(f -> fileNameSet.contains(f.getName())).collect(Collectors.toList());
                for (File f : currentLocatingFileList) {
                    Files.copy(Paths.get(f.getPath()), Paths.get(tmpLocationDirectory.getPath() + "/" + f.getName()));
                }
            }
            ZipUtil.zip(tmpLocationDirectory.toString(), locatingZipPath, true);
            FileInputStream locatingStream = new FileInputStream(new File(locatingZipPath));
            locatingMd5 = DigestUtils.md5DigestAsHex(locatingStream);
            locatingStream.close();
            FileUtils.deleteFile(tmpLocationDirectory);
        }

        //2、修改定位图的md5值
        mapInfo.setLocatingMd5(locatingMd5);
        updateMapInfo(mapInfo,mapInfoDao,mapInfo.getVehicleMapCode(),mapInfo.getLocatingCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importMap(MultipartFile multiPartFile) throws Exception {
        byte[] fileByte = null;    //压缩文件
        String tmpDir = null;       //解压临时目录
        File roadNetFile = null;        //压缩包中roadNet文件
        String mapCode = null;      //地图编码

        if (multiPartFile == null) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        fileByte = FileUtils.getByteArray(multiPartFile.getInputStream());
        if (fileByte == null || fileByte.length <= 0) {
            throw new FleetException(I18nMessageUtils.getMessage("system.missing.parameter"));
        }
        String originalFilename = multiPartFile.getOriginalFilename();
        if (!originalFilename.endsWith(MapFileSuffixConstant.MAP_PACKAGE_SUFFIX)) {
            String message = I18nMessageUtils.getMessage("system.name.format.error", originalFilename);
            throw new FleetException(message);
        }

        originalFilename = originalFilename.lastIndexOf(".") >= 0 ? originalFilename.substring(0, originalFilename.lastIndexOf(".")) : originalFilename;
        originalFilename += System.currentTimeMillis();
        tmpDir = LOCATING_PUBLISH_PATH + "tmp/" + originalFilename + "/";

        try {
            //解压到临时文件
            FileUtils.writeZipDataToLocal(tmpDir, new ByteArrayInputStream(fileByte, 0, fileByte.length));
            //校验地图文件
            File tmpDirFile = new File(tmpDir);
            this.checkMapZipFileStructure(tmpDirFile);

            File[] files = tmpDirFile.listFiles();
            mapCode = files[0].getName();

            //压缩包里的文件结构：
            //mapCode/xxx.roadnet、location1...、location2...、location3...
            File sourcePath = files[0];
            files = sourcePath.listFiles();
            roadNetFile = Stream.of(files).filter(f -> f.getName().endsWith(".roadnet") && f.isFile()).findFirst().orElse(null);
            //解析路网数据
            JSONObject roadNetDataJson = JSON.parseObject(FileUtils.readFileData(roadNetFile.getPath()));
            RoadNetData roadNetData = roadNetDataJson.toJavaObject(RoadNetData.class);
            FileUtils.deleteFile(roadNetFile);
            //导入定位图数据
            String locatingDraftPath = LOCATING_DRAFT_PATH + mapCode + "/";
            this.importLocating(sourcePath.getPath(), locatingDraftPath, roadNetData);
            //导入路网数据
            this.importRoadNet(roadNetData, roadNetDataJson);
        } catch (FleetException e) {
            LOGGER.error("操作异常", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("地图导入异常,", e);
            String message = I18nMessageUtils.getMessage("vehicleMap.map.import.error", e.getMessage());
            throw new FleetException(message);
        }finally {
            FileUtils.deleteFile(tmpDir);
        }
    }

    private void checkMapZipFileStructure(File tmpDirFile) {
        if (!tmpDirFile.exists() || !tmpDirFile.isDirectory()) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.import.structure.error");
            throw new FleetException(message);
        }
        File[] files = tmpDirFile.listFiles();
        if (files == null || files.length == 0) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.import.structure.error");
            throw new FleetException(message);
        }
        //mapCode/xxx.roadnet、location1、location2、location3
        files = files[0].listFiles();
        if (files == null || files.length == 0) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.import.structure.error");
            throw new FleetException(message);
        }
        File roadNetFile = Stream.of(files).filter(f -> f.getName().endsWith(".roadnet") && f.isFile()).findFirst().orElse(null);
        if (roadNetFile == null) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.import.in.edit.page.error");
            throw new FleetException(message);
        }
        List<File> infoList = Stream.of(files).filter(f -> f.getName().endsWith(".info") && f.isFile()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(infoList)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.import.missing.info.error");
            throw new FleetException(message);
        }
        List<File> pngList = Stream.of(files).filter(f -> f.getName().endsWith(".png") && f.isFile()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pngList) || pngList.size() < infoList.size()) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.import.missing.png.error");
            throw new FleetException(message);
        }
    }

    private void importLocating(String sourcePath, String destPath, RoadNetData roadNetData) throws IOException {
        List<MapInfoDTO> locatingInfoList = roadNetData.getLocatingInfo();
        if (CollectionUtils.isEmpty(locatingInfoList)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.import.missing.locating.error");
            throw new FleetException(message);
        }
        MapInfoDTO defaultMap = locatingInfoList.stream().filter(MapInfoDTO::isDefault).findFirst().orElse(null);
        if (defaultMap == null) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.import.appoint.default.error");
            throw new FleetException(message);
        }

        //校验导入的地图中的定位图是否重复
        String mapCode = roadNetData.getMapCode();
        for (MapInfoDTO mapInfoDTO : locatingInfoList) {
            //不允许导入Default-White-Map定位图
            String locatingCode = mapInfoDTO.getLocatingCode();
            if (MapConstant.DEFAULT_LOCATING_MAP_CODE.equals(locatingCode)) {
                String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.import.file.is.forbidden.error", MapConstant.DEFAULT_LOCATING_MAP_CODE);
                throw new FleetException(message);
            }
            checkLocatingDuplicate(mapCode, locatingCode);
        }

        //导入激光文件到草稿目录
        FileUtils.deleteFile(destPath);
        FileUtils.copyFolderContent(sourcePath, true, destPath);
        generateZip(destPath);

        VehicleMap vehicleMap = vehicleMapDao.selectById(roadNetData.getMapCode());
        if (vehicleMap == null) {
            vehicleMap = new VehicleMap();
            vehicleMap.setCode(roadNetData.getMapCode());
            vehicleMap.setName(roadNetData.getMapName());
            vehicleMap.setCreatorName(roadNetData.getCreatorName());
            vehicleMap.setCreateTime(roadNetData.getCreateTime());
            vehicleMap.setIsDraft(1);
            vehicleMapDao.insert(vehicleMap);
        } else {
            vehicleMap.setCreatorName(roadNetData.getCreatorName());
            vehicleMap.setCreateTime(roadNetData.getCreateTime());
            vehicleMap.setIsDraft(1);
            vehicleMapDao.updateById(vehicleMap);
        }

        //定位图
        mapInfoDraftDao.delete(new QueryWrapper<MapInfoDraft>().eq("vehicle_map_code", roadNetData.getMapCode()));
        locatingInfoList.forEach(mapInfoDTO -> {
            MapInfoDraft mapInfoDraft = ConvertUtils.sourceToTarget(mapInfoDTO, MapInfoDraft.class);
            mapInfoDraft.setVehicleMapCode(roadNetData.getMapCode());
            mapInfoDraftDao.insert(mapInfoDraft);
        });
    }

    //如果没有zip,则生成
    private void generateZip(String path) {
        File fileList = new File(path);
        File[] fileArray = null;
        if (fileList.exists() && fileList.isDirectory()) {
            fileArray = fileList.listFiles();
        }
        if (fileArray == null || fileArray.length <= 0) {
            return;
        }
        List<File> infoList = Stream.of(fileArray).filter(f -> f.getName().endsWith(".info")).collect(Collectors.toList());
        if(CollUtil.isEmpty(infoList)){
            return;
        }
        for(File info : infoList){
            String locatingCode = info.getName().substring(0, info.getName().lastIndexOf("."));
            //如果当前info对应的定位图有压缩包，则不生成压缩包
            String zipFileName = locatingCode + ".zip";
            File zipFile = Stream.of(fileArray).filter(f -> zipFileName.equals(f.getName())).findFirst().orElse(null);
            if(zipFile != null){
                continue;
            }
            //如果当前定位图相关的文件集合为空，则不生成压缩包
            Set<String> fileNameSet = locatingFileSuffixs.stream().map(suffix -> locatingCode + suffix).collect(Collectors.toSet());
            fileNameSet.removeIf(n -> n.endsWith(".zip"));
            List<File> locatingList = Stream.of(fileArray).filter(f -> fileNameSet.contains(f.getName())).collect(Collectors.toList());
            if (CollUtil.isEmpty(locatingList)) {
                continue;
            }
            File tmpLocationDirectory = new File(path + "/" + locatingCode);
            try {
                if (!tmpLocationDirectory.exists()) {
                    tmpLocationDirectory.mkdirs();
                }
                for (File f : locatingList) {
                    try {
                        Files.copy(Paths.get(f.getPath()), Paths.get(tmpLocationDirectory.getPath() + "/" + f.getName()));
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                File locatingZipFile = new File(path + "/" + zipFileName);
                ZipUtil.zip(tmpLocationDirectory.toString(), locatingZipFile.toString(), true);
            }finally {
                FileUtils.deleteFile(tmpLocationDirectory);
            }
        }
    }

    private void importRoadNet(RoadNetData roadNetData, JSONObject roadNetDataJson) {
        List<Marker> markers = roadNetData.getMarkers();
        markerDraftDao.delete(new QueryWrapper<MarkerDraft>().eq("vehicle_map_code", roadNetData.getMapCode()));
        if (!CollectionUtils.isEmpty(markers)) {
            JSONArray markerArray = roadNetDataJson.getJSONArray("markers");
            Map<String, JSONObject> markerMap = new HashMap<>();
            for (int i = 0; i < markerArray.size(); i++) {
                JSONObject markerJson = markerArray.getJSONObject(i);
                markerMap.put(markerJson.getString("code"), markerJson);
            }

            List<MarkerDraft> markerDrafts = ConvertUtils.sourceToTarget(markers, MarkerDraft.class);
            //设置默认值
            markerDrafts.stream().filter(m -> m.getParams() == null).forEach(marker -> marker.setParams(new MarkerParam()));
            markerDrafts.stream().filter(m -> m.getChargeProp() == null).forEach(marker -> marker.setChargeProp(new MarkerChargeProp()));
            //兼容5.0.13之前版本的老数据
            markerDrafts.stream().filter(m -> MARKER_TYPE_CHARGING.equals(m.getType())).forEach(marker -> {
                String markerCode = marker.getCode();
                JSONObject markerJson = markerMap.get(markerCode);
                if(marker.getChargeProp().getChargeType() == null){
                    marker.getChargeProp().setChargeType(CHARGE_TYPE_SMART_CHARGE);
                }
                if(marker.getChargeProp().getDockingType() == null){
                    String dockingType = Optional.ofNullable(markerJson).map(m -> m.getString("dockingType")).orElse("Vpoint");
                    marker.getChargeProp().setDockingType(dockingType);
                }
                if(marker.getChargeProp().getDockingDirection() == null){
                    Integer dockingDirection = Optional.ofNullable(markerJson).map(m -> m.getIntValue("dockingDirection")).orElse(1);
                    marker.getChargeProp().setDockingDirection(dockingDirection);
                }
            });
            dbUtils.insertBatchById(MarkerDraft.class, markerDrafts, 200);
        }

        List<Path> paths = roadNetData.getPaths();
        pathDraftDao.delete(new QueryWrapper<PathDraft>().eq("vehicle_map_code", roadNetData.getMapCode()));
        if (!CollectionUtils.isEmpty(paths)) {
            paths.stream().filter(p -> p.getExtendParamList() == null).forEach(p -> p.setExtendParamList(new ArrayList<>()));
            List<PathDraft> pathDrafts = ConvertUtils.sourceToTarget(paths, PathDraft.class);
            dbUtils.insertBatchById(PathDraft.class,pathDrafts,200);
        }

        List<MapAreaDTO> mapAreaDTOS = roadNetData.getMapAreas();
        mapAreaDraftDao.delete(new QueryWrapper<MapAreaDraft>().eq("vehicle_map_code", roadNetData.getMapCode()));
        if (!CollectionUtils.isEmpty(mapAreaDTOS)) {
            List<MapAreaDraft> mapAreaDrafts = ConvertUtils.sourceToTarget(mapAreaDTOS, MapAreaDraft.class);
            dbUtils.insertBatchById(MapAreaDraft.class,mapAreaDrafts,200);
        }

        List<AutoDoorDTO> autoDoorDTOS = roadNetData.getAutoDoors();
        autoDoorService.deleteDraftByVehicleMapCode(roadNetData.getMapCode());
        if (!CollectionUtils.isEmpty(autoDoorDTOS)) {
            List<AutoDoorDraft> autoDoorDrafts = AutoDoorUtils.autoDoorDTOToAutoDoorDraft(autoDoorDTOS);
            dbUtils.insertBatchById(AutoDoorDraft.class,autoDoorDrafts,200);
        }

        List<AirShowerDoorDTO> airShowerDoorDTOS = roadNetData.getAirShowerDoors();
        airShowerDoorService.deleteDraftByVehicleMapCode(roadNetData.getMapCode());
        if (!CollectionUtils.isEmpty(airShowerDoorDTOS)) {
            airShowerDoorDTOS.stream().forEach(airShowerDoorDTO -> {
                AirShowerDoorDraft airShowerDoorDraft = ConvertUtils.sourceToTarget(airShowerDoorDTO, AirShowerDoorDraft.class);
                List<AutoDoorDTO> autoDoorDTOS1 = airShowerDoorDTO.getDoors();
                if (!CollectionUtils.isEmpty(autoDoorDTOS1)) {
                    List<AutoDoorDraft> autoDoorDrafts = AutoDoorUtils.autoDoorDTOToAutoDoorDraft(autoDoorDTOS1);
                    autoDoorDrafts.stream().forEach(autoDoor -> {
                        autoDoor.setAsdCode(airShowerDoorDTO.getCode());
                        autoDoorDraftDao.insert(autoDoor);
                    });
                }
                airShowerDoorDraftDao.insert(airShowerDoorDraft);
            });
        }
    }

    private RoadNetData getMapRoadNetData(String mapCode, boolean isDraft) {
        RoadNetData roadNetData = new RoadNetData();
        roadNetData.setMapCode(mapCode);
        VehicleMap vehicleMap = vehicleMapDao.selectById(mapCode);
        roadNetData.setMapName(vehicleMap.getName());
        roadNetData.setCreatorName(vehicleMap.getCreatorName());
        roadNetData.setCreateTime(vehicleMap.getCreateTime());

        List<MapInfoDTO> mapInfoDTOList = this.getMapInfoList(mapCode, isDraft);
        if (mapInfoDTOList == null) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.locatingmap.is.empty.error", mapCode);
            throw new FleetException(message);
        }
        roadNetData.setLocatingInfo(mapInfoDTOList);

        List<Marker> markers = markerService.selectByVehicleMapCode(mapCode, isDraft);
        roadNetData.setMarkers(markers);
        List<Path> paths = pathService.selectByVehicleMapCode(mapCode, isDraft);
        roadNetData.setPaths(paths);

        List<MapArea> mapAreas = mapAreaService.selectByVehicleMapCode(mapCode, isDraft);
        if (!CollectionUtils.isEmpty(mapAreas)) {
            roadNetData.setMapAreas(ConvertUtils.sourceToTarget(mapAreas, MapAreaDTO.class));
        }
        List<AirShowerDoorDTO> airShowerDoorDTOS = airShowerDoorService.selectByVehicleMapCode(mapCode, isDraft);
        if (!CollectionUtils.isEmpty(airShowerDoorDTOS)) {
            roadNetData.setAirShowerDoors(airShowerDoorDTOS);
        }
        List<AutoDoorDTO> autoDoorDTOS = autoDoorService.selectByVehicleMapCode(mapCode, isDraft);
        if (!CollectionUtils.isEmpty(autoDoorDTOS)) {
            roadNetData.setAutoDoors(autoDoorDTOS);
        }
        return roadNetData;
    }

    @Override
    public void export(HttpServletResponse response, String mapCode, boolean isDraft) {
        byte[] bytes = null;
        String roadNetFilePath = null;
        ByteArrayOutputStream outputStream = null;
        try {
            /**
             * 生成路网文件
             */
            RoadNetData roadNetData = this.getMapRoadNetData(mapCode, isDraft);
            String locatingMapCurrentPath = MapFileUtils.LOCATING_CURRENT_PATH + mapCode;
            String locatingMapDraftPath = MapFileUtils.LOCATING_DRAFT_PATH + mapCode;
            if (isDraft) {
                if (!new File(locatingMapDraftPath).exists()) {
                    FileUtils.createLocalDir(locatingMapDraftPath);
                }
                roadNetFilePath = locatingMapDraftPath + "/" + mapCode + ".roadnet";
                File roadNetFile = new File(roadNetFilePath);
                if (!roadNetFile.exists()) {
                    roadNetFile.createNewFile();
                }
                FormatJsonUtils.writeFile(JSON.toJSONString(roadNetData), roadNetFile);
                outputStream = new ByteArrayOutputStream();
                ZipUtils.toZip(locatingMapDraftPath, outputStream, true);
                bytes = outputStream.toByteArray();
            } else {
                if (!new File(locatingMapCurrentPath).exists()) {
                    FileUtils.createLocalDir(locatingMapCurrentPath);
                }
                roadNetFilePath = locatingMapCurrentPath  + "/" + mapCode + ".roadnet";
                File roadNetFile = new File(roadNetFilePath);
                if (!roadNetFile.exists()) {
                    roadNetFile.createNewFile();
                }
                FormatJsonUtils.writeFile(JSON.toJSONString(roadNetData), roadNetFile);
                outputStream = new ByteArrayOutputStream();
                ZipUtils.toZip(locatingMapCurrentPath, outputStream, true);
                bytes = outputStream.toByteArray();
            }
            //返回前端数据
            String fileName = new String(mapCode.getBytes(), StandardCharsets.ISO_8859_1) + ".zip";
            FileUtils.writeBytesToResponse(response, bytes, fileName);
        } catch (Exception e) {
            LOGGER.error("地图导出异常，{}", mapCode, e);
            String message = I18nMessageUtils.getMessage("vehicleMap.map.export.error", e.getMessage());
            throw new FleetException(message);
        } finally {
            FileUtils.deleteFile(new File(roadNetFilePath));
            try {
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyMap(CopyMapDTO copyMapDTO) throws IOException {
        String originMapCode = copyMapDTO.getOriginCode();
        String newMapCode = copyMapDTO.getCode();
        String originFolderLocating = LOCATING_CURRENT_PATH + originMapCode + "/";
        String targetFolder = LOCATING_DRAFT_PATH + newMapCode + "/";
        try {
            VehicleMap newVehicleMap = VehicleMap.builder().code(newMapCode).isDraft(1).isProd(0)
                    .createTime(new Date()).creatorName(SecurityUser.getUser().getRealName()).build();
            newVehicleMap.setName(copyMapDTO.getMapName());
            vehicleMapDao.insert(newVehicleMap);
            List<MapInfo> originMapInfos = getListByVehicleMapCode(originMapCode,mapInfoDao);
            if(!CollectionUtils.isEmpty(originMapInfos)){
                originMapInfos.forEach(it->{
                    MapInfoDraft newMapInfo = new MapInfoDraft();
                    BeanUtils.copyPropertiesIgnoreNull(it, newMapInfo);
                    newMapInfo.setVehicleMapCode(newMapCode);
                    newMapInfo.setLocatingMd5(null);
                    mapInfoDraftDao.insert(newMapInfo);
                });
            }

            List<Marker> originMarkers = markerService.selectByVehicleMapCode(originMapCode, false);
            if (!CollectionUtils.isEmpty(originMarkers)) {
                originMarkers.stream().forEach(originMarker -> {
                    originMarker.setCode(originMarker.getCode().replace(originMapCode,newMapCode));
                    originMarker.setVehicleMapCode(newMapCode);
                    markerDraftDao.insert(ConvertUtils.sourceToTarget(originMarker, MarkerDraft.class));
                });
            }
            List<Path> originPaths = pathService.selectByVehicleMapCode(originMapCode, false);
            if (!CollectionUtils.isEmpty(originPaths)) {
                originPaths.stream().forEach(originPath -> {
                    originPath.setCode(originPath.getCode().replace(originMapCode,newMapCode));
                    originPath.setVehicleMapCode(newMapCode);
                    originPath.setStartMarkerCode(originPath.getStartMarkerCode().replace(originMapCode,newMapCode));
                    originPath.setEndMarkerCode(originPath.getEndMarkerCode().replace(originMapCode,newMapCode));
                    pathDraftDao.insert(ConvertUtils.sourceToTarget(originPath, PathDraft.class));
                });
            }
            List<MapArea> originMapAreas = mapAreaService.selectByVehicleMapCode(originMapCode, false);
            if (!CollectionUtils.isEmpty(originMapAreas)) {
                originMapAreas.stream().forEach(originMapArea -> {
                    originMapArea.setCode(originMapArea.getCode().replace(originMapCode,newMapCode));
                    originMapArea.setVehicleMapCode(newMapCode);
                    mapAreaDraftDao.insert(ConvertUtils.sourceToTarget(originMapArea, MapAreaDraft.class));
                });
            }
            List<AutoDoor> originAutoDoors = autoDoorService.selectByVehicleMapCode(originMapCode);
            if (!CollectionUtils.isEmpty(originAutoDoors)) {
                originAutoDoors.stream().forEach(originAutoDoor -> {
                    originAutoDoor.setCode(originAutoDoor.getCode().replace(originMapCode,newMapCode));
                    originAutoDoor.setVehicleMapCode(newMapCode);
                    originAutoDoor.setPathCodes(originAutoDoor.getPathCodes().replace(originMapCode,newMapCode));
                    autoDoorDraftDao.insert(ConvertUtils.sourceToTarget(originAutoDoor, AutoDoorDraft.class));
                });
            }
            List<AirShowerDoor> originAirShowerDoors = airShowerDoorService.selectByVehicleMapCode(originMapCode);
            if (!CollectionUtils.isEmpty(originAirShowerDoors)) {
                originAirShowerDoors.stream().forEach(originAirShowerDoor -> {
                    QueryWrapper<AutoDoor> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("asd_code",originAirShowerDoor.getCode());
                    List<AutoDoor> originAirAutoDoors = autoDoorDao.selectList(queryWrapper);
                    String newAirCode = originAirShowerDoor.getCode().replace(originMapCode,newMapCode);
                    originAirAutoDoors.stream().forEach(autoDoor -> {
                        autoDoor.setCode(UUID.randomUUID().toString());
                        autoDoor.setVehicleMapCode(newMapCode);
                        autoDoor.setPathCodes(autoDoor.getPathCodes().replace(originMapCode,newMapCode));
                        autoDoor.setAsdCode(newAirCode);
                        autoDoorDraftDao.insert(ConvertUtils.sourceToTarget(autoDoor, AutoDoorDraft.class));
                    });
                    originAirShowerDoor.setCode(newAirCode);
                    originAirShowerDoor.setVehicleMapCode(newMapCode);
                    airShowerDoorDraftDao.insert(ConvertUtils.sourceToTarget(originAirShowerDoor, AirShowerDoorDraft.class));
                });
            }

            /**
             * 定位文件复制
             */
            //复制定位文件
            FileUtils.copyFolderContent(originFolderLocating, true, targetFolder);
            //修改info文件
            File newFile = new File(targetFolder + newMapCode + "/");
            File[] newFiles = newFile.listFiles();
            if (newFiles == null || newFiles.length == 0) {
                return;
            }
            List<File> locationList = Stream.of(newFiles).filter(File::isDirectory).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(locationList)) {
                return;
            }
            locationList.forEach(directory -> {
                File[] fs = directory.listFiles();
                if (fs == null || fs.length == 0) {
                    return;
                }
                File infoFile = Stream.of(fs).filter(f -> f.isFile() && f.getName().endsWith(".info")).findFirst().orElse(null);
                if (infoFile != null) {
                    String infoFilePath = infoFile.getPath();
                    String infoStr = FileUtils.readFileData(infoFilePath);
                    if(!StringUtils.isEmpty(infoStr)) {
                        MapInfoDraft mapInfoDraft = JSON.parseObject(infoStr, MapInfoDraft.class);
                        mapInfoDraft.setVehicleMapCode(newMapCode);
                        FormatJsonUtils.writeFile(JSONObject.toJSONString(mapInfoDraft), infoFilePath);
                    }
                }
            });
        } catch (Exception e) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.copy.error", e.getMessage());
            throw new FleetException(message);
        }
    }

    /**
     * 地图编辑页面：查询正式、草稿
     * 模糊查询当前地图上的元素
     * 数据范围：支持搜索点位名称、点位编码、电梯编码、自动门编码、风淋门编码、区域
     */
    @Override
    public List<MapElementDTO> getElements(MapElementQueryDTO query) {
        String vehicleMapCode = query.getVehicleMapCode();
        if (query.getIsDraft() == null) {
            query.setIsDraft(false);
        }

        List<MapElementDTO> elements = new ArrayList<>();
        MapGraphInfo mapGraphInfo = getMapGraphInfo(vehicleMapCode, query.getIsDraft());
        addMatchedElementList(query,Lists.newArrayList(mapGraphInfo),elements);
        addMatchedElevatorList(query,elevatorService.selectList(),elements);

        //处理元素值
        resolveElements(query,elements,mapGraphInfo.getAgvMap().getDefaultLocatingCode());
        //排序
        sort(elements);
        return elements;
    }

    /**
     * 监控大屏页面：查询正式
     * 模糊查询当前地图上的元素
     * 数据范围：支持搜索点位名称、点位编码、电梯编码、自动门编码、风淋门编码、区域、小车
     */
    @Override
    public ElementsQueryDTO getMonitorElements(MapElementQueryDTO query) {
        List<MapElementDTO> elements = new ArrayList<>();
        MapGraphInfo mapGraphInfo = getMapGraphInfo(query.getVehicleMapCode(), false);

        addMatchedElementList(query, Lists.newArrayList(mapGraphInfo), elements);
        //电梯包含多地图，比较麻烦，因此去掉
        //addMatchedElevatorList(query,elevatorService.selectList(),matchedList);
        addMatchedVehicleList(query,defaultVehiclePool.getAll(),elements);

        //处理元素值
        resolveElements(query,elements,mapGraphInfo.getAgvMap().getDefaultLocatingCode());
        //设置是否属于当前地图
        elements.forEach(ele -> ele.setIsCurrentMap(!StringUtils.isEmpty(ele.getVehicleMapCode()) && ele.getVehicleMapCode().equalsIgnoreCase(query.getVehicleMapCode())));
        //排序
        sort(elements);

        //返回各类型的数量
        Map<String, List<MapElementDTO>> collect = elements.stream().collect(Collectors.groupingBy(MapElementDTO::getType));
        Map<String, Integer> map = new HashMap<>(collect.size());
        AtomicInteger all = new AtomicInteger(0);
        collect.forEach((k, v) -> {
            map.put(k, v.size());
            all.addAndGet(v.size());
        });
        map.put("All", all.get());

        if (!elements.isEmpty()) {
            elements = elements.subList(0, Math.min(elements.size(), query.getSize() == null ? 30 : query.getSize()));
        }
        return ElementsQueryDTO.builder().elements(elements).countMap(map).build();
    }

    private void resolveElements(MapElementQueryDTO query,List<MapElementDTO> elements,String defaultLocatingCode){
        //过滤类型
        if (!StringUtils.isEmpty(query.getType())) {
            elements.removeIf(ele -> !ele.getType().equals(query.getType()));
        }
        String queryLocatingCode = query.getLocatingCode();
        if(!CollectionUtils.isEmpty(elements)){
            //将点位数据转换成对应定位图上的数据
            elements.forEach(ele->{
                if("Marker".equals(ele.getType())){
                    Marker marker = JSON.parseObject(JSON.toJSONString(ele.getData()),Marker.class);
                    MarkerDTO markerDTO = MapUtils.convertToMarkerDTO(queryLocatingCode, marker);
                    ele.setData(markerDTO);
                }
            });
            //区域只在默认定位图上显示
            if(!Objects.equals(queryLocatingCode,defaultLocatingCode)){
                elements.removeIf(it->"MapArea".equals(it.getType()));
            }
        }
    }

    private MapGraphInfo getMapGraphInfo(String vehicleMapCode, Boolean isDraft) {
        MapGraphInfo mapGraphInfo = new MapGraphInfo();
        mapGraphInfo.setAgvMap(selectByCode(vehicleMapCode,isDraft));
        mapGraphInfo.setMarkers(markerService.selectByVehicleMapCode(vehicleMapCode, isDraft).stream().collect(Collectors.toMap(Marker::getCode, Function.identity())));
        mapGraphInfo.setPaths(pathService.selectByVehicleMapCode(vehicleMapCode, isDraft).stream().collect(Collectors.toMap(Path::getCode, Function.identity())));
        mapGraphInfo.setMapAreas(mapAreaService.selectByVehicleMapCode(vehicleMapCode, isDraft).stream().collect(Collectors.toMap(MapArea::getCode, Function.identity())));
        List<AutoDoorDTO> autoDoorDTOS = autoDoorService.selectByVehicleMapCode(vehicleMapCode, isDraft);
        mapGraphInfo.setAutoDoors(ConvertUtils.sourceToTarget(autoDoorDTOS, AutoDoor.class).stream().collect(Collectors.toMap(AutoDoor::getCode, Function.identity())));
        List<AirShowerDoorDTO> airShowerDoorDTOS = airShowerDoorService.selectByVehicleMapCode(vehicleMapCode, isDraft);
        mapGraphInfo.setAirShowerDoors(ConvertUtils.sourceToTarget(airShowerDoorDTOS, AirShowerDoor.class).stream().collect(Collectors.toMap(AirShowerDoor::getCode, Function.identity())));
        return mapGraphInfo;
    }

    private void match(String keyWord, String value, Object data, String type, Integer sortType, String vehicleMapCode,List<MapElementDTO> dataMap){
        if (value == null) {
            return;
        }

        String code = value;
        if(data instanceof Marker){
            Marker m = (Marker)data;
            code = m.getCode();
        }

        final String code1 = code;
        long count = dataMap.stream().filter(it -> it.getCode().equals(code1)).count();
        if (count > 0) {
            return;
        }
        if(StringUtils.isEmpty(keyWord) || keyWord.equals(value)){
            MapElementDTO mapElement = MapElementDTO.builder().code(code).data(data).matchType(MapElementDTO.MATCHTYPE_ACCURATE).type(type).sortType(sortType).vehicleMapCode(vehicleMapCode).build();
            dataMap.add(mapElement);
            return;
        }
        if (value.toLowerCase().contains(keyWord.toLowerCase())) {
            MapElementDTO mapElement = MapElementDTO.builder().code(code).data(data).matchType(MapElementDTO.MATCHTYPE_DIM).type(type).sortType(sortType).vehicleMapCode(vehicleMapCode).build();
            dataMap.add(mapElement);
        }
    }

    private List<MapElementDTO> addMatchedElementList(MapElementQueryDTO query,List<MapGraphInfo> mapInfoList,List<MapElementDTO> dataMap){
        if (!CollectionUtils.isEmpty(mapInfoList)) {
            mapInfoList.forEach(mapGraphInfo -> {
                if (!CollectionUtils.isEmpty(mapGraphInfo.getMarkers())) {
                    mapGraphInfo.getMarkers().values().forEach(item -> {
                        match(query.getKeyWord(),item.getCode(),item,"Marker",6,item.getVehicleMapCode(),dataMap);
                        match(query.getKeyWord(),item.getName(),item,"Marker",5,item.getVehicleMapCode(),dataMap);
                    });
                }
                if (!CollectionUtils.isEmpty(mapGraphInfo.getAutoDoors())) {
                    mapGraphInfo.getAutoDoors().values().forEach(item -> {
                        match(query.getKeyWord(),item.getCode(),item,"AutoDoor",2,item.getVehicleMapCode(),dataMap);
                    });
                }
                if (!CollectionUtils.isEmpty(mapGraphInfo.getAirShowerDoors())) {
                    mapGraphInfo.getAirShowerDoors().values().forEach(item -> {
                        match(query.getKeyWord(),item.getCode(),item,"AirShowerDoor",3,item.getVehicleMapCode(),dataMap);
                    });
                }
                if (!CollectionUtils.isEmpty(mapGraphInfo.getMapAreas())) {
                    mapGraphInfo.getMapAreas().values().forEach(item -> {
                        match(query.getKeyWord(),item.getCode(),item,"MapArea",4,item.getVehicleMapCode(),dataMap);
                    });
                }
            });
        }
        return dataMap;
    }

    private List<MapElementDTO> addMatchedElevatorList(MapElementQueryDTO query,List<ElevatorDTO> elevators,List<MapElementDTO> dataMap){
        if (!CollectionUtils.isEmpty(elevators)) {
            elevators.forEach(item -> {
                match(query.getKeyWord(),item.getCode(),item,"Elevator",1,"",dataMap);
            });
        }
        return dataMap;
    }

    private List<MapElementDTO> addMatchedVehicleList(MapElementQueryDTO query,List<Vehicle> vehicleList,List<MapElementDTO> dataMap){
        if (!CollectionUtils.isEmpty(vehicleList)) {
            vehicleList.forEach(item -> {
                VehicleDetailDTO dto = ConvertUtils.sourceToTarget(item,VehicleDetailDTO.class);
                dto.setVehicleStatus(item,query.getLocatingCode());
                match(query.getKeyWord(),item.getVehicleCode(),dto,"Vehicle",0,item.getVehicleMapCode(),dataMap);
            });
        }
        return dataMap;
    }

    private void sort(List<MapElementDTO> all) {
        if (CollectionUtils.isEmpty(all)) {
            return;
        }
        all.sort(Comparator.comparing(MapElementDTO::getMatchType)
                .thenComparing(MapElementDTO::getSortType)
                .thenComparing(MapElementDTO::getMatchLength)
                .thenComparing(i -> i.getCode().length())
                .thenComparing(MapElementDTO::getCode));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editMap(String vehicleMapCode) {
        VehicleMap vehicleMap = vehicleMapDao.selectById(vehicleMapCode);
        if (Objects.isNull(vehicleMap)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.not.exist.error", vehicleMapCode);
            throw new FleetException(message);
        }
        if (!BooleanUtils.toBoolean(vehicleMap.getIsDraft())) {
            /**
             * 获取正式表数据插入草稿表
             */
            this.roadNetCurrentToDraft(vehicleMapCode);
            /**
             * 复制激光地图到草稿目录
             */
            try {
                MapFileUtils.locatingCurrentToDraft(vehicleMapCode);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        /**
         * 更新地图表vehicle_map
         */
        vehicleMap.setIsDraft(1);
        vehicleMap.setEditTime(new Date());
        vehicleMapDao.updateById(vehicleMap);
    }

    @Override
    public void processUndo(String mapCode,EditMapAction editMapAction) {
        Integer actionType = editMapAction.getActionType();
        switch (actionType) {
            //该步骤为新增时，处理undo需要“删除”新增的元素
            case MapConstant.CREATE_ELEMENTS:
                this.deleteElementsAndSendMessage(mapCode, editMapAction.getPreMarkers(), editMapAction.getPrePaths(), editMapAction.getPreMapAreas());
                break;
            //该步骤为更新时，处理undo需要重新将元素“更新”
            case MapConstant.UPDATE_ELEMENTS:
                this.updateElementsAndSendMessage(mapCode, editMapAction.getOriMarkers(), editMapAction.getOriPaths(), editMapAction.getOriMapAreas());
                break;
            case MapConstant.DELETE_ELEMENTS:
                this.insertElementsAndSendMessage(mapCode, editMapAction.getOriMarkers(), editMapAction.getOriPaths(), editMapAction.getOriMapAreas());
                break;
            default:
                break;
        }
    }

    @Override
    public void processRedo(String mapCode,EditMapAction editMapAction) {
        Integer actionType = editMapAction.getActionType();
        switch (actionType) {
            //该步骤为新增时，处理undo需要“删除”新增的元素
            case MapConstant.CREATE_ELEMENTS:
                this.insertElementsAndSendMessage(mapCode, editMapAction.getPreMarkers(), editMapAction.getPrePaths(), editMapAction.getPreMapAreas());
                break;
            //该步骤为更新时，处理undo需要重新将元素“更新”
            case MapConstant.UPDATE_ELEMENTS:
                this.updateElementsAndSendMessage(mapCode, editMapAction.getPreMarkers(), editMapAction.getPrePaths(), editMapAction.getPreMapAreas());
                break;
            case MapConstant.DELETE_ELEMENTS:
                this.deleteElementsAndSendMessage(mapCode, editMapAction.getOriMarkers(), editMapAction.getOriPaths(), editMapAction.getOriMapAreas());
                break;
            default:
                break;
        }
    }

    private void insertElementsAndSendMessage(String mapCode, List<MarkerDraft> markers, List<PathDraft> paths, List<MapAreaDraft> mapAreas) {
        if (!CollectionUtils.isEmpty(markers)) {
            dbUtils.insertBatchById(MarkerDraft.class, markers, 500);
        }
        List<PathDraft> processPaths = new ArrayList<>();
        if (!CollectionUtils.isEmpty(paths)) {
            dbUtils.insertBatchById(PathDraft.class, paths, 500);
            //特殊处理，当增加的路径有反向路径，也要保证反向路径能传到前端
            Set<String> startEndCodeSet = paths.stream().map(p -> p.getStartMarkerCode() + "_" + p.getEndMarkerCode()).collect(Collectors.toSet());
            for (PathDraft pd : paths) {
                processPaths.add(pd);
                String key = pd.getEndMarkerCode() + "_" + pd.getStartMarkerCode();
                if (!startEndCodeSet.contains(key)) {
                    List<Path> reversePaths = pathService.selectByStartMarkerCodeAndEndMarkerCode(pd.getEndMarkerCode(), pd.getStartMarkerCode(), true);
                    if (!CollectionUtils.isEmpty(reversePaths)) {
                        PathDraft reversePathDraft = ConvertUtils.sourceToTarget(reversePaths.get(0), PathDraft.class);
                        processPaths.add(reversePathDraft);
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(mapAreas)) {
            dbUtils.insertBatchById(MapAreaDraft.class, mapAreas, 500);
        }
        sendWsMessageAsync(mapCode, ConvertUtils.sourceToTarget(markers, Marker.class), ConvertUtils.sourceToTarget(processPaths, Path.class), ConvertUtils.sourceToTarget(mapAreas, MapArea.class));
    }

    private void deleteElementsAndSendMessage(String mapCode, List<MarkerDraft> markers, List<PathDraft> paths, List<MapAreaDraft> mapAreas) {
        if (!CollectionUtils.isEmpty(paths)) {
            List<String> pathCodes = paths.stream().map(PathDraft::getCode).collect(Collectors.toList());
            pathDraftDao.deleteBatchIds(pathCodes);
        }
        if (!CollectionUtils.isEmpty(markers)) {
            List<String> markerCodes = markers.stream().map(MarkerDraft::getCode).collect(Collectors.toList());
            markerDraftDao.deleteBatchIds(markerCodes);
        }
        if (!CollectionUtils.isEmpty(mapAreas)) {
            List<String> mapAreaCodes = mapAreas.stream().map(MapAreaDraft::getCode).collect(Collectors.toList());
            mapAreaDraftDao.deleteBatchIds(mapAreaCodes);
        }
        sendWebSocketMessageAsync(mapCode, markers, paths, mapAreas);
    }

    private void updateElementsAndSendMessage(String mapCode, List<MarkerDraft> markerDrafts, List<PathDraft> pathDrafts, List<MapAreaDraft> mapAreaDrafts) {
        if (!CollectionUtils.isEmpty(markerDrafts)) {
            dbUtils.updateBatchById(MarkerDraft.class,markerDrafts,50);
        }
        if (!CollectionUtils.isEmpty(pathDrafts)) {
            dbUtils.updateBatchById(PathDraft.class,pathDrafts,50);
        }
        if (!CollectionUtils.isEmpty(mapAreaDrafts)) {
            dbUtils.updateBatchById(MapAreaDraft.class,mapAreaDrafts,50);
        }
        //发送消息到打开当前地图的窗口
        SocketMessageModel messageModel = new SocketMessageModel().update();
        List<Marker> markers = ConvertUtils.sourceToTarget(markerDrafts, Marker.class);
        messageModel.setMarkers(markers);
        List<Path> paths = ConvertUtils.sourceToTarget(pathDrafts, Path.class);
        messageModel.setPaths(paths);
        List<MapArea> mapAreas = ConvertUtils.sourceToTarget(mapAreaDrafts, MapArea.class);
        messageModel.setMapAreas(mapAreas);
        MapUpdateSocketController.sendMessage(mapCode, messageModel);
    }

    private void roadNetCurrentToDraft (String vehicleMapCode) {
        List<MapInfo> mapInfoList = getListByVehicleMapCode(vehicleMapCode,mapInfoDao);
        if (!CollectionUtils.isEmpty(mapInfoList)) {
            List<MapInfoDraft> mapInfoDraftList = ConvertUtils.sourceToTarget(mapInfoList, MapInfoDraft.class);
            mapInfoDraftList.forEach(mapInfoDraftDao::insert);
        }
        List<Marker> markers = markerService.selectByVehicleMapCode(vehicleMapCode,false);
        if (!CollectionUtils.isEmpty(markers)) {
            List<MarkerDraft> markerDrafts = ConvertUtils.sourceToTarget(markers, MarkerDraft.class);
            markerDrafts.stream().forEach(markerDraft -> {
                markerDraftDao.insert(markerDraft);
            });
        }
        List<Path> paths = pathService.selectByVehicleMapCode(vehicleMapCode, false);
        if (!CollectionUtils.isEmpty(paths)) {
            List<PathDraft> pathDrafts = ConvertUtils.sourceToTarget(paths, PathDraft.class);
            pathDrafts.stream().forEach(pathDraft -> {
                pathDraftDao.insert(pathDraft);
            });
        }
        List<MapArea> mapAreas = mapAreaService.selectByVehicleMapCode(vehicleMapCode, false);
        if (!CollectionUtils.isEmpty(mapAreas)) {
            List<MapAreaDraft> mapAreaDrafts = ConvertUtils.sourceToTarget(mapAreas, MapAreaDraft.class);
            mapAreaDrafts.stream().forEach(mapAreaDraft -> {
                mapAreaDraftDao.insert(mapAreaDraft);
            });
        }
        List<AutoDoor> allAutoDoors = this.getListByVehicleMapCode(vehicleMapCode, autoDoorDao);
        if (!CollectionUtils.isEmpty(allAutoDoors)) {
            List<AutoDoorDraft> doorDraftList = ConvertUtils.sourceToTarget(allAutoDoors, AutoDoorDraft.class);
            doorDraftList.forEach(autoDoorDTO -> {
                autoDoorDraftDao.insert(autoDoorDTO);
            });
        }
        List<AirShowerDoorDTO> airShowerDoorDTOS = airShowerDoorService.selectByVehicleMapCode(vehicleMapCode, false);
        if (!CollectionUtils.isEmpty(airShowerDoorDTOS)) {
            airShowerDoorDTOS.stream().forEach(airShowerDoorDTO -> {
                AirShowerDoorDraft airShowerDoorDraft = ConvertUtils.sourceToTarget(airShowerDoorDTO, AirShowerDoorDraft.class);
                airShowerDoorDraftDao.insert(airShowerDoorDraft);
            });
        }
    }

    /**
     * 暂停是异步操作
     * @param mapCode 如果不传，就暂停当前系统的所有地图
     */
    @Override
    public void pauseMap(String mapCode) {
        List<VehicleMap> allVehicleMap = this.getAllVehicleMap(new ArrayList<>());
        Set<String> mapCodes = allVehicleMap.stream().map(VehicleMap::getCode).collect(Collectors.toSet());
        if (!StringUtils.isEmpty(mapCode) && !mapCodes.contains(mapCode)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.not.exist.error", mapCode);
            throw new FleetException(message);
        }
        if (!StringUtils.isEmpty(mapCode)) {
            mapCodes = Sets.newHashSet(mapCode);
        }
        LOGGER.debug("暂停的地图：{}", mapCodes);
        //修改数据库
        LambdaUpdateWrapper updateWrapper = new LambdaUpdateWrapper<VehicleMap>()
                .set(VehicleMap::getPauseStatus, MapConstant.MAP_PAUSE_STATUS_PAUSING)
                .in(VehicleMap::getCode, mapCodes);
        vehicleMapDao.update(null, updateWrapper);
    }

    /**
     * 恢复是同步操作
     * @param mapCode 如果不传，就恢复当前系统的所有地图
     * @return
     */
    @Override
    public List<VehiclePauseResultDTO> recoverMap(String mapCode) {
        List<VehicleMap> allVehicleMap = this.getAllVehicleMap(new ArrayList<>());
        Set<String> mapCodes = allVehicleMap.stream().map(VehicleMap::getCode).collect(Collectors.toSet());
        if (!StringUtils.isEmpty(mapCode) && !mapCodes.contains(mapCode)) {
            String message = I18nMessageUtils.getMessage("vehicleMap.map.not.exist.error", mapCode);
            throw new FleetException(message);
        }
        if (!StringUtils.isEmpty(mapCode)) {
            mapCodes = Sets.newHashSet(mapCode);
        }

        //由于地图暂停线程在不断轮询，所以可能某一刻抢不到锁，需要多尝试几次
        boolean lockResult = MapPauseUtil.applyLock(mapCodes);
        long start = System.currentTimeMillis();
        while(!lockResult){
            ThreadUtils.sleep(100);
            lockResult = MapPauseUtil.applyLock(mapCodes);
            if(lockResult || System.currentTimeMillis() - start >= 10 * 1000){
                break;
            }
        }
        if (!lockResult) {
            throw new FleetException(I18nMessageUtils.getMessage("vehicleMap.map.operating.duplicate.error"));
        }
        List<VehiclePauseResultDTO> list;
        try {
            LOGGER.debug("恢复的地图：{}", mapCodes);
            list = this.getHandleVehicleResult(mapCodes);
            //修改数据库
            LambdaUpdateWrapper updateWrapper = new LambdaUpdateWrapper<VehicleMap>()
                    .set(VehicleMap::getPauseStatus, MapConstant.MAP_PAUSE_STATUS_NORMAL)
                    .in(VehicleMap::getCode, mapCodes);
            vehicleMapDao.update(null, updateWrapper);
        }finally {
            MapPauseUtil.release(mapCodes);
        }
        return list;
    }

    private List<VehiclePauseResultDTO> getHandleVehicleResult(Set<String> mapCodes) {
        List<VehiclePauseResultDTO> list = new ArrayList<>();
        List<Vehicle> vehicles = vehiclePoolService.selectAll();
        vehicles = vehicles.stream().filter(v -> mapCodes.contains(v.getVehicleMapCode())).collect(Collectors.toList());
        if(CollUtil.isEmpty(vehicles)){
            return list;
        }

        Map<String, CompletableFuture<VehiclePauseResultDTO>> futureMap = new HashMap<>();
        for (Vehicle vehicle : vehicles) {
            futureMap.put(vehicle.getVehicleCode(), CompletableFuture.supplyAsync(() -> recoverVehicle(vehicle), asyncExecutor));
        }
        for (Map.Entry<String, CompletableFuture<VehiclePauseResultDTO>> item : futureMap.entrySet()) {
            String vehicleCode = item.getKey();
            CompletableFuture<VehiclePauseResultDTO> future = item.getValue();
            VehiclePauseResultDTO vehiclePauseResultDTO;
            try {
                vehiclePauseResultDTO = future.get();
            } catch (Exception e) {
                vehiclePauseResultDTO = VehiclePauseResultDTO.builder().vehicleCode(vehicleCode).result(false).desc("操作超时").build();
            }
            list.add(vehiclePauseResultDTO);
        }
        return list;
    }

    private static final Integer WAIT_TIMEOUT = 30 * 1000;
    private VehiclePauseResultDTO recoverVehicle(Vehicle vehicle) {
        VehiclePauseResultDTO build;
        long start = System.currentTimeMillis();
        while (true) {
            if (System.currentTimeMillis() - start > WAIT_TIMEOUT) {
                throw new FleetException(I18nMessageUtils.getMessage("system.operate.timeout"));
            }
            ThreadUtils.sleep(1000);
            if (!CONNECT_STATUS.equals(vehicle.getConnectStatus())) {
                build = VehiclePauseResultDTO.builder().vehicleCode(vehicle.getVehicleCode()).result(false).desc("机器人已经断线").build();
                break;
            } else if (vehicle.getRunningStatus() != null && SOFTSTOP_STATUS_CLOSE.equals(vehicle.getRunningStatus().getSoftStopSwitch())) {
                build = VehiclePauseResultDTO.builder().vehicleCode(vehicle.getVehicleCode()).result(true).desc("操作成功").build();
                break;
            } else {
                vehicle.closeSoftStop();
            }
        }
        return build;
    }

}
