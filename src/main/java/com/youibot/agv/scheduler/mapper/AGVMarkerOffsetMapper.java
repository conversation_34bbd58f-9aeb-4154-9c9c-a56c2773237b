package com.youibot.agv.scheduler.mapper;

import com.youibot.agv.scheduler.entity.AGVMarkerOffset;
import com.youibot.agv.scheduler.util.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface AGVMarkerOffsetMapper extends BaseMapper<AGVMarkerOffset> {

    @Select("select * from agv_marker_offset where agv_code = #{agvCode} and marker_id = #{markerId} ")
    AGVMarkerOffset findByAGVCodeAndMarkerId(@Param("agvCode")String agvCode, @Param("markerId")String markerId);
}
