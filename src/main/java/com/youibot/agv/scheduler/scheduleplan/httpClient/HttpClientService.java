package com.youibot.agv.scheduler.scheduleplan.httpClient;

import com.alibaba.fastjson.JSON;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Map;

/**
 * <AUTHOR> E-mail:<EMAIL>
 * @version CreateTime: 2019年4月24日 上午10:51:39
 */
@Service
public class HttpClientService {

    @Autowired
    private CloseableHttpClient httpClient;

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientService.class);

    private static final String APPLICATION_JSON = "application/json";

    private static final String CONTENT_TYPE_TEXT_JSON = "text/json";

    /**
     * @param url
     * @return
     * @throws Exception
     */
    public HttpResult doGet(String url) throws IOException {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        // 声明 http get 请求
        HttpGet httpGet = new HttpGet(url);
        // 装载配置信息
        // httpGet.setConfig(config);
        httpGet.addHeader(HTTP.CONTENT_TYPE, APPLICATION_JSON);
        // 发起请求
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(httpGet.toString());
        }
        CloseableHttpResponse response = this.httpClient.execute(httpGet);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(response.toString());
        }
        return new HttpResult(response.getStatusLine().getStatusCode(),
                EntityUtils.toString(response.getEntity(), "UTF-8"));
    }

    /**
     * @param url
     * @return
     * @throws Exception
     */
    public HttpResult doGet(String url, Map<String, Object> map) throws URISyntaxException, IOException {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        URIBuilder uriBuilder = new URIBuilder(url);
        if (map != null) {
            // 遍历map,拼接请求参数
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                uriBuilder.setParameter(entry.getKey(), entry.getValue().toString());
            }
        }
        // 调用不带参数的get请求
        return this.doGet(uriBuilder.build().toString());

    }

    /**
     * 带参数的post请求
     *
     * @param url
     * @param object
     * @return
     * @throws IOException
     * @throws ClientProtocolException
     */
    public HttpResult doPost(String url, Object object) throws ClientProtocolException, IOException {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        // 声明httpPost请求
        HttpPost httpPost = new HttpPost(url);
        // 加入配置信息
        // httpPost.setConfig(config);
        httpPost.addHeader(HTTP.CONTENT_TYPE, APPLICATION_JSON);

        StringEntity se = new StringEntity(JSON.toJSONString(object),"UTF-8");
        httpPost.setEntity(se);
        // 发起请求
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(httpPost.toString());
        }
        CloseableHttpResponse response = this.httpClient.execute(httpPost);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(response.toString());
        }
        return new HttpResult(response.getStatusLine().getStatusCode(),
                EntityUtils.toString(response.getEntity(), "UTF-8"));
    }

    /**
     * 带参数的post请求
     *
     * @param url
     * @param object
     * @return
     * @throws IOException
     * @throws ClientProtocolException
     */
    public HttpResult doPut(String url, Object object) throws ClientProtocolException, IOException {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        // 声明httpPost请求
        HttpPut httpPut = new HttpPut(url);
        // 加入配置信息
        // httpPut.setConfig(config);
        httpPut.addHeader(HTTP.CONTENT_TYPE, APPLICATION_JSON);

        StringEntity se = new StringEntity(JSON.toJSONString(object),"UTF-8");
        // se.setContentType(CONTENT_TYPE_TEXT_JSON);
        // se.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE, APPLICATION_JSON));
        httpPut.setEntity(se);
        // 发起请求
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(httpPut.toString());
        }
        CloseableHttpResponse response = this.httpClient.execute(httpPut);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(response.toString());
        }
        return new HttpResult(response.getStatusLine().getStatusCode(),
                EntityUtils.toString(response.getEntity(), "UTF-8"));
    }

    /**
     * 不带参数post请求
     *
     * @param url
     * @return
     * @throws IOException
     * @throws ClientProtocolException
     */
    public HttpResult doPost(String url) throws ClientProtocolException, IOException {
        return this.doPost(url, null);
    }

    public HttpResult doPut(String url) throws IOException {
        return this.doPut(url, null);
    }
}
