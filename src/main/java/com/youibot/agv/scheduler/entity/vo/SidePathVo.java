package com.youibot.agv.scheduler.entity.vo;

import com.youibot.agv.scheduler.entity.PathParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@Data
@ApiModel(value = "pathVo", description = "sidePath页面数据")
public class SidePathVo implements Serializable {

    @ApiModelProperty(value = "开始标记点", position = 3)
    private String startMarkerId;

    @ApiModelProperty(value = "结束标记点", position = 4)
    private String endMarkerId;

    @ApiModelProperty(value = "路径类型: 0、普通路径 1、二维码对接路径 2、脱离对接路径", position = 15)
    private Integer pathType;

    @ApiModelProperty(value = "工位编码", position = 16)
    private Integer workStationCode;

    @ApiModelProperty(value = "agv方向: 0、双向 1、正向 2、反向", position = 11)
    private Integer agvDirection;

    @ApiModelProperty(value = "AGV要朝向的角度（地图坐标系下）  -180 ~ 180", position = 14)
    private Double directionAgv;

    @ApiModelProperty(value = "AGV要朝向的角度2（地图坐标系下）  -180 ~ 180", position = 14)
    private Double directionAgv2;

    @ApiModelProperty(value = "货架要朝向的角度（地图坐标系下）  -180 ~ 180", position = 15)
    private Double directionShelf;

    @ApiModelProperty(value = "货架要朝向的角度2（地图坐标系下）  -180 ~ 180", position = 17)
    private Double directionShelf2;

    @ApiModelProperty(value = "uint, 货架旋转速度，单位，转/每分钟", position = 16)
    private Integer rpmShelf;

    @ApiModelProperty(value = "位置，1、起始点 2、结束点", position = 17)
    private Integer position;

    @ApiModelProperty(value = "生效路径 1、全部路径 2、第一段路径", position = 18)
    private Integer effectivePath;

    @ApiModelProperty(value = "是否有动作,1、有 0、没有", position = 19)
    private Integer hasAction;

    @ApiModelProperty(value = "路径权重系数", position = 20)
    private Double weightRatio;

    @ApiModelProperty(value = "agv方向2: -180 ~ 180", position = 14)
    private Integer agvDirection2;

    @ApiModelProperty(value = "路径导航参数",position = 21)
    private PathParam pathParam;

    //可选
    @ApiModelProperty(value = "路径前任务",position = 22)
    private SidePathMission beforeMission;

    //可选
    @ApiModelProperty(value = "路径后任务",position = 23)
    private SidePathMission afterMission;

    @ApiModelProperty(value = "货架类型", position = 16)
    private Integer shelfType;

    @ApiModelProperty(value = "相机类型, 位姿精调的相机类型：DOWN底部相机 UP顶部相机 LEFT左侧相机  RIGHT右侧相机", position = 16)
    private String camType;

    @ApiModelProperty(value = "顶部二维码工位ID")
    private Integer upQrDockId;

    @ApiModelProperty(value = "底部二维码工位ID")
    private Integer downQrDockId;
}
