package com.youibot.agv.scheduler.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "auto_door")
@ApiModel(value = "AutoDoor", description = "自动门")
public class AutoDoor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "select uuid()")
    @ApiModelProperty(value = "ID")
    private String id;

    @Column
    @ApiModelProperty(value = "名称", position = 1)
    private String name;

    @Column
    @ApiModelProperty(value = "自动门ip", position = 2)
    private String ip;

    @Column
    @ApiModelProperty(value = "自动门端口", position = 3)
    private Integer port;

    @Column
    @ApiModelProperty(value = "开门地址", position = 4)
    private Integer openAddress;

    @Column
    @ApiModelProperty(value = "开门状态地址", position = 5)
    private Integer openStatusAddress;

    @Column
    @ApiModelProperty(value = "关门地址", position = 6)
    private Integer closeAddress;

    @Column
    @ApiModelProperty(value = "关门状态地址", position = 7)
    private Integer closeStatusAddress;

    @Column
    @ApiModelProperty(value = "门已开:OPEN 门未开:NOT_OPEN 通讯异常:ERROR 未绑路径:UNBOUND_PATH", position = 8)
    private String currentStatus;

    @Column
    @ApiModelProperty(value = "类型 AUTO_DOOR:自动门   AIR_SHOWER_DOOR:风淋门", position = 9)
    private String type;

    @Column
    @ApiModelProperty(value = "位置 当type=AIR_SHOWER_DOOR时有值  FRONT:前门  BACK:后门", position = 10)
    private String position;

    @Column
    @ApiModelProperty(value = "关联门ID 当type=AIR_SHOWER_DOOR时有值", position = 11)
    private String relationDoorId;

    @Column
    @ApiModelProperty(value = "停留时间 单位:秒  机器人需要在两道风淋门间停留一段时间", position = 12)
    private Integer residenceTime;

    @Column
    @ApiModelProperty(value = "最后一次关门完成时间", position = 13)
    private Date lastCloseDoorTime;

    @Column
    @ApiModelProperty(value = "控制模式 LOCAL:本地控制 SCHEDULER:调度控制",  position = 14)
    private String controlMode;

    @Column
    @ApiModelProperty(value = "创建时间", position = 15)
    private Date createTime;

    @Column
    @ApiModelProperty(value = "更新时间", position = 16)
    private Date updateTime;

    @Transient
    @JsonIgnore
    private List<Path> paths;
}
