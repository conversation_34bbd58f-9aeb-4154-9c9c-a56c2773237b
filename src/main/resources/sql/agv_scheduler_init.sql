-- 数据库时区设置 UTC
SET GLOBAL time_zone = '+08:00';
SET time_zone = '+08:00';

flush privileges;
-- 查询数据库时区：SELECT @@global.time_zone, @@session.time_zone;

use agv_scheduler;

-- ----------------------------
-- are_type 初始化数据
-- ----------------------------
INSERT INTO `map_area_type` (id,name,description)
VALUES ('AVOIDANCE_OFF_AREA', '避障关闭区载', NULL);
INSERT INTO `map_area_type` (id,name,description)
VALUES ('SINGLE_AGV_AREA', '单机工作区域', NULL);

-- 创建id自增函数
DELIMITER $$

DROP FUNCTION IF EXISTS `next_value`$$
CREATE FUNCTION `next_value`(name varchar(50)) RETURNS int(11)
begin
declare _cur int;
declare _maxvalue int;  -- 接收最大值
declare _increment int; -- 接收增长步数
set _increment = (select increment_value from sys_sequence where seq_name = name);
set _maxvalue = (select max_value from sys_sequence where seq_name = name);
set _cur = (select current_value from sys_sequence where seq_name = name);
update sys_sequence                      -- 更新当前值
set current_value = _cur + increment_value
where seq_name = name ;
if(_cur + _increment >= _maxvalue) then  -- 判断是都达到最大值
     update sys_sequence
       set current_value = min_value
       where seq_name = name ;
end if;
return _cur;

end$$
DELIMITER ;

-- ----------------------------------
-- sys_sequence（用于id自增） 初始化数据
-- ----------------------------------
INSERT INTO `sys_sequence` (`seq_name`, `min_value`, `max_value`, `current_value`, `increment_value`)
VALUES ('common_key', 1, 99999999, 1, 1);


INSERT INTO `language` (`id`, `current_use`)
VALUES ('common', 'CHINESE');


-- 初始化系统默认配置
insert into system_config values(replace(uuid(),"-",""), 10, 30, 30, 1, 1, 'GMT+8', 1, '172.17.0.1', sysdate(), sysdate());

-- 初始化admin用户
INSERT INTO `user`(`id`, `name`, `user_name`, `password`, `admin`) VALUES ('ce41ce331ff3-4027afbce35ab17e508d', 'admin', 'admin', 'admin', 1);

-- 初始化Logo信息
insert into logo_info values(replace(uuid(),"-",""), '深圳优艾智合机器人科技有限公司', 'YOUIFleet v4.8.3-tms2', 'YOUIFleet', 36, 'https://www.youibot.com/' ,'深圳优艾智合机器人科技有限公司', '/static/images/logo.png', sysdate(), sysdate());

-- 初始化调度配置参数信息
insert into scheduler_config values(1, 20.0, 40.0, 80.0, 500.0 ,-1.0, 1.0, 1, 1, 10, 10,0,30,1800,600,168,1,-1.0,500,1,0,0, sysdate(), sysdate());

-- 初始化异常信息
INSERT INTO `abnormal_prompt` VALUES ('81cff324-a124-11ec-aa26-8c8caa7e2ae0', '3', '地图', '202013', '使用的地图不存在', '请重新配置任务的动作', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d090f5-a124-11ec-aa26-8c8caa7e2ae0', '3', '地图', '202014', '使用的地图未启用', '请启用该任务使用的地图', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d11de3-a124-11ec-aa26-8c8caa7e2ae0', '3', '地图', '202028', '机器人当前站点不存在', '请检查地图中该站点的配置数据', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d17b12-a124-11ec-aa26-8c8caa7e2ae0', '3', '地图', '302102', '目标点不可达', '请检查地图该路径导航的起点和终点之间是否可达', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d17b13-a124-11ec-aa26-8c8caa7e2ae0', '3', '地图', '300530', '机器人离线', '请检查机器人网络及系统运行状态', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d1d757-a124-11ec-aa26-8c8caa7e2ae0', '3', '机器人', '201000', '机器人底盘异常', '请查看该机器人上报的异常信息', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d23fb0-a124-11ec-aa26-8c8caa7e2ae0', '3', '机器人', '202021', '机器人脱轨', '请将机器人移动到地图点位后执行重定位', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d2bc5b-a124-11ec-aa26-8c8caa7e2ae0', '3', '机器人', '202035', '机器人前方持续有障碍物', '请移除机器人前方障碍物并复位机器人', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d32b54-a124-11ec-aa26-8c8caa7e2ae0', '3', '机器人', '203000', '脚本机械臂异常', '请联系技术支持排查', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d3a508-a124-11ec-aa26-8c8caa7e2ae0', '3', '机器人', '204001', '机器人状态不满足充电', '请检查机器人状态', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d4119a-a124-11ec-aa26-8c8caa7e2ae0', '3', '机器人', '205001', '机器人状态不满足泊车', '请检查机器人状态', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d49d2c-a124-11ec-aa26-8c8caa7e2ae0', '2', '机器人', '206001', '电池电量低', '请充电', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d53f4c-a124-11ec-aa26-8c8caa7e2ae0', '2', '机器人', '206002', '按钮急停', '请查看机器人', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d5abd5-a124-11ec-aa26-8c8caa7e2ae0', '2', '机器人', '206003', '安全设备急停', '请查看机器人', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d64568-a124-11ec-aa26-8c8caa7e2ae0', '2', '机器人', '206004', '碰撞急停', '请查看机器人', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d6b3f2-a124-11ec-aa26-8c8caa7e2ae0', '2', '机器人', '206005', '升降电机急停', '请查看机器人', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d70b64-a124-11ec-aa26-8c8caa7e2ae0', '2', '机器人', '206007', '辊筒急停', '请查看机器人', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d7a2b8-a124-11ec-aa26-8c8caa7e2ae0', '3', '机器人', '302101', '机器人脱轨', '请将机器人移动到地图点位后重定位', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d82d74-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202004', '路径导航起点不可在电梯点', '机器人当前在电梯点, 请先将机器人先移出电梯', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d8c61e-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202005', '目标站点不可用', '请检查目标站点是否已被禁用或删除', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d961dc-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202006', '路径导航起点不可在设备范围内', '请先将机器人移到空旷位置', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81d9db58-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202020', '自动门数据为空', '请检查路径导航中自动门数据是否存在', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81da48c5-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202022', '路径导航起点不可在设备范围内', '请先将机器人移到空旷位置', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81dab134-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202023', '切换手动模式导致任务异常', '请重新执行该任务', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81db48b7-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202024', '读到错误码', '读取寄存器时设备上报错误', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81dbae91-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202025', '读寄存器超时', '请检查与该寄存器的IP地址和端口网络是否连通', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81dc19b7-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202026', '动作缺少参数', '请检查该任务的动作配置', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81dc8bc2-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202027', '读寄存器失败', '请检查该寄存器是否支持当前功能码', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81dcf73d-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202029', 'HTTP请求异常', '请检查网络连接是否连通', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81dd519f-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202030', 'URL地址为空', '请检查Http Post动作的配置参数', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ddc5ac-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202031', '当前作业不存在', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81de3edf-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202032', '当前任务不存在', '该任务未创建或已被删除，请停止当前作业', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81decab1-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202033', '上传文件出错', '请检查文件格式是否符合要求', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81df5020-a124-11ec-aa26-8c8caa7e2ae0', '3', '任务', '202034', '楼层数据为空', '请检查路径导航中楼层数据是否存在', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('67ws367d-a124-5624-aa26-8c8caa7e2ae0', '3', '任务', '206009', '取消路径导航异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81dfb816-a124-11ec-aa26-8c8caa7e2ae0', '3', '网络', '202007', '下载电梯数据失败', '请检查与Fleet系统之间的网络是否联通', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e03847-a124-11ec-aa26-8c8caa7e2ae0', '3', '网络', '202016', 'mobus通讯失败', '请检查modbus通讯是否连通', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e0b1e7-a124-11ec-aa26-8c8caa7e2ae0', '3', '网络', '202017', '申请电梯超时', '请检查电梯通讯是否连通', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e1247e-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '200000', '系统异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e17b1b-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202000', '路径导航异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e1cdb4-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202001', '路径规划异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e232b2-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202002', '路径规划超时', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e2a225-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202003', '系统异常', 'Compass系统异常，标记点不存在', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e31ad2-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202008', '系统异常', '请联系Compass开发人员，使用电梯时导航类型异常', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e3ad8b-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202009', '系统异常', '请联系Compass开发人员，电梯路径上的标记点起点或终点不存在', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e421d7-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202010', '系统异常', '请联系Compass开发人员，申请电梯时内部数据错误', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e4a7eb-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202011', '电梯移动超时', '请检查电梯是否出现故障', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e53335-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202012', '系统异常', '请联系Compass开发人员，目标地图Id为空', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e5be27-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202015', '系统异常', '请联系Compass开发人员，找不到电梯数据', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e63c29-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '202019', '系统异常', 'Compass系统异常，该动作数据为空', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e6a0cd-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '204002', '充电执行异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e7100f-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '204003', '充电取消异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e773e8-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '205002', '泊车执行异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e7f2e8-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '205003', '泊车取消异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e86517-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '300000', '系统异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e8d07f-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '302000', '作业执行异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e93b8c-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '302001', '作业分配异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e996e6-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '302002', '作业下发异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ea150d-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '302003', '停止作业失败', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81e93b9c-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '302005', '无法查找关联的泊车点，请检查地图配置', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81eaa0bd-a124-11ec-aa26-8c8caa7e2ae0', '3', '系统', '302100', '路径规划异常', '请提交当前地图、Compass、Fleet日志给到售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81eafd59-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570001', '机械臂通用失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81eb550f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570002', '机械臂接口参数错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81eba61f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570003', '未兼容的指令接口', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ebfc07-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570004', '机器人连接失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ec5438-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570005', '机械臂socket通讯消息收发异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ecb41a-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570006', 'Socket断开连接', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ed33fd-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570007', '创建请求失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ed9bde-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570008', '请求相关的内部变量出错', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ee0d07-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570009', '请求超时', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ee7710-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570010', '发送请求信息失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81eedebe-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570011', '响应信息为空', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ef6533-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570012', '响应信息header不符', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81efe7d5-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570013', '解析响应失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f04a9d-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570014', '正解出错', '终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务。', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f0a155-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570015', '逆解出错', '终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务。', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f10f77-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570016', '工具标定出错', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f15f77-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570017', '工具标定参数有错', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f1c3b4-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570018', '坐标系标定失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f24728-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570019', '基坐标系转用户座标失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f2cac6-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570020', '用户坐标系转基座标失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f335cb-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570021', '机器人上电失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f3a53c-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570022', '机器人断电失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f416d2-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570023', '机器人使能失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f48986-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570024', '机器人下使能失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f4eca1-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570025', '机器人复位失败', '再次点击复位按钮进行复位，如果还失败，请取出示教器，清除报错', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f56d98-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570026', '机器人暂停失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f5df35-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570027', '机器人停止失败', '拍下急停按钮，取下机械臂抓手上的物料，上电，使能，手动将机械臂回到原点后，恢复任务', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f642ad-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570028', '机器人状态获取失败', '终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，恢复任务', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f69bdb-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570041', 'move指令阻塞等待超时', '终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，检查超时原因。1.如因为路径点轨迹融合半径太大，导致运动不到位，可适当将对应步骤的速度降低。然后单机测试运行该任务，如果不会再报超时，即可恢复任务；2.如因为触发防护性停止导致机械臂暂停时间超过1分钟，可以直接恢复任务。', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f71cb7-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570042', '运动相关的内部变量出错', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f77e69-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570043', '运动请求失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f7f539-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570044', '生成运动请求失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f85bba-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570045', '运动被事件中断', '终止当前任务，取下机械臂抓手上的物料，点击复位按钮进行复位，如果无法复位，请使用示教器清除错误。清错后重新上电、使能，手动将机械臂回到原点后，恢复任务', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f8c7a6-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570046', '运动相关的路点容器的长度不符合规定', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f940aa-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570047', '服务器响应返回错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81f9c445-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570048', '真实机械臂不存在', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fa6947-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570049', '调用缓停接口失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fad482-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570050', '调用急停接口失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fb35dd-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570051', '调用暂停接口失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fba8c7-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570052', '调用继续接口失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fc4184-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '570053', '运动被条件中断', '终止当前任务，取下机械臂抓手上的物料，手动将机械臂回到原点后，检查失效条件。如果是非传感器自身原图导致的条件失效，请恢复任务。如果是传感器问题，请联系售后人员解决', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fca161-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571001', '关节运动属性配置错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fd024b-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571002', '直线运动属性配置错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fd5908-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571003', '轨迹运动属性配置错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fda39b-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571004', '无效的运动属性配置', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fe0095-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '571005', '等待机器人停止', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fe6566-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '571006', '超出关节运动范围', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81fed620-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571007', '请正确设置MODEP第一个路点', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ff3117-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571008', '传送带跟踪配置错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('81ff9609-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571009', '传送带轨迹类型错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820013f3-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571010', '相对坐标变换逆解失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82006ee0-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '571011', '示教模式发生碰撞', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8200cf07-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571012', '运动属性配置错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82014302-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571101', '轨迹异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8201a477-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571102', '轨迹规划错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82020158-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571103', '二型在线轨迹规划失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82026d72-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571104', '逆解失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8202d988-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '571105', '动力学限制保护', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820337ce-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571106', '传送带跟踪失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8203b9e6-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '571107', '超出传送带工作范围', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82042c31-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '571108', '关节超出范围', '终止当前任务，取下机械臂抓手上的物料。检查MOS错误日志，查看是哪个点导致的关节超限，修改该点位后，将机械臂回到原点，重新开始任务', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82048938-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '571109', '关节超速', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8204f40a-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571110', '离线轨迹规划失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820563bd-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571200', '控制器异常，逆解失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8205ca08-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571201', '控制器异常，状态异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820628d9-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571300', '运动进入到stop阶段', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82069013-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571301', '运动被事件中断', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8206fa3c-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '571401', '机械臂未定义的失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82074d5f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572100', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8207a189-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572101', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820806a5-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572102', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8208764d-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572103', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8208eef5-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572104', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820948be-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572105', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82099b8b-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572106', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820a0e47-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572107', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820a7b21-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572108', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820ae07d-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572109', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820b6542-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572110', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820bd3ba-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572111', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820c3767-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572112', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820ca5bb-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572113', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820d068f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572114', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820d60d3-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572115', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820dc7ff-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572116', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820e29ed-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572117', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820eb851-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572118', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820f3e0f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572119', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820f9a5f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572120', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('820fec38-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572121', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821048dd-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572122', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8210b396-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572123', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821111d3-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572124', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82116dd1-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572125', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8211e49d-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572126', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82125d2f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572127', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8212c5dd-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572128', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8213406a-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572129', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8213bc18-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572130', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821419d2-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572131', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82146e0f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572132', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8214ccb2-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572133', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82151d19-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572134', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82157bf8-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572135', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8215e607-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572136', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82164934-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572137', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8216b461-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572138', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82171a0c-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '572139', 'PLC客户端异常', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821777dc-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '573100', 'E84 消息等待超时', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8217d276-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '573101', 'E84不支持的操作类型', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8218374d-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '573102', 'E84不支持的机器人上下料状态', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8218a564-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574100', '夹爪打开失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82190c35-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574101', '夹爪关闭失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821959c7-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574102', '夹爪复位失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8219a5c3-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574200', '距离传感器检测超时', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8219f456-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574201', '抓手有无料检测传感器检测超时', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821a4f78-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574300', '相机未连接', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821ac0bf-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574301', '相机内部错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821b16cd-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574302', '超时', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821b6cd7-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574303', '相机未知命令', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821bd3df-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574304', '索引超出范围', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821c376f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574305', '自变量太少', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821c8a2f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574306', '无效自变量类型', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821ce326-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574307', '无效自变量', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821d57b2-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574308', '不允许的命令', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821db0e0-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574309', '不允许的组合', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821e0fd7-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574310', '相机忙', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821e81ec-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574311', '未完全实施', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821ece35-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574312', '不支持', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821f1e81-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574313', '结果字符串过⻓', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821f710d-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574314', '⽆效相机 ID', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('821fea05-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574315', '⽆效相机特征 ID', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82203cc3-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574316', '不同的配⽅名称', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82208c2a-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574317', '不同版本', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8220d878-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574318', '没有标定', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82212334-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574319', '标定失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82216ea1-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574320', '⽆效标定数据', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8221bed1-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574321', '未达到给定的标定位置', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82221fe3-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574322', '⽆启动命令', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8222ac33-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574323', '特征未经过训练', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822316b8-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574324', '特征未找到', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82236af0-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574325', '特征未映射', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8223cd53-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574326', '部件位置未经过训练', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82242231-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574327', '机器⼈位置未经过训练', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82247d7b-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574328', '⽆效部件 ID', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8224ebe5-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574329', '未定位此部件的所有特征', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822549c7-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574330', '部件⽆有效夹持纠正', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8225c79d-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574350', '相机读取socket错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82261edd-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574351', '相机响应信息header不符', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822678ba-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574352', '解析相机响应失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8226d4a9-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '574390', '相机拍照失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82274805-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575100', '无效的储位ID', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8227c51a-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575101', '储位检测超时', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82282dc5-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575300', '无物料信息检测sensor', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822896d2-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575301', 'smart tag 传感器未连接', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8228f6df-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575302', 'smart tag 读取失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8229507b-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575303', 'smart tag 读取超时', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8229c480-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575304', 'smart tag 数据无效', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822a442d-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '575900', 'smart tag sensor 故障', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822aa93e-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575401', 'RFID 传感器未连接', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822b0066-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575402', 'RFID 读取失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822b6112-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575403', 'RFID 读取超时', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822bbd5b-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '575404', 'RFID 数据无效', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822c124c-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '575901', 'RFID sensor 故障', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822cb062-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '576100', '升降柱超出运动范围', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822d1d5e-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '576101', '无效的控制指令', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822d7ece-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579000', '取消任务失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822ddbe5-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579101', '暂停任务失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822e3488-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579102', '恢复任务失败', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822e891d-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579103', 'buffer解析错误', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822f022b-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579104', '未找到任务', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822f6180-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579105', '任务列表未更新', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('822fcf8e-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579106', '存在未完成的任务', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82302c31-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579201', '无效步骤类型', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8230863d-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579202', '未找到pose value', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8230de83-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579203', '未找到joint value', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82313abe-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579204', '未找到偏移量', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8231b06a-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579205', '无效的feature ID', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82323c8f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '579206', '无效的条件类型', '请联系mos系统开发人员处理', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8232d242-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110001', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823344d0-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110002', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8233a8f6-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110003', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82343b14-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110004', '左驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82348b09-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110005', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8234e4be-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110006', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82353df0-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110007', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82359ba9-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110008', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8235ebe2-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110009', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82363e67-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110010', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82369e58-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110011', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8236f4dd-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110012', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8237519e-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110013', '左驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8237f1a0-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110014', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823840cc-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110015', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82388b39-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110016', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8238e7b1-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110017', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82394fc6-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110018', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8239aa9e-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110019', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823a050e-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110020', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823a4e5d-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110021', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823a978c-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110022', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823ae066-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110023', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823b28f8-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110024', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823b78f8-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110025', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823bde94-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110026', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823c2639-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110027', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823c76b7-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110028', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823cd1c5-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110029', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823d2bb5-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110030', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823d722c-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110031', '左驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823dbce7-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110032', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823e1147-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110033', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823e73af-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110034', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823ecd2c-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110035', '右驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823f2910-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110036', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823f7221-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110037', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('823fbd08-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110038', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82400877-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110039', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82405238-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110040', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8240b4e8-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110041', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8241150a-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110042', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82416383-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110043', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8241c088-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110044', '右驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82421626-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110045', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82425ef8-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110046', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8242b580-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110047', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824301e5-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110048', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824371c6-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110049', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8243bd55-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110050', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82440b2f-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110051', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82445a0f-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110052', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8244af00-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110053', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8244f662-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110054', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824541a2-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110055', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8245a26a-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110056', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82460abf-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110057', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82465f6a-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110058', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8246bda1-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110059', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82472066-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110060', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82478b5e-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110061', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8247e1ed-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110062', '右驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82484091-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110063', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8248a253-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110064', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8248f6a9-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110065', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82494e65-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110066', '顶升驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8249b267-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110067', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824a0a54-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110068', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824a5b6b-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110069', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824aa7fc-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110070', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824b0b22-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110071', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824b54a4-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110072', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824ba27c-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110073', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824bf5a3-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110074', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824c52bd-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110075', '顶升驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824c9bda-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110076', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824cef6f-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110077', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824d4108-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110078', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824dad35-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110079', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824dfaf1-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110080', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824e5151-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110081', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824e9964-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110082', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824ee245-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110083', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824f2947-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110084', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824f8412-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110085', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('824fee5f-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110086', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82504b82-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110087', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82509360-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110088', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8250e837-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110089', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82513dbd-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110090', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82518a9b-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110091', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8251d400-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110092', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82521a1b-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110093', '顶升驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82526b6b-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110094', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8252e074-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110095', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82533d38-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110096', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8253b437-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110097', '旋转驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8254050d-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110098', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82545812-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110099', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8254b368-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110100', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825520e9-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110101', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82558648-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110102', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8255e597-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110103', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82563f00-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110104', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82569082-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110105', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8256df89-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110106', '旋转驱动器-异常', '需要关机并静置一小时后再使用，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82572f27-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110107', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8257921b-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110108', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8257fc37-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110109', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82584f4a-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110110', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82589445-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110111', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8258de14-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110112', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8259305c-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110113', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825981e9-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110114', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8259d440-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110115', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825a451b-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110116', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825aa2fb-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110117', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825aef61-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110118', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825b3784-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110119', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825b8809-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110120', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825bd328-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110121', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825c1b4d-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110122', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825c697b-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110123', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825cbc21-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110124', '旋转驱动器-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825d0375-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110200', '电机初始化-异常', '行走电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825d49ab-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110201', '电机初始化-异常', '升降电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825d8e94-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110202', '电机初始化-异常', '插取电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825dd403-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110203', '电机初始化-异常', '旋转电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825e18e3-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110204', '电机初始化-异常', '夹取电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825e6708-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110205', '电机初始化-异常', '森创升降电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825eb31e-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110206', '电机初始化-异常', '森创旋转电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825f0afd-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110207', '电机初始化-异常', 'SR电机报文发送失败，可能因为行走电机未配置正确的参数或者未连接，重启若无法恢复请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825f548f-a124-11ec-aa26-8c8caa7e2ae0', '1', '软件', '110300', '电机控制-异常', '下发的旋转指令超过范围：-180~180，清除错误后重新下发即可', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825fac70-a124-11ec-aa26-8c8caa7e2ae0', '1', '软件', '110301', '电机控制-异常', '下发的旋转速度指令超过范围，清除错误后重新下发即可，速度最大8转每分钟，', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('825ff7d8-a124-11ec-aa26-8c8caa7e2ae0', '1', '软件', '110302', '电机控制-异常', '下发的升降指令超过范围，清除错误后重新下发即可，若在合理范围内一直不能成功，请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8260475a-a124-11ec-aa26-8c8caa7e2ae0', '1', '软件', '110303', '电机控制-异常', '下发的升降速度指令超过范围，清除错误后重新下发即可，速度10mm/s', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82608d70-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110400', '电机运行-异常', '触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8260d9e7-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110401', '电机运行-异常', '触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826125fa-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110402', '电机运行-异常', '触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82616dae-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110403', '电机运行-异常', '触发急停再解除，或者重启机器人后观察是否恢复，否则联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8261b855-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110500', '声光系统-异常', '重启后若依旧无法解决，则联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82620b74-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110501', '声光系统-异常', '重启后若依旧无法解决，则联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82625550-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110502', '声光系统-异常', '确认音频名字无误后重新下发指令', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82629d96-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '110503', '声光系统-异常', '确认音频名字无误后重新下发指令', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8262eea4-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110504', '声光系统-异常', '重启后若依旧无法解决，则联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82633d0b-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '110505', '声光系统-异常', '检查socketIP和端口是否配置正确，如果正确则联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8263899f-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '120100', 'bms-异常', '检查串口是否正常', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8263d31f-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '120101', 'bms-异常', '检查端口号是否正确，检查电池和品牌是否有问题', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826419a9-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '120001', '充电-异常', '可能电池的通信线连接不良，或者间歇性通信失败，重新执行任务，若依旧出错请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8264603c-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '120002', '充电-异常', '重启重新尝试后若依旧报错请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8264a8c7-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '120003', '充电-异常', '重启重新尝试后若依旧报错请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8264f107-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '120004', '充电-异常', '重启重新尝试后若依旧报错请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82653a6d-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '120005', '充电-异常', '可能环境因素导致对接信息采集失败，调整环境至良好无干扰的情况再重新尝试任务，若调整后依旧失败请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8265839b-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '120006', '充电-异常', '可能环境因素导致对接信息采集失败，调整环境至良好无干扰的情况再重新尝试任务，若调整后依旧失败请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8265ca24-a124-11ec-aa26-8c8caa7e2ae0', '2', '硬件', '120007', '充电-异常', '若没良好对接充电桩，请调整对接参数，确保充电桩处于自动模式，', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826611ec-a124-11ec-aa26-8c8caa7e2ae0', '2', '硬件', '120008', '充电-异常', '降低充满百分比，默认为97%，若降低到89%依旧还有问题，请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82665895-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '121001', '音频-异常', '确认所指定播放的音频名字存在与AGV内，注意音频名字末尾不要加后缀，如.mp3，确保音频为mp3格式，', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8266a0e9-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113001', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8266e61a-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113002', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82672dc0-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113003', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8267729e-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113004', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8267c3b4-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113005', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826822a9-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113006', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82688574-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113007', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8268d260-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113008', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82691a56-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113009', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82696736-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113010', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8269c540-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113011', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826a1eb6-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113012', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826a786e-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113013', 'Can卡-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826ac864-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113015', 'Can卡-异常', '可能CAN卡未连接或者连接线断开，若重启无法解决，请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826b18ff-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113016', 'Can卡-异常', '可能CAN卡设备异常，若多次重启未恢复，请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826b6986-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '113017', 'Can卡-异常', '可能CAN卡设备异常，若多次重启未恢复，请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826bbb2b-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '123004', 'Socket-异常', '重新确认API端口号与接口号是否正确', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826c0974-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '123005', 'Socket-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826c636f-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '123006', 'Socket-异常', '无法获取配置信息，请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826cc9b6-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '115001', 'IMU-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826d589f-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '115002', 'IMU-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826daba1-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '115003', 'IMU-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826e06e0-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '115004', 'IMU-异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826e6542-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '127001', '导航执行异常', '需要停止当前任务，并联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826ebb4f-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '127002', '导航执行异常', '需要停止当前任务，并且人工判断机器人是否脱轨，如果脱轨，请将机器人移动到路径上，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826f153e-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '127003', '导航执行异常', '需要停止当前任务，并且人工判断定位数据是否存在，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826f62f0-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '127004', '导航执行异常', '需要停止当前任务，并且人工判断货架下方标记物是否识别正常，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('826fbba1-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '127005', '导航执行异常', '需要停止当前任务，并且判断是否存在雷达以及pcl相关报错，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827012e0-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '127006', '导航执行异常', '需要停止当前任务，并且判断是否存在电机相关报错，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82706f32-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '127007', '导航执行异常', '需要停止当前任务，并且判断是否存在定位相关报错，如果没有，激光定位情况下请判断是否存在雷达相关报错，二维码定位判断是否存在二维码传感器通讯异常，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8270bf05-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '127009', '导航执行异常', '需要停止当前任务，当前路径周遭人工特征是否存在被遮挡的情况，如果存在，请避免遮挡，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827126f1-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '127010', '导航执行异常', '需要停止当前任务，并且判断当前激光定位数据是否正常，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82717e0a-a124-11ec-aa26-8c8caa7e2ae0', '1', '软件', '127011', '导航执行异常', '请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8271db6a-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '128001', '对接执行异常', '需要清除错误状态，并且判断当前机器人是否在执行对接，或者是否在之前执行对接指令后未执行脱离对接，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82724b5a-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '128002', '对接执行异常', '需要清除错误状态，并且判断当前指定的对接目标是否合理，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8272a704-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '128003', '对接执行异常', '需要清除错误状态，并且判断特征检测模块运行状态，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827311dd-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '128004', '对接执行异常', '需要清除错误状态，并且判断特征检测模块运行状态，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82738b59-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '128005', '对接执行异常', '需要清除错误状态，并且判断特征检测模块运行状态，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8273f440-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '128006', '对接执行异常', '需要清除错误状态，并请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827449fd-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '128007', '对接执行异常', '需要清除错误状态，并且判断特征检测模块运行状态，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8274a76f-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '128008', '对接执行异常', '需要清除错误状态，需要清除错误状态，并且判断当前机器人是否在执行脱离对接，或者是否在之前未执行对接动作，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82750ca5-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '128009', '对接执行异常', '需要清除错误状态，并且判断是否存在定位相关报错，如果没有，请判断是否存在雷达相关报错，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82755d67-a124-11ec-aa26-8c8caa7e2ae0', '1', '软件', '130001', '定位异常', '请尝试重定位，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8275b804-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '133001', '特征检测执行异常', '人工判断机器人是否在对接范围内，特征是否与机器人雷达高度一致，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82760ec5-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '133002', '特征检测执行异常', '人工判断是否有相似特征，调整机器人对接距离或方向，如果无法回复或异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82768377-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '133003', '特征检测执行异常', '判断是否存在激光雷达相关报错，如果无法恢复或异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8276e540-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '133004', '特征检测执行异常', '判断是否存在定位相关报错，如果无法恢复或异常出现多次请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82773c34-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '135001', '建图执行异常', '判断是否存在激光雷达相关报错，如果无法恢复或异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827795cc-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '135002', '建图执行异常', '人工判断绘图过程是否形成回环，如果一直没有回环请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8277ef1f-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '119001', '节点异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82786118-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '119002', '节点异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8278cf35-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '119003', '节点异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82793c48-a124-11ec-aa26-8c8caa7e2ae0', '3', '软件', '119004', '节点异常', '需要重启恢复，如果无法恢复或者异常多次出现请联系售后', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82799f05-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140000', '脚本异常', '请检查socket或者lua指令的参数，例如：长度、类型等', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827a0e2e-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140001', '脚本异常', '指定控制的脚本不存在，请检查脚本的名字或者ID', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827a7c45-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140002', '脚本异常', '脚本正在运行中，不运行用户当前的控制指令', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827ad841-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140003', '脚本异常', '脚本子线程没有正常退出，这种情况属于致命BUG，请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827b3512-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140004', '脚本异常', '启动脚本超时，检测脚本存放路径是否正确，如检测无误，请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827b8ed4-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140005', '脚本异常', '停止脚本超时，检测脚本是否已经退出，如检测无误，请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827bf1fd-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140006', '脚本异常', '下发的控制指令不存在，请检查json指令的command', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827c686e-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140007', '脚本异常', '指定的脚本变量地址错误，请检查下发的脚本变量的地址是否不在指定范围内【0，31】', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827cd1a9-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140008', '脚本异常', '请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827d3242-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140009', '脚本异常', '请检查lua脚本保存的后缀名是否正确，并联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827dad1b-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140010', '脚本异常', '配置的用户lua脚本保存路径错误，请检查配置文件', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827e11d9-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140011', '脚本异常', '请检查json格式是否正确，或者socket协议的包头包尾是否已经添加', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827e8130-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140012', '脚本异常', 'JSON字段中缺少“command”字段，请联系前端开发人员进行核对', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827eedfc-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140013', '脚本异常', '变量类型错误，请选择string/double/int', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827f494d-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140014', '脚本异常', '脚本向compass请求路径导航中，compass返回的状态错误', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('827f9fdf-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140015', '脚本异常', '脚本向compass请求路径导航中，compass反馈异常', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('828002a7-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140016', '脚本异常', '脚本向compass请求路径导航中，compass的json错误', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8280661d-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140017', '脚本异常', '脚本向compass请求路径导航过程中，compass的socket服务断开', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8280d755-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140018', '脚本异常', 'moveLine接口发生的错误，请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82813fb9-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140019', '脚本异常', '对接出错，请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8281a1e1-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140020', '脚本异常', '等待小车运动超时，请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82820563-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '140021', '脚本异常', '运行中的脚本数量到达阈值，请停止继续启动脚本或者停止当前运行中的脚本', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('828258a1-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '145000', '脚本异常', '连接机械臂超时，请检测机械臂是否已经上电、网线是否正常', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8282b715-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '145001', '脚本异常', '机械臂没有建立连接，请检测机械臂是否已经上电、网线是否正常', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82830709-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '145002', '脚本异常', '请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8283599d-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '145003', '脚本异常', '控制机械臂输入的参数错误，请根据协议检查参数', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8283a9c6-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '145004', '脚本异常', '机械臂返回的消息错误，请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8283fb0b-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '145005', '脚本异常', '下发控制机械臂的指令错误，请根据协议检查下发的指令', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82844ab0-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '145006', '脚本异常', '请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82849b91-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '145007', '脚本异常', '请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('828503c4-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '146000', '脚本异常', '请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82856ced-a124-11ec-aa26-8c8caa7e2ae0', '2', '软件', '146001', '脚本异常', '请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8285d4d0-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '170001', '上集成异常', '请联系开发人员', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('828623b9-a124-11ec-aa26-8c8caa7e2ae0', '2', '硬件', '170002', '上集成异常', '请按照上集成操作手册复位', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82867a85-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '170003', '上集成异常', '请复位驱动器清除故障，或者断电重启', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8286d0f0-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '170004', '上集成异常', '请复位驱动器清除故障，或者断电重启', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('828725f7-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '170005', '上集成异常', '请复位驱动器清除故障，或者断电重启', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('82877e54-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '170006', '上集成异常', '请复位驱动器清除故障，或者断电重启', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8288944f-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '170007', '上集成异常', '请复位驱动器清除故障，或者断电重启', sysdate(), sysdate());
INSERT INTO `abnormal_prompt` VALUES ('8288e79e-a124-11ec-aa26-8c8caa7e2ae0', '3', '硬件', '170008', '上集成异常', '松开急停可恢复', sysdate(), sysdate());
