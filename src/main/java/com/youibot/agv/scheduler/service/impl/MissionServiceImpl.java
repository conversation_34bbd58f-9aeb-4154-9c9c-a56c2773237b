package com.youibot.agv.scheduler.service.impl;

import com.youibot.agv.scheduler.engine.pathplan.agvmapgraph.MapGraphUtil;
import com.youibot.agv.scheduler.engine.util.MissionActionSortUtils;
import com.youibot.agv.scheduler.entity.*;
import com.youibot.agv.scheduler.exception.ADSParameterException;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.mapper.MissionMapper;
import com.youibot.agv.scheduler.service.*;
import com.youibot.agv.scheduler.util.MessageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.text.MessageFormat;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class MissionServiceImpl extends BaseServiceImpl<Mission> implements MissionService {

    @Autowired
    private MissionMapper missionMapper;

    @Autowired
    private MissionActionService missionActionService;

    @Autowired
    private MissionActionParameterService missionActionParameterService;

    @Autowired
    private MissionGlobalVariableService missionGlobalVariableService;

    @Override
    public Integer insert(Mission mission) {
//        checkName(mission);
        return super.insert(mission);
    }

    @Override
    public Integer update(Mission mission) {
//        checkName(mission);
        Integer version = mission.getVersion();
        mission.setVersion(version == null ? 1 : version + 1);
        return super.update(mission);
    }

    private void checkName(Mission mission) {
        List<Mission> missionList = this.findAll();
        for (Mission missionDB : missionList) {
            if (!StringUtils.isEmpty(mission.getName()) && mission.getName().equals(missionDB.getName()) && !missionDB.getId().equals(mission.getId())) {
                throw new ADSParameterException(MessageFormat.format(MessageUtils.getMessage("service.name_is_exists"), mission.getName()));
            }
        }
    }

    @Override
    public void updateMissionVersion(String missionId) {
        missionMapper.updateMissionVersion(missionId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void invalid(String id) {
        Mission mission = this.selectById(id);
        if (mission == null) {
            return;
        }
        mission.setIsDeleted(1);
        this.updateByPrimaryKeySelective(mission);
        //关联作废动作列表
        List<MissionAction> missionActions = missionActionService.selectByMissionId(id);
        if (!CollectionUtils.isEmpty(missionActions)) {
            missionActions.stream().peek(x -> x.setIsDeleted(1)).collect(Collectors.toList());
            missionActionService.batchUpdate(missionActions);
        }
    }

    @Override
    public Mission selectByCode(String missionCode) {
        Example example = new Example(Mission.class);
        example.createCriteria().andEqualTo("code", missionCode).andEqualTo("isDeleted", 0);
        return super.selectOneByExample(example);
    }

     @Override
  public Mission selectById(String missionId) {
             Example example = new Example(Mission.class);
             example.createCriteria().andEqualTo("id", missionId).andEqualTo("isDeleted", 0);
            return super.selectOneByExample(example);
    }

    @Override
    public List<Mission> selectByIds(List<String> missionIds) {
              Example example = new Example(Mission.class);
             example.createCriteria().andIn("id", missionIds).andEqualTo("isDeleted", 0);
              return super.selectByExample(example);
    }


}
