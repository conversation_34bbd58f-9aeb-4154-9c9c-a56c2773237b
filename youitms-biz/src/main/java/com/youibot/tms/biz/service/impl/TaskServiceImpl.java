package com.youibot.tms.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Throwables;
import com.youibot.tms.biz.entity.Agv;
import com.youibot.tms.biz.entity.StorageLocation;
import com.youibot.tms.biz.entity.Task;
import com.youibot.tms.biz.entity.TaskNode;
import com.youibot.tms.biz.enums.TaskNodeType;
import com.youibot.tms.biz.enums.TaskStatus;
import com.youibot.tms.biz.flow.dto.FleetAndTmsTaskDTO;
import com.youibot.tms.biz.forest.dto.MissionWorkParam;
import com.youibot.tms.biz.forest.dto.fleet5.TaskDetailApiDTO;
import com.youibot.tms.biz.forest.dto.fleet5.TaskNodeDetailApiDTO;
import com.youibot.tms.biz.forest.dto.fleet5.TaskOperateApiDTO;
import com.youibot.tms.biz.forest.enums.FleetTaskNodeStatus;
import com.youibot.tms.biz.forest.enums.FleetTaskNodeType;
import com.youibot.tms.biz.mapper.TaskMapper;
import com.youibot.tms.biz.service.*;
import com.youibot.tms.biz.thread.TransportCommandHandleThread;
import com.youibot.tms.biz.thread.TransportCommandHandleThreadUtils;
import com.youibot.tms.common.core.redis.RedisCache;
import com.youibot.tms.common.exception.ServiceException;
import com.youibot.tms.common.utils.ToolUtil;
import com.youibot.tms.system.dto.DataScopeParam;
import com.youibot.workflow.core.executor.WorkFlowExecutor;
import com.youibot.workflow.entity.WorkFlow;
import com.youibot.workflow.service.WorkFlowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 任务模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Slf4j
@Service
public class TaskServiceImpl extends ServiceImpl<TaskMapper, Task> implements TaskService {

    @Resource
    private TaskMapper taskMapper;
    @Resource
    private RedisCache redisCache;
    @Autowired
    private WorkFlowService workFlowService;
    @Autowired
    private WorkFlowExecutor workFlowExecutor;
    @Autowired
    private FleetMissionWorkExecutingQueue fleetMissionWorkExecutingQueue;


    private final static String ORDER_CODE_INCREMENT_KEY = "order_code_increment";

    /**
     * 不足4位的数字，前面补0
     */
    private final static String FORMAT = "%04d";

    private final static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");

    @Override
    public Page<Task> page(Page<Task> page, QueryWrapper<Task> queryWrapper) {
        DataScopeParam dataScopeParam = DataScopeParam.buildDataScopeParam("biz_task_zone", "task_id");
        if (dataScopeParam != null) {
            queryWrapper.apply(dataScopeParam.getFilterSql());
        }
        return this.baseMapper.selectPage(page, queryWrapper, dataScopeParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean save(Task task) {
        //查询起点、终点、交换点信息
        return super.save(task);
    }


    @Override
    public synchronized String incrementCode() {
        Date now = new Date();
        long sn = redisCache.increment(ORDER_CODE_INCREMENT_KEY);
        return "T" + DATE_FORMAT.format(now) + String.format(FORMAT, sn);
    }

    @Override
    public Task selectByTaskCode(String taskCode) {
        return taskMapper.selectByTaskCode(taskCode);
    }

    @Override
    public Task selectByWorkFlowId(String workFlowId) {
        return taskMapper.selectByWorkFlowId(workFlowId);
    }


    @Autowired
    private FleetProxyService fleetProxyService;
    @Resource
    private AgvService agvService;
    @Resource
    private McsTaskService mcsTaskService;
    @Resource
    private StorageLocationService storageLocationService;

    @Override
    public void stopTask(Long taskId) {
        Task task = this.getById(taskId);
        if (ToolUtil.isEmpty(task)) {
            throw new ServiceException("未找到此任务！任务ID：" + taskId);
        }
        stopTask(task);
    }

    @Override
    public void stopTaskByCode(String taskCode) {
        Task task = this.selectByTaskCode(taskCode);
        if (ToolUtil.isEmpty(task)) {
            throw new ServiceException("未找到此任务！任务编号：" + taskCode);
        }
        stopTask(task);
    }

    private void stopTask(Task task) {
        TransportCommandHandleThreadUtils.waitRunning();
        if (!TaskStatus.EXECUTING.equals(task.getStatus()) && !TaskStatus.SUSPENDED.equals(task.getStatus()) && !TaskStatus.ERROR.equals(task.getStatus())) {
            throw new ServiceException("任务已经结束，无法终止！");
        }
        task.setStatus(TaskStatus.CANCEL);
        this.updateById(task);
        //终止所有的MCS任务
        mcsTaskService.stopTaskByTmsTaskCode(task.getCode());
        try {
            if (ToolUtil.isNotEmpty(task.getWorkFlowId())) {
                WorkFlow workFlow = workFlowService.getById(task.getWorkFlowId());
                if (workFlow != null) {
                    try {
                        if (ToolUtil.isNotEmpty(workFlow.getCurrentNodeId())) {
                            fleetProxyService.stopMissionByWorkFlowNodeId(workFlow.getCurrentNodeId());
                        }
                    } catch (Exception e) {
                        log.error("停止Fleet任务时发生异常：{}", Throwables.getStackTraceAsString(e));
                    }
                    workFlowExecutor.stop(workFlow);
                }
            }
        } catch (Exception e) {
            log.error("停止任务：[{}]对应的工作流流程时发生异常：{}", task.getCode(), Throwables.getStackTraceAsString(e));
        }
        try {
            agvService.releaseAgvByTaskCode(task.getCode());
        } catch (Exception e) {
            log.error("停止任务：[{}]释放机器人时发生异常：{}", task.getCode(), Throwables.getStackTraceAsString(e));
        }
        //释放被预定的机器人库位
        storageLocationService.releaseStorageLocationByTaskCode(task.getCode());
    }

    @Override
    public void continueExecution(Task task) {
        log.info("继续执行任务：{}", task.getCode());
        WorkFlow workFlow = workFlowService.getById(task.getWorkFlowId());
        if (workFlow != null) {
            String currentNodeId = workFlow.getCurrentNodeId();
            if (ToolUtil.isNotEmpty(currentNodeId)) {
                FleetAndTmsTaskDTO fleetAndTmsTaskDTO = fleetMissionWorkExecutingQueue.get(currentNodeId);
                if (fleetAndTmsTaskDTO != null) {
                    MissionWorkParam missionWorkParam = fleetAndTmsTaskDTO.getMissionWorkParam();
                    if (missionWorkParam != null) {
                        String missionWorkId = missionWorkParam.getMissionWorkId();
                        log.info("继续执行任务：{},Fleet任务编号:{}", task.getCode(), missionWorkId);
                        TaskDetailApiDTO missionWork = fleetProxyService.getMissionWork(missionWorkId);
                        if (missionWork != null) {
                            log.info("继续执行任务：{},查询到Fleet任务信息:{}", task.getCode(), JSON.toJSONString(missionWork));
                            List<TaskNodeDetailApiDTO> taskNodeDetails = missionWork.getTaskNodeDetails();
                            if (ToolUtil.isNotEmpty(taskNodeDetails)) {
                                for (TaskNodeDetailApiDTO taskNodeDetail : taskNodeDetails) {
                                    if (FleetTaskNodeType.RobotArmScript.name().equals(taskNodeDetail.getType())) {
                                        if (taskNodeDetail.getStatus().equals(FleetTaskNodeStatus.Fail.name())) {
                                            log.info("继续执行任务：{},Fleet任务节点：{}", task.getCode(), taskNodeDetail.getName());
                                            //先判断物料是已经抓到车上了还是没有抓到车上，假如在车身上，那么判断当前是OW取料还是放料任务,假如是取料，并且物料在车上，那么就跳过任务，
                                            // 假如不在车身上就重试任务，假如是放料，并且物料在车上，那么就重试任务，假如物料不在车上，那么就跳过任务。
                                            TaskNode taskNode = workFlowExecutor.getCurrentForItemValue(workFlow, TaskNode.class);
                                            Agv agv = agvService.selectByAgvCode(fleetAndTmsTaskDTO.getMissionWorkParam().getAgvCode());
                                            List<StorageLocation> storageLocations = storageLocationService.list(Wrappers.<StorageLocation>lambdaQuery().eq(StorageLocation::getCarrierCode, taskNode.getCarrierCode())
                                                    .eq(StorageLocation::getDeviceCode, agv.getDeviceCode()));
                                            if (ToolUtil.isNotEmpty(storageLocations)) {
                                                TaskNodeType taskNodeType = taskNode.getTaskNodeType();
                                                if (TaskNodeType.OW_PICK_UP.equals(taskNodeType) || TaskNodeType.OW_AND_ATS_PICK_UP.equals(taskNodeType)
                                                        || TaskNodeType.OW_PICK_UP_AND_ATS_PUT_DOWN.equals(taskNodeType)) {
                                                    log.info("物料在机器人车身，由于当前是取料节点，因此跳过此节点继续执行后续节点！流程ID:[{}]tms任务编号:[{}]mcs任务编号:[{}]机器人编号:[{}]物料编码:[{}]"
                                                            , workFlow.getId(), task.getCode(), taskNode.getCustomTaskCode(),
                                                            fleetAndTmsTaskDTO.getMissionWorkParam().getAgvCode(), taskNode.getCarrierCode());
                                                    TaskOperateApiDTO taskOperateApiDTO = new TaskOperateApiDTO();
                                                    taskOperateApiDTO.setTaskNo(missionWorkId);
                                                    taskOperateApiDTO.setExternalTaskNo(task.getCode());
                                                    workFlowExecutor.suspend(workFlow);
                                                    fleetProxyService.cancelTask(taskOperateApiDTO);
                                                    workFlowExecutor.skipCurrentNodeRecovery(workFlow);
                                                } else {
                                                    log.info("物料在机器人车身，由于当前是放料节点，因此恢复此节点继续执行此节点！流程ID:[{}]tms任务编号:[{}]mcs任务编号:[{}]机器人编号:[{}]物料编码:[{}]"
                                                            , workFlow.getId(), task.getCode(), taskNode.getCustomTaskCode(),
                                                            fleetAndTmsTaskDTO.getMissionWorkParam().getAgvCode(), taskNode.getCarrierCode());
                                                    TaskOperateApiDTO taskOperateApiDTO = new TaskOperateApiDTO();
                                                    taskOperateApiDTO.setTaskNo(missionWorkId);
                                                    taskOperateApiDTO.setExternalTaskNo(task.getCode());
                                                    workFlowExecutor.suspend(workFlow);
                                                    fleetProxyService.cancelTask(taskOperateApiDTO);
                                                    workFlowExecutor.recovery(workFlow);
                                                }
                                                return;
                                            } else {
                                                TaskNodeType taskNodeType = taskNode.getTaskNodeType();
                                                if (TaskNodeType.OW_PICK_UP.equals(taskNodeType) || TaskNodeType.OW_AND_ATS_PICK_UP.equals(taskNodeType)
                                                        || TaskNodeType.OW_PICK_UP_AND_ATS_PUT_DOWN.equals(taskNodeType)) {
                                                    log.info("物料不在机器人车身，由于当前是取料节点，因此恢复此节点继续执行此节点！流程ID:[{}]tms任务编号:[{}]mcs任务编号:[{}]机器人编号:[{}]物料编码:[{}]"
                                                            , workFlow.getId(), task.getCode(), taskNode.getCustomTaskCode(),
                                                            fleetAndTmsTaskDTO.getMissionWorkParam().getAgvCode(), taskNode.getCarrierCode());
                                                    TaskOperateApiDTO taskOperateApiDTO = new TaskOperateApiDTO();
                                                    taskOperateApiDTO.setTaskNo(missionWorkId);
                                                    taskOperateApiDTO.setExternalTaskNo(task.getCode());
                                                    workFlowExecutor.suspend(workFlow);
                                                    fleetProxyService.cancelTask(taskOperateApiDTO);
                                                    workFlowExecutor.recovery(workFlow);
                                                } else {
                                                    log.info("物料不在机器人车身，由于当前是放料节点，因此跳过此节点继续执行后续节点！流程ID:[{}]tms任务编号:[{}]mcs任务编号:[{}]机器人编号:[{}]物料编码:[{}]"
                                                            , workFlow.getId(), task.getCode(), taskNode.getCustomTaskCode(),
                                                            fleetAndTmsTaskDTO.getMissionWorkParam().getAgvCode(), taskNode.getCarrierCode());
                                                    TaskOperateApiDTO taskOperateApiDTO = new TaskOperateApiDTO();
                                                    taskOperateApiDTO.setTaskNo(missionWorkId);
                                                    taskOperateApiDTO.setExternalTaskNo(task.getCode());
                                                    workFlowExecutor.suspend(workFlow);
                                                    fleetProxyService.cancelTask(taskOperateApiDTO);
                                                    workFlowExecutor.skipCurrentNodeRecovery(workFlow);
                                                }
                                                return;
                                            }
                                        } else {
                                            log.info("继续执行任务：{},机械臂控制类型的Fleet任务节点没有发生异常，无需恢复任务：{}", task.getCode(), taskNodeDetail.getName());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        throw new ServiceException("任务正在执行中！");
    }
}
