pipeline {
  agent any

  environment {
	// 是否启用脚本, false情况下不进行部署或不进行更新
	STATE = true

	// 脚本模式, 部署(DEPLOY)或者更新(UPDATE), DEPLOY模式下前端、后端不能同时提交
	MODE = "UPDATE"

	// 前端(WEB) 后端(BACK)
	PLATFORM = "BACK"

	// 前端配置文件在static目录下为V1，根目录下为V2
    MARK_VERSION = "V2"

	// 前端及后端分支
	FRONT_END_PROJECT_BRANCH = "feature/v4.8.3-tms2"
	BACK_END_PROJECT_BRANCH = "feature/v4.8.3-tms2"

	// 前端及后端镜像
	FRONT_END_IMAGE_PATH = "youifleetweb:latest"
    BACK_END_IMAGE_PATH = "youifleet:latest"

	// 应用名称
	APPLICATION_NAME = "youifleet-v4-8-3-tms2"

	// 前端（80）、后端（8080、5005）、MQ（1883、18083）端口号
	FRONT_END_APP_NODEPORT = 3000
	BACK_END_APP_NODEPORT = 3001
    BACK_END_DEBUG_APP_NODEPORT = 3002
	MQ_ONE_APP_NODEPORT = 3003
	MQ_TWO_APP_NODEPORT = 3004

	// 部署模式, K8S或者DOCKER部署
	DEPLOYMENT_MODE = "DOCKER"

    // DOCKER部署配置
	// 用于DOCKER部署模式下部署的服务器IP及挂载目录, 只需要修改DEPLOYMENT_IP即可
	DEPLOYMENT_IP = "**********"

	DOCKER_MOUNT_DIR = "/data/sda1/dockerVolume"
	DOCKER_APP_MOUNT_DIR = "${DOCKER_MOUNT_DIR}/${APPLICATION_NAME}"
  }

  triggers {
    GenericTrigger (
            causeString: 'Triggered by $ref',
            genericVariables: [[key: 'ref', value: '$.ref']],

            printContributedVariables: true,
            printPostContent: true,
			// 分支名, 用于自动触发判断
            regexpFilterExpression: '^refs/heads/feature/v4.8.3-tms2$',
            regexpFilterText: '$ref',

            token: 'youibot@%!A2022fleet'
    )
  }

  stages {
    stage("Clean Workspace") {
            steps {
                sh """
                sudo rm -rf ./*
                """
            }
    }

	stage("Deployment") {
      when { 
        expression { 
          env.STATE && env.MODE == "DEPLOY" 
        }
      }
      steps {
        script {
          if (env.DEPLOYMENT_MODE == "K8S") {
			sh """#!/bin/bash -il
            cd /data/K8S/script/application
            cp ./k8s-youifleet-v2-statefulset.yaml ./k8s-youifleet-v2-deploy-statefulset.yaml
			sed -i "s/APP_NAME/$APPLICATION_NAME/g" k8s-youifleet-v2-deploy-statefulset.yaml
			sed -i 's#FRONT_END_IMAGE_PATH#*************:8000/youifleet/${FRONT_END_IMAGE_PATH}#g' k8s-youifleet-v2-deploy-statefulset.yaml
            sed -i 's#BACK_END_IMAGE_PATH#*************:8000/youifleet/${BACK_END_IMAGE_PATH}#g' k8s-youifleet-v2-deploy-statefulset.yaml
			sed -i "s/FRONT_END_APP_NODEPORT/$FRONT_END_APP_NODEPORT/g" k8s-youifleet-v2-deploy-statefulset.yaml
			sed -i "s/BACK_END_APP_NODEPORT/$BACK_END_APP_NODEPORT/g" k8s-youifleet-v2-deploy-statefulset.yaml
			sed -i "s/BACK_END_DEBUG_APP_NODEPORT/$BACK_END_DEBUG_APP_NODEPORT/g" k8s-youifleet-v2-deploy-statefulset.yaml
			sed -i "s/MQ_ONE_APP_NODEPORT/$MQ_ONE_APP_NODEPORT/g" k8s-youifleet-v2-deploy-statefulset.yaml
			sed -i "s/MQ_TWO_APP_NODEPORT/$MQ_TWO_APP_NODEPORT/g" k8s-youifleet-v2-deploy-statefulset.yaml
			kubectl apply -f ./k8s-youifleet-v2-deploy-statefulset.yaml
			"""
          } else {
            sh """#!/bin/bash
            sshpass -p 'youibot@%!YH20221226' ssh youibot@$DEPLOYMENT_IP '''
            sudo mkdir -p $DOCKER_APP_MOUNT_DIR/web
            sudo mkdir -p $DOCKER_APP_MOUNT_DIR/nginx && sudo chmod 777 -R $DOCKER_APP_MOUNT_DIR
            sudo cp /data/K8S/script/application/youifleetweb/nginx.conf $DOCKER_APP_MOUNT_DIR/nginx
            sudo docker run -d --name $APPLICATION_NAME -p $BACK_END_APP_NODEPORT:8080 -p $BACK_END_DEBUG_APP_NODEPORT:5005 -v $DOCKER_APP_MOUNT_DIR:/server/ -v /home/<USER>/youibot_map:/home/<USER>/youibot_map/ *************:8000/youifleet/youifleet:latest
            sudo docker run -d --name $APPLICATION_NAME-mq -p $MQ_ONE_APP_NODEPORT:1883 -p $MQ_TWO_APP_NODEPORT:18083 emqx/emqx:4.2.1
            sudo docker run -d --name $APPLICATION_NAME-nginx -p $FRONT_END_APP_NODEPORT:80 -v $DOCKER_APP_MOUNT_DIR/web:/usr/share/nginx/html/ -v $DOCKER_APP_MOUNT_DIR/nginx/nginx.conf:/etc/nginx/nginx.conf:ro nginx:latest
            sed -i "s/8080/$BACK_END_APP_NODEPORT/g" $DOCKER_APP_MOUNT_DIR/nginx/nginx.conf
            '''
            """
          }
        }
      }
    }

	stage("K8S Service Init") {
		when {
			allOf {
				expression { env.STATE.toBoolean() }
				expression { env.MODE == "DEPLOY" }
				expression { env.DEPLOYMENT_MODE == "K8S" }
			}
		}
		steps {
			build job: "YOUIFleetFront", parameters: [
				string(name: "APPLICATION_NAME", value: env.APPLICATION_NAME)
			]
		}
	}

    stage("Update Service") {
      when { 
        expression { 
          env.STATE && (env.MODE == "DEPLOY" || (env.PLATFORM in ["WEB", "BACK"] && env.MODE == "UPDATE")) 
        }
      }
      steps {
        script {
          def targetJob = (env.PLATFORM == "WEB") ? "YOUIFleetWEBUpdate" : "YOUIFleetUpdate"
          def params = [
            string(name: "APPLICATION_NAME", value: env.APPLICATION_NAME),
            string(name: "DEPLOYMENT_MODE", value: env.DEPLOYMENT_MODE),
            string(name: "DEPLOYMENT_IP", value: env.DEPLOYMENT_IP),
            string(name: "DOCKER_APP_MOUNT_DIR", value: env.DOCKER_APP_MOUNT_DIR)
          ]

          if (env.PLATFORM == "WEB") {
            params.add(string(name: "FRONT_END_PROJECT_BRANCH", value: env.FRONT_END_PROJECT_BRANCH))
            params.add(string(name: "BACK_END_APP_NODEPORT", value: env.BACK_END_APP_NODEPORT))
            params.add(string(name: "MARK_VERSION", value: env.MARK_VERSION))
          } else {
            params.add(string(name: "BACK_END_PROJECT_BRANCH", value: env.BACK_END_PROJECT_BRANCH))
          }

          build job: targetJob, parameters: params
        }
      }
    }

    stage("Service Restart") {
      when { expression { env.STATE } }
      steps {
        script {
          def cmd = (env.DEPLOYMENT_MODE == "K8S") ? 
            "kubectl rollout restart statefulset $APPLICATION_NAME -n youifleet-system" :
            "sshpass -p 'youibot@%!YH20221226' ssh youibot@$DEPLOYMENT_IP 'sudo docker restart $APPLICATION_NAME'"

          sh """#!/bin/bash -il
          $cmd
          """
        }
      }
    }
  }

  post {
    always {
      script {
        def status = currentBuild.currentResult
        def message = generateDingTalkMessage(status == "SUCCESS")
        dingtalk (
            robot: "K8S",
            type:'ACTION_CARD',
            atAll: true,
            title: "项目更新监控：${env.JOB_NAME}",
            messageUrl: 'xxxx',
            text: message
        )
      }
    }
  }

}

def generateDingTalkMessage(isSuccess) {
    return [
    "### [${env.JOB_NAME}](${env.JOB_URL})",
    "- 任务：[${currentBuild.displayName}](${env.BUILD_URL})",
    "- 状态：<font color=${isSuccess ? '#00FF00' : '#EE0000'}>${isSuccess ? '成功' : '失败'}</font>",
    "- 持续时间：${currentBuild.durationString.split('and counting')[0]}",
    "- 执行人：${currentBuild.buildCauses.shortDescription}"
    ]
}