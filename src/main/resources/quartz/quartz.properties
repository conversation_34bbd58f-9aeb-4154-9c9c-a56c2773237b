#ID����Ϊ�Զ���ȡ ÿһ�����벻ͬ �����е�����ʵ������Ψһ�ģ�
org.quartz.scheduler.instanceId=AUTO
org.quartz.scheduler.instanceName=clusteredScheduler
#ָ�����ȳ�������߳��Ƿ�Ӧ�����ػ��߳�
#org.quartz.scheduler.makeSchedulerThreadDaemon=true
#ThreadPoolʵ�ֵ�����
org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=false
#�߳�����
org.quartz.threadPool.threadCount=5
#�߳����ȼ�
org.quartz.threadPool.threadPriority=5
#���ݱ��淽ʽΪ�־û�
org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX
#StdJDBCDelegate˵��֧�ּ�Ⱥ
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate
#quartz�ڲ����ǰ׺
org.quartz.jobStore.tablePrefix=QRTZ_
#�Ƿ���뼯Ⱥ
org.quartz.jobStore.isClustered=false
#����������ҵ�ӳ�ʱ��
org.quartz.jobStore.misfireThreshold=60000