#system
system.missing.parameter=missing parameter
system.data.type.error=Data type error
system.code.format.error=Code [%s] format error
system.name.format.error=Name [%s] format error
system.code.duplicate.error=Code [%s] duplicate error
system.code.is.empty.error=Code [%s] cannot be empty
system.operate.timeout=Operate timeout
system.directory.is.empty=The file of current directory [%s] is empty
system.directory.is.not.exists=The current directory [%s] is not exists
system.account.is.not.exists=Account does not exist
system.account.is.already.exists=Account already exists
system.account.passwd.is.error=Password error
system.account.old.passwd.is.error=The original password is incorrect
system.account.is.disable=The account has been deactivated
system.account.has.no.permission=This account does not have any page permissions, please contact the administrator
system.menu.config.is.error=The superior menu cannot serve itself
system.menu.delete.error=Delete the submenu or button first
system.account.permission.deny=Insufficient user permissions
system.account.token.invalid=Not logged in or logged in has expired
system.db.record.duplicate.error=The record already exists in the database
system.no.avaliable.marker=The set of incoming points is not available
system.no.avaliable.vehicle=The incoming robot collection is not available
system.version.is.dismatch=当前系统版本不匹配（待翻译）

#license
license.certificate.failure=Certificate Unknown Exception
license.certificate.not.uploaded=Certificate not uploaded!
license.certificate.validate.failed=The uploaded certificate data is incorrect
license.certificate.expired=Certificate has expired

#excel
excel.export.error=exporting Excel file exception
excel.import.error=parsing Excel file exception
excel.import.code.empty=Import failed, data validation failed on line [{0}], reason: empty encoding
excel.import.name.empty=Import failed, data validation failed on line [{0}], reason: name is empty
excel.import.type.empty=Import failed, data validation failed on line [{0}], reason: type is empty
excel.import.row.empty=Import failed, data validation failed on line [{0}], reason: empty row count
excel.import.colum.empty=Import failed, data validation failed on line [{0}], reason: empty number of columns
excel.import.layer.empty=Import failed, data validation failed on line [{0}], reason: number of layers is empty
excel.import.workHeight.empty=Import failed, data validation failed on line [{0}], reason: job height is empty
excel.import.code.exists=Import failed, data validation failed on line [{0}], reason: encoding [{1}] already exists
excel.import.barcode.exists=Import failed, data validation failed on line [{0}], reason: container encoding [{1}] already exists
excel.import.usage.status.error=Import failed, data validation failed on line [{0}], reason: status is error or it may not match the current system language
excel.import.occupy.status.error=Import failed, data validation failed on line [{0}], reason: occupy status is error or it may not match the current system language
excel.import.notice.level.error=Import failed, data validation failed on line [{0}], reason: notice level is error or it may not match the current system language
excel.import.event.type.error=Import failed, data validation failed on line [{0}], reason: event type is error or it may not match the current system language

excel.import.code.repeat=Import failed, duplicate code [{0}] exists in Excel table
excel.import.barcode.repeat=Import failed, there are duplicate container codes [{0}] in the Excel table
excel.import.data.convert.fail=Import failed, data validation failed on line [{0}], reason: incorrect data format in column [{1}]
excel.import.format.error=The table format is incorrect.Please export the template and import it again
excel.import.name.format.error=The imported file format is incorrect

#language
language.empty=Language does not exist.
language.inuse=The language is in use.
language.upload.file.error=The uploaded file format is incorrect.
language.upload.missing.info=The language pack is missing the info file.
language.upload.missing.service =The language pack is missing the service file.
language.upload.missing.web=The language pack is missing a web file.
language.code.duplicate=Code [{0}] already exists.
language.code.nonstandard=Code [{0}] does not meet internationalization requirements.

#vehicle
vehicle.batchOperation.result ={0} robots were successfully operated, and {1} robots failed.
vehicle.operation.fail=Operation robot [%s] failed, [%s]
vehicle.connect.fail=Channel not connected or disconnected
vehicle.request.timeout=Request timeout，%s
vehicle.is.not.login.error=Robot not logged in, please log in first!
vehicle.network.error=Robot operation failed, robot [%s] network not connected
vehicle.code.duplicate=Code [{0}] already exists, please enter again.
vehicle.name.duplicate=名称[{0}]已存在, 请重新输入（待翻译）
vehicle.name.pattern=当前值[{0}]需要由字母或数字、下划线组成（待翻译）
vehicle.network.anomaly =Communication failed, please check whether the robot is connected.
vehicle.navigation.cancel=Robot path navigation was cancelled.
vehicle.locationMapCode.empty=The robot has no location map in use.
vehicle.out.of.trace.error=Vehicle out of trace！
vehicle.aim.marker.unreachable.error=The target point is unreachable!
vehicle.empty=The robot does not exist.
vehicle.type.empty=The robot type [%s] does not exist.
vehicle.type.bind.duplicate=The robot type [%s] cannot be bounded duplicate.
vehicle.type.excel.head.code=code
vehicle.type.excel.head.name=name
vehicle.type.excel.head.rotatable=Allow rotation.
vehicle.group.excel.head.code=code
vehicle.group.excel.head.name=name
vehicle.wait.reason.marker.occupied=前方点位[{0}]被其他机器人占用（待翻译）
vehicle.wait.reason.marker.inControlArea=前方点位[{0}]处于封控区域（待翻译）
vehicle.wait.reason.noParkArea.occupied=前方禁停区域被其他机器人占用（待翻译）
vehicle.wait.reason.auto.door.closed=前方自动门设备未开门（待翻译）
vehicle.wait.reason.airshower.door.closed=前方风淋门设备未开门（待翻译）
vehicle.wait.reason.elevator.door.closed=前方电梯设备未开门（待翻译）
vehicle.wait.reason.marker.inForbiddenArea=前方点位[{0}]处于禁入区域（待翻译）

#warehouse
warehouse.code.empty=location code is empty
warehouse.material.type.absent=material code [{0}] does not exist
warehouse.code.duplicate=location code [{0}] already exists, please re-enter
warehouse.barcode.duplicate=container code [{0}] already exists, please re-enter
warehouse.start.need.less.than.end=Start row/column/layer must be smaller than End row/column/layer
warehouse.height.number.consistent=The number of floors should be equal to the number of job height data
warehouse.material.type.error=Error in obtaining material type data
warehouse.empty.or.disable=The storage location does not exist or has been disabled
warehouse.status.lock=This storage location has been locked
warehouse.status.store=This location is in storage status
warehouse.barcode.inuse=This container barcode is already in use by another storage location
warehouse.area.code.duplicate=warehouse area code [{0}] already exists, please re-enter
warehouse.type.code.duplicate=type code [{0}] already exists, please re-enter
warehouse.area.excel.head.code=encoding
warehouse.area.excel.head.name=Name
warehouse.type.excel.head.code=encoding
warehouse.type.excel.head.name=Name
warehouse.excel.head.code=encoding
warehouse.excel.head.type.code=type encoding
warehouse.excel.head.area.code=warehouse code
warehouse.excel.head.row=number of rows
warehouse.excel.head.colum=number of columns
warehouse.excel.head.layer=Number of Floors
warehouse.excel.head.work.height=homework height
warehouse.excel.head.work.marker=job location
warehouse.excel.head.occupy.status=occupancy status
warehouse.excel.head.barcode=container barcode
warehouse.excel.head.usage.code=enabled status
warehouse.excel.head.param1=Extended Attribute 1
warehouse.excel.head.param2=Extended Attribute 2
warehouse.excel.head.param3=Extended Attribute 3
warehouse.excel.head.param4=Extended Attribute 4
warehouse.excel.head.param5=Extended Attribute 5
warehouse.excel.head.status.updatedate=Warehouse occupy status update date
warehouse.usage.status.enable=enabled
warehouse.usage.status.disable=disabled
warehouse.occupy.status.lock=locked
warehouse.occupy.status.store=storage
warehouse.occupy.status.free=free

#statistics
statistics.cannot.gt.today=The selected date cannot exceed today
statistics.cannot.lt.one.year.ago=The selected date cannot be earlier than one year, as stated in statistics.canot.lt.one.ear.ago
statistics.start.cannot.gt.end=The start time cannot be greater than the end time
statistics.name.avgHandleTime=Average processing time
statistics.name.number=quantity
statistics.name.other=Other
statistics.name.no.map=No map available
statistics.name.low=low
statistics.name.lower=lower
statistics.name.medium=medium
statistics.name.higher=higher
statistics.name.high=high
statistics.name.busy=busy
statistics.name.free=idle
statistics.name.abnormal=Exception
statistics.name.charge=Charging
statistics.name.park=Parking
statistics.name.work=assignment
statistics.name.disconnect=not connected
statistics.name.wait=waiting
statistics.name.running=Execution
statistics.name.total.task=total task
statistics.name.create=New
statistics.name.finished=completed
statistics.name.cancel=Cancel
statistics.name.avgExecuteTime=Average Execution Time
statistics.name.avgAllocateTime=Average execution time
statistics.name.actual.rate=actual utilization rate
statistics.name.theory.rate=theoretical utilization rate
statistics.name.cpu.rate.total=Total usage rate
statistics.name.cpu.rate.java=JAVA
statistics.name.cpu.rate.mysql=MySQL
statistics.name.cpu.rate.other=Other
statistics.name.memo.rate.total=Total memory rate
statistics.name.memo.rate.java=JAVA
statistics.name.memo.rate.mysql=MySQL
statistics.name.memo.rate.other=Other
statistics.name.disk.total=Total disk capacity
statistics.name.disk.used=Disk used capacity
statistics.name.disk.free=Disk free capacity
statistics.name.mysql.select=Select
statistics.name.mysql.delete=Delete
statistics.name.mysql.update=Update
statistics.name.mysql.insert=Insert
statistics.name.mysql.times.select=Select
statistics.name.mysql.times.delete=Delete
statistics.name.mysql.times.update=Update
statistics.name.mysql.times.insert=Insert
statistics.unit.number=number
statistics.unit.tai=unit
statistics.unit.time=times
statistics.unit.second=seconds
statistics.unit.minute=minutes
statistics.unit.hour=hour
statistics.unit.day=days

#config
config.unit.day=days
config.unit.meter=meters
config.unit.second=seconds
config.value.range=configured data range
config.company.name=Shenzhen YOUIBOT Robot Technology Co., Ltd
config.title.systemVersion=version number
config.remark.systemVersion=version number
config.title.ownCompany=All rights reserved
config.remark.ownCompany=All rights reserved
config.title.licenseCompanyName=Authorization Information - Company Name
config.remark.licenseCompanyName=Authorization Information - Company Name
config.title.licenseValidTimeRange=Authorization Information - Validity Period
config.remark.licenseValidTimeRange=Authorization Information - Validity Period
config.title.userOptLogExpireTime=Operation Log
config.remark.userOptLogExpireTime=User Action Log Retention Time
config.title.interfaceLogExpireTime=Interface Log
config.remark.interfaceLogExpireTime=system interface log retention time
config.title.runningLogExpireTime=Run Log
config.remark.runningLogExpireTime=system run log retention time
config.title.notificationExpireTime=notification
config.remark.notificationExpireTime=system alarm notification retention time
config.title.businessDataExpireTime=Business Data
config.remark.businessDataExpireTime=the retention time of operational data for the business, including task lists and other business data
config.title.reportDataExpireTime=Report Data
config.remark.reportDataExpireTime=Record the retention time of archived report data
config.title.markerSpacingCheck=Point spacing
config.remark.markerSpacingCheck=Point spacing judgment enabled
config.title.markerSpacing=Point spacing
config.remark.markerSpacing=Point spacing (mm)
config.title.markerAndPathSpacingCheck=Point to path spacing
config.remark.markerAndPathSpacingCheck=Point to path spacing judgment enabled
config.title.markerAndPathSpacing=Point to path spacing
config.remark.markerAndPathSpacing=Point to path spacing (mm)
config.title.blockCheckEnable=obstacle avoidance and rePlanning
config.remark.blockCheckEnable=When the robot encounters an obstacle, the scheduling system re plans the path to make the robot detour
config.title.blockCheckInterval=obstacle avoidance and rePlanning
config.remark.blockCheckInterval=obstacle avoidance trigger duration
config.title.removeBlockInterval=obstacle avoidance and rePlanning
config.remark.removeBlockInterval=duration of obstacle reset
config.title.abnormalVehicleRunPolicy=Faulty Robot
config.remark.abnormalVehicleRunPolicy=Set the robot's execution strategy when encountering a fault or offline robot ahead
config.title.freeVehicleRunPolicy=Idle Robot
config.remark.freeVehicleRunPolicy=When encountering an idle robot ahead, set the execution strategy for the robot
config.title.workVehicleRunPolicy=Busy Robot
config.remark.workVehicleRunPolicy=When encountering a busy robot ahead, set the robot's execution strategy
config.title.avoidMarkerTypes=Conflict Avoidance Point Types
config.remark.avoidMarkerTypes=The types of points that robots are allowed to avoid when multiple robot paths conflict
config.title.pathApplyLength=issuing path distance
config.remark.pathApplyLength=The path length issued by the scheduling system to the robot, which can be increased when the network environment is poor
config.title.autoReleaseResource=Disconnected Release Resource
config.remark.autoReleaseResource=After the robot disconnects the network for a certain time, the scheduling system releases the position and area occupied by the robot
config.title.disconnectionTime=Disconnect to release resources
config.remark.disconnectionTime=duration of disconnection
config.title.occupyResourceRange=Virtual radius of robot
config.remark.occupyResourceRange=When the robot is not at a point or path, use the center of the robot as the center and this value as the radius to occupy all points contained in the circle
config.title.trackRadius=track radius
config.remark.trackRadius=When the distance from the robot to the nearest point or path exceeds this value, the system determines that the robot has derailed
config.title.channelAvoidance=Channel Avoidance
config.remark.channelAvoidance=After enabling channel avoidance, the opposing robot can actively avoid outside the channel
config.title.autoDoorAdvanceLength=Call the automatic door in advance
config.remark.autoDoorAdvanceLength=When the distance between the robot and the point in front of the automatic door is less than this value, it will call to open the door.When this value is 0, it will not call in advance
config.title.showerDoorAdvanceLength=Call the air shower door in advance
config.remark.showerDoorAdvanceLength=When the distance between the robot and the point in front of the air shower door is less than this value, it will call to open the door.When this value is 0, it will not call in advance
config.title.elevatorAdvanceLength=Call the elevator in advance
config.remark.elevatorAdvanceLength=When the distance from the robot to the point in front of the elevator is less than this value, it will call to open the door.When this value is 0, it will not call in advance
config.title.highPerformanceMode=高性能模式（待翻译）
config.remark.highPerformanceMode=当调度大规模机器人时，可启用高性能模式提高调度效率（待翻译）
config.title.highBattery=high battery (%)
config.remark.highBattery=high battery (%)
config.title.lowBattery=low battery (%)
config.remark.lowBattery=low battery (%)
config.title.chargeTaskTypeId=Create Task
config.remark.chargeTaskTypeId=Create Task
config.title.autoCharge=Status
config.remark.autoCharge=Status
config.title.parkTaskTypeId=Create Task
config.remark.parkTaskTypeId=Create Task
config.title.autoPark=Status
config.remark.autoPark=Status
config.title.pushCycle=push cycle
config.remark.pushCycle=push only once within this time range
config.title.vehicleStatusPushUrl=Robot Status Interface Address
config.remark.vehicleStatusPushUrl=When the robot status changes, push the robot message to the interface address
config.title.noticePushUrl=message push address
config.remark.noticePushUrl=When a message of the wrong type appears or disappears, push abnormal data to the interface address
config.title.pdaVersion=Latest version of PDA
config.remark.pdaVersion=Latest version of PDA
config.title.vehicleStatusPushInterval=Monitoring console robot status push interval (ms)
config.remark.vehicleStatusPushInterval=Monitoring console robot status push interval (ms)
config.title.noticePushInterval=Monitoring console small bell push interval (ms)
config.remark.noticePushInterval=Monitoring console small bell push interval (ms)
config.title.mapElementPushInterval=Monitoring Console Map Element Status Change Push Interval (ms)
config.remark.mapElementPushInterval=Monitoring Console Map Element Status Change Push Interval (ms)
config.title.driveFreeVehicleFreeTime=空闲时长（待翻译）
config.remark.driveFreeVehicleFreeTime=当驱赶空闲机器人时，空闲机器人需要达到指定空闲时长（待翻译）
config.title.thirdSystemTrafficAreaReqUrl=交管区域资源申请地址（待翻译）
config.remark.thirdSystemTrafficAreaReqUrl=Fleet作为客户端时向服务端申请交管区域资源，设定申请接口地址（待翻译）
config.property.is.exist.error=The system attribute already exists!
config.property.type.is.duplicate.error=Duplicate system type classification!

#notice
notice.missing.notice.config=The system is missing the configuration information for this exception code
notice.level.common=normal
notice.level.warning=warning
notice.level.error=error
notice.record.status.not.close=not closed
notice.record.status.closed=closed
notice.config.excel.head.code=encoding
notice.config.excel.head.level=level
notice.config.excel.head.source=source
notice.config.excel.head.invalidTime=invalidTime
notice.config.excel.head.desc=description
notice.config.excel.head.solution=solution
notice.record.excel.head.code=encoding
notice.record.excel.head.level=level
notice.record.excel.head.source=source
notice.record.excel.head.desc=description
notice.record.excel.head.solution=solution
notice.record.excel.head.status=status
notice.record.excel.head.vehicle=vehicle
notice.record.excel.head.task=task
notice.record.excel.head.device=device
notice.record.excel.head.map=map
notice.record.excel.head.create.date=creation time
notice.record.excel.head.update.date=update time
notice.record.excel.head.close.date=close time
notice.record.http.request.param=Abnormal notification status change HTTP push, request parameters
notice.record.http.response.param=Abnormal notification status change HTTP push, response parameters

notice.description.100001=base heartbeat timeout.
notice.solution.100001=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.100002=Core board startup timeout.
notice.solution.100002=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.100020=Communication failure of status light.
notice.solution.100020=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100021=Signal lamp communication failure.
notice.solution.100021=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100030=Single point array radar 1 communication timeout.
notice.solution.100030=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100031=Single point array radar 1 failure.
notice.solution.100031=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100032=Single point array radar 2 communication timeout.
notice.solution.100032=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100033=Single point array radar 2 failure.
notice.solution.100033=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100034=Single point array radar 3 communication timeout.
notice.solution.100034=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100035=Single point array radar 3 fault.
notice.solution.100035=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100036=Single point array radar 4 communication timeout.
notice.solution.100036=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100037=Single point array radar 4 failure.
notice.solution.100037=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100050=Security Radar 1 communication timeout.
notice.solution.100050=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100051=Safety radar 1 failure.
notice.solution.100051=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100052=Security Radar 2 communication timeout.
notice.solution.100052=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100053=Security Radar 2 malfunction.
notice.solution.100053=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100054=Security Radar 3 communication timeout.
notice.solution.100054=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100055=Security Radar 3 malfunction.
notice.solution.100055=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100056=Security Radar 4 communication timeout.
notice.solution.100056=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100057=Safety Radar 4 is out of order.
notice.solution.100057=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100100 =Communication timeout of Re expansion board 0.
notice.solution.100100=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100101 =Communication timeout of RE expansion board 1.
notice.solution.100101=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100102 =Communication timeout of Re expansion board 2.
notice.solution.100102=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100103 =Communication timeout of RE expansion board 3.
notice.solution.100103=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100104 =Communication timeout of RE expansion board 4.
notice.solution.100104=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100105 =Communication timeout of RE expansion board 5.
notice.solution.100105=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100106 =Communication timeout of RE expansion board 6.
notice.solution.100106=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100107 =Communication timeout of Re expansion board 7.
notice.solution.100107=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100108 =Communication timeout of RE expansion board 8.
notice.solution.100108=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100109 =Communication timeout of Re extension board 9.
notice.solution.100109=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100110 =Communication timeout of RE expansion board 10.
notice.solution.100110=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100111 =Communication timeout of RE expansion board 11.
notice.solution.100111=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100112 =Communication timeout of RE expansion board 12.
notice.solution.100112=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100113 =Communication timeout of RE expansion board 13.
notice.solution.100113=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.100114 =Communication timeout of RE expansion board 14.
notice.solution.100114=The wiring may be loose. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.100115 =Communication timeout of RE expansion board 15.
notice.solution.100115=The wiring may be loose. Please contact the after-sales department if it can't be recovered or the abnormality occurs many times.
notice.description.105000=CAN communication failure of the left driver.
notice.solution.105000=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105001=Start of Can node of left driver timed out.
notice.solution.105001=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105002=Power-on startup of the left drive timed out.
notice.solution.105002=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105003=PDO configuration file of left drive is missing.
notice.solution.105003=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105009=Error in undefined left drive.
notice.solution.105009=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105010=Left-hand drive encoder failure.
notice.solution.105010=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105011=The voltage of the left driver is too high.
notice.solution.105011=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105012=The voltage of the left driver is too low.
notice.solution.105012=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105013=Left-hand drive overcurrent
notice.solution.105013=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105014=Left drive temperature is too high.
notice.solution.105014=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105015=The operation error of the left driver is too large.
notice.solution.105015=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105016=Left driver logic voltage is abnormal.
notice.solution.105016=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105017=Left-hand drive motor failure.
notice.solution.105017=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105018=Left drive failure.
notice.solution.105018=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105019=System data error on the left drive.
notice.solution.105019=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105020=Error in software operation of left driver.
notice.solution.105020=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105021=The motor configuration of the left drive is incorrect.
notice.solution.105021=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105022=Error in positive limit of left drive.
notice.solution.105022=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105023=Negative limit error of the left driver.
notice.solution.105023=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105024=overspeed alarm of the left drive.
notice.solution.105024=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105025=The left drive is overloaded.
notice.solution.105025=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105026=CAN BUS fault of left driver.
notice.solution.105026=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105027=Wrong OpenCan parameter of the left driver.
notice.solution.105027=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105028=The communication of OpenCan on the left driver is abnormal.
notice.solution.105028=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105029=Abnormal locking of the left drive.
notice.solution.105029=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105030=Left drive stopped abnormally.
notice.solution.105030=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105031=Abnormal phase voltage of left driver.
notice.solution.105031=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105100=CAN communication failure of right driver.
notice.solution.105100=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105101=Start of Can node of right driver timed out.
notice.solution.105101=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105102=Power-on startup of right drive timed out.
notice.solution.105102=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105103=PDO configuration file of right drive is missing.
notice.solution.105103=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105109=Error in undefined right drive.
notice.solution.105109=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105110=encoder failure of right drive.
notice.solution.105110=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105111=The voltage of the right driver is too high.
notice.solution.105111=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105112=The voltage of the right driver is too low.
notice.solution.105112=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105113=Right-hand drive overcurrent.
notice.solution.105113=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105114=Right drive temperature is too high.
notice.solution.105114=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105115=The operation error of the right drive is too large.
notice.solution.105115=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105116=Abnormal logic voltage of right driver.
notice.solution.105116=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105117=Motor fault of right drive.
notice.solution.105117=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105118=Right drive failure.
notice.solution.105118=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105119=System data error in the right drive.
notice.solution.105119=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105120=Error in software operation of right driver.
notice.solution.105120=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105121=The motor configuration of the right drive is incorrect.
notice.solution.105121=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105122=Error in positive limit of right drive.
notice.solution.105122=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105123=Negative limit error of the right driver.
notice.solution.105123=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105124=overspeed alarm of the right drive.
notice.solution.105124=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105125=The right drive is overloaded.
notice.solution.105125=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105126=CAN BUS fault of right driver.
notice.solution.105126=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105127=Wrong OpenCan parameter of the right driver.
notice.solution.105127=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105128=Abnormal OpenCan communication of the right driver.
notice.solution.105128=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105129=Abnormal locking of the right drive.
notice.solution.105129=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105130=Right drive stopped abnormally.
notice.solution.105130=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105131=Abnormal phase voltage of right driver.
notice.solution.105131=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105200=CAN communication failure of lifting driver.
notice.solution.105200=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105201=The startup of Can node of lifting driver timed out.
notice.solution.105201=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105202=The lifting drive found the origin timeout.
notice.solution.105202=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105203=Power-on start-up of lifting drive timed out.
notice.solution.105203=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105204=PDO configuration parameter of lifting driver is missing.
notice.solution.105204=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105209=Error in undefined lifting drive.
notice.solution.105209=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105210=encoder failure of lifting drive.
notice.solution.105210=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105211=The voltage of the lifting drive is too high.
notice.solution.105211=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105212=The voltage of the lifting drive is too low.
notice.solution.105212=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105213=Overcurrent of lifting driver.
notice.solution.105213=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105214=Elevating drive temperature is too high.
notice.solution.105214=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105215=The operation error of the lifting driver is too large.
notice.solution.105215=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105216=Abnormal logic voltage of lifting driver.
notice.solution.105216=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105217=Motor failure of lifting drive.
notice.solution.105217=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105218=Elevator drive failure.
notice.solution.105218=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105219=System data error of lifting drive.
notice.solution.105219=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105220=Error occurred when the software of lifting driver runs.
notice.solution.105220=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105221=Motor configuration of lifting drive is wrong.
notice.solution.105221=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105222=Error in positive limit of lifting drive.
notice.solution.105222=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105223=Negative limit error of lifting driver.
notice.solution.105223=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105224=overspeed alarm of lifting drive.
notice.solution.105224=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105225=Elevator drive is overloaded.
notice.solution.105225=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105226=CAN BUS fault of lifting driver.
notice.solution.105226=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105227=Error in OpenCan parameter of lifting driver.
notice.solution.105227=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105228=OpenCan communication of lifting driver is abnormal.
notice.solution.105228=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105229=The lifting drive stopped abnormally.
notice.solution.105229=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105230=Abnormal phase voltage of lifting driver.
notice.solution.105230=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105300=CAN communication failure of rotary drive.
notice.solution.105300=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105301=The startup of the rotary drive Can node timed out.
notice.solution.105301=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105302=Rotating the drive to find the origin timed out.
notice.solution.105302=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105303=Power-on startup of rotary drive timed out.
notice.solution.105303=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105304=PDO configuration parameter of rotary drive is missing.
notice.solution.105304=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105309=Error in undefined rotary drive.
notice.solution.105309=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105310=Rotary drive encoder failure.
notice.solution.105310=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105311=The voltage of the rotary drive is too high.
notice.solution.105311=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105312=The voltage of the rotary drive is too low.
notice.solution.105312=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105313=Rotating drive overcurrent.
notice.solution.105313=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105314=Rotating drive temperature is too high.
notice.solution.105314=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105315=The running error of the rotary drive is too large.
notice.solution.105315=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105316=Abnormal logic voltage of rotary drive.
notice.solution.105316=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105317=Rotating drive motor failure.
notice.solution.105317=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105318=Rotating drive failure.
notice.solution.105318=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105319=System data error of rotating drive.
notice.solution.105319=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105320=Error in running the rotary drive software.
notice.solution.105320=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105321=Wrong motor configuration of rotary drive.
notice.solution.105321=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105322=Error in positive limit of rotary drive.
notice.solution.105322=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105323=Negative limit error of rotary drive.
notice.solution.105323=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105324=overspeed alarm of rotating drive.
notice.solution.105324=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105325=Rotating drive is overloaded.
notice.solution.105325=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105326=CAN BUS fault of rotary drive.
notice.solution.105326=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105327=Wrong OpenCan parameter of rotary drive.
notice.solution.105327=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105328=OpenCan communication of rotary drive is abnormal.
notice.solution.105328=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105329=Rotating drive stopped abnormally.
notice.solution.105329=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105330=Abnormal phase voltage of rotary driver.
notice.solution.105330=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105500=BMS communication is abnormal.
notice.solution.105500=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105501=BMS undefined error.
notice.solution.105501=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105502=Abnormal charging overcurrent.
notice.solution.105502=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105503=Abnormal discharge and overcurrent.
notice.solution.105503=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105504=Battery undervoltage
notice.solution.105504=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105505=Battery overvoltage
notice.solution.105505=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105506=Overall undervoltage.
notice.solution.105506=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105507=Overall overpressure
notice.solution.105507=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105508=The differential pressure is higher than the upper limit of allowable differential pressure for charging.
notice.solution.105508=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105509=Cell pressure difference exceeds the upper limit.
notice.solution.105509=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105510=Battery temperature exceeds the upper limit of charging temperature.
notice.solution.105510=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105511=Battery temperature exceeds the upper limit of discharge temperature.
notice.solution.105511=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105512=The battery cell temperature difference exceeds the upper limit of charging temperature.
notice.solution.105512=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105513=The battery cell temperature difference exceeds the upper limit of discharge temperature.
notice.solution.105513=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105514=Battery temperature is lower than the lower limit of charge and discharge.
notice.solution.105514=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105515=MOS tube temperature exceeds the upper limit of charging temperature.
notice.solution.105515=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105516 =The temperature difference of MOS tube exceeds the upper limit of temperature difference.
notice.solution.105516=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105551=bms exception (including protection and sampling failure ...)
notice.solution.105551=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105552=BMS package is abnormal (including overvoltage, overcurrent, high and low temperature faults, etc.)
notice.solution.105552=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105610=PDO1 overcurrent
notice.solution.105610=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105611=PDO2 overcurrent
notice.solution.105611=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105612=PDO3 overcurrent
notice.solution.105612=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105613=PDO4 overcurrent
notice.solution.105613=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105614=PDO5 overcurrent
notice.solution.105614=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105615=PDO6 overcurrent
notice.solution.105615=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105616=PDO7 overcurrent
notice.solution.105616=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105617=Total PDO current is too large.
notice.solution.105617=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105700=Four wheels and four turns, and the left steering motor is undefined.
notice.solution.105700=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105701=Four wheels and four turns, software error of left steering motor.
notice.solution.105701=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105702=Four wheels and four turns, and the left steering motor is overvoltage.
notice.solution.105702=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105703=Four wheels and four turns, and the left steering motor has low voltage.
notice.solution.105703=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105704=Four wheels and four turns, the left steering motor started incorrectly.
notice.solution.105704=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105705=Four wheels and four turns, and the left steering motor has overcurrent.
notice.solution.105705=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105706=Four wheels and four turns, left steering motor encoder error.
notice.solution.105706=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105707=Four wheels and four turns, the temperature of the left steering motor is too high.
notice.solution.105707=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105708=Four wheels and four turns, the left steering motor circuit board is too high.
notice.solution.105708=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105709=Four wheels and four turns, communication of the left steering motor timed out.
notice.solution.105709=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105800=Four wheels and four turns, and the right steering motor is undefined.
notice.solution.105800=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105801=Four wheels and four turns, software error of right steering motor.
notice.solution.105801=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105802=Four wheels and four turns, and the right steering motor is overvoltage.
notice.solution.105802=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105803=Four wheels and four turns, and the right steering motor has low voltage.
notice.solution.105803=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105804=Four wheels and four turns, the right steering motor started incorrectly.
notice.solution.105804=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105805=Four wheels and four turns, and the right steering motor has overcurrent.
notice.solution.105805=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105806=Four wheels and four turns, encoder error of right steering motor.
notice.solution.105806=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105807=Four wheels and four turns, and the temperature of the right steering motor is too high.
notice.solution.105807=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105808=Four wheels and four turns, and the circuit board of the right steering motor is too high.
notice.solution.105808=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105809=Four wheels and four turns, communication of the right steering motor timed out.
notice.solution.105809=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105900=CAN communication failure of steering wheel and front wheel travel driver.
notice.solution.105900=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105901=The Can node of steering wheel and front wheel traveling driver started overtime.
notice.solution.105901=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105902=Power-on start-up of steering wheel and front wheel driving device timed out.
notice.solution.105902=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105903=PDO configuration parameter of steering wheel and front wheel walking driver is missing.
notice.solution.105903=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105909=Steering wheel, front wheel travel driver is not defined correctly.
notice.solution.105909=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105910=Steering wheel, front wheel travel driver encoder failure.
notice.solution.105910=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105911=The driver voltage of steering wheel and front wheels is too high.
notice.solution.105911=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105912=The driver voltage of steering wheel and front wheels is too low.
notice.solution.105912=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105913=The steering wheel, the front-wheel running driver has an overcurrent.
notice.solution.105913=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105914=Steering wheel, front wheel running motor temperature is too high.
notice.solution.105914=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105915=The running error of steering wheel and front wheel walking motor is too large.
notice.solution.105915=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105916=Abnormal logic voltage of steering wheel and front wheel traveling motor.
notice.solution.105916=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105917=Malfunction of steering wheel and front wheel traveling motor.
notice.solution.105917=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105918=Malfunction of steering wheel and front wheel traveling drive.
notice.solution.105918=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105919=Data error in steering wheel and front wheel drive system.
notice.solution.105919=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105920=Error in running the software of steering wheel and front wheel walking driver.
notice.solution.105920=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105921=Wrong configuration of steering wheel and front wheel motor.
notice.solution.105921=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105922=Overspeed alarm of steering wheel and front wheel traveling motor.
notice.solution.105922=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105923=Steering wheel, front wheel running motor is overloaded.
notice.solution.105923=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105924=CAN BUS fault of steering wheel and front wheel traveling driver.
notice.solution.105924=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105925=The OpenCan parameter of steering wheel and front wheel traveling driver is wrong.
notice.solution.105925=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105926=OpenCan communication of steering wheel and front wheel driving device is abnormal.
notice.solution.105926=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105927=The steering wheel and the front wheel are locked abnormally.
notice.solution.105927=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.105928=Steering wheel, front wheel travel driver stopped abnormally.
notice.solution.105928=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.105929=Abnormal phase voltage of steering wheel and front wheel traveling motor.
notice.solution.105929=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106000=CAN communication failure of steering wheel and front wheel steering driver.
notice.solution.106000=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106001=The Can node of steering wheel and front wheel driver started overtime.
notice.solution.106001=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106002=Power-on start-up of steering wheel and front wheel steering driver timed out.
notice.solution.106002=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106003=PDO configuration parameter of steering wheel and front wheel driver is missing.
notice.solution.106003=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106004=Steering wheel, front wheel steering driver found the origin timeout.
notice.solution.106004=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106009=Steering wheel, front wheel steering drive undefined error.
notice.solution.106009=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106010=Steering wheel, front wheel steering driver encoder failure.
notice.solution.106010=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106011=Steering wheel, front wheel steering driver voltage is too high.
notice.solution.106011=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106012=Steering wheel, front wheel steering driver voltage is too low.
notice.solution.106012=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106013=Steering wheel, front wheel steering driver overcurrent.
notice.solution.106013=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106014=Steering wheel, front wheel steering motor temperature is too high.
notice.solution.106014=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106015=The running error of steering motor of steering wheel and front wheel is too large.
notice.solution.106015=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106016=Abnormal logic voltage of steering motor of steering wheel and front wheel.
notice.solution.106016=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106017=Steering wheel, front wheel steering motor failure.
notice.solution.106017=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106018=Steering wheel, front wheel steering drive failure.
notice.solution.106018=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106019=Data error in steering drive system of steering wheel and front wheel.
notice.solution.106019=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106020=Steering wheel, front wheel steering driver software running error.
notice.solution.106020=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106021=The steering motor of steering wheel and front wheel is configured incorrectly.
notice.solution.106021=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106022=Positive limit error of steering driver of steering wheel and front wheel.
notice.solution.106022=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106023=Negative limit error of steering driver of steering wheel and front wheel.
notice.solution.106023=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106024=Overspeed alarm for steering motor of steering wheel and front wheel.
notice.solution.106024=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106025=Steering wheel, front wheel steering motor is overloaded.
notice.solution.106025=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106026=CAN BUS fault of steering wheel and front wheel driver.
notice.solution.106026=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106027=The OpenCan parameter of steering wheel and front wheel driver is wrong.
notice.solution.106027=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106028=OpenCan communication of steering wheel and front wheel steering driver is abnormal.
notice.solution.106028=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106029=Steering wheel and front wheel steering driver stopped abnormally.
notice.solution.106029=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106030=Abnormal phase voltage of steering motor of steering wheel and front wheel.
notice.solution.106030=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106100=CAN communication failure of steering wheel and rear wheel travel driver.
notice.solution.106100=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106101=The Can node of steering wheel and rear wheel traveling driver started overtime.
notice.solution.106101=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106102=Power-on start-up of steering wheel and rear wheel travel driver timed out.
notice.solution.106102=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106103=PDO configuration parameter of steering wheel and rear wheel walking driver is missing.
notice.solution.106103=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106109=The steering wheel and rear wheel travel driver are not defined correctly.
notice.solution.106109=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106110=Steering wheel, rear wheel travel driver encoder failure.
notice.solution.106110=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106111=The driver voltage of steering wheel and rear wheel is too high.
notice.solution.106111=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106112=The driver voltage of steering wheel and rear wheel is too low.
notice.solution.106112=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106113=Overcurrent of steering wheel and rear wheel traveling driver.
notice.solution.106113=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106114=Steering wheel, rear wheel traveling motor temperature is too high.
notice.solution.106114=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106115=The running error of steering wheel and rear wheel walking motor is too large.
notice.solution.106115=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106116=Abnormal logic voltage of steering wheel and rear wheel traveling motor.
notice.solution.106116=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106117=Malfunction of steering wheel and rear wheel traveling motor.
notice.solution.106117=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106118=Malfunction of steering wheel and rear wheel traveling drive.
notice.solution.106118=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106119=Data error in steering wheel and rear wheel driving system.
notice.solution.106119=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106120=Error in running the software of steering wheel and rear wheel walking driver.
notice.solution.106120=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106121=The motor of steering wheel and rear wheel is misconfigured.
notice.solution.106121=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106122=Overspeed alarm of steering wheel and rear wheel traveling motor.
notice.solution.106122=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106123=Steering wheel, rear wheel traveling motor is overloaded.
notice.solution.106123=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106124=CAN BUS fault of steering wheel and rear wheel traveling driver.
notice.solution.106124=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales department.
notice.description.106125=The OpenCan parameter of steering wheel and rear wheel traveling driver is wrong.
notice.solution.106125=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106126=Abnormal OpenCan communication of steering wheel and rear wheel traveling drive.
notice.solution.106126=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106127=The steering wheel and the rear wheel are locked abnormally.
notice.solution.106127=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106128=Steering wheel, the rear wheel stops running abnormally.
notice.solution.106128=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106129=Abnormal phase voltage of steering wheel and rear wheel traveling motor.
notice.solution.106129=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106200=CAN communication failure of steering wheel and rear wheel steering driver.
notice.solution.106200=Restart recovery is required. Please contact the after-sales department if it cannot be recovered or the abnormality appears many times. Restart recovery is required. If it cannot be recovered or the abnormality appears many times, please contact the after-sales department.
notice.description.106201=The Can node of steering wheel and rear wheel driver started overtime.
notice.solution.106201=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106202=Power-on start-up of steering wheel and rear wheel driver timed out.
notice.solution.106202=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106203=PDO configuration parameters of steering wheel and rear wheel driver are missing.
notice.solution.106203=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106204=Steering wheel, rear wheel steering driver found the origin timeout.
notice.solution.106204=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales department.
notice.description.106209=The steering wheel and rear wheel steering drive are undefined.
notice.solution.106209=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106210=Steering wheel, rear wheel steering driver encoder failure.
notice.solution.106210=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106211=Steering wheel, rear wheel steering driver voltage is too high.
notice.solution.106211=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106212=Steering wheel, rear wheel steering driver voltage is too low.
notice.solution.106212=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106213=Steering wheel, rear wheel steering driver overcurrent.
notice.solution.106213=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106214=The steering motor of steering wheel and rear wheel is overheated.
notice.solution.106214=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106215=The running error of steering motor of steering wheel and rear wheel is too large.
notice.solution.106215=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106216=Abnormal logic voltage of steering motor of steering wheel and rear wheel.
notice.solution.106216=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106217=Steering motor failure of steering wheel and rear wheel.
notice.solution.106217=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106218=Steering wheel, rear wheel steering drive failure.
notice.solution.106218=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106219=Data error in steering drive system of steering wheel and rear wheel.
notice.solution.106219=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106220=Error in software operation of steering driver for steering wheel and rear wheel.
notice.solution.106220=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106221=The steering motor of steering wheel and rear wheel is configured incorrectly.
notice.solution.106221=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106222=Positive limit error of steering driver of steering wheel and rear wheel.
notice.solution.106222=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106223=Negative limit error of steering driver of steering wheel and rear wheel.
notice.solution.106223=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106224=Overspeed alarm for steering motor of steering wheel and rear wheel.
notice.solution.106224=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact the after-sales department.
notice.description.106225=Steering wheel and rear wheel steering motor are overloaded.
notice.solution.106225=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106226=CAN BUS fault of steering wheel and rear wheel driver.
notice.solution.106226=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106227=The OpenCan parameter of steering wheel and rear wheel driver is wrong.
notice.solution.106227=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106228=OpenCan communication of steering wheel and rear wheel driver is abnormal.
notice.solution.106228=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.106229=Steering wheel and rear wheel steering drive stopped abnormally.
notice.solution.106229=It needs to be restarted for recovery. If it cannot be recovered or the abnormality occurs many times, please contact after-sales.
notice.description.106230=Abnormal phase voltage of steering motor of steering wheel and rear wheel.
notice.solution.106230=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.110001=Left drive-exception.
notice.solution.110001=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110002=Left drive-exception.
notice.solution.110002=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110003=Left drive-exception.
notice.solution.110003=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110004=Left drive-exception.
notice.solution.110004=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110005=Left drive-exception.
notice.solution.110005=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110006=Left drive-exception.
notice.solution.110006=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110007=Left drive-exception.
notice.solution.110007=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110008=Left drive-exception.
notice.solution.110008=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110009=Left drive-exception.
notice.solution.110009=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110010=Left drive-exception.
notice.solution.110010=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110011=Left drive-exception.
notice.solution.110011=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110012=Left drive-exception.
notice.solution.110012=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110013=Left drive-exception.
notice.solution.110013=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110014=Left drive-exception.
notice.solution.110014=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110015=Left drive-exception.
notice.solution.110015=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110016=Left drive-exception.
notice.solution.110016=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110017=Left drive-exception.
notice.solution.110017=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110018=Left drive-exception.
notice.solution.110018=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110019=Left drive-exception.
notice.solution.110019=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110020=Left drive-exception.
notice.solution.110020=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110021=Left drive-exception.
notice.solution.110021=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110022=Left drive-exception.
notice.solution.110022=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110023=Left drive-exception.
notice.solution.110023=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110024=Left drive-exception.
notice.solution.110024=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110025=Left drive-exception.
notice.solution.110025=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110026=Left drive-exception.
notice.solution.110026=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110027=Left drive-exception.
notice.solution.110027=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110028=Left drive-exception.
notice.solution.110028=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110029=Left drive-exception.
notice.solution.110029=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110030=Left drive-exception.
notice.solution.110030=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110031=Left drive-exception.
notice.solution.110031=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110032=Right drive-exception.
notice.solution.110032=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110033=Right drive-exception.
notice.solution.110033=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110034=Right drive-exception.
notice.solution.110034=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110035=Right drive-exception.
notice.solution.110035=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110036=Right drive-exception.
notice.solution.110036=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110037=Right drive-exception.
notice.solution.110037=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110038=Right drive-exception.
notice.solution.110038=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110039=Right drive-exception.
notice.solution.110039=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110040=Right drive-exception.
notice.solution.110040=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110041=Right drive-exception.
notice.solution.110041=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110042=Right drive-exception.
notice.solution.110042=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110043=Right drive-exception.
notice.solution.110043=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110044=Right drive-exception.
notice.solution.110044=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110045=Right drive-exception.
notice.solution.110045=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110046=Right drive-exception.
notice.solution.110046=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110047=Right drive-exception.
notice.solution.110047=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110048=Right drive-exception.
notice.solution.110048=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110049=Right drive-exception.
notice.solution.110049=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110050=Right drive-exception.
notice.solution.110050=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110051=Right drive-exception.
notice.solution.110051=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110052=Right drive-exception.
notice.solution.110052=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110053=Right drive-exception.
notice.solution.110053=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110054=Right drive-exception.
notice.solution.110054=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110055=Right drive-exception.
notice.solution.110055=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110056=Right drive-exception.
notice.solution.110056=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110057=Right drive-exception.
notice.solution.110057=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110058=Right drive-exception.
notice.solution.110058=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110059=Right drive-exception.
notice.solution.110059=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110060=Right drive-exception.
notice.solution.110060=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110061=Right drive-exception.
notice.solution.110061=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110062=Right drive-exception.
notice.solution.110062=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110063=Jack-up driver-exception.
notice.solution.110063=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110064=Jack-up driver-exception.
notice.solution.110064=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110065=Jack-up driver-exception.
notice.solution.110065=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110066=Jack-up driver-exception.
notice.solution.110066=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110067=Jack-up driver-exception.
notice.solution.110067=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110068=Jack-up driver-exception.
notice.solution.110068=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110069=Jack-up driver-exception.
notice.solution.110069=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110070=Jack-up driver-exception.
notice.solution.110070=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110071=Jack-up driver-exception.
notice.solution.110071=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110072=Jack-up driver-exception.
notice.solution.110072=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110073=Jack-up driver-exception.
notice.solution.110073=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110074=Jack-up driver-exception.
notice.solution.110074=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110075=Jack-up driver-exception.
notice.solution.110075=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110076=Jack-up driver-exception.
notice.solution.110076=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110077=Jack-up driver-exception.
notice.solution.110077=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110078=Jack-up driver-exception.
notice.solution.110078=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110079=Jack-up driver-exception.
notice.solution.110079=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110080=Jack-up driver-exception.
notice.solution.110080=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110081=Jack-up driver-exception.
notice.solution.110081=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110082=Jack-up driver-exception.
notice.solution.110082=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110083=Jack-up driver-exception.
notice.solution.110083=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110084=Jack-up driver-exception.
notice.solution.110084=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110085=Jack-up driver-exception.
notice.solution.110085=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110086=Jack-up driver-exception.
notice.solution.110086=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110087=Jack-up driver-exception.
notice.solution.110087=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110088=Jack-up driver-exception.
notice.solution.110088=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110089=Jack-up driver-exception.
notice.solution.110089=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110090=Jack-up driver-exception.
notice.solution.110090=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110091=Rotating drive-exception.
notice.solution.110091=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110092=Rotating drive-exception.
notice.solution.110092=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110093=Rotating drive-exception.
notice.solution.110093=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110094=Rotating drive-exception.
notice.solution.110094=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110095=Rotating drive-exception.
notice.solution.110095=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110096=Rotating drive-exception.
notice.solution.110096=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110097=Rotating drive-exception.
notice.solution.110097=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110098=Rotating drive-exception.
notice.solution.110098=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110099=Rotating drive-exception.
notice.solution.110099=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110100=Rotating drive-exception.
notice.solution.110100=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110101=Rotating drive-exception.
notice.solution.110101=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110102=Rotating drive-exception.
notice.solution.110102=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110103=Rotating drive-exception.
notice.solution.110103=Abnormal drive: it needs to be shut down and allowed to stand for one hour before use. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110104=Rotating drive-exception.
notice.solution.110104=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110105=Rotating drive-exception.
notice.solution.110105=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110106=Rotating drive-exception.
notice.solution.110106=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110107=Rotating drive-exception.
notice.solution.110107=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110108=Rotating drive-exception.
notice.solution.110108=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110109=Rotating drive-exception.
notice.solution.110109=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110110=Rotating drive-exception.
notice.solution.110110=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110111=Rotating drive-exception.
notice.solution.110111=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110112=Rotating drive-exception.
notice.solution.110112=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110113=Rotating drive-exception.
notice.solution.110113=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110114=Rotating drive-exception.
notice.solution.110114=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110115=Rotating drive-exception.
notice.solution.110115=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110116=Rotating drive-exception.
notice.solution.110116=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110117=Rotating drive-exception.
notice.solution.110117=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110118=Rotating drive-exception.
notice.solution.110118=Drive exception: recovery needs to be restarted. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.110200=Motor initialization-exception.
notice.solution.110200=The message sending of the walking motor failed, possibly because the walking motor is not configured with correct parameters or is not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110201=Motor initialization-exception.
notice.solution.110201=The message sending of the lifting motor failed, possibly because the walking motor is not configured with correct parameters or is not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110202=Motor initialization-exception.
notice.solution.110202=The message sending of the plug-in motor failed, possibly because the walking motor is not configured with correct parameters or is not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110203=Motor initialization-exception.
notice.solution.110203=The message sending of rotating motor failed, probably because the walking motor was not configured with correct parameters or was not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110204=Motor initialization-exception.
notice.solution.110204=Sending the message of the clamping motor failed, possibly because the walking motor is not configured with correct parameters or is not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110205=Motor initialization-exception.
notice.solution.110205=Sen Chuang's lifting motor message failed to be sent, probably because the walking motor was not configured with correct parameters or was not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110206=Motor initialization-exception.
notice.solution.110206=Sen Chuang rotating motor failed to send the message, probably because the walking motor was not configured with correct parameters or was not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110207=Motor initialization-exception.
notice.solution.110207 =The message sending of SR motor failed, probably because the walking motor was not configured with correct parameters or was not connected. Please contact the after-sales service if the restart cannot be resumed.
notice.description.110300=Motor control-abnormal.
notice.solution.110300=The rotation command issued is out of range: -180~180. Clear the error and reissue it.
notice.description.110301=Motor control-abnormal.
notice.solution.110301=The rotation speed command issued is out of range. Clear the error and reissue it. Maximum speed is 8 revolutions per minute.
notice.description.110302=Motor control-abnormal.
notice.solution.110302=The lifting instruction issued is out of range. Clear the error and reissue it. If you can't succeed within a reasonable range, please contact the after-sales department.
notice.description.110303=Motor control-abnormal.
notice.solution.110303=The vertical speed instruction issued is out of range. Clear the error and reissue it. Speed is 10 mm/s.
notice.description.110400=Motor running-abnormal.
notice.solution.110400=trigger an emergency stop and then release it, or restart the robot and observe whether it recovers, otherwise contact the after-sales department.
notice.description.110401=Motor running-abnormal.
notice.solution.110401=trigger an emergency stop and then release it, or restart the robot and observe whether it recovers, otherwise contact the after-sales department.
notice.description.110402=Motor running-abnormal.
notice.solution.110402=trigger an emergency stop and then release it, or restart the robot and observe whether it recovers, otherwise contact the after-sales department.
notice.description.110403=Motor running-abnormal.
notice.solution.110403=trigger an emergency stop and then release it, or restart the robot and observe whether it recovers, otherwise contact the after-sales department.
notice.description.113001=Can card-exception.
notice.solution.113001=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113002=Can card-exception.
notice.solution.113002=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113003=Can card-exception.
notice.solution.113003=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113004=Can card-exception.
notice.solution.113004=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113005=Can card-exception.
notice.solution.113005=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113006=Can card-exception.
notice.solution.113006=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113007=Can card-exception.
notice.solution.113007=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113008=Can card-exception.
notice.solution.113008=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113009=Can card-exception.
notice.solution.113009=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113010=Can card-exception.
notice.solution.113010=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113011=Can card-exception.
notice.solution.113011=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113012=Can card-exception.
notice.solution.113012=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113013=Can card-exception.
notice.solution.113013=Can card is abnormal: it needs to be restarted for recovery. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.113015=Can card-exception.
notice.solution.113015=Can card is abnormal: the CAN card may not be connected or the connecting cable may be disconnected. If the restart can't solve it, please contact the after-sales.
notice.description.113016=Can card-exception.
notice.solution.113016=Can card is abnormal: the CAN card equipment may be abnormal. If it is not recovered after repeated restarts, please contact the after-sales department.
notice.description.113017=Can card-exception.
notice.solution.113017=Can card is abnormal: the CAN card equipment may be abnormal. If it is not recovered after repeated restarts, please contact the after-sales department.
notice.description.114000=IMU- exception.
notice.solution.114000=Check whether the serial port number is correct.
notice.description.114001=IMU- exception.
notice.solution.114001=Check whether the serial port is connected properly.
notice.description.114002=IMU- exception.
notice.solution.114002=Check whether the serial port is connected properly.
notice.description.114003=IMU- exception.
notice.solution.114003=Check whether the serial port is connected properly and the interference.
notice.description.114004=IMU- exception.
notice.solution.114004=Check whether the serial port is connected properly and the interference.
notice.description.120001=Charging-Abnormal
notice.solution.120001=It is possible that the communication line of the battery is not connected well, or the intermittent communication fails. Re-execute the task. If there is still an error, please contact the after-sales department.
notice.description.120002=Charging-Abnormal
notice.solution.120002=If the error is still reported after restarting and retrying, please contact after-sales.
notice.description.120003=Charging-Abnormal
notice.solution.120003=If the error is still reported after restarting and retrying, please contact after-sales.
notice.description.120004=Charging-Abnormal
notice.solution.120004=If the error is still reported after restarting and retrying, please contact after-sales.
notice.description.120005=Charging-Abnormal
notice.solution.120005=The docking information collection failed due to possible environmental factors. Adjust the environment to a good condition without interference and then try the task again. If it still fails after adjustment, please contact the after-sales department.
notice.description.120006=Charging-Abnormal
notice.solution.120006=The docking information collection failed due to possible environmental factors. Adjust the environment to a good condition without interference and then try the task again. If it still fails after adjustment, please contact the after-sales department.
notice.description.120007=Charging-Abnormal
notice.solution.120007=If the charging pile is not properly docked, please adjust the docking parameters. Ensure that the charging pile is in automatic mode.
notice.description.120008=Charging-Abnormal
notice.solution.120008=Decrease the percentage of fullness, and the default is 97%. If there is still a problem if it is reduced to 89%, please contact after-sales.
notice.description.120100=bms- exception.
notice.solution.120100=Check whether the serial port is normal.
notice.description.120101=bms- exception
notice.solution.120101=Check whether the reading instruction data is correct, and check the battery communication protocol.
notice.description.120102=bms- exception.
notice.solution.120102=Check whether the written instruction data is correct, and check the battery communication protocol.
notice.description.120103=bms- exception.
notice.solution.120103=Check whether the serial port is normal.
notice.description.120104=bms- exception.
notice.solution.120104=Check whether the serial port is normal.
notice.description.120106=bms- exception.
notice.solution.120106=Check whether the serial data interferes.
notice.description.120107=bms- exception.
notice.solution.120107=Check the number of batteries.
notice.description.120108=bms- exception.
notice.solution.120108=Check the voltage of double batteries.
notice.description.121001=Audio-Exception
notice.solution.121001=Confirm that the specified audio name exists in AGV, and be careful not to add a suffix at the end of the audio name, such as.mp3.. Make sure the audio is in mp3 format.
notice.description.123004 =socket-exception
notice.solution.123004=reconfirm whether the API port number and interface number are correct.
notice.description.123005 =socket-exception
notice.solution.123005=It needs to be restarted for recovery. Please contact the after-sales department if it cannot be recovered or the abnormality occurs many times.
notice.description.123006 =socket-exception
notice.solution.123006=Unable to obtain configuration information, please contact after-sales.
notice.description.127001=Exception in navigation execution.
notice.solution.127001=Abnormal navigation execution: the current task needs to be stopped and the after-sales department should be contacted.
notice.description.127002=Exception in navigation execution.
notice.solution.127002=Abnormal navigation execution: it is necessary to stop the current task and manually judge whether the robot is derailed. If it is derailed, please move the robot to the path. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127003=Exception in navigation execution.
notice.solution.127003=Exception in navigation execution: it is necessary to stop the current task and manually judge whether the positioning data exists. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127004=Exception in navigation execution.
notice.solution.127004=Abnormal navigation execution: it is necessary to stop the current task and manually judge whether the marker under the shelf is recognized normally. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127005=Exception in navigation execution.
notice.solution.127005=Abnormal navigation execution: it is necessary to stop the current task and determine whether there are radar and pcl related errors. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127006=Exception in navigation execution.
notice.solution.127006=Abnormal navigation execution: it is necessary to stop the current task and determine whether there is a motor-related error. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127007=Exception in navigation execution.
notice.solution.127007=Abnormal navigation execution: it is necessary to stop the current task and determine whether there is any positioning-related error. If not, please determine whether there is any radar-related error in the case of laser positioning, and whether there is any communication abnormality of QR code sensor in QR code positioning. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127008=Exception in navigation execution.
notice.solution.127008=Abnormal navigation execution: it is necessary to stop the current task and judge whether the lidar data is normal. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127009=Exception in navigation execution.
notice.solution.127009=Abnormal navigation execution: the current task needs to be stopped, and whether the artificial features around the current path are occluded. If so, please avoid occlusion. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127010=Exception in navigation execution.
notice.solution.127010=Navigation execution is abnormal: it is necessary to stop the current task and judge whether the current laser positioning data is normal. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.127011=Navigation execution exception 111
notice.solution.127011=Abnormal navigation execution: it is necessary to stop the current task and judge whether the current laser positioning data is normal. If you can't recover or the abnormality appears many times, please contact after-sales. 222
notice.description.127012=The angle change range of directional curve is too large.
notice.solution.127012=Please reduce the curvature of the path to make it smoother.
notice.description.128001=Exception in execution of pair.
notice.solution.128001=Exception in docking execution: it is necessary to clear the error state, and judge whether the current robot is docking, or whether it has not been docked after the previous docking instruction. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128002=Exception in execution of pair.
notice.solution.128002=Exception in docking execution: it is necessary to clear the error state and judge whether the currently specified docking target is reasonable. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128003=Exception in execution of pair.
notice.solution.128003=Exception in docking execution: it is necessary to clear the error state and judge the running state of the feature detection module. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128004=Exception in execution of pair.
notice.solution.128004=Exception in docking execution: it is necessary to clear the error state and determine the running state of the feature detection module. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128005=Exception in execution of pair.
notice.solution.128005=Exception in docking execution: it is necessary to clear the error state and determine the running state of the feature detection module. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128006=Exception in execution of pair.
notice.solution.128006=Exception in docking execution: the error status needs to be cleared, and please contact the after-sales department.
notice.description.128007=Exception in execution of pair.
notice.solution.128007=Exception in docking execution: it is necessary to clear the error state and judge the running state of the feature detection module. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128008=Exception in execution of pair.
notice.solution.128008=Exception in docking: it is necessary to clear the error state, and it is necessary to determine whether the current robot is docking or not. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128009=Exception in execution of pair.
notice.solution.128009=It is necessary to clear the error status and determine whether there is a positioning-related error. If not, please determine whether there is a radar-related error. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128010=Exception in execution of pair.
notice.solution.128010=Exception in docking execution: it is necessary to know the error state and judge the running state of feature detection. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.128011=Abnormal alignment of QR code shelf.
notice.solution.128011=Abnormal alignment of two-dimensional code shelf: it is necessary to clear the error state and determine whether another alignment is performed during the alignment of two-dimensional code shelf.
notice.description.128012=Abnormal alignment of QR code shelf.
notice.solution.128012=Abnormal alignment of QR code shelf: check whether the QR code is illuminated, and if it is illuminated, check whether the QR code identification node is turned on and whether the node parameters are configured correctly.
notice.description.128100=Abnormal side alignment.
notice.solution.128100=Abnormal side alignment: it is necessary to clear the error state and determine whether another alignment is performed during the side alignment.
notice.description.128101=Abnormal side alignment.
notice.solution.128101=Check whether the distance between the side docking sensor and the machine is within the threshold range.
notice.description.130001=Location exception.
notice.solution.130001=Please try to relocate. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.133001=Exception in feature detection execution.
notice.solution.133001=Abnormal execution of feature detection: manually judge whether the robot is within the docking range and whether the features are highly consistent with the robot radar. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.133002=Exception in feature detection execution.
notice.solution.133002=Abnormal feature detection: manually judge whether there are similar features and adjust the docking distance or direction of the robot. If you can't reply or the abnormality appears many times, please contact after-sales.
notice.description.133003=Exception in feature detection execution.
notice.solution.133003=Abnormal execution of feature detection: judge whether there is an error related to lidar. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.133004=Exception in feature detection execution.
notice.solution.133004=Exception in feature detection: judge whether there is a positioning related error. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.135001=Exception in drawing execution.
notice.solution.135001=Abnormal execution of drawing: judge whether there is an error related to lidar. If you can't recover or the abnormality appears many times, please contact after-sales.
notice.description.135002=Exception in drawing execution.
notice.solution.135002=Exception in drawing execution: manually judge whether the drawing process forms a loop. If there is no return, please contact after-sales.
notice.description.140000=Script exception.
notice.solution.140000=Script exception: Please check the parameters of socket or lua instruction, such as length and type.
notice.description.140001=Script exception.
notice.solution.140001=Script exception: The specified control script does not exist. Please check the name or ID of the script.
notice.description.140002=Script exception.
notice.solution.140002=Script exception: The script is running, and the user's current control instruction is not running.
notice.description.140003=Script exception.
notice.solution.140003=Script exception: The script sub-thread did not exit normally, which is a fatal BUG. Please contact the developer.
notice.description.140004=Script exception.
notice.solution.140004=Script exception: The startup script timed out, and it is checked whether the storage path of the script is correct. If the test is correct, please contact the developer.
notice.description.140005=Script exception.
notice.solution.140005=Script exception: Stop the script timeout, and check whether the script has exited. If the test is correct, please contact the developer.
notice.description.140006=Script exception.
notice.solution.140006=Script exception: The issued control instruction does not exist. Please check the command of json instruction.
notice.description.140007=Script exception.
notice.solution.140007=Script exception: The address of the specified script variable is wrong. Please check whether the address of the distributed script variable is not within the specified range [0,31].
notice.description.140008=Script exception.
notice.solution.140008=Script exception: Please contact the developer.
notice.description.140009=Script exception.
notice.solution.140009=Script exception: Please check whether there is a main function in lua script.
notice.description.140010=Script exception.
notice.solution.140010=Script exception: Please check whether there is an exception function in lua script.
notice.description.140011=Script exception.
notice.solution.140011=Script exception: Please check whether there is a cancel function in lua script.
notice.description.140012=Script exception.
notice.solution.140012=Script exception: Check the configuration file in this case.
notice.description.140013=Script exception.
notice.solution.140013=Script exception: There is something wrong with the json transmitted by the user. Please check the following json data and contact the developer.
notice.description.140014=Script exception.
notice.solution.140014=Script exception: There is something wrong with the json transmitted by the user. Please check the following json data and contact the developer.
notice.description.140015=Script exception.
notice.solution.140015=Script exception: Check the variable type required by the interface.
notice.description.140016=Script exception.
notice.solution.140016=Script exception: 1. Check whether the target point exists; 2. Check whether there is a path leading to the target point at the current position; 3. Check whether the robot positioning is invalid; If you don't contact the developer.
notice.description.140017=Script exception.
notice.solution.140017=Script exception: 1. Check whether the target point exists; 2. Check whether there is a path leading to the target point at the current position; 3. Check whether the robot positioning is invalid; If you don't contact the developer.
notice.description.140018=Script exception.
notice.solution.140018=Script exception: There is something wrong with the fields sent by compass, please contact the developer.
notice.description.140019=Script exception.
notice.solution.140019=Script exception: compass's socket service is disconnected.
notice.description.140020=Script exception.
notice.solution.140020=Script exception: Error in linear motion, please contact the developer.
notice.description.140021=Script exception.
notice.solution.140021=Script exception: Check whether the environment meets the docking conditions, please contact the developer.
notice.description.140022=Script exception.
notice.solution.140022=Script exception: Check whether the algorithm node is abnormal, please contact the developer.
notice.description.140023=Script exception.
notice.solution.140023=Script exception: Check whether the format of the script is correct, please contact the developer.
notice.description.140024=Script exception.
notice.solution.140024=Script exception: Exception found during script execution, please contact the developer.
notice.description.140025=Script exception.
notice.solution.140025=Script exception: The navigation of the script execution path is abnormal, please contact the developer.
notice.description.140026=Script exception.
notice.solution.140026=Script exception: call new script exception, please contact the developer.
notice.description.140027=Script exception.
notice.solution.140027=Script exception: Please edit the script with the suffix ".lua" correctly.
notice.description.140028=Script exception.
notice.solution.140028=Script exception: Unknown exception, please contact the developer.
notice.description.145000=Script exception.
notice.solution.145000=Script exception: The connection of the mechanical arm timed out. Please check whether the mechanical arm is powered on and whether the network cable is normal.
notice.description.145001=Script exception.
notice.solution.145001=Script exception: The mechanical arm is not connected. Please check whether the mechanical arm is powered on and whether the network cable is normal.
notice.description.145002=Script exception.
notice.solution.145002=Script exception: Please contact the developer.
notice.description.145003=Script exception.
notice.solution.145003=Script exception: The parameters entered by the control manipulator are wrong. Please check the parameters according to the protocol.
notice.description.145004=Script exception.
notice.solution.145004=Script exception: The message returned by the mechanical arm is wrong, please contact the developer.
notice.description.145005=Script exception.
notice.solution.145005=Script exception: Error in issuing the command to control the manipulator. Please check the issued command according to the protocol.
notice.description.145006=Script exception.
notice.solution.145006=Script exception: Please contact the developer.
notice.description.145007=Script exception.
notice.solution.145007=Script exception: Please contact the developer.
notice.description.146000=Script exception.
notice.solution.146000=Script exception: Please contact the developer.
notice.description.146001=Script exception.
notice.solution.146001=Script exception: Please contact the developer.
notice.description.147001=Acousto-optic system-abnormal.
notice.solution.147001=If it still cannot be solved after restart, contact the after-sales department.
notice.description.147002=Single point radar-abnormal.
notice.solution.147002=If it still cannot be solved after restart, contact the after-sales department.
notice.description.147004=Side photoelectric docking-abnormal.
notice.solution.147004=Check the network IP address.
notice.description.150000=Radar anomaly.
notice.solution.150000=Check the network port and ip address.
notice.description.150002=Radar anomaly.
notice.solution.150002=Check whether the setting frequency is within a reasonable range.
notice.description.150003=Radar anomaly.
notice.solution.150003=Check whether the set sampling rate is within a reasonable range.
notice.description.150004=Radar anomaly.
notice.solution.150004=Check whether the network is normal.
notice.description.150005=Radar anomaly.
notice.solution.150005=Check whether the network is normal.
notice.description.150100=PLC client exception.
notice.solution.150100=Check the network ip address and port number.
notice.description.150101=PLC client exception.
notice.solution.150101=Check the network ip address and port number.
notice.description.150102=PLC client exception.
notice.solution.150102=Check whether the network is normal.
notice.description.150103=PLC client exception.
notice.solution.150103=Check whether the network is normal.
notice.description.150104=PLC client exception.
notice.solution.150104=Check whether there is an emergency stop signal triggered.
notice.description.150151=Security PLC client exception.
notice.solution.150151=Check the network ip address and port number.
notice.description.150152=Abnormal security PLC client.
notice.solution.150152=Check whether the network is normal.
notice.description.150153=Abnormal security PLC client.
notice.solution.150153=Check whether the network is normal.
notice.description.150154=Security PLC client exception.
notice.solution.150154=Check whether there is an emergency stop signal triggered.
notice.description.150155=Security PLC client exception.
notice.solution.150155=Check encoder alarm.
notice.description.150300=Abnormal QR code.
notice.solution.150300=Check whether the serial port number is correct.
notice.description.150301=Abnormal QR code.
notice.solution.150301=Check whether the serial port is connected properly.
notice.description.150302=Abnormal QR code.
notice.solution.150302=Check whether the serial port is connected properly.
notice.description.150303=Abnormal QR code.
notice.solution.150303=Check whether the serial port is connected properly and the interference.
notice.description.150304=Abnormal QR code.
notice.solution.150304=Check whether the serial port is connected properly and the interference.
notice.description.150310=Abnormal QR code.
notice.solution.150310=Check whether the camera is connected properly.
notice.description.150311=Abnormal QR code.
notice.solution.150311=Check whether the camera is connected properly.
notice.description.150312=Abnormal QR code.
notice.solution.150312=Check whether the camera is connected properly.
notice.description.150313=Abnormal QR code.
notice.solution.150313=Check whether the camera is connected properly.
notice.description.150400=3D camera exception.
notice.solution.150400=Check whether the camera is connected properly.
notice.description.150401=3D camera exception.
notice.solution.150401=Check whether the camera is properly configured.
notice.description.150500=Ultrasonic abnormality.
notice.solution.150500=Check whether the ultrasonic connection is normal.
notice.description.150501=Ultrasonic abnormality.
notice.solution.150501=Check whether the ultrasonic wave is configured normally.
notice.description.170001=Integration exception on.
notice.solution.170001=Integration exception on: Please contact the developer.
notice.description.170002=Integration exception on.
notice.solution.170002=Upper integrated alarm: Please reset according to the upper integrated operation manual.
notice.description.170003=Integration exception on.
notice.solution.170003=Integration exception: Please reset the drive to clear the fault, or power off and restart.
notice.description.170004=Integration exception on.
notice.solution.170004=Integration exception: Please reset the drive to clear the fault, or power off and restart.
notice.description.170005=Integration exception on.
notice.solution.170005=Integration exception: Please reset the drive to clear the fault, or power off and restart.
notice.description.170006=Integration exception on.
notice.solution.170006=Integration exception: Please reset the drive to clear the fault, or power off and restart.
notice.description.170007=Integration exception on.
notice.solution.170007=Integration exception: Please reset the drive to clear the fault, or power off and restart.
notice.description.170008=Integration exception on.
notice.solution.170008=Exception in integration: Release the emergency stop to recover.
notice.description.171001=Exception in Haikang Yuntai (upper integration)
notice.solution.171001=abnormality of Haikang Yuntai: 1. Check whether the network cable is loose; 2. Check whether the network communication is normal.
notice.description.171002=sonar sensor (upper integration) is abnormal.
notice.solution.171002=Abnormal sonar sensor: Please check whether the equipment connection is normal.
notice.description.171003=Abnormal drop sensor.
notice.solution.171003=Abnormal drop sensor: Please check whether the equipment is connected normally.
notice.description.171004=Abnormal drop sensor.
notice.solution.171004=Abnormal drop sensor: Please check whether the equipment is connected normally.
notice.description.171005=Abnormal drop sensor.
notice.solution.171005=Abnormal drop sensor: Please check whether the equipment is connected normally.
notice.description.171006=Abnormal drop sensor.
notice.solution.171006=Abnormal drop sensor: Please check whether the equipment is connected normally.
notice.description.171007=Ultrasonic sensor (upper integration) is abnormal.
notice.solution.171007=Abnormal ultrasonic sensor: Please check whether the equipment is connected normally.
notice.description.171008=Ultrasonic sensor (upper integration) is abnormal.
notice.solution.171008=Abnormal ultrasonic sensor: Please check whether the equipment is connected normally.
notice.description.171009=Ultrasonic sensor (upper integration) is abnormal.
notice.solution.171009=Abnormal ultrasonic sensor: Please check whether the equipment is connected normally.
notice.description.171010=Ultrasonic sensor (upper integration) is abnormal.
notice.solution.171010=Abnormal ultrasonic sensor: Please check whether the equipment is connected normally.
notice.description.200017=Cannot find the instruction issued to Pilot.
notice.solution.200017=Please contact technical support.
notice.description.200018=Instruction execution failed.
notice.solution.200018=Please contact technical support.
notice.description.200101=Push the button to stop.
notice.solution.200101=Please check the status of the robot.
notice.description.200102=Emergency stop of safety equipment.
notice.solution.200102=Please check the status of the robot.
notice.description.200103=collision emergency stop.
notice.solution.200103=Please check the status of the robot.
notice.description.200104=Unexpected emergency stop of route navigation.
notice.solution.200104=Please check the status of the robot.
notice.description.200105=Emergency stop of robot lifting.
notice.solution.200105=Robot lifting and emergency stop.
notice.description.200106=Robot lifting error.
notice.solution.200106=Robot lifting error.
notice.description.200107=Emergency stop of robot drum.
notice.solution.200107=Emergency stop of robot drum.
notice.description.200108=Robot roller error.
notice.solution.200108=Robot roller error.
notice.description.200109=Robot startup paused.
notice.solution.200109=Robot startup paused.
notice.description.200110=Robot Manual Control Mode
notice.solution.200110=Robot Manual Control Mode
notice.description.200111=Robot is not located.
notice.solution.200111=Robot is not located.
notice.description.200112=The control mode of this robot only supports hardware knob control!
notice.solution.200112=The control mode of this robot only supports hardware knob control!
notice.description.200113=The robot arm is not ready.
notice.solution.200113=Check the status of the robot arm.
notice.description.200114=Robot abnormality
notice.solution.200114=Check the status of the robot
notice.description.300001=System internal error.
notice.solution.300001=Please contact technical support.
notice.description.300002=Statistical module program exception.
notice.solution.300002=Please contact technical support.
notice.description.300003=Map module program is abnormal.
notice.solution.300003=Please contact technical support.
notice.description.300004=Robot module program is abnormal.
notice.solution.300004=Please contact technical support.
notice.description.300005=Exception in task module program.
notice.solution.300005=Please contact technical support.
notice.description.300006=Abnormal traffic control module program.
notice.solution.300006=Please contact technical support.
notice.description.300007=Abnormal degree of event module.
notice.solution.300007=Please contact technical support.
notice.description.300008=Air shower door module is abnormal.
notice.solution.300008=Please contact technical support.
notice.description.300009=Abnormal automatic door module.
notice.solution.300009=Please contact technical support.
notice.description.300010=Elevator module is abnormal.
notice.solution.300010=Please contact technical support.
notice.description.300011=Call box module is abnormal.
notice.solution.300011=Please contact technical support.
notice.description.300012=异常（待翻译）
notice.solution.300012=Please contact technical support.
notice.description.300013=CPU资源占用过高（待翻译）
notice.solution.300013=Please contact technical support.
notice.description.300014=内存资源占用过高（待翻译）
notice.solution.300014=Please contact technical support.
notice.description.300015=硬盘资源占用过高（待翻译）
notice.solution.300015=Please contact technical support.
notice.description.300101=Robot  failed to occupy point .
notice.solution.300101=The position where the robot is located has been occupied by other robots. Please move the robot to the road network.
notice.description.300102=Robot  failed to occupy area .
notice.solution.300102=The stand-alone area where the robot is located has been occupied by other robots. Please move the robot to the road network.
notice.description.300103=Robot  failed to occupy elevator .
notice.solution.300103=The elevator where the robot is located has been occupied by other robots. Please move the robot to the road network.
notice.description.300104=Robot  has been disconnected.
notice.solution.300104=1: Please check whether the network connection between the robot and the server is normal; 2: Please check whether the power of the robot is turned on; 3: Please check whether the system IP and port configured by the robot are correct;
notice.description.300105=Robot  is out of orbit.
notice.solution.300105=Please check whether the positioning state of the robot is accurate. If it is, please move the robot to the road network.
notice.description.300106=Robot  Manual Control Mode
notice.solution.300106=The robot is in manual control mode, please switch to automatic control mode.
notice.description.300201=Connection of automatic door  failed.
notice.solution.300201=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the equipment IP and port number are configured correctly in the map layout interface;
notice.description.300202=Connection of air shower door  failed.
notice.solution.300202=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the equipment IP and port number are configured correctly in the map layout interface;
notice.description.300203=Connection of elevator  failed.
notice.solution.300203=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the equipment IP and port number are configured correctly in the map layout interface;
notice.description.300204=Failed to read the automatic door  command.
notice.solution.300204=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the device reading address configuration is correct in the map layout interface;
notice.description.300205=Failed to read the air shower door  command.
notice.solution.300205=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the device reading address configuration is correct in the map layout interface;
notice.description.300206=Failed to read elevator  instruction.
notice.solution.300206=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3: Please check whether the device reading address configuration is correct in the map layout interface;
notice.description.300207=Failed to write the automatic door  command.
notice.solution.300207=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3. Please check whether the write address configuration is correct in the map layout interface;
notice.description.300208=Failed to write the instruction  to the air shower door.
notice.solution.300208=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3. Please check whether the write address configuration is correct in the map layout interface;
notice.description.300209=Failed to write elevator  command.
notice.solution.300209=1: Please check whether the network connection between the device and the server is normal; 2: Please check whether the power supply of the equipment is turned on; 3. Please check whether the write address configuration is correct in the map layout interface;
notice.description.300210=No path is bound for automatic door .
notice.solution.300210=Please open the map editing interface and bind the path for this automatic door.
notice.description.300211=The path of air shower door  is not bound.
notice.solution.300211=Please open the map editing interface and bind the path for the air shower door.
notice.description.300212=Unbound point of elevator 
notice.solution.300212=Please open the map editing interface and bind the points for the elevator.
notice.description.300213=Call box  is not connected to the dispatching system.
notice.solution.300213=1: Please check whether the network connection between the call box and the server is normal; 2: Please check whether the power of the call box is turned on; 3: Please use the call box configuration tool to check whether the server address configuration of the call box is correct;
notice.description.300214=Call box  is not configured in the system.
notice.solution.300214=Please configure the task flow of event type for this call box.
notice.description.300215=The task flow  associated with call box  has not been published.
notice.solution.300215=Please publish the task flow bound by this call box.
notice.description.300216=Duplicate call box  number.
notice.solution.300216=There are multiple call boxes configured with the same call box ID. Please use the call box configuration tool to reconfigure the call boxes.
notice.description.300217=Call box  software exception.
notice.solution.300217=Please use the call box configuration tool to connect the call box and check the program problems of the call box.
notice.description.300218=Call box  hardware exception.
notice.solution.300218=Please use the call box configuration tool to connect the call box and check the hardware problem of the call box.
notice.description.300219=Abnormal configuration of call box .
notice.solution.300219=Please use the call box configuration tool to connect the call box and check the configuration problem of the call box.
notice.description.300220=Call box  has low battery.
notice.solution.300220=Please charge the call box manually.
notice.description.300301=Exception in task .
notice.solution.300301=Please contact technical support.
notice.description.300302=Exception in task  node .
notice.solution.300302=Please contact technical support.
notice.description.300303=Robot type  No robot.
notice.solution.300303=Please bind the robot to this robot type.
notice.description.300304=Robot group  has no robots.
notice.solution.300304=Please bind the robot to this robot group.
notice.description.300305=Robot  does not exist.
notice.solution.300305=Please open the pop-up window of task details and the interface of robot list, and check whether a nonexistent robot was used to create the task.
notice.description.300306=Map  has no robots.
notice.solution.300306=Please put available robots on this map.
notice.description.300307=Point  does not exist.
notice.solution.300307=Please open the pop-up window of task details and the interface of map list, and check whether non-existent points are used to create the task.
notice.description.300308=The target point  is unreachable.
notice.solution.300308=1: Please check whether the robot's point and target point are on the same map; 2. Please check whether there is an available path between the point where the robot is located and the target point;
notice.description.300309=Control illegal robots 
notice.solution.300309=1: When the node does not specify the robot to be controlled, there must be only one "dynamic robot allocation" or "designated robot allocation" node before the node; 2: When the node has designated the robot to be controlled, the robot designated by the node needs to be the output parameter of the node of "Dynamic robot allocation" or "Designated robot allocation";
notice.description.300310=Failed to send robot  command.
notice.solution.300310=Please check whether the network connection between the robot and the server is normal;
notice.description.300311=Robot  cannot receive instructions.
notice.solution.300311=1: Please check whether the robot is in automatic control mode; 2: Please check whether the robot has been pressed the emergency stop button; 3: Please check whether the robot is connected; 4: Please check whether the robot is in an abnormal state;
notice.description.300312=Robot  failed to execute the instruction.
notice.solution.300312=Please contact technical support.
notice.description.300313=No point available.
notice.solution.300313=1: The node specifies a map, but no point can be found on the map; 2: The node specifies the point type, but no point can be found;
notice.description.300314=Data format conversion failed.
notice.solution.300314=Please check the input values of node parameters in the task details interface.
notice.description.300315=Unable to connect Plc.
notice.solution.300315=1: Please check whether the network connection between Plc and server is normal; 2: Please check whether the power supply of Plc is turned on; 3: Please check whether the Plc address port configured by the node is correct;
notice.description.300316=Register value is out of range.
notice.solution.300316=Please open the task details pop-up window to check whether the out-of-range register value was used to create the task.
notice.description.300317=The  attribute of the acquisition point  is empty.
notice.solution.300317=Please open the map editing page and check the properties of the point.
notice.description.300318=Location  does not exist or is not enabled.
notice.solution.300318=Please open the location page and check whether the creation task uses a location that does not exist or is not enabled.
notice.description.300319=The reservoir area  does not exist.
notice.solution.300319=Please open the library page and check whether the creation task uses a non-existent library.
notice.description.300320=Location type  does not exist.
notice.solution.300320=Please open the location type page and check whether the creation task uses a location type that does not exist.
notice.description.300321=No location available.
notice.solution.300321=Please open the location page and check whether there is a location selected by the task.
notice.description.300322=Container barcode  does not exist.
notice.solution.300322=Please open the location page and check whether the creation task uses a container code that does not exist.
notice.description.300323=The adjacent points of point  are not unique.
notice.solution.300323=Please check whether the point selected for creating the task has no adjacent points or has multiple adjacent points.
notice.description.300324=Container barcode  already exists.
notice.solution.300324=Please open the location page and check whether the same container code already exists.
notice.description.300325=Location  has been occupied.
notice.solution.300325=Please open the location page and check whether the location has been occupied.
notice.description.300326 =Exception in http  request.
notice.solution.300326=1: Please check whether the Http request address is configured correctly; 2: Please check whether the network connection between the device and the server is normal;
notice.description.300327=There are multiple qualified points under this number .
notice.solution.300327=Please check whether there are multiple points selected for creating the task.
notice.description.300328=Unable to find the location that meets the requirements.
notice.solution.300328=Please open the location page and check whether there is a location selected by the task.
notice.description.300329=区域不存在（待翻译）
notice.solution.300329=请打开任务流程详情页面，在对应节点填写已存在的禁入区域编码（待翻译）
notice.description.300330=该区域禁止操作（待翻译）
notice.solution.300330=请打开任务流程详情页面，在对应节点填写可操作的区域编码（待翻译）
notice.description.300401=Navigation conflict of robot , no avoidance point available.
notice.solution.300401=Please contact technical support.
notice.description.300501=Failed to write the map file to disk.
notice.solution.300501=Please reconfigure the access rights of this disk directory.
notice.description.300502=Failed to read the disk map file.
notice.solution.300502=Please reconfigure the access rights of this disk directory.
notice.description.570001=Robot universal failure.
notice.solution.570001=Please contact the mos developer for handling.
notice.description.570002=Wrong interface parameters of the robot arm.
notice.solution.570002=Please contact the mos developer for handling.
notice.description.570003=Incompatible instruction interface.
notice.solution.570003=Please contact the mos developer for handling.
notice.description.570004=Robot connection failed.
notice.solution.570004=Please contact the mos developer for handling.
notice.description.570005=Exception in sending and receiving communication message of mechanical arm socket.
notice.solution.570005=Please contact the mos developer for handling.
notice.description.570006 =Socket disconnected.
notice.solution.570006=Please contact the mos developer for handling.
notice.description.570007=Create request failed.
notice.solution.570007=Please contact the mos developer for handling.
notice.description.570008=Error in requesting related internal variables.
notice.solution.570008=Please contact the mos developer for handling.
notice.description.570009=The request timed out.
notice.solution.570009=Please contact the mos developer for handling.
notice.description.570010=Failed to send the requested information.
notice.solution.570010=Please contact the mos developer for handling.
notice.description.570011=The response information is empty.
notice.solution.570011=Please contact the mos developer for handling.
notice.description.570012=The response information header does not match.
notice.solution.570012=Please contact the mos developer for handling.
notice.description.570013=Failed to parse the response.
notice.solution.570013=Please contact the mos developer for handling.
notice.description.570014=Error in the correct solution.
notice.solution.570014=Terminate the current task, remove the materials from the gripper of the mechanical arm, manually return the mechanical arm to the original point, and then resume the task.
notice.description.570015=Error in inverse solution.
notice.solution.570015=Terminate the current task, remove the materials from the gripper of the mechanical arm, manually return the mechanical arm to the original point, and then resume the task.
notice.description.570016=Error in tool calibration.
notice.solution.570016=Please contact the mos developer for handling.
notice.description.570017=Error in tool calibration parameters.
notice.solution.570017=Please contact the mos developer for handling.
notice.description.570018=Coordinate system calibration failed.
notice.solution.570018=Please contact the mos developer for handling.
notice.description.570019=Failed to convert base coordinate system to user coordinate system.
notice.solution.570019=Please contact the mos developer for handling.
notice.description.570020=Failed to convert user coordinate system to base coordinate system.
notice.solution.570020=Please contact the mos developer for handling.
notice.description.570021=Robot failed to power on.
notice.solution.570021=Please contact the mos developer for handling.
notice.description.570022=Robot failed to power off.
notice.solution.570022=Please contact the mos developer for handling.
notice.description.570023=Robot enabling failed.
notice.solution.570023=Please contact the mos developer for handling.
notice.description.570024=Enabling failed under the robot.
notice.solution.570024=Please contact the mos developer for handling.
notice.description.570025=Robot reset failed.
notice.solution.570025=Click the reset button again to reset. If it still fails, please take out the teaching device and clear the error.
notice.description.570026=Robot pause failed.
notice.solution.570026=Please contact the mos developer for handling.
notice.description.570027=Robot stop failed.
notice.solution.570027=Press the emergency stop button, remove the material from the gripper of the mechanical arm, power on and enable it, and manually return the mechanical arm to the original point before resuming the task.
notice.description.570028=Robot status acquisition failed.
notice.solution.570028=Terminate the current task, remove the materials from the gripper of the mechanical arm, manually return the mechanical arm to the original point, and then resume the task.
notice.description.570029=Robot encoder status synchronization failed.
notice.solution.570029=Please contact the mos developer for handling.
notice.description.570030=Robot mode is incorrect.
notice.solution.570030=Please contact the mos developer for handling.
notice.description.570031=Robot JOG failed to move.
notice.solution.570031=Please contact the mos developer for handling.
notice.description.570032=Robot drag teaching setting failed.
notice.solution.570032=Please contact the mos developer for handling.
notice.description.570033=Robot speed setting failed.
notice.solution.570033=Please contact the mos developer for handling.
notice.description.570034=Robot waypoint removal failed.
notice.solution.570034=Please contact the mos developer for handling.
notice.description.570035=The acquisition of the current coordinate system of the robot failed.
notice.solution.570035=Please contact the mos developer for handling.
notice.description.570036=Robot coordinate system setting failed.
notice.solution.570036=Please contact the mos developer for handling.
notice.description.570037=Robot weight center of gravity setting failed.
notice.solution.570037=Please contact the mos developer for handling.
notice.description.570038=Robot IO setting failed.
notice.solution.570038=Please contact the mos developer for handling.
notice.description.570039=Robot TCP setup failed.
notice.solution.570039=Please contact the mos developer for handling.
notice.description.570040=Robot TCP acquisition failed.
notice.solution.570040=Please contact the mos developer for handling.
notice.description.570041=move instruction blocked waiting timeout.
notice.solution.570041=Terminate the current task, remove the material from the gripper of the mechanical arm, manually return the mechanical arm to the original point, and check the timeout reason. 1. If the motion is not in place because the path point trajectory fusion radius is too large, the speed of the corresponding step can be reduced appropriately. Then the single machine tests and runs the task, and if the timeout is not reported again, the task can be resumed. 2. If the mechanical arm pauses for more than 1 minute because of triggering the protective stop, the task can be resumed directly.
notice.description.570042=Error in internal variables related to motion.
notice.solution.570042=Please contact the mos developer for handling.
notice.description.570043=Motion request failed.
notice.solution.570043=Please contact the mos developer for handling.
notice.description.570044=Failed to generate the motion request.
notice.solution.570044=Please contact the mos developer for handling.
notice.description.570045=Motion was interrupted by an event.
notice.solution.570045=Terminate the current task, remove the material from the gripper of the mechanical arm, and click the reset button to reset it. If it cannot be reset, please use the teaching device to clear the error. After clearing the error, power on and enable it again, and manually return the mechanical arm to the original point, then resume the task.
notice.description.570046=The length of the waypoint container related to the movement does not meet the requirements.
notice.solution.570046=Please contact the mos developer for handling.
notice.description.570047=The server response returned an error.
notice.solution.570047=Please contact the mos developer for handling.
notice.description.570048=The real robot arm does not exist.
notice.solution.570048=Please contact the mos developer for handling.
notice.description.570049=Failed to call the slow stop interface.
notice.solution.570049=Please contact the mos developer for handling.
notice.description.570050=Calling the emergency stop interface failed.
notice.solution.570050=Please contact the mos developer for handling.
notice.description.570051=Call to pause interface failed.
notice.solution.570051=Please contact the mos developer for handling.
notice.description.570052=Call to continue interface failed.
notice.solution.570052=Please contact the mos developer for handling.
notice.description.570053=Motion was conditionally interrupted.
notice.solution.570053=Terminate the current task, remove the material from the gripper of the mechanical arm, manually return the mechanical arm to the origin, and check the failure condition. If the condition fails due to the original image of the non-sensor, please resume the task. If it is a sensor problem, please contact the after-sales personnel to solve it.
notice.description.570054=Motion was manually interrupted.
notice.solution.570054=Please contact the mos developer for handling.
notice.description.571001=The joint motion attribute is misconfigured.
notice.solution.571001=Please contact the mos developer for handling.
notice.description.571002=Wrong configuration of linear motion attribute.
notice.solution.571002=Please contact the mos developer for handling.
notice.description.571003=The trajectory motion attribute is misconfigured.
notice.solution.571003=Please contact the mos developer for handling.
notice.description.571004=Invalid motion attribute configuration.
notice.solution.571004=Please contact the mos developer for handling.
notice.description.571005=Wait for the robot to stop.
notice.solution.571005=Please contact the mos developer for handling.
notice.description.571006=Out of joint motion range.
notice.solution.571006=Please contact the mos developer for handling.
notice.description.571007=Please set the first waypoint of MODEP correctly.
notice.solution.571007=Please contact the mos developer for handling.
notice.description.571008=Incorrect conveyor tracking configuration.
notice.solution.571008=Please contact the mos developer for handling.
notice.description.571009=Wrong conveyor track type.
notice.solution.571009=Please contact the mos developer for handling.
notice.description.571010=Inverse solution of relative coordinate transformation failed.
notice.solution.571010=Please contact the mos developer for handling.
notice.description.571011=Teaching modes collide.
notice.solution.571011=Please contact the mos developer for handling.
notice.description.571012=The motion attribute is configured incorrectly.
notice.solution.571012=Please contact the mos developer for handling.
notice.description.571101=Abnormal trajectory.
notice.solution.571101=Please contact the mos developer for handling.
notice.description.571102=Trajectory planning error.
notice.solution.571102=Please contact the mos developer for handling.
notice.description.571103=Type II online trajectory planning failed.
notice.solution.571103=Please contact the mos developer for handling.
notice.description.571104=Inverse solution failed.
notice.solution.571104=Please contact the mos developer for handling.
notice.description.571105=kinetic limit protection
notice.solution.571105=Please contact the mos developer for handling.
notice.description.571106=Conveyor tracking failed.
notice.solution.571106=Please contact the mos developer for handling.
notice.description.571107=Out of working range of conveyor belt.
notice.solution.571107=Please contact the mos developer for handling.
notice.description.571108=The joint is out of range.
notice.solution.571108=Terminate the current task and remove the material from the gripper of the mechanical arm. Check the MOS error log to see which point caused the joint overrun. After modifying this point, return the mechanical arm to the original point and start the task again.
notice.description.571109=Joint overspeed.
notice.solution.571109=Please contact the mos developer for handling.
notice.description.571110=Offline trajectory planning failed.
notice.solution.571110=Please contact the mos developer for handling.
notice.description.571200=The controller is abnormal, and the inverse solution failed.
notice.solution.571200=Please contact the mos developer for handling.
notice.description.571201=Abnormal controller, abnormal state.
notice.solution.571201=Please contact the mos developer for handling.
notice.description.571300=The movement has entered the stop stage.
notice.solution.571300=Please contact the mos developer for handling.
notice.description.571401=undefined failure of the manipulator.
notice.solution.571401=Please contact the mos developer for handling.
notice.description.571501=The robot arm ListenNode is not started.
notice.solution.571501=Please contact the mos developer for handling.
notice.description.572100=PLC client exception.
notice.solution.572100=Please contact the mos developer for handling.
notice.description.572101=PLC client exception.
notice.solution.572101=Please contact the mos developer for handling.
notice.description.572102=PLC client exception.
notice.solution.572102=Please contact the mos developer for handling.
notice.description.572103=PLC client exception.
notice.solution.572103=Please contact the mos developer for handling.
notice.description.572104=PLC client exception.
notice.solution.572104=Please contact the mos developer for handling.
notice.description.572105=PLC client exception.
notice.solution.572105=Please contact the mos developer for handling.
notice.description.572106=PLC client exception.
notice.solution.572106=Please contact the mos developer for handling.
notice.description.572107=PLC client exception.
notice.solution.572107=Please contact the mos developer for handling.
notice.description.572108=PLC client exception.
notice.solution.572108=Please contact the mos developer for handling.
notice.description.572109=PLC client exception.
notice.solution.572109=Please contact the mos developer for handling.
notice.description.572110=PLC client exception.
notice.solution.572110=Please contact the mos developer for handling.
notice.description.572111=PLC client exception.
notice.solution.572111=Please contact the mos developer for handling.
notice.description.572112=PLC client exception.
notice.solution.572112=Please contact the mos developer for handling.
notice.description.572113=PLC client exception.
notice.solution.572113=Please contact the mos developer for handling.
notice.description.572114=PLC client exception.
notice.solution.572114=Please contact the mos developer for handling.
notice.description.572115=PLC client exception.
notice.solution.572115=Please contact the mos developer for handling.
notice.description.572116=PLC client exception.
notice.solution.572116=Please contact the mos developer for handling.
notice.description.572117=PLC client exception.
notice.solution.572117=Please contact the mos developer for handling.
notice.description.572118=PLC client exception.
notice.solution.572118=Please contact the mos developer for handling.
notice.description.572119=PLC client exception.
notice.solution.572119=Please contact the mos developer for handling.
notice.description.572120=PLC client exception.
notice.solution.572120=Please contact the mos developer for handling.
notice.description.572121=PLC client exception.
notice.solution.572121=Please contact the mos developer for handling.
notice.description.572122=PLC client exception.
notice.solution.572122=Please contact the mos developer for handling.
notice.description.572123=PLC client exception.
notice.solution.572123=Please contact the mos developer for handling.
notice.description.572124=PLC client exception.
notice.solution.572124=Please contact the mos developer for handling.
notice.description.572125=PLC client exception.
notice.solution.572125=Please contact the mos developer for handling.
notice.description.572126=PLC client exception.
notice.solution.572126=Please contact the mos developer for handling.
notice.description.572127=PLC client exception.
notice.solution.572127=Please contact the mos developer for handling.
notice.description.572128=PLC client exception.
notice.solution.572128=Please contact the mos developer for handling.
notice.description.572129=PLC client exception.
notice.solution.572129=Please contact the mos developer for handling.
notice.description.572130=PLC client exception.
notice.solution.572130=Please contact the mos developer for handling.
notice.description.572131=PLC client exception.
notice.solution.572131=Please contact the mos developer for handling.
notice.description.572132=PLC client exception.
notice.solution.572132=Please contact the mos developer for handling.
notice.description.572133=PLC client exception.
notice.solution.572133=Please contact the mos developer for handling.
notice.description.572134=PLC client exception.
notice.solution.572134=Please contact the mos developer for handling.
notice.description.572135=PLC client exception.
notice.solution.572135=Please contact the mos developer for handling.
notice.description.572136=PLC client exception.
notice.solution.572136=Please contact the mos developer for handling.
notice.description.572137=PLC client exception.
notice.solution.572137=Please contact the mos developer for handling.
notice.description.572138=PLC client exception.
notice.solution.572138=Please contact the mos developer for handling.
notice.description.572139=PLC client exception.
notice.solution.572139=Please contact the mos developer for handling.
notice.description.572150=The hardware controller is not initialized.
notice.solution.572150=Please contact the mos developer for handling.
notice.description.572200=PLC hardware exception.
notice.solution.572200=Please contact the mos developer for handling.
notice.description.572201=PLC hardware exception.
notice.solution.572201=Please contact the mos developer for handling.
notice.description.572202=PLC hardware exception.
notice.solution.572202=Please contact the mos developer for handling.
notice.description.572203=PLC hardware exception.
notice.solution.572203=Please contact the mos developer for handling.
notice.description.572204=PLC hardware exception.
notice.solution.572204=Please contact the mos developer for handling.
notice.description.572205=PLC hardware exception.
notice.solution.572205=Please contact the mos developer for handling.
notice.description.572206=PLC hardware exception.
notice.solution.572206=Please contact the mos developer for handling.
notice.description.572207=PLC hardware exception.
notice.solution.572207=Please contact the mos developer for handling.
notice.description.572208=PLC hardware exception.
notice.solution.572208=Please contact the mos developer for handling.
notice.description.572209=PLC hardware exception.
notice.solution.572209=Please contact the mos developer for handling.
notice.description.572210=PLC hardware exception.
notice.solution.572210=Please contact the mos developer for handling.
notice.description.572211=PLC hardware exception.
notice.solution.572211=Please contact the mos developer for handling.
notice.description.572212=PLC hardware exception.
notice.solution.572212=Please contact the mos developer for handling.
notice.description.572213=PLC hardware exception.
notice.solution.572213=Please contact the mos developer for handling.
notice.description.572214=PLC hardware exception.
notice.solution.572214=Please contact the mos developer for handling.
notice.description.572215=PLC hardware exception.
notice.solution.572215=Please contact the mos developer for handling.
notice.description.572216=PLC hardware exception.
notice.solution.572216=Please contact the mos developer for handling.
notice.description.572217=PLC hardware exception.
notice.solution.572217=Please contact the mos developer for handling.
notice.description.572218=PLC hardware exception.
notice.solution.572218=Please contact the mos developer for handling.
notice.description.572219=PLC hardware exception.
notice.solution.572219=Please contact the mos developer for handling.
notice.description.572220=PLC hardware exception.
notice.solution.572220=Please contact the mos developer for handling.
notice.description.572221=PLC hardware exception.
notice.solution.572221=Please contact the mos developer for handling.
notice.description.572222=PLC hardware exception.
notice.solution.572222=Please contact the mos developer for handling.
notice.description.572223=PLC hardware exception.
notice.solution.572223=Please contact the mos developer for handling.
notice.description.572224=PLC hardware exception.
notice.solution.572224=Please contact the mos developer for handling.
notice.description.572225=PLC hardware exception.
notice.solution.572225=Please contact the mos developer for handling.
notice.description.572226=PLC hardware exception.
notice.solution.572226=Please contact the mos developer for handling.
notice.description.572227=PLC hardware exception.
notice.solution.572227=Please contact the mos developer for handling.
notice.description.572228=PLC hardware exception.
notice.solution.572228=Please contact the mos developer for handling.
notice.description.572229=PLC hardware exception.
notice.solution.572229=Please contact the mos developer for handling.
notice.description.572230=PLC hardware exception.
notice.solution.572230=Please contact the mos developer for handling.
notice.description.572231=PLC hardware exception.
notice.solution.572231=Please contact the mos developer for handling.
notice.description.572232=PLC hardware exception.
notice.solution.572232=Please contact the mos developer for handling.
notice.description.572233=PLC hardware exception.
notice.solution.572233=Please contact the mos developer for handling.
notice.description.572234=PLC hardware exception.
notice.solution.572234=Please contact the mos developer for handling.
notice.description.572235=PLC hardware exception.
notice.solution.572235=Please contact the mos developer for handling.
notice.description.572236=PLC hardware exception.
notice.solution.572236=Please contact the mos developer for handling.
notice.description.572237=PLC hardware exception.
notice.solution.572237=Please contact the mos developer for handling.
notice.description.572238=PLC hardware exception.
notice.solution.572238=Please contact the mos developer for handling.
notice.description.572239=PLC hardware exception.
notice.solution.572239=Please contact the mos developer for handling.
notice.description.572240=PLC hardware exception.
notice.solution.572240=Please contact the mos developer for handling.
notice.description.572241=PLC hardware exception.
notice.solution.572241=Please contact the mos developer for handling.
notice.description.572242=PLC hardware exception.
notice.solution.572242=Please contact the mos developer for handling.
notice.description.572243=PLC hardware exception.
notice.solution.572243=Please contact the mos developer for handling.
notice.description.572244=PLC hardware exception.
notice.solution.572244=Please contact the mos developer for handling.
notice.description.572245=PLC hardware exception.
notice.solution.572245=Please contact the mos developer for handling.
notice.description.572246=PLC hardware exception.
notice.solution.572246=Please contact the mos developer for handling.
notice.description.572247=PLC hardware exception.
notice.solution.572247=Please contact the mos developer for handling.
notice.description.572248=PLC hardware exception.
notice.solution.572248=Please contact the mos developer for handling.
notice.description.572249=PLC hardware exception.
notice.solution.572249=Please contact the mos developer for handling.
notice.description.572250=PLC hardware exception.
notice.solution.572250=Please contact the mos developer for handling.
notice.description.572251=PLC hardware exception.
notice.solution.572251=Please contact the mos developer for handling.
notice.description.572252=PLC hardware exception.
notice.solution.572252=Please contact the mos developer for handling.
notice.description.572253=PLC hardware exception.
notice.solution.572253=Please contact the mos developer for handling.
notice.description.572254=PLC hardware exception.
notice.solution.572254=Please contact the mos developer for handling.
notice.description.572255=PLC hardware exception.
notice.solution.572255=Please contact the mos developer for handling.
notice.description.572256=PLC hardware exception.
notice.solution.572256=Please contact the mos developer for handling.
notice.description.572257=PLC hardware exception.
notice.solution.572257=Please contact the mos developer for handling.
notice.description.572258=PLC hardware exception.
notice.solution.572258=Please contact the mos developer for handling.
notice.description.572259=PLC hardware exception.
notice.solution.572259=Please contact the mos developer for handling.
notice.description.572260=PLC hardware exception.
notice.solution.572260=Please contact the mos developer for handling.
notice.description.572261=PLC hardware exception.
notice.solution.572261=Please contact the mos developer for handling.
notice.description.572262=PLC hardware exception.
notice.solution.572262=Please contact the mos developer for handling.
notice.description.572263=PLC hardware exception.
notice.solution.572263=Please contact the mos developer for handling.
notice.description.572264=PLC hardware exception.
notice.solution.572264=Please contact the mos developer for handling.
notice.description.572265=PLC hardware exception.
notice.solution.572265=Please contact the mos developer for handling.
notice.description.572266=PLC hardware exception.
notice.solution.572266=Please contact the mos developer for handling.
notice.description.572267=PLC hardware exception.
notice.solution.572267=Please contact the mos developer for handling.
notice.description.572268=PLC hardware exception.
notice.solution.572268=Please contact the mos developer for handling.
notice.description.572269=PLC hardware exception.
notice.solution.572269=Please contact the mos developer for handling.
notice.description.572270=PLC hardware exception.
notice.solution.572270=Please contact the mos developer for handling.
notice.description.572271=PLC hardware exception.
notice.solution.572271=Please contact the mos developer for handling.
notice.description.572272=PLC hardware exception.
notice.solution.572272=Please contact the mos developer for handling.
notice.description.572273=PLC hardware exception.
notice.solution.572273=Please contact the mos developer for handling.
notice.description.572274=PLC hardware exception.
notice.solution.572274=Please contact the mos developer for handling.
notice.description.572275=PLC hardware exception.
notice.solution.572275=Please contact the mos developer for handling.
notice.description.572276=PLC hardware exception.
notice.solution.572276=Please contact the mos developer for handling.
notice.description.572277=PLC hardware exception.
notice.solution.572277=Please contact the mos developer for handling.
notice.description.572278=PLC hardware exception.
notice.solution.572278=Please contact the mos developer for handling.
notice.description.572279=PLC hardware exception.
notice.solution.572279=Please contact the mos developer for handling.
notice.description.572280=PLC hardware exception.
notice.solution.572280=Please contact the mos developer for handling.
notice.description.572281=PLC hardware exception.
notice.solution.572281=Please contact the mos developer for handling.
notice.description.572282=PLC hardware exception.
notice.solution.572282=Please contact the mos developer for handling.
notice.description.572283=PLC hardware exception.
notice.solution.572283=Please contact the mos developer for handling.
notice.description.572284=PLC hardware exception.
notice.solution.572284=Please contact the mos developer for handling.
notice.description.572285=PLC hardware exception.
notice.solution.572285=Please contact the mos developer for handling.
notice.description.572286=PLC hardware exception.
notice.solution.572286=Please contact the mos developer for handling.
notice.description.572287=PLC hardware exception.
notice.solution.572287=Please contact the mos developer for handling.
notice.description.572288=PLC hardware exception.
notice.solution.572288=Please contact the mos developer for handling.
notice.description.572289=PLC hardware exception.
notice.solution.572289=Please contact the mos developer for handling.
notice.description.572290=PLC hardware exception.
notice.solution.572290=Please contact the mos developer for handling.
notice.description.572291=PLC hardware exception.
notice.solution.572291=Please contact the mos developer for handling.
notice.description.572292=PLC hardware exception.
notice.solution.572292=Please contact the mos developer for handling.
notice.description.572293=PLC hardware exception.
notice.solution.572293=Please contact the mos developer for handling.
notice.description.572294=PLC hardware exception.
notice.solution.572294=Please contact the mos developer for handling.
notice.description.572295=PLC hardware exception.
notice.solution.572295=Please contact the mos developer for handling.
notice.description.572296=PLC hardware exception.
notice.solution.572296=Please contact the mos developer for handling.
notice.description.572297=PLC hardware exception.
notice.solution.572297=Please contact the mos developer for handling.
notice.description.572298=PLC hardware exception.
notice.solution.572298=Please contact the mos developer for handling.
notice.description.572299=PLC hardware exception.
notice.solution.572299=Please contact the mos developer for handling.
notice.description.572300=PLC hardware exception.
notice.solution.572300=Please contact the mos developer for handling.
notice.description.572301=PLC hardware exception.
notice.solution.572301=Please contact the mos developer for handling.
notice.description.572302=PLC hardware exception.
notice.solution.572302=Please contact the mos developer for handling.
notice.description.572303=PLC hardware exception.
notice.solution.572303=Please contact the mos developer for handling.
notice.description.572304=PLC hardware exception.
notice.solution.572304=Please contact the mos developer for handling.
notice.description.572305=PLC hardware exception.
notice.solution.572305=Please contact the mos developer for handling.
notice.description.572306=PLC hardware exception.
notice.solution.572306=Please contact the mos developer for handling.
notice.description.572307=PLC hardware exception.
notice.solution.572307=Please contact the mos developer for handling.
notice.description.572308=PLC hardware exception.
notice.solution.572308=Please contact the mos developer for handling.
notice.description.572309=PLC hardware exception.
notice.solution.572309=Please contact the mos developer for handling.
notice.description.572310=PLC hardware exception.
notice.solution.572310=Please contact the mos developer for handling.
notice.description.572311=PLC hardware exception.
notice.solution.572311=Please contact the mos developer for handling.
notice.description.572312=PLC hardware exception.
notice.solution.572312=Please contact the mos developer for handling.
notice.description.572313=PLC hardware exception.
notice.solution.572313=Please contact the mos developer for handling.
notice.description.572314=PLC hardware exception.
notice.solution.572314=Please contact the mos developer for handling.
notice.description.572315=PLC hardware exception.
notice.solution.572315=Please contact the mos developer for handling.
notice.description.572316=PLC hardware exception.
notice.solution.572316=Please contact the mos developer for handling.
notice.description.572317=PLC hardware exception.
notice.solution.572317=Please contact the mos developer for handling.
notice.description.572318=PLC hardware exception.
notice.solution.572318=Please contact the mos developer for handling.
notice.description.572319=PLC hardware exception.
notice.solution.572319=Please contact the mos developer for handling.
notice.description.572320=PLC hardware exception.
notice.solution.572320=Please contact the mos developer for handling.
notice.description.572321=PLC hardware exception.
notice.solution.572321=Please contact the mos developer for handling.
notice.description.572322=PLC hardware exception.
notice.solution.572322=Please contact the mos developer for handling.
notice.description.572323=PLC hardware exception.
notice.solution.572323=Please contact the mos developer for handling.
notice.description.572324=PLC hardware exception.
notice.solution.572324=Please contact the mos developer for handling.
notice.description.572325=PLC hardware exception.
notice.solution.572325=Please contact the mos developer for handling.
notice.description.572326=PLC hardware exception.
notice.solution.572326=Please contact the mos developer for handling.
notice.description.572327=PLC hardware exception.
notice.solution.572327=Please contact the mos developer for handling.
notice.description.572328=PLC hardware exception.
notice.solution.572328=Please contact the mos developer for handling.
notice.description.572329=PLC hardware exception.
notice.solution.572329=Please contact the mos developer for handling.
notice.description.572330=PLC hardware exception.
notice.solution.572330=Please contact the mos developer for handling.
notice.description.572331=PLC hardware exception.
notice.solution.572331=Please contact the mos developer for handling.
notice.description.572332=PLC hardware exception.
notice.solution.572332=Please contact the mos developer for handling.
notice.description.572333=PLC hardware exception.
notice.solution.572333=Please contact the mos developer for handling.
notice.description.572334=PLC hardware exception.
notice.solution.572334=Please contact the mos developer for handling.
notice.description.572335=PLC hardware exception.
notice.solution.572335=Please contact the mos developer for handling.
notice.description.572336=PLC hardware exception.
notice.solution.572336=Please contact the mos developer for handling.
notice.description.572337=PLC hardware exception.
notice.solution.572337=Please contact the mos developer for handling.
notice.description.572338=PLC hardware exception.
notice.solution.572338=Please contact the mos developer for handling.
notice.description.572339=PLC hardware exception.
notice.solution.572339=Please contact the mos developer for handling.
notice.description.572340=PLC hardware exception.
notice.solution.572340=Please contact the mos developer for handling.
notice.description.572341=PLC hardware exception.
notice.solution.572341=Please contact the mos developer for handling.
notice.description.572342=PLC hardware exception.
notice.solution.572342=Please contact the mos developer for handling.
notice.description.572343=PLC hardware exception.
notice.solution.572343=Please contact the mos developer for handling.
notice.description.572344=PLC hardware exception.
notice.solution.572344=Please contact the mos developer for handling.
notice.description.572345=PLC hardware exception.
notice.solution.572345=Please contact the mos developer for handling.
notice.description.572346=PLC hardware exception.
notice.solution.572346=Please contact the mos developer for handling.
notice.description.572347=PLC hardware exception.
notice.solution.572347=Please contact the mos developer for handling.
notice.description.572348=PLC hardware exception.
notice.solution.572348=Please contact the mos developer for handling.
notice.description.572349=PLC hardware exception.
notice.solution.572349=Please contact the mos developer for handling.
notice.description.572350=PLC hardware exception.
notice.solution.572350=Please contact the mos developer for handling.
notice.description.572351=PLC hardware exception.
notice.solution.572351=Please contact the mos developer for handling.
notice.description.572352=PLC hardware exception.
notice.solution.572352=Please contact the mos developer for handling.
notice.description.572353=PLC hardware exception.
notice.solution.572353=Please contact the mos developer for handling.
notice.description.572354=PLC hardware exception.
notice.solution.572354=Please contact the mos developer for handling.
notice.description.572355=PLC hardware exception.
notice.solution.572355=Please contact the mos developer for handling.
notice.description.572356=PLC hardware exception.
notice.solution.572356=Please contact the mos developer for handling.
notice.description.572357=PLC hardware exception.
notice.solution.572357=Please contact the mos developer for handling.
notice.description.572358=PLC hardware exception.
notice.solution.572358=Please contact the mos developer for handling.
notice.description.572359=PLC hardware exception.
notice.solution.572359=Please contact the mos developer for handling.
notice.description.572360=PLC hardware exception.
notice.solution.572360=Please contact the mos developer for handling.
notice.description.572361=PLC hardware exception.
notice.solution.572361=Please contact the mos developer for handling.
notice.description.572362=PLC hardware exception.
notice.solution.572362=Please contact the mos developer for handling.
notice.description.572363=PLC hardware exception.
notice.solution.572363=Please contact the mos developer for handling.
notice.description.572364=PLC hardware exception.
notice.solution.572364=Please contact the mos developer for handling.
notice.description.572365=PLC hardware exception.
notice.solution.572365=Please contact the mos developer for handling.
notice.description.572366=PLC hardware exception.
notice.solution.572366=Please contact the mos developer for handling.
notice.description.572367=PLC hardware exception.
notice.solution.572367=Please contact the mos developer for handling.
notice.description.572368=PLC hardware exception.
notice.solution.572368=Please contact the mos developer for handling.
notice.description.572369=PLC hardware exception.
notice.solution.572369=Please contact the mos developer for handling.
notice.description.572370=PLC hardware exception.
notice.solution.572370=Please contact the mos developer for handling.
notice.description.572371=PLC hardware exception.
notice.solution.572371=Please contact the mos developer for handling.
notice.description.572372=PLC hardware exception.
notice.solution.572372=Please contact the mos developer for handling.
notice.description.572373=PLC hardware exception.
notice.solution.572373=Please contact the mos developer for handling.
notice.description.572374=PLC hardware exception.
notice.solution.572374=Please contact the mos developer for handling.
notice.description.572375=PLC hardware exception.
notice.solution.572375=Please contact the mos developer for handling.
notice.description.572376=PLC hardware exception.
notice.solution.572376=Please contact the mos developer for handling.
notice.description.572377=PLC hardware exception.
notice.solution.572377=Please contact the mos developer for handling.
notice.description.572378=PLC hardware exception.
notice.solution.572378=Please contact the mos developer for handling.
notice.description.572379=PLC hardware exception.
notice.solution.572379=Please contact the mos developer for handling.
notice.description.572380=PLC hardware exception.
notice.solution.572380=Please contact the mos developer for handling.
notice.description.572381=PLC hardware exception.
notice.solution.572381=Please contact the mos developer for handling.
notice.description.572382=PLC hardware exception.
notice.solution.572382=Please contact the mos developer for handling.
notice.description.572383=PLC hardware exception.
notice.solution.572383=Please contact the mos developer for handling.
notice.description.572384=PLC hardware exception.
notice.solution.572384=Please contact the mos developer for handling.
notice.description.572385=PLC hardware exception.
notice.solution.572385=Please contact the mos developer for handling.
notice.description.572386=PLC hardware exception.
notice.solution.572386=Please contact the mos developer for handling.
notice.description.572387=PLC hardware exception.
notice.solution.572387=Please contact the mos developer for handling.
notice.description.572388=PLC hardware exception.
notice.solution.572388=Please contact the mos developer for handling.
notice.description.572389=PLC hardware exception.
notice.solution.572389=Please contact the mos developer for handling.
notice.description.572390=PLC hardware exception.
notice.solution.572390=Please contact the mos developer for handling.
notice.description.572391=PLC hardware exception.
notice.solution.572391=Please contact the mos developer for handling.
notice.description.572392=PLC hardware exception.
notice.solution.572392=Please contact the mos developer for handling.
notice.description.572393=PLC hardware exception.
notice.solution.572393=Please contact the mos developer for handling.
notice.description.572394=PLC hardware exception.
notice.solution.572394=Please contact the mos developer for handling.
notice.description.572395=PLC hardware exception.
notice.solution.572395=Please contact the mos developer for handling.
notice.description.572396=PLC hardware exception.
notice.solution.572396=Please contact the mos developer for handling.
notice.description.572397=PLC hardware exception.
notice.solution.572397=Please contact the mos developer for handling.
notice.description.572398=PLC hardware exception.
notice.solution.572398=Please contact the mos developer for handling.
notice.description.572399=PLC hardware exception.
notice.solution.572399=Please contact the mos developer for handling.
notice.description.573100=E84 message waiting timeout.
notice.solution.573100=Please contact the mos developer for handling.
notice.description.573101=E84 Unsupported operation type.
notice.solution.573101=Please contact the mos developer for handling.
notice.description.573102=E84 Unsupported robot loading and unloading status.
notice.solution.573102=Please contact the mos developer for handling.
notice.description.573103 =Error in calling mqtt _ client function.
notice.solution.573103=Please contact the mos developer for handling.
notice.description.573104=mqtt got data timeout.
notice.solution.573104=Please contact the mos developer for handling.
notice.description.573105=mqtt sent data error.
notice.solution.573105=Please contact the mos developer for handling.
notice.description.573106=mqtt received data error.
notice.solution.573106=Please contact the mos developer for handling.
notice.description.573107=mqtt received fault information.
notice.solution.573107=Please contact the mos developer for handling.
notice.description.573108=Radar area switching failed.
notice.solution.573108=Please contact the mos developer for handling.
notice.description.574100=gripper failed to open.
notice.solution.574100=Please contact the mos developer for handling.
notice.description.574101=gripper closing failed.
notice.solution.574101=Please contact the mos developer for handling.
notice.description.574102=gripper reset failed.
notice.solution.574102=Please contact the mos developer for handling.
notice.description.574103=gripper 485 protocol reception failed.
notice.solution.574103=Please contact the mos developer for handling.
notice.description.574200=Distance sensor detection timeout.
notice.solution.574200=Please contact the mos developer for handling.
notice.description.574201=The sensor for detecting whether there is any material in the gripper has timed out.
notice.solution.574201=Please contact the mos developer for handling.
notice.description.574300=The camera is not connected.
notice.solution.574300=Please contact the mos developer for handling.
notice.description.574301=Camera internal error.
notice.solution.574301=Please contact the mos developer for handling.
notice.description.574302=Timeout.
notice.solution.574302=Please contact the mos developer for handling.
notice.description.574303=Unknown camera command.
notice.solution.574303=Please contact the mos developer for handling.
notice.description.574304=Index is out of range.
notice.solution.574304=Please contact the mos developer for handling.
notice.description.574305=There are too few independent variables.
notice.solution.574305=Please contact the mos developer for handling.
notice.description.574306=Invalid argument type.
notice.solution.574306=Please contact the mos developer for handling.
notice.description.574307=Invalid argument.
notice.solution.574307=Please contact the mos developer for handling.
notice.description.574308=command not allowed.
notice.solution.574308=Please contact the mos developer for handling.
notice.description.574309=disallowed combination.
notice.solution.574309=Please contact the mos developer for handling.
notice.description.574310=Camera busy
notice.solution.574310=Please contact the mos developer for handling.
notice.description.574311=Not fully implemented.
notice.solution.574311=Please contact the mos developer for handling.
notice.description.574312=Not supported.
notice.solution.574312=Please contact the mos developer for handling.
notice.description.574313=The result string is too long.
notice.solution.574313=Please contact the mos developer for handling.
notice.description.574314 =effect camera ID
notice.solution.574314=Please contact the mos developer for handling.
notice.description.574315 =effect camera feature ID
notice.solution.574315=Please contact the mos developer for handling.
notice.description.574316=Different collocation names.
notice.solution.574316=Please contact the mos developer for handling.
notice.description.574317=Different versions.
notice.solution.574317=Please contact the mos developer for handling.
notice.description.574318=Not calibrated.
notice.solution.574318=Please contact the mos developer for handling.
notice.description.574319=Calibration failed.
notice.solution.574319=Please contact the mos developer for handling.
notice.description.574320 =effective calibration data.
notice.solution.574320=Please contact the mos developer for handling.
notice.description.574321=The given calibration position has not been reached.
notice.solution.574321=Please contact the mos developer for handling.
notice.description.574322 =Start command
notice.solution.574322=Please contact the mos developer for handling.
notice.description.574323=The feature has not been trained.
notice.solution.574323=Please contact the mos developer for handling.
notice.description.574324=Feature not found.
notice.solution.574324=Please contact the mos developer for handling.
notice.description.574325=The feature is not mapped.
notice.solution.574325=Please contact the mos developer for handling.
notice.description.574326=Part location is not trained.
notice.solution.574326=Please contact the mos developer for handling.
notice.description.574327=Machine location is untrained.
notice.solution.574327=Please contact the mos developer for handling.
notice.description.574328 =effective part ID
notice.solution.574328=Please contact the mos developer for handling.
notice.description.574329=Not all features of this part have been located.
notice.solution.574329=Please contact the mos developer for handling.
notice.description.574330=Effective clamping correction of parts.
notice.solution.574330=Please contact the mos developer for handling.
notice.description.574331=Effective clamping correction of parts.
notice.solution.574331=Please contact the mos developer for handling.
notice.description.574350=Error in camera reading socket.
notice.solution.574350=Please contact the mos developer for handling.
notice.description.574351=The camera response information header does not match.
notice.solution.574351=Please contact the mos developer for handling.
notice.description.574352=Failed to parse the camera response.
notice.solution.574352=Please contact the mos developer for handling.
notice.description.574360=Camera internal reference calibration failed.
notice.solution.574360=Please contact the mos developer for handling.
notice.description.574361=Camera hand-eye calibration failed.
notice.solution.574361=Please contact the mos developer for handling.
notice.description.574362=No internal reference calibration data.
notice.solution.574362=Please contact the mos developer for handling.
notice.description.574363=No hand-eye calibration data.
notice.solution.574363=Please contact the mos developer for handling.
notice.description.574364=Camera data acquisition failed.
notice.solution.574364=Please contact the mos developer for handling.
notice.description.574365=Camera data storage failed.
notice.solution.574365=Please contact the mos developer for handling.
notice.description.574366=Failed to obtain the coordinates of feature points.
notice.solution.574366=Please contact the mos developer for handling.
notice.description.574367=The deviation between the clamping position calculated by taking photos and the clamping position of teaching is too large.
notice.solution.574367=Please contact the mos developer for handling.
notice.description.574368=Failed to create the template image.
notice.solution.574368=Please contact the mos developer for handling.
notice.description.574369=Failed to get algorithm parameters.
notice.solution.574369=Please contact the mos developer for handling.
notice.description.574370=Exception in camera image processing.
notice.solution.574370=Please contact the mos developer for handling.
notice.description.574390=Camera failed to take a picture.
notice.solution.574390=Please contact the mos developer for handling.
notice.description.574391=Camera recipe setting failed.
notice.solution.574391=Please contact the mos developer for handling.
notice.description.574392=Failed to get camera trigger parameters.
notice.solution.574392=Please contact the mos developer for handling.
notice.description.574393=Failed to save the camera project.
notice.solution.574393=Please contact the mos developer for handling.
notice.description.574394=Camera is not initialized.
notice.solution.574394=Please contact the mos developer for handling.
notice.description.574400=Failed to open the serial port of light source equipment.
notice.solution.574400=Please contact the mos developer for handling.
notice.description.574401=Abnormal reading and writing of serial port of light source equipment.
notice.solution.574401=Please contact the mos developer for handling.
notice.description.574410=The light source failed to turn on.
notice.solution.574410=Please contact the mos developer for handling.
notice.description.574411=The light source failed to turn off.
notice.solution.574411=Please contact the mos developer for handling.
notice.description.574412=Failed to obtain brightness of light source.
notice.solution.574412=Please contact the mos developer for handling.
notice.description.575100=Invalid locator ID.
notice.solution.575100=Please contact the mos developer for handling.
notice.description.575101=Location detection timeout.
notice.solution.575101=Please contact the mos developer for handling.
notice.description.575200=Location lock lock timeout.
notice.solution.575200=Please contact the mos developer for handling.
notice.description.575201=Parking lock unlock timeout.
notice.solution.575201=Please contact the mos developer for handling.
notice.description.575300=No material information detection sensor.
notice.solution.575300=Please contact the mos developer for handling.
notice.description.575301 =Smart Tag sensor is not connected.
notice.solution.575301=Please contact the mos developer for handling.
notice.description.575302 =Smart tag read failed.
notice.solution.575302=Please contact the mos developer for handling.
notice.description.575303 =Smart tag read timeout.
notice.solution.575303=Please contact the mos developer for handling.
notice.description.575304 =Smart tag data is invalid.
notice.solution.575304=Please contact the mos developer for handling.
notice.description.575401=RFID sensor is not connected.
notice.solution.575401=Please contact the mos developer for handling.
notice.description.575402=RFID reading failed.
notice.solution.575402=Please contact the mos developer for handling.
notice.description.575403=RFID read timeout.
notice.solution.575403=Please contact the mos developer for handling.
notice.description.575404 =Invalid RFID data.
notice.solution.575404=Please contact the mos developer for handling.
notice.description.575405=RFID request data error.
notice.solution.575405=Please contact the mos developer for handling.
notice.description.575900 =smart tag sensor failure.
notice.solution.575900=Please contact the mos developer for handling.
notice.description.575901 =RFID sensor failure.
notice.solution.575901=Please contact the mos developer for handling.
notice.description.576100=Lifting column is out of motion range.
notice.solution.576100=Please contact the mos developer for handling.
notice.description.576101=Invalid control instruction.
notice.solution.576101=Please contact the mos developer for handling.
notice.description.576102=Invalid multi-axis control mode.
notice.solution.576102=Please contact the mos developer for handling.
notice.description.576103=The multi-axis device is not ready, or there is an exception.
notice.solution.576103=Please contact the mos developer for handling.
notice.description.576104=Can card is not connected.
notice.solution.576104=Please contact the mos developer for handling.
notice.description.576105=The equipment of the step shaft exceeds the travel.
notice.solution.576105=Please contact the mos developer for handling.
notice.description.576106=The multi-axis device cannot get the current position.
notice.solution.576106=Please contact the mos developer for handling.
notice.description.576107=Multi-axis device movement failed.
notice.solution.576107=Please contact the mos developer for handling.
notice.description.576108=The motion of multi-axis device was conditionally interrupted.
notice.solution.576108=Please contact the mos developer for handling.
notice.description.576109=The target point of multi-axis equipment exceeds the set limit.
notice.solution.576109=Please contact the mos developer for handling.
notice.description.576110=The motion of multi-axis equipment was manually interrupted.
notice.solution.576110=Please contact the mos developer for handling.
notice.description.576111=Single-axis device instruction blocking wait timeout, which did not stop for more than 600s s.
notice.solution.576111=Please contact the mos developer for handling.
notice.description.576201=Invalid multi-axis parameter.
notice.solution.576201=Please contact the mos developer for handling.
notice.description.576202=Multi-axis management initialization failed.
notice.solution.576202=Please contact the mos developer for handling.
notice.description.577100=JSON parsing of Yuntai task failed.
notice.solution.577100=Please contact the mos developer for handling.
notice.description.577101=The type of action performed by Yuntai is wrong.
notice.solution.577101=Please contact the mos developer for handling.
notice.description.577102=Yuntai channel error.
notice.solution.577102=Please contact the mos developer for handling.
notice.description.577103=Yuntai channel type is wrong.
notice.solution.577103=Please contact the mos developer for handling.
notice.description.577104=The name of Yuntai camera is wrong.
notice.solution.577104=Please contact the mos developer for handling.
notice.description.577105=Yuntai failed to take photos.
notice.solution.577105=Please contact the mos developer for handling.
notice.description.577106=Yuntai failed to perform video recording.
notice.solution.577106=Please contact the mos developer for handling.
notice.description.577107=Yuntai failed to perform parameter setting.
notice.solution.577107=Please contact the mos developer for handling.
notice.description.577108=Yuntai failed to perform ordinary temperature measurement.
notice.solution.577108=Please contact the mos developer for handling.
notice.description.577109=Remote copy of Yuntai image failed.
notice.solution.577109=Please contact the mos developer for handling.
notice.description.577110=Yuntai failed to obtain SGC parameters. Check whether the camera name issued by SGC corresponds.
notice.solution.577110=Please contact the mos developer for handling.
notice.description.578100=JSON parsing of sensor failed.
notice.solution.578100=Please contact the mos developer for handling.
notice.description.578101=Sensor name does not exist.
notice.solution.578101=Please contact the mos developer for handling.
notice.description.578102=Incoming G300M4 has the wrong mode.
notice.solution.578102=Please contact the mos developer for handling.
notice.description.578201 =Initialization of XSLAB voiceprint sensor failed.
notice.solution.578201=Please contact the mos developer for handling.
notice.description.578202 =g300m4 partial discharge sensor initialization failed.
notice.solution.578202=Please contact the mos developer for handling.
notice.description.578203 =fs00802 Initialization of Fushen sensor failed.
notice.solution.578203=Please contact the mos developer for handling.
notice.description.578204=Failed to receive G300M4 data.
notice.solution.578204=Please contact the mos developer for handling.
notice.description.578205 =G300M4 working mode error.
notice.solution.578205=Please contact the mos developer for handling.
notice.description.578206=The sensor failed to initialize or ran incorrectly. Please check the configuration.
notice.solution.578206=Please contact the mos developer for handling.
notice.description.579000=The system is not initialized.
notice.solution.579000=Please contact the mos developer for handling.
notice.description.579100=Canceling the task failed.
notice.solution.579100=Please contact the mos developer for handling.
notice.description.579101=Failed to pause the task.
notice.solution.579101=Please contact the mos developer for handling.
notice.description.579102=Recovery task failed.
notice.solution.579102=Please contact the mos developer for handling.
notice.description.579103 =buffer parsing error.
notice.solution.579103=Please contact the mos developer for handling.
notice.description.579104=Task not found.
notice.solution.579104=Please contact the mos developer for handling.
notice.description.579105=The task list was not updated.
notice.solution.579105=Please contact the mos developer for handling.
notice.description.579106=There are unfinished tasks.
notice.solution.579106=Please contact the mos developer for handling.
notice.description.579107=The task was manually interrupted.
notice.solution.579107=Please contact the mos developer for handling.
notice.description.579201=Invalid step type.
notice.solution.579201=Please contact the mos developer for handling.
notice.description.579202=pose value not found.
notice.solution.579202=Please contact the mos developer for handling.
notice.description.579203=joint value not found.
notice.solution.579203=Please contact the mos developer for handling.
notice.description.579204=Offset not found.
notice.solution.579204=Please contact the mos developer for handling.
notice.description.579205=Invalid feature ID.
notice.solution.579205=Please contact the mos developer for handling.
notice.description.579206=Invalid condition type.
notice.solution.579206=Please contact the mos developer for handling.
notice.description.579207=Invalid condition parameter.
notice.solution.579207=Please contact the mos developer for handling.
notice.description.579208=Failed to get the action list.
notice.solution.579208=Please contact the mos developer for handling.
notice.description.579209=The mechanical arm is not at the origin.
notice.solution.579209=Please contact the mos developer for handling.
notice.description.579210=Integrated lock, chassis is moving.
notice.solution.579210=Please contact the mos developer for handling.
notice.description.579211 =Failed to parse the socket protocol.
notice.solution.579211=Please contact the mos developer for handling.

#log
log.export.interfaceLog.excelName=Interface Log
log.export.operationLog.excelName=Operation Log
log.operation.description.success=Success
log.operation.description.fail=Fail
log.third.system.operator=Third party system

log.controller.api.task.create=[API]Create Task
log.controller.api.task.cancel=[API]Cancel Task
log.controller.api.task.overNode=[API]Skip task node
log.controller.api.traffic.occupy=[API]Apply traffic area
log.controller.api.traffic.release=[API]Release traffic area
log.controller.api.vehicle.operation=[API]Operate vehicle
log.controller.api.vehicle.globalPause=[API]Global Pause
log.controller.api.vehicle.globalResume=[API]Global Resume
log.controller.language.delete=Delete language
log.controller.language.import=Import language
log.controller.language.export=Export language
log.controller.language.switch=Switch language
log.controller.license.upload=Upload license
log.controller.license.delete=Delete license
log.controller.operationLog.delete=Delete operation Log
log.controller.sysLog.delete=Delete system Log
log.controller.airShowerDoor.insert=Insert air shower door
log.controller.airShowerDoor.update=Update air shower door
log.controller.airShowerDoor.delete=Delete air shower door
log.controller.airShowerDoor.open=Open air shower door
log.controller.airShowerDoor.close=Close air shower door
log.controller.autoDoor.insert=Insert auto door
log.controller.autoDoor.update=Update auto door
log.controller.autoDoor.delete=Delete auto door
log.controller.autoDoor.open=Open auto door
log.controller.autoDoor.close=Close auto door
log.controller.elevator.insert=Insert elevator
log.controller.elevator.update=Update elevator
log.controller.elevator.delete=Delete elevator
log.controller.elevator.import=Import elevator
log.controller.elevator.export=Export elevator
log.controller.elevator.open=Open elevator
log.controller.elevator.close=Close elevator
log.controller.mapArea.insert=Insert area
log.controller.mapArea.enable=启用区域（待翻译）
log.controller.mapArea.disable=禁用区域（待翻译）
log.controller.mapArea.update=Update element
log.controller.mapArea.delete=Delete element
log.controller.marker.insert=Insert marker
log.controller.marker.transcribe=Transcribe marker
log.controller.marker.update=Update element
log.controller.marker.delete=Delete element
log.controller.path.insert=Insert path
log.controller.path.update=Update element
log.controller.path.delete=Delete element
log.controller.vehicleMap.insert=Insert map
log.controller.vehicleMap.update=Update map
log.controller.vehicleMap.delete=Delete map
log.controller.vehicleMap.batchDelete=Batch delete map
log.controller.vehicleMap.deleteDraft=Delete map draft
log.controller.vehicleMap.batchGenerateElement=Batch generate marker and path
log.controller.vehicleMap.batchUpdateElement=Update element
log.controller.vehicleMap.batchDeleteElement=Delete element
log.controller.vehicleMap.import=Import map
log.controller.vehicleMap.export=Export map
log.controller.vehicleMap.copy=Copy map
log.controller.vehicleMap.pause=Map Global Pause
log.controller.vehicleMap.recover=Map Global Resume
log.controller.vehicleMap.publish=Publish map
log.controller.vehicleMap.locatingMap.update=Update locating map
log.controller.vehicleMap.locatingMap.import=Import locating map
log.controller.vehicleMap.locatingMap.changeDefault=Switch default locating map
log.controller.vehicleMap.locatingMap.delete=Delete locating map
log.controller.noticeConfig.insert=Insert notice config
log.controller.noticeConfig.update=Update notice config
log.controller.noticeConfig.delete=Delete notice config
log.controller.noticeConfig.export=Export notice config
log.controller.noticeConfig.import=Import notice config
log.controller.noticeRecord.insert=Insert notice record
log.controller.noticeRecord.update=Update notice record
log.controller.noticeRecord.delete=Delete notice record
log.controller.noticeRecord.activation=Activation notice record
log.controller.noticeRecord.ignore=Ignore notice record
log.controller.noticeRecord.ignoreVehicle=Ignore notice record about vehicle
log.controller.noticeRecord.export=Export notice record
log.controller.pda.config=(PDA)Update version info
log.controller.pda.containerEnter=(PDA)Container enter
log.controller.pda.containerExit=(PDA)Container exit
log.controller.pda.execute=(PDA)Insert task
log.controller.security.login=Log in system
log.controller.security.logout=Log out system
log.controller.sys.menu.insert=Insert menu
log.controller.sys.menu.update=Update menu
log.controller.sys.menu.delete=Delete menu
log.controller.sys.role.insert=Insert role
log.controller.sys.role.update=Update role
log.controller.sys.role.delete=Delete role
log.controller.sys.property.batchUpdate=Batch update system property
log.controller.sys.property.insert=Insert system property
log.controller.sys.property.update=Update system property
log.controller.sys.property.delete=Delete system property
log.controller.sys.user.password=Update password
log.controller.sys.user.password.reset=Reset password
log.controller.sys.user.insert=Insert user
log.controller.sys.user.update=Update user
log.controller.sys.user.delete=Delete user
log.controller.task.nodeConfig.insert=Insert node config
log.controller.task.nodeConfig.update=Update node config
log.controller.task.nodeConfig.delete=Delete node config
log.controller.task.nodeConfig.batchCommon=Set node common
log.controller.task.nodeConfig.export=Export node config
log.controller.task.nodeConfig.import=Import node config
log.controller.task.insert=Insert task
log.controller.task.cancel=Cancel task
log.controller.task.delete=Delete task
log.controller.task.skip=Skip task node
log.controller.task.retry=Retry task node
log.controller.task.batchCancel=Batch cancel task
log.controller.task.cancelAll=One key cancel all task
log.controller.task.export=Export task record
log.controller.task.import=Import task record
log.controller.task.remark=Remark task record
log.controller.task.type.insert=Insert task type
log.controller.task.type.update=Update task type
log.controller.task.type.copy=Copy task type
log.controller.task.type.delete=Delete task type
log.controller.task.type.enable=Enable task type
log.controller.task.type.disable=Disable task type
log.controller.task.type.export=Export task type
log.controller.task.type.import=Import task type
log.controller.vehicle.stop.open=Pause vehicle
log.controller.vehicle.stop.close=Recover vehicle
log.controller.vehicle.delete=Delete vehicle
log.controller.vehicle.restart=Restart vehicle
log.controller.vehicle.shutdown=Shutdown vehicle
log.controller.vehicle.controls.manualMode=Switch manual control
log.controller.vehicle.controls.autoMode=Switch auto control
log.controller.vehicle.scheduler.manualMode=Switch manual scheduler
log.controller.vehicle.scheduler.autoMode=Switch auto scheduler
log.controller.vehicle.update=Update vehicle config
log.controller.vehicle.updateBatch=Batch update vehicle config
log.controller.vehicle.updateGroupBatch=Batch update vehicle group
log.controller.vehicle.updateTypeBatch=Batch update vehicle type
log.controller.vehicle.resource.clear=Clear vehicle resource
log.controller.vehicle.reset=Reset vehicle
log.controller.vehicle.dockingReset=Docking reset
log.controller.vehicle.charge=Common charge
log.controller.vehicle.group.insert=Insert vehicle group
log.controller.vehicle.group.update=Update vehicle group
log.controller.vehicle.group.delete=Delete vehicle group
log.controller.vehicle.group.export=Export vehicle group
log.controller.vehicle.group.import=Import vehicle group
log.controller.vehicle.type.insert=Insert vehicle type
log.controller.vehicle.type.update=Update vehicle type
log.controller.vehicle.type.delete=Delete vehicle type
log.controller.vehicle.type.export=Export vehicle type
log.controller.vehicle.type.import=Import vehicle type
log.controller.vehicle.map.appoint=Appoint map
log.controller.vehicle.map.relocation=Relocation vehicle
log.controller.warehouse.area.insert=Import warehouse area
log.controller.warehouse.area.update=Update warehouse area
log.controller.warehouse.area.delete=Delete warehouse area
log.controller.warehouse.area.export=Export warehouse area
log.controller.warehouse.area.import=Import warehouse area
log.controller.warehouse.type.insert=Insert warehouse type
log.controller.warehouse.type.update=Update warehouse type
log.controller.warehouse.type.delete=Delete warehouse type
log.controller.warehouse.type.export=Export warehouse type
log.controller.warehouse.type.import=Import warehouse type
log.controller.warehouse.insert=Insert warehouse
log.controller.warehouse.batchInsert=Batch insert warehouse
log.controller.warehouse.update=Update warehouse
log.controller.warehouse.delete=Delete warehouse
log.controller.warehouse.enable=Enable warehouse
log.controller.warehouse.disable=Disable warehouse
log.controller.warehouse.export=Export warehouse
log.controller.warehouse.import=Import warehouse
log.controller.event.insert=Insert event
log.controller.event.update=Update event
log.controller.event.copy=Copy event
log.controller.event.delete=Delete event
log.controller.event.enable=Enable event
log.controller.event.disable=Disable event
log.controller.event.export=Export event
log.controller.event.import=Import event

log.operation.excel.head.operator=User
log.operation.excel.head.description=Description
log.operation.excel.head.success=Result
log.operation.excel.head.errorMsg=Fail Reason
log.operation.excel.head.wasteTime=Response Time
log.operation.excel.head.ip=Client Ip Address
log.operation.excel.head.paramsIn=Request Info
log.operation.excel.head.paramsOut=Response Info
log.operation.excel.head.operationTime=Operation Time

log.interface.excel.head.description=Description
log.interface.excel.head.success=Result
log.interface.excel.head.errorMsg=Fail Reason
log.interface.excel.head.wasteTime=Response Time
log.interface.excel.head.url=URL
log.interface.excel.head.paramsIn=Request Info
log.interface.excel.head.paramsOut=Response Info
log.interface.excel.head.operationTime=Operation Time

log.system.module.task=Task
log.system.module.task.allocation=Task allocation
log.system.module.charge.allocation=Charge allocation
log.system.module.park.allocation=Park allocation
log.system.module.resource.apply=Resource apply
log.system.module.traffic.avoid=Traffic avoid
log.system.module.vehicle=Vehicle
log.system.module.event=Event
log.system.module.system=System
log.system.module.autodoor=Autodoor
log.system.module.airshowerdoor=Airshowerdoor
log.system.module.elevator=Elevator

log.system.type.Running=Running Log
log.system.type.Warning=Warning Log
log.system.type.Error=Error Log

log.system.system.start=The scheduling system has started successfully
log.system.path.plan.is.unreachable=Robot movement node, robot  failed to plan path, target point  is unreachable
log.system.instruction.status.upload=Robot  feedback instruction status
log.system.resource.elevator.apply.success=Robot  applied for elevator  to open successfully
log.system.resource.elevator.ride.success=Elevator  completed lifting, door opened successfully
log.system.resource.vehicle.clear.resource=Robot  network connection timed out, automatically clearing resources occupied by the robot

log.system.vehicle.connect=Robot  connected to scheduling system
log.system.vehicle.disconnect=Robot  disconnected from scheduling system
log.system.vehicle.out.of.trace=Out of trace
log.system.vehicle.on.trace=Not out of trace
log.system.vehicle.close.stop=Robot  emergency stop button restored
log.system.vehicle.open.stop=Robot  emergency stop button pressed
log.system.vehicle.pause.close=Pause closed
log.system.vehicle.pause.open=Pause opened
log.system.vehicle.manual.control=Robot  switched to manual control mode
log.system.vehicle.auto.control=Robot  switched to automatic control mode
log.system.vehicle.work.status.work=Work
log.system.vehicle.work.status.free=Free
log.system.vehicle.connect.status.connect=connect
log.system.vehicle.connect.status.disconnect=disconnect
log.system.vehicle.control.status.manual=Manual
log.system.vehicle.control.status.auto=Auto
log.system.vehicle.abnormal.status.abnormal=Abnormal
log.system.vehicle.abnormal.status.normal=Normal
log.system.vehicle.position.status.notLocated=NotLocated
log.system.vehicle.position.status.located=Located

log.system.charge.scheduler.error=Robot charging scheduling encountered an exception
log.system.charge.create.task.success=Robot  created charging task  successfully, current battery level , charging point 
log.system.charge.create.task.fail=Robot  failed to create charging task, task flow [-]
log.system.charge.vehicle.disable=Robot  is not enabled for automatic charging
log.system.charge.battery.value.is.null=Robot  current battery level is null
log.system.charge.no.usable.charge.marker=Robot  unable to obtain charging point, blocked charging points , occupied charging points , unreachable charging points 
log.system.charge.get.other.charge.marker=Robot  low battery , preempting robot  charging point 
log.system.park.scheduler.error=Robot parking scheduling encountered an exception
log.system.park.vehicle.disable=Robot  is not enabled for automatic parking
log.system.park.no.usable.park.marker=Robot  unable to obtain parking point, blocked parking points , occupied parking points , unreachable parking points 
log.system.park.create.task.success=Robot  created parking task  successfully, parking point 
log.system.park.create.task.fail=Robot  failed to create parking task, task flow [-]

log.system.traffic.marker.is.not.avaliable.error=Robot  cannot go to avoidance marker , replanning path
log.system.traffic.resource.conflict=Conflict between , robot  replanning path to marker 
log.system.traffic.detect.obstacle=Robot  detected obstacle, start replanning path
log.system.traffic.detect.vehicle=Robot  detected obstacle ahead, start replanning path
log.system.traffic.detect.control.area=Robot  encountered controlled area , start replanning path
log.system.traffic.detect.map.publish=Robot  replanning path, user manually published map 
log.system.traffic.detect.vehicle.error=Path navigation detected obstacle ahead, triggered detour error, robot 
log.system.traffic.detect.vehicle.drive=Robot  detected obstacle ahead, driving away robot  to marker 
log.system.traffic.detect.vehicle.drive.error=Path navigation detected obstacle ahead, triggered driving away error, robot 

log.system.auto.door.thread.error=Automatic door program encountered an exception
log.system.auto.door.connect.error=Failed to connect to automatic door 
log.system.auto.door.no.bind.path.error=Automatic door  is not bound to a path
log.system.auto.door.read.error=Failed to read command from automatic door 
log.system.auto.door.write.error=Failed to write command to automatic door 
log.system.auto.door.open.ok=Automatic door  opened successfully
log.system.auto.door.close.ok=Automatic door  closed successfully

log.system.air.shower.thread.error=Air shower door program encountered an exception
log.system.air.shower.no.bind.path.error=Air shower door  is not bound to a path
log.system.air.shower.connect.error=Failed to connect to air shower door 
log.system.air.shower.read.error=Failed to read command from air shower door 
log.system.air.shower.write.error=Failed to write command to air shower door 
log.system.air.shower.open.ok=Air shower door  opened successfully
log.system.air.shower.close.ok=Air shower door  closed successfully

log.system.elevator.thread.error=Elevator program encountered an exception
log.system.elevator.no.bind.path.error=Elevator  is not bound to points
log.system.elevator.vehicle.leave=Robot  left elevator  successfully used
log.system.elevator.vehicle.apply.run=Robot  applied for elevator  lift
log.system.elevator.connect.error=Failed to connect to elevator 
log.system.elevator.read.error=Failed to read command from elevator 
log.system.elevator.write.error=Failed to write command to elevator 

log.system.task.allocation.error=Robot task scheduling encountered an exception
log.system.task.allocation.cancel.old.task.success=Task has been interrupted, new task  is being bound to robot 
log.system.task.allocation.interrupt.current.task=Task dispatch to robot, interrupt robot's current running task 
log.system.task.allocation.success=Task  allocated successfully, robot 
log.system.task.allocation.fail.vehicle.no.exist=Failed to allocate specified robot, robot  does not exist
log.system.task.allocation.fail.vehicle.state.error=Failed to allocate specified robot, robot  state does not match, current state is 
log.system.task.allocation.fail.vehicle.locked=Failed to allocate specified robot, robot  is already occupied by task 
log.system.task.start.run=Task started execution
log.system.task.run.error=Task  execution encountered an error
log.system.task.run.finish=Task  execution completed
log.system.task.cancel.run=Task  execution canceled
log.system.task.status.change.callback.request.param=Task  status change callback request param
log.system.task.status.change.callback.response.param=Task  status change callback response param

log.system.node.start=Node started execution
log.system.node.end=Node completed execution
log.system.node.cancel=Node execution canceled
log.system.node.error=Node execution encountered an error
log.system.node.no.available.marker=Failed to allocate marker, no available markers
log.system.node.start.send=Node execution, robot  started sending commands
log.system.node.send.succeed=Node execution, robot  command sent successfully
log.system.node.send.error=Node execution, robot  failed to send command
log.system.node.run.succeed=Node execution, robot  completed the command
log.system.node.run.error=Node execution, robot  failed to execute the command
log.system.node.start.send.cancel=Node execution, robot  started sending stop command
log.system.node.send.cancel.succeed=Node execution, robot  stop command sent successfully
log.system.node.send.cancel.error=Node execution, robot  failed to send stop command
log.system.node.stop.succeed=Node execution, robot  stop command succeeded
log.system.node.stop.error=Node execution, robot  failed to execute stop command
log.system.node.cancel.timeout=Node execution, robot  stop command timed out, system forcefully stopped the task
log.system.node.button.release.succeed=Release button node, device  released successfully
log.system.node.button.release.error=Release button node encountered an exception, unable to connect to device 
log.system.node.button.reset.succeed=Reset button node execution succeeded, device 
log.system.node.button.reset.error=Reset button node execution encountered an exception, unable to connect to device 
log.system.node.vehicle.no.assign.error=Node execution, robot  not assigned lock
log.system.node.vehicle.no.exist.error=Node execution, robot  does not exist
log.system.node.vehicle.disconnect.error=Robot movement node, robot  state verification failed, robot disconnected
log.system.node.vehicle.abnormal.error=Robot movement node, robot  state verification failed, robot has abnormality
log.system.node.vehicle.state.error=Node execution, robot  state does not match
log.system.node.vehicle.move.state.change.re.pathplan=Robot movement node, robot  state changed, re-planning path
log.system.node.vehicle.move.send.instruction=Robot movement node, robot  sent move command
log.system.node.vehicle.move.finish=Robot movement node, robot  move command completed
log.system.node.vehicle.move.error=Robot movement node, robot  move command execution failed
log.system.node.vehicle.move.send.cancel=Robot movement node, sending cancel move command
log.system.node.vehicle.move.send.cancel.fail=Robot movement node, failed to send cancel move command
log.system.node.vehicle.move.cancel.success=Robot movement node, move command canceled successfully
log.system.node.vehicle.move.cancel.fail=Robot movement node, move command cancel failed
log.system.node.vehicle.move.no.position=Robot movement node, robot  has no position data
log.system.node.vehicle.move.stay.tartget.marker=Robot movement node, robot  is already at the target point
log.system.node.vehicle.move.pathplan.start=Robot movement node, robot  starts path planning
log.system.node.vehicle.move.pathplan.success=Robot movement node, robot  path planning succeeded
log.system.node.vehicle.move.pathplan.message=Robot movement node, Generated path planning data
log.system.node.vehicle.move.pathplan.cancel=Robot movement node, Detected path navigation cancellation
log.system.node.vehicle.move.re.pathplan=Robot movement node, robot  is not on the planned path, re-planning the path
log.system.node.vehicle.move.stop.button=Robot movement node, robot  has been stopped by emergency stop button
log.system.node.set.variable.param.is.empty=Set variable node, cannot find the variable
log.system.node.set.variable.param.change.error=Set variable node, variable  is not number, cannot be change to be number
log.system.node.set.variable.param.source.is.unknown=Set variable node, the source  of the variable  is unknown
log.system.node.set.variable.param.is.change=Set variable node, the value of variable  is changed from  to 
log.system.node.finish.request.param=Finish task node, The request param of http
log.system.node.finish.response.param=Finish task node, The response data of http
log.system.node.http.request.param=Http task node, The request param of http
log.system.node.http.response.param=Http task node, The response data of http
log.system.node.http.check.param=Description HTTP parity node is missing
log.system.node.http.check.path=Description HTTP parameter verification failed because no corresponding field was found
log.system.node.http.check.fail=HTTP parameter verification fails


log.system.trigger.callbox.success=Triggered call box  button  event successfully, created task 
log.system.trigger.callbox.fail=Failed to trigger call box  button  event, task flow 
log.system.trigger.fix.success=Triggered fixed-time event successfully, created task 
log.system.trigger.fix.fail=Failed to trigger fixed-time event, task flow 
log.system.trigger.plc.success=Triggered PLC event successfully, created task 
log.system.trigger.plc.fail=Failed to trigger PLC event, task flow 
log.system.trigger.task.cancel.success=Triggered task  cancellation event successfully, created task 
log.system.trigger.task.cancel.fail=Failed to trigger task  cancellation event, task flow 
log.system.trigger.task.finish.success=Triggered task  completion event successfully, created task 
log.system.trigger.task.finish.fail=Failed to trigger task  completion event, task flow 
log.system.trigger.vehicle.abnormal.success=Triggered robot  abnormal event successfully, created task 
log.system.trigger.vehicle.abnormal.fail=Failed to trigger robot  abnormal event, task flow 
log.system.trigger.vehicle.plc.success=Triggered robot  PLC event successfully, created task 
log.system.trigger.vehicle.plc.fail=Failed to trigger robot  PLC event, task flow 

log.system.export.error=The quantity exported at a time cannot exceed a fixed quantity. Please re filter
log.system.export.name=Running Log
log.system.download.file.not.exist=Cannot find the file：
log.system.download.file.error=Failed to download the file

log.system.excel.head.module=Type
log.system.excel.head.type=Level
log.system.excel.head.content=Description
log.system.excel.head.data=Data
log.system.excel.head.message=Body
log.system.excel.head.vehicleCodes=Vehicles
log.system.excel.head.taskNos=Task
log.system.excel.head.createDate=Create Date
log.system.excel.head.lastTime=Last Update Time

#validation
validation.id.require=ID can not be empty
validation.id.null=ID has to be empty
validation.pid.require=Parent ID, cannot be empty
validation.sort.number=The sort value cannot be less than 0
validation.sysparams.paramcode.require=Parameter encoding cannot be empty
validation.sysparams.paramvalue.require=Parameter values cannot be empty
validation.sysuser.username.require=The username cannot be empty
validation.sysuser.password.require=The password cannot be empty
validation.sysuser.realname.require=The realname cannot be empty
validation.sysuser.email.require=Mailbox cannot be empty
validation.sysuser.email.error=Incorrect email format
validation.sysuser.mobile.require=The phone number cannot be empty
validation.sysuser.superadmin.range=Super administrator values range from 0 to 1
validation.sysuser.status.range=State ranges from 0 to 1
validation.sysmenu.pid.require=Please select superior menu
validation.sysmenu.name.require=Menu name cannot be empty
validation.sysmenu.type.range=Menu type ranges from 0 to 1
validation.sysrole.name.require=The role name cannot be empty
validation.schedule.status.range=Status ranges from 0 to 1
validation.schedule.cron.require=Cron expression cannot be empty
validation.schedule.bean.require=Bean name cannot be empty
validation.news.title.require=The title cannot be empty
validation.news.content.require=Content cannot be empty
validation.news.pubdate.require=The release time cannot be empty
validation.map.marker.name=The marker name can be null, or consist of char,digit,underline，and startWith char, the length of name must not be more then 20
validation.map.marker.type.require=The marker type cannot be empty
validation.map.marker.code.require=The marker code cannot be empty
validation.map.marker.type=The marker type must be in (ChargingMarker,NavigationMarker,WorkMarker)
validation.map.marker.x.require=The marker X coordinate cannot be empty
validation.map.marker.y.require=The marker Y coordinate cannot be empty
validation.map.path.type.require=The path type cannot be empty
validation.map.path.type=The path type must be in (Common、QR_Down、Shelflegs、Symbol_V、Reflector、LeaveDocking、Pallet)
validation.map.path.startMarkerCode.require=The startMarkerId of path cannot be empty
validation.map.path.endMarkerCode.require=The endMarkerId of path cannot be empty
validation.map.path.weightRatio.require=The value of weightRatio must be Positive
validation.map.area.areaType.require=The areaType cannot be empty, the value must be in(SingleAgvArea、ShowArea、ControlArea、ChannelArea、NoRotatingArea、NoParkingArea)
validation.map.area.areaType=The areaType must be in (SingleAgvArea、ShowArea、ControlArea、ChannelArea、NoRotatingArea、NoParkingArea)
validation.map.area.polygon.require=The polygon cannot be empty
validation.map.area.operateType.require=The operateType cannot be empty, the value must be in（1: polygon、2:Rectangle Area）
validation.map.type.require=The map type cannot be empty
validation.map.code.require=The map code cannot be empty
validation.map.name.require=The map name cannot be empty
validation.map.originX.require=The map originX cannot be empty
validation.map.originY.require=The map originY cannot be empty
validation.map.resolution.require=The map resolution cannot be empty
validation.map.height.require=The map height cannot be empty
validation.map.width.require=The map width cannot be empty
validation.door.code.require=The door code cannot be empty
validation.door.ip.require=The door ip cannot be empty
validation.door.port.require=The door port cannot be empty
validation.door.openAddress.require=The door openAddress cannot be empty
validation.door.openStatusAddress.require=The door openStatusAddress cannot be empty
validation.door.closeAddress.require=The door closeAddress cannot be empty
validation.door.closeStatusAddress.require=The door closeStatusAddress cannot be empty
validation.door.pathCodes.require=The door pathCodes cannot be empty
validation.elevator.code.require=The elevator code cannot be empty
validation.elevator.ip.require=The elevator ip cannot be empty
validation.elevator.port.require=The elevator port cannot be empty
validation.elevator.controlAddress.require=The elevator controlAddress cannot be empty
validation.elevator.destAddress.require=The elevator destAddress cannot be empty
validation.elevator.openAddress.require=The elevator openAddress cannot be empty
validation.elevator.readFunctionCode.require=The elevator readFunctionCode cannot be empty
validation.elevator.writeFunctionCode.require=The elevator writeFunctionCode cannot be empty
validation.property.type.require=The property type cannot be empty
validation.property.category.require=The property category cannot be empty
validation.property.propertyKey.require=The property propertyKey cannot be empty
validation.property.valueType.require=The property valueType cannot be empty

#vehicleMap
vehicleMap.airShowerDoor.not.exist.error=Operation failed, air shower door [%s] does not exist
vehicleMap.airShowerDoor.add.error=Abnormal addition of air shower door, please check the operation log
vehicleMap.airShowerDoor.update.error=Abnormal modification of air shower door [%s], please check the operation log
vehicleMap.airShowerDoor.delete.error=Abnormal deletion of air shower door [%s], please check the operation log
vehicleMap.airShowerDoor.bind.path.error=Abnormal bind of air shower door [%s], please check the duplicate path code

vehicleMap.autoDoor.not.exist.error=Operation failed, automatic door [%s] does not exist
vehicleMap.autoDoor.already.bind.other.device.error=Operation failed, path [%s] is already bound to device
vehicleMap.autoDoor.add.error=New automatic door exception, please check the operation log
vehicleMap.autoDoor.update.error=Abnormal modification of automatic door [%s], please check the operation log
vehicleMap.autoDoor.delete.error=Abnormal deletion of automatic door [%s], please check the operation log

vehicleMap.elevator.add.error=Abnormal addition of elevator, please check the operation log
vehicleMap.elevator.update.error=Abnormal modification of elevator [%s], please check the operation log
vehicleMap.elevator.delete.error=Abnormal deletion of elevator [%s], please check the operation log
vehicleMap.elevator.not.exist.error=Operation failed, elevator [%s] does not exist
vehicleMap.elevator.file.format.error=Elevator file format error
vehicleMap.elevator.import.already.exist.error=Elevator import exception, elevator [%s] already exists
vehicleMap.elevator.import.error=Import elevator abnormal, reason:%s
vehicleMap.elevator.export.error=Export elevator abnormal, reason:%s
vehicleMap.elevator.publish.check.error=Map release check, the associated elevator is currently in use. Should it be forcibly released
vehicleMap.elevator.import.bind.map.error=Elevator [%s] import failed, please import all maps bound to the elevator first
vehicleMap.elevator.bind.multi.marker.error=Elevator binding exception, elevator cannot bind multiple points on the same map

vehicleMap.mapArea.not.exist.error=Operation failed, region [%s] does not exist
vehicleMap.mapArea.add.error=New region exception, please check the operation log
vehicleMap.mapArea.update.error=Exception in modifying area [%s], please check the operation log
vehicleMap.mapArea.update.occupied.error=Exception in modifying area [%s],The current resource is being used
vehicleMap.mapArea.delete.error=Abnormal deletion of area [%s], please check the operation log

vehicleMap.marker.add.error=New point exception, please check the operation log
vehicleMap.marker.update.error=Abnormal modification of point [%s], please check the operation log
vehicleMap.marker.delete.error=Abnormal deletion of point [%s], please check the operation log
vehicleMap.marker.not.exist.error=Operation failed, point [%s] does not exist
vehicleMap.marker.already.bind.other.device.error=Operation failed, device bound to point [%s]
vehicleMap.marker.spacing.error=The distance between points is less than the set value [%s]

vehicleMap.path.bind.marker.no.exist.error=Operation failed, point [%s] bound to path does not exist
vehicleMap.path.already.bind.device.error=Operation failed, path [%s] is already bound to device
vehicleMap.path.already.exist.error=Operation failed, path [%s] already exists
vehicleMap.path.not.exist.error=Operation failed, path [%s] does not exist
vehicleMap.path.add.error=New path exception, please check the operation log
vehicleMap.path.update.error=Exception in modifying path [%s], please check the operation log
vehicleMap.path.delete.error=Abnormal deletion path [%s], please check the operation log

vehicleMap.map.operating.duplicate.error=During execution, please do not repeat the operation
vehicleMap.map.not.exist.error=Map [%s] does not exist, please exit the map editing page
vehicleMap.map.file.format.error=The format of the imported file [%s] is incorrect
vehicleMap.map.add.error=Abnormal addition of map, please check the operation log
vehicleMap.map.update.error=Abnormal modification of map [%s], please check the operation log
vehicleMap.map.delete.error=Abnormal deletion of map [%s], please check the operation log
vehicleMap.map.roadnet.update.error=Modifying map [%s] road network elements is abnormal, please check the operation log
vehicleMap.map.roadnet.delete.error=Deleting map [%s] road network elements is abnormal, please check the operation log
vehicleMap.map.draft.delete.error=Deleting map [%s] road network draft is abnormal, please check the operation log
vehicleMap.map.is.not.publish.error=Map [%s] does not have an official version
vehicleMap.map.import.error=Map import exception, reason: %s
vehicleMap.map.import.structure.error=The imported map file format is incorrect
vehicleMap.map.import.in.edit.page.error=Please import the location map on the map editing page
vehicleMap.map.import.missing.info.error=The imported map is missing the info file
vehicleMap.map.import.missing.png.error=The imported map is missing the png file
vehicleMap.map.import.missing.locating.error=The imported map file format is incorrect: the road network file does not have location map information
vehicleMap.map.import.appoint.default.error=The imported map file format is incorrect: the road network file does not specify a default positioning map
vehicleMap.map.export.error=Map export exception, reason:%s
vehicleMap.map.copy.error=Map copying exception, reason:%s
vehicleMap.map.reset.error=There are no revocable operations on the current map [%s]
vehicleMap.map.recover.error=There are no recoverable operations on the current map [%s]
vehicleMap.map.global.recover.error=Robot recovery failed：%s
vehicleMap.map.publish.occupied.error=Failed to publish map, The current resource [%s] is being used

vehicleMap.map.locatingmap.is.empty.error=Map [%s] location map data is empty!
vehicleMap.map.locatingmap.code.is.empty.error=Map [%s] location map code is empty!
vehicleMap.map.locatingmap.not.exist.error=Location map [%s] does not exist, please check the operation log
vehicleMap.map.locatingmap.update.error=Abnormal modification of positioning map [%s], please check the operation log
vehicleMap.map.locatingmap.default.error=Cannot delete default location map [%s] for [%s]
vehicleMap.map.locatingmap.import.structure.error=The directory structure of the imported location map file is abnormal
vehicleMap.map.locatingmap.import.file.is.empty.error=The list of imported location map files is empty
vehicleMap.map.locatingmap.import.file.is.missing.error=The imported location map [%s] file is missing
vehicleMap.map.locatingmap.export.error=Export location map [%s] abnormal, reason:%s

#device
device.connect.error=Device operation failed, device [%s] is not connected to the network
device.open.error=Device [%s] activation exception, please check the operation log
device.close.error=Device shutdown [%s] exception, please check the operation log
device.is.in.use.error=Device operation failed, device [%s] is currently in use

#task
task.node.config.export.file.name=Node settings
task.node.config.export.error=Node settings export exception, reason:%s
task.node.config.import.error=Node settings import exception, reason:%s
task.node.is.not.exist.error=Task node [%s] does not exist
task.node.is.not.allow.retry.error=Task node [%s] does not allow retry
task.node.is.not.allow.skip.error=Task node [%s] is not allowed to be skipped
task.type.is.not.published.error=Failed to add task, using unpublished task process [%s]
task.type.is.not.exist.error=Task process [%s] does not exist
task.type.export.error=Task process export exception, reason:%s
task.type.import.error=Task process import exception, reason:%s
task.type.node.is.empty.error=Failed to enable task, task process [%s] is missing available nodes
task.type.enable.el.parse.error=Enabling failed due to:%s
task.type.enable.param.is.null.error=There are nodes with undefined parameters: [%s]
task.type.node.while.is.empty.error=Node with empty loop: [%s]
task.type.prefix.name=Task type
task.is.not.exist.error=Task [%s] does not exist
task.delete.running.error=Cannot delete ongoing tasks
task.cancel.running.error=Cancel failed, this task is prohibited from cancellation
task.export.file.name=Task
task.export.error=Task export exception, reason:%s
task.import.error=Task import exception, reason:%s
task.import.code.duplicate.error=Failed to upload task record, duplicate task code [%s]
task.cancel.timeout.error=Cancel robot command timeout, please restart the robot [%s] to clear the command
task.insert.vehicle.not.exist.error=Failed to add task, the input robot [%s] does not exist
task.insert.marker.not.exist.error=Failed to add task, the input point [%s] does not exist
task.insert.marker.lock.error=This point [%s] is already occupied by another task
task.insert.map.not.exist.error=Failed to add task, the input map [%s] does not exist
task.insert.dynamic.param.format.error=The format [%s] of the dynamic parameter [%s] passed in is incorrect
task.excel.head.taskNo=Task No
task.excel.head.externalTaskNo=External Task No
task.excel.head.name=Name
task.excel.head.status=Status
task.excel.head.priority=Priority
task.excel.head.vehicleCodes=VehicleCodes
task.excel.head.source=Source
task.excel.head.createDate=CreateDate
task.excel.head.startTime=StartTime
task.excel.head.endTime=EndTime
task.excel.head.remark=Remark
task.excel.head.callbackUrl=CallbackUrl

task.event.not.exist.error=Event[%s]is not exist
task.event.bound.taskType.is.null.error=Event [%s] is not associated with task flow
task.event.running.duplicate.error=The current event cannot be repeated and there is a running task
task.event.plc.condition.check.fail.error=Register trigger condition verification failed, please check
task.event.vehicle.condition.check.fail.error=Register of robot trigger condition verification failed, please check
task.event.fix.interval.time.error=The interval time setting is incorrect, please check
task.event.relate.task.contain.cancel.node.error=The task [%s] associated with the cancellation task node cannot contain a cancellation task node
task.event.relate.task.create.error=Failed to create task [%s] associated with task node cancellation, reason: [%s]

task.event.type.fixedTime=FixedTime event
task.event.type.button=Button event
task.event.type.plc=Plc event
task.event.type.vehiclePlc=VehiclePlc event
task.event.type.vehicleAbnormal=VehicleAbnormal event
task.event.type.taskCancel=TaskCancel event
task.event.type.taskFinished=TaskFinished event
task.event.status.enable=Enable
task.event.status.disable=Disable
task.event.repeat.allow=Allow
task.event.repeat.disallow=Disallow
task.event.export.file.name=Event

task.event.excel.head.code=Event code
task.event.excel.head.name=Even name
task.event.excel.head.type=Event type
task.event.excel.head.isAllowRepeat=Allow Repeat
task.event.excel.head.taskTypeId=Task type
task.event.excel.head.status=Status
task.event.excel.head.param=Event param
task.event.excel.head.taskParam=Task param