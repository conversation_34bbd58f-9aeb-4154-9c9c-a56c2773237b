2025-02-26T15:56:26,762 [Async-agv-manage-2] ERROR service.impl.LoginCommandCommandServiceImpl 468- agvCode:[M1740556469462],event:[登录流程], 登录失败，失败原因:java.lang.NullPointerException
2025-02-26T15:56:26,798 [Async-agv-manage-2] DEBUG service.impl.LoginCommandCommandServiceImpl 489- agvCode:[M1740556469462],event:[登录流程],发送mq消息成功：[{"code":10002,"message":"登录失败，null","uniqueFlag":"19d80ff8-7f31-4396-a9e5-20f0eaf54a3b"}]
2025-02-26T15:58:26,244 [Async-agv-manage-3] DEBUG service.impl.LoginCommandCommandServiceImpl 459- agvCode:[M1740556469462],event:[登录流程]，小车记录已存在，建立mq连接
2025-02-26T15:58:26,250 [Async-agv-manage-3] DEBUG service.impl.LoginCommandCommandServiceImpl 489- agvCode:[M1740556469462],event:[登录流程],发送mq消息成功：[{"code":10001,"currentStamp":1740556706228,"message":"登陆成功","uniqueFlag":"56e6dbe0-ed53-4517-9836-4db92815c391"}]
2025-02-26T15:58:26,262 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.OnlineMessageHandle 32- agvCode:[M1740556469462]，event:[上线通知],topic：[/online]
2025-02-26T15:58:26,263 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.LoginCommandCommandServiceImpl 656- agvCode:[M1740556469462],event:[上线通知],开始新增agvLog----------------
2025-02-26T15:58:26,268 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.LoginCommandCommandServiceImpl 658- agvCode:[M1740556469462],event:[上线通知],结束新增agvLog----------------
2025-02-26T15:58:26,272 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.LoginCommandCommandServiceImpl 647- agvCode:[M1740556469462],event:[上线通知],设置小车状态为在线
2025-02-26T16:00:07,048 [qtp1918672990-201] DEBUG controller.v3.AGVController 187- agvCode:[M1740556469462],event:[更新机器人信息],content:更新完成: Agv(id=2e26972b-f417-11ef-92f6-8c8caa44c2f8, ip=null, agvCode=M1740556469462, agvId=eeb118c6-f416-11ef-92f6-8c8caa44c2f8, agvName=l2, navigationType=LASER, agvType=, agvGroupId=a78da436-f417-11ef-92f6-8c8caa44c2f8, shapeInfo={"length":800,"width":600}, status=1, onlineStatus=1, controlMode=2, appointStatus=0, abnormalStatus=1, workStatus=1, mapStatus=0, agvColor=#8BA0D2, autoCharge=2, autoPark=2, autoAllocation=1, bindParkMarkers=null, bindChargeMarkers=null, bindParkConfig=false, bindChargeConfig=false, createTime=Wed Feb 26 15:56:27 GMT+08:00 2025, updateTime=Wed Feb 26 15:58:28 GMT+08:00 2025, vehicle=null, chargeMarkerCodes=null, linePatrolMode=true, linePatroWeight=0)
2025-02-26T16:00:21,483 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","appointStatus":0,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":40.0,"battery_voltage":52.308},"controlMode":2,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":0,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.0,"speed_vy":0.0,"speed_w":0.0},"time":1740556821479,"useElevator":false,"workStatus":1}]
2025-02-26T16:02:21,564 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","appointStatus":0,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":39.0,"battery_voltage":52.308},"controlMode":2,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":0,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.0,"speed_vy":0.0,"speed_w":0.0},"time":1740556941560,"useElevator":false,"workStatus":1}]
2025-02-26T16:03:50,301 [qtp1918672990-35] DEBUG controller.v3.VehicleControlController 79- agvCode:[M1740556469462],event:[切换为自动控制模式],content:[指令下发完成]
2025-02-26T16:03:50,407 [pool-4-thread-1] DEBUG service.impl.VehicleCommandServiceImpl 227- agvCode:[M1740556469462],event[发送充电指令]，content:[发送下发充电mq消息完成，message:{"chargeMode":0,"retryCount":5,"uniqueFlag":"42b51faa-ea88-4993-a13a-f6ed8b6a2d53","retryInterval":10,"id":16,"command":"startCharge","chargeMarkerId":"823284f2-b766-4480-a8f3-89cd678e4531"}]
2025-02-26T16:03:51,459 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 160- start process agv request path plan agvCode:[M1740556469462],aimMarkerCodes:[160]
2025-02-26T16:03:51,464 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 171- agvCode:[M1740556469462], side path长度:[18].
2025-02-26T16:03:51,464 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 172- sidePathPlanResult---------------------------start------------------------
2025-02-26T16:03:51,465 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[46]->[48],T0:[0.0]
2025-02-26T16:03:51,465 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[48]->[228],T0:[0.0]
2025-02-26T16:03:51,465 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[228]->[50],T0:[0.0]
2025-02-26T16:03:51,465 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[50]->[52],T0:[0.0]
2025-02-26T16:03:51,465 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[52]->[54],T0:[0.0]
2025-02-26T16:03:51,465 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[54]->[56],T0:[0.0]
2025-02-26T16:03:51,466 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[56]->[58],T0:[0.0]
2025-02-26T16:03:51,466 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[58]->[138],T0:[0.0]
2025-02-26T16:03:51,466 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[138]->[162],T0:[0.0]
2025-02-26T16:03:51,466 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[162]->[139],T0:[0.0]
2025-02-26T16:03:51,466 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[139]->[140],T0:[0.0]
2025-02-26T16:03:51,466 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[140]->[141],T0:[0.0]
2025-02-26T16:03:51,466 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[141]->[142],T0:[0.0]
2025-02-26T16:03:51,466 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[142]->[143],T0:[0.0]
2025-02-26T16:03:51,466 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[143]->[144],T0:[0.0]
2025-02-26T16:03:51,467 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[144]->[145],T0:[0.0]
2025-02-26T16:03:51,467 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[145]->[146],T0:[0.0]
2025-02-26T16:03:51,467 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[146]->[160],T0:[0.0]
2025-02-26T16:03:51,467 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 198- agvCode:[M1740556469462],event:[路径规划],content:[路径规划完成，耗时8ms]
2025-02-26T16:03:51,468 [SimpleAsyncTaskExecutor-2] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:03:51,468 [SimpleAsyncTaskExecutor-2] DEBUG service.impl.CheckAndSendPathServiceImpl 543- agvCode:[M1740556469462],event:[clear],当前点位:[46]
2025-02-26T16:03:51,473 [SimpleAsyncTaskExecutor-2] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":173,"methodName":"addSidePathPlanResult","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger","fileName":"PathPlanManger.java","lineNumber":247,"methodName":"start","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger$$FastClassBySpringCGLIB$$5fc88548","fileName":"<generated>","lineNumber":-1,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.cglib.proxy.MethodProxy","fileName":"MethodProxy.java","lineNumber":218,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation","fileName":"CglibAopProxy.java","lineNumber":749,"methodName":"invokeJoinpoint","nativeMethod":false},{"className":"org.springframework.aop.framework.ReflectiveMethodInvocation","fileName":"ReflectiveMethodInvocation.java","lineNumber":163,"methodName":"proceed","nativeMethod":false},{"className":"org.springframework.aop.interceptor.AsyncExecutionInterceptor","fileName":"AsyncExecutionInterceptor.java","lineNumber":115,"methodName":"lambda$invoke$0","nativeMethod":false},{"className":"org.springframework.aop.interceptor.AsyncExecutionInterceptor$$Lambda$503/967613282","lineNumber":-1,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:03:51,473 [SimpleAsyncTaskExecutor-2] DEBUG mqtt.util.MqttUtils 204- event:[mq推送消息], topic:[/fleet/M1740556469462/pathPlan], message:[{"aimMarkerId":"823284f2-b766-4480-a8f3-89cd678e4531","missionWorkActionId":"SMART_CHARGE_MOVE_TO_MARKER","missionWorkId":"SMART_CHARGE","status":1,"uniqueFlag":"18815bdf-3e21-4bc9-9752-5b9027870699"}]
2025-02-26T16:04:14,505 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[48], occupyAgvCode:[M1740556469462]]
2025-02-26T16:04:14,506 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [46]->[48], t0:[0], t1:[1], id:[c457ca40-ceab-4b47-ad25-34048de0a43f]
]
2025-02-26T16:04:14,509 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [46]->[48], t0:[0], t1:[1], id:[c457ca40-ceab-4b47-ad25-34048de0a43f]
]]
2025-02-26T16:04:20,581 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[228], occupyAgvCode:[M1740556469462]]
2025-02-26T16:04:20,582 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [48]->[228], t0:[0], t1:[1], id:[3634e6e1-412a-45a9-a4b7-db18d331ed2a]
]
2025-02-26T16:04:20,585 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [48]->[228], t0:[0], t1:[1], id:[3634e6e1-412a-45a9-a4b7-db18d331ed2a]
]]
2025-02-26T16:04:21,614 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":39.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":1,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.4419814289889512,"speed_vy":0.0,"speed_w":-0.004988813877693232},"time":1740557061611,"useElevator":false,"workStatus":3}]
2025-02-26T16:04:31,679 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[50], occupyAgvCode:[M1740556469462]]
2025-02-26T16:04:31,680 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [228]->[50], t0:[0], t1:[1], id:[7d4997dd-c2f8-45f8-9b60-8323ba3044be]
]
2025-02-26T16:04:31,684 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [228]->[50], t0:[0], t1:[1], id:[7d4997dd-c2f8-45f8-9b60-8323ba3044be]
]]
2025-02-26T16:04:33,730 [qtp1918672990-33] DEBUG service.impl.VehicleCommandServiceImpl 241- agvCode:[M1740556469462],event[充电取消]，content:[发送取消充电mq消息完成，message:{"uniqueFlag":"07957d2e-e1e7-4e3d-8f62-e17176504d8d","id":16,"command":"cancelCharge"}]
2025-02-26T16:04:33,791 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.ChargeSchedulerServiceImpl 77- agvCode:[M1740556469462],event:[取消充电调度],取消充电调度完成：{"chargeId":"823284f2-b766-4480-a8f3-89cd678e4531","chargePointCode":"160","chargePointMapName":"FAB_10","chargeType":0,"createTime":1740557030000,"finishTime":1740557073787,"id":16,"startTime":1740557030000,"status":"CANCEL","updateTime":1740557073787,"vehicleId":"M1740556469462"}
2025-02-26T16:04:33,791 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:04:33,796 [MQTT Call: fleet_M1740556469462] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.messageHandle.ChargeMessageHandle","fileName":"ChargeMessageHandle.java","lineNumber":74,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.DefaultSubscribe","fileName":"DefaultSubscribe.java","lineNumber":64,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.VehicleSubscribe","fileName":"VehicleSubscribe.java","lineNumber":100,"methodName":"messageArrived","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":499,"methodName":"deliverMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":402,"methodName":"handleMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":206,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.Executors$RunnableAdapter","fileName":"Executors.java","lineNumber":511,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":180,"methodName":"access$201","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":293,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor","fileName":"ThreadPoolExecutor.java","lineNumber":1149,"methodName":"runWorker","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor$Worker","fileName":"ThreadPoolExecutor.java","lineNumber":624,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:04:33,796 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 611- agvCode:[M1740556469462], event:[clear],清理小车的路径资源-开始
2025-02-26T16:04:33,797 [MQTT Call: fleet_M1740556469462] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[30.release success, marker:[228], occupyAgvCode:[null]]
2025-02-26T16:04:33,798 [MQTT Call: fleet_M1740556469462] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[30.release success, marker:[50], occupyAgvCode:[null]]
2025-02-26T16:04:33,798 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 651- agvCode:[M1740556469462], event:[clear],清理小车的路径资源-结束
2025-02-26T16:04:34,346 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.ChargeSchedulerServiceImpl 77- agvCode:[M1740556469462],event:[取消充电调度],取消充电调度完成：{"chargeId":"823284f2-b766-4480-a8f3-89cd678e4531","chargePointCode":"160","chargePointMapName":"FAB_10","chargeType":0,"createTime":1740557030000,"finishTime":1740557074345,"id":16,"startTime":1740557030000,"status":"CANCEL","updateTime":1740557074345,"vehicleId":"M1740556469462"}
2025-02-26T16:04:34,347 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:04:34,352 [MQTT Call: fleet_M1740556469462] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.messageHandle.ChargeMessageHandle","fileName":"ChargeMessageHandle.java","lineNumber":74,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.DefaultSubscribe","fileName":"DefaultSubscribe.java","lineNumber":64,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.VehicleSubscribe","fileName":"VehicleSubscribe.java","lineNumber":100,"methodName":"messageArrived","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":499,"methodName":"deliverMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":402,"methodName":"handleMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":206,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.Executors$RunnableAdapter","fileName":"Executors.java","lineNumber":511,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":180,"methodName":"access$201","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":293,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor","fileName":"ThreadPoolExecutor.java","lineNumber":1149,"methodName":"runWorker","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor$Worker","fileName":"ThreadPoolExecutor.java","lineNumber":624,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:04:34,752 [pool-4-thread-4] DEBUG service.impl.VehicleCommandServiceImpl 227- agvCode:[M1740556469462],event[发送充电指令]，content:[发送下发充电mq消息完成，message:{"chargeMode":0,"retryCount":5,"uniqueFlag":"6f560681-b2ed-4d08-950c-43597bce4a3a","retryInterval":10,"id":19,"command":"startCharge","chargeMarkerId":"823284f2-b766-4480-a8f3-89cd678e4531"}]
2025-02-26T16:04:35,886 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 160- start process agv request path plan agvCode:[M1740556469462],aimMarkerCodes:[160]
2025-02-26T16:04:35,890 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 171- agvCode:[M1740556469462], side path长度:[18].
2025-02-26T16:04:35,890 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 172- sidePathPlanResult---------------------------start------------------------
2025-02-26T16:04:35,890 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[46]->[48],T0:[0.9017031250000007]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[48]->[228],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[228]->[50],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[50]->[52],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[52]->[54],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[54]->[56],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[56]->[58],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[58]->[138],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[138]->[162],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[162]->[139],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[139]->[140],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[140]->[141],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[141]->[142],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[142]->[143],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[143]->[144],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[144]->[145],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[145]->[146],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[146]->[160],T0:[0.0]
2025-02-26T16:04:35,892 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 198- agvCode:[M1740556469462],event:[路径规划],content:[路径规划完成，耗时6ms]
2025-02-26T16:04:35,893 [SimpleAsyncTaskExecutor-2] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:04:35,896 [SimpleAsyncTaskExecutor-2] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":173,"methodName":"addSidePathPlanResult","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger","fileName":"PathPlanManger.java","lineNumber":247,"methodName":"start","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger$$FastClassBySpringCGLIB$$5fc88548","fileName":"<generated>","lineNumber":-1,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.cglib.proxy.MethodProxy","fileName":"MethodProxy.java","lineNumber":218,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation","fileName":"CglibAopProxy.java","lineNumber":749,"methodName":"invokeJoinpoint","nativeMethod":false},{"className":"org.springframework.aop.framework.ReflectiveMethodInvocation","fileName":"ReflectiveMethodInvocation.java","lineNumber":163,"methodName":"proceed","nativeMethod":false},{"className":"org.springframework.aop.interceptor.AsyncExecutionInterceptor","fileName":"AsyncExecutionInterceptor.java","lineNumber":115,"methodName":"lambda$invoke$0","nativeMethod":false},{"className":"org.springframework.aop.interceptor.AsyncExecutionInterceptor$$Lambda$503/967613282","lineNumber":-1,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:04:35,896 [SimpleAsyncTaskExecutor-2] DEBUG mqtt.util.MqttUtils 204- event:[mq推送消息], topic:[/fleet/M1740556469462/pathPlan], message:[{"aimMarkerId":"823284f2-b766-4480-a8f3-89cd678e4531","missionWorkActionId":"SMART_CHARGE_MOVE_TO_MARKER","missionWorkId":"SMART_CHARGE","status":1,"uniqueFlag":"74a8293b-d8b8-4bb9-8a3e-d3dcb22c537c"}]
2025-02-26T16:04:35,957 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[48], occupyAgvCode:[M1740556469462]]
2025-02-26T16:04:35,957 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[228], occupyAgvCode:[M1740556469462]]
2025-02-26T16:04:35,957 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[50], occupyAgvCode:[M1740556469462]]
2025-02-26T16:04:35,958 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [46]->[48], t0:[0.902], t1:[1], id:[c457ca40-ceab-4b47-ad25-34048de0a43f]
side path: [48]->[228], t0:[0], t1:[1], id:[3634e6e1-412a-45a9-a4b7-db18d331ed2a]
side path: [228]->[50], t0:[0], t1:[1], id:[7d4997dd-c2f8-45f8-9b60-8323ba3044be]
]
2025-02-26T16:04:35,961 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [46]->[48], t0:[0.902], t1:[1], id:[c457ca40-ceab-4b47-ad25-34048de0a43f]
side path: [48]->[228], t0:[0], t1:[1], id:[3634e6e1-412a-45a9-a4b7-db18d331ed2a]
side path: [228]->[50], t0:[0], t1:[1], id:[7d4997dd-c2f8-45f8-9b60-8323ba3044be]
]]
2025-02-26T16:04:47,464 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[48], occupyAgvCode:[null]]
2025-02-26T16:04:47,697 [qtp1918672990-30] DEBUG service.impl.VehicleCommandServiceImpl 241- agvCode:[M1740556469462],event[充电取消]，content:[发送取消充电mq消息完成，message:{"uniqueFlag":"b3ecdfde-0652-4669-ac64-5eb950a19193","id":19,"command":"cancelCharge"}]
2025-02-26T16:04:47,714 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.ChargeSchedulerServiceImpl 77- agvCode:[M1740556469462],event:[取消充电调度],取消充电调度完成：{"chargeId":"823284f2-b766-4480-a8f3-89cd678e4531","chargePointCode":"160","chargePointMapName":"FAB_10","chargeType":0,"createTime":1740557075000,"finishTime":1740557087711,"id":19,"startTime":1740557075000,"status":"CANCEL","updateTime":1740557087711,"vehicleId":"M1740556469462"}
2025-02-26T16:04:47,714 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:04:47,714 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 543- agvCode:[M1740556469462],event:[clear],当前点位:[228]
2025-02-26T16:04:47,720 [MQTT Call: fleet_M1740556469462] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.messageHandle.ChargeMessageHandle","fileName":"ChargeMessageHandle.java","lineNumber":74,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.DefaultSubscribe","fileName":"DefaultSubscribe.java","lineNumber":64,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.VehicleSubscribe","fileName":"VehicleSubscribe.java","lineNumber":100,"methodName":"messageArrived","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":499,"methodName":"deliverMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":402,"methodName":"handleMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":206,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.Executors$RunnableAdapter","fileName":"Executors.java","lineNumber":511,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":180,"methodName":"access$201","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":293,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor","fileName":"ThreadPoolExecutor.java","lineNumber":1149,"methodName":"runWorker","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor$Worker","fileName":"ThreadPoolExecutor.java","lineNumber":624,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:04:47,720 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 611- agvCode:[M1740556469462], event:[clear],清理小车的路径资源-开始
2025-02-26T16:04:47,721 [MQTT Call: fleet_M1740556469462] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[20.release success, marker:[50], occupyAgvCode:[null]]
2025-02-26T16:04:47,722 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 651- agvCode:[M1740556469462], event:[clear],清理小车的路径资源-结束
2025-02-26T16:04:48,310 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.ChargeSchedulerServiceImpl 77- agvCode:[M1740556469462],event:[取消充电调度],取消充电调度完成：{"chargeId":"823284f2-b766-4480-a8f3-89cd678e4531","chargePointCode":"160","chargePointMapName":"FAB_10","chargeType":0,"createTime":1740557075000,"finishTime":1740557088309,"id":19,"startTime":1740557075000,"status":"CANCEL","updateTime":1740557088309,"vehicleId":"M1740556469462"}
2025-02-26T16:04:48,310 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:04:48,310 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 543- agvCode:[M1740556469462],event:[clear],当前点位:[228]
2025-02-26T16:04:48,314 [MQTT Call: fleet_M1740556469462] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.messageHandle.ChargeMessageHandle","fileName":"ChargeMessageHandle.java","lineNumber":74,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.DefaultSubscribe","fileName":"DefaultSubscribe.java","lineNumber":64,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.VehicleSubscribe","fileName":"VehicleSubscribe.java","lineNumber":100,"methodName":"messageArrived","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":499,"methodName":"deliverMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":402,"methodName":"handleMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":206,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.Executors$RunnableAdapter","fileName":"Executors.java","lineNumber":511,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":180,"methodName":"access$201","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":293,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor","fileName":"ThreadPoolExecutor.java","lineNumber":1149,"methodName":"runWorker","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor$Worker","fileName":"ThreadPoolExecutor.java","lineNumber":624,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:04:48,852 [pool-4-thread-5] DEBUG service.impl.VehicleCommandServiceImpl 227- agvCode:[M1740556469462],event[发送充电指令]，content:[发送下发充电mq消息完成，message:{"chargeMode":0,"retryCount":5,"uniqueFlag":"de2cba6c-0ab6-49d2-a1cf-60ffcd00cb92","retryInterval":10,"id":20,"command":"startCharge","chargeMarkerId":"823284f2-b766-4480-a8f3-89cd678e4531"}]
2025-02-26T16:04:49,734 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 160- start process agv request path plan agvCode:[M1740556469462],aimMarkerCodes:[160]
2025-02-26T16:04:49,738 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 171- agvCode:[M1740556469462], side path长度:[16].
2025-02-26T16:04:49,738 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 172- sidePathPlanResult---------------------------start------------------------
2025-02-26T16:04:49,738 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[228]->[50],T0:[0.0]
2025-02-26T16:04:49,738 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[50]->[52],T0:[0.0]
2025-02-26T16:04:49,738 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[52]->[54],T0:[0.0]
2025-02-26T16:04:49,738 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[54]->[56],T0:[0.0]
2025-02-26T16:04:49,738 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[56]->[58],T0:[0.0]
2025-02-26T16:04:49,738 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[58]->[138],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[138]->[162],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[162]->[139],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[139]->[140],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[140]->[141],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[141]->[142],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[142]->[143],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[143]->[144],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[144]->[145],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[145]->[146],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[146]->[160],T0:[0.0]
2025-02-26T16:04:49,739 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 198- agvCode:[M1740556469462],event:[路径规划],content:[路径规划完成，耗时5ms]
2025-02-26T16:04:49,740 [SimpleAsyncTaskExecutor-2] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:04:49,740 [SimpleAsyncTaskExecutor-2] DEBUG service.impl.CheckAndSendPathServiceImpl 543- agvCode:[M1740556469462],event:[clear],当前点位:[228]
2025-02-26T16:04:49,744 [SimpleAsyncTaskExecutor-2] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":173,"methodName":"addSidePathPlanResult","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger","fileName":"PathPlanManger.java","lineNumber":247,"methodName":"start","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger$$FastClassBySpringCGLIB$$5fc88548","fileName":"<generated>","lineNumber":-1,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.cglib.proxy.MethodProxy","fileName":"MethodProxy.java","lineNumber":218,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation","fileName":"CglibAopProxy.java","lineNumber":749,"methodName":"invokeJoinpoint","nativeMethod":false},{"className":"org.springframework.aop.framework.ReflectiveMethodInvocation","fileName":"ReflectiveMethodInvocation.java","lineNumber":163,"methodName":"proceed","nativeMethod":false},{"className":"org.springframework.aop.interceptor.AsyncExecutionInterceptor","fileName":"AsyncExecutionInterceptor.java","lineNumber":115,"methodName":"lambda$invoke$0","nativeMethod":false},{"className":"org.springframework.aop.interceptor.AsyncExecutionInterceptor$$Lambda$503/967613282","lineNumber":-1,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:04:49,744 [SimpleAsyncTaskExecutor-2] DEBUG mqtt.util.MqttUtils 204- event:[mq推送消息], topic:[/fleet/M1740556469462/pathPlan], message:[{"aimMarkerId":"823284f2-b766-4480-a8f3-89cd678e4531","missionWorkActionId":"SMART_CHARGE_MOVE_TO_MARKER","missionWorkId":"SMART_CHARGE","status":1,"uniqueFlag":"ec6d61e7-8789-40e1-843e-4d5149a75779"}]
2025-02-26T16:04:49,820 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[50], occupyAgvCode:[M1740556469462]]
2025-02-26T16:04:49,821 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [228]->[50], t0:[0], t1:[1], id:[7d4997dd-c2f8-45f8-9b60-8323ba3044be]
]
2025-02-26T16:04:49,824 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [228]->[50], t0:[0], t1:[1], id:[7d4997dd-c2f8-45f8-9b60-8323ba3044be]
]]
2025-02-26T16:04:56,879 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.WillMessageHandle 25- agvCode:[M1740556469462],event:[遗言],topic：[/will]
2025-02-26T16:04:56,888 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.LoginCommandCommandServiceImpl 611- agvCode:[M1740556469462],event:[遗言], 设置小车状态为离线
2025-02-26T16:07:19,299 [Async-agv-manage-3] DEBUG service.impl.LoginCommandCommandServiceImpl 459- agvCode:[M1740556469462],event:[登录流程]，小车记录已存在，建立mq连接
2025-02-26T16:07:19,314 [Async-agv-manage-3] DEBUG service.impl.LoginCommandCommandServiceImpl 489- agvCode:[M1740556469462],event:[登录流程],发送mq消息成功：[{"code":10001,"currentStamp":1740557239273,"message":"登陆成功","uniqueFlag":"97b91fda-53f0-444f-8922-eea21555e709"}]
2025-02-26T16:07:19,349 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.OnlineMessageHandle 32- agvCode:[M1740556469462]，event:[上线通知],topic：[/online]
2025-02-26T16:07:19,351 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.LoginCommandCommandServiceImpl 656- agvCode:[M1740556469462],event:[上线通知],开始新增agvLog----------------
2025-02-26T16:07:19,359 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.LoginCommandCommandServiceImpl 658- agvCode:[M1740556469462],event:[上线通知],结束新增agvLog----------------
2025-02-26T16:07:19,362 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.LoginCommandCommandServiceImpl 647- agvCode:[M1740556469462],event:[上线通知],设置小车状态为在线
2025-02-26T16:07:20,070 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","appointStatus":0,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":25.0,"battery_voltage":52.308},"controlMode":2,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":0,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.0,"speed_vy":0.0,"speed_w":0.0},"time":1740557240032,"useElevator":false,"workStatus":1}]
2025-02-26T16:07:44,337 [qtp1660228600-186] DEBUG controller.v3.VehicleControlController 79- agvCode:[M1740556469462],event:[切换为自动控制模式],content:[指令下发完成]
2025-02-26T16:07:46,723 [ForkJoinPool.commonPool-worker-2] DEBUG service.impl.VehicleCommandServiceImpl 241- agvCode:[M1740556469462],event[充电取消]，content:[发送取消充电mq消息完成，message:{"uniqueFlag":"e3f19b69-7f0a-4a5d-993c-c3fe68bd7a70","id":20,"command":"cancelCharge"}]
2025-02-26T16:07:46,739 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.ChargeSchedulerServiceImpl 77- agvCode:[M1740556469462],event:[取消充电调度],取消充电调度完成：{"chargeId":"823284f2-b766-4480-a8f3-89cd678e4531","chargePointCode":"160","chargePointMapName":"FAB_10","chargeType":0,"createTime":1740557089000,"finishTime":1740557266734,"id":20,"startTime":1740557089000,"status":"CANCEL","updateTime":1740557266735,"vehicleId":"M1740556469462"}
2025-02-26T16:07:46,739 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:07:46,739 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 543- agvCode:[M1740556469462],event:[clear],当前点位:[99]
2025-02-26T16:07:46,750 [MQTT Call: fleet_M1740556469462] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.messageHandle.ChargeMessageHandle","fileName":"ChargeMessageHandle.java","lineNumber":74,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.DefaultSubscribe","fileName":"DefaultSubscribe.java","lineNumber":64,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.VehicleSubscribe","fileName":"VehicleSubscribe.java","lineNumber":100,"methodName":"messageArrived","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":499,"methodName":"deliverMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":402,"methodName":"handleMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":206,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.Executors$RunnableAdapter","fileName":"Executors.java","lineNumber":511,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":180,"methodName":"access$201","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":293,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor","fileName":"ThreadPoolExecutor.java","lineNumber":1149,"methodName":"runWorker","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor$Worker","fileName":"ThreadPoolExecutor.java","lineNumber":624,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:07:49,899 [ForkJoinPool.commonPool-worker-13] INFO service.impl.VehicleCommandServiceImpl 166- agvCode:[M1740556469462],event[发送作业指令]，content:[发送下发任务mq消息成功,missionWork:{"agvCode":"M1740556469462","agvGroupId":"","agvName":"l2","agvType":"","createTime":1740557270000,"id":"db79c574-88f7-424b-80ee-b7b1510b10e1","missionGroupId":"","missionId":"107b4794-d177-11ef-9bf4-8c8caa44c2f8","name":"m26","runtimeParam":"{\"markerCode\":\"44\",\"STATION\":\"1\",\"linePatrol\":true,\"UNLOADONLY\":false,\"on_key_u_turn\":false,\"agvMapId\":\"FAB_10\",\"releaseMode\":4,\"seq\":1.0}","sequence":2,"status":"RUNNING","updateTime":1740557269872}]
2025-02-26T16:07:50,040 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:07:50,040 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 543- agvCode:[M1740556469462],event:[clear],当前点位:[99]
2025-02-26T16:07:50,044 [MQTT Call: fleet_M1740556469462] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.service.impl.MissionWorkCommandServiceImpl","fileName":"MissionWorkCommandServiceImpl.java","lineNumber":116,"methodName":"updateMissionWork","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.messageHandle.MissionWorkMessageHandle","fileName":"MissionWorkMessageHandle.java","lineNumber":58,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.DefaultSubscribe","fileName":"DefaultSubscribe.java","lineNumber":64,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.VehicleSubscribe","fileName":"VehicleSubscribe.java","lineNumber":100,"methodName":"messageArrived","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":499,"methodName":"deliverMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":402,"methodName":"handleMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":206,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.Executors$RunnableAdapter","fileName":"Executors.java","lineNumber":511,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":180,"methodName":"access$201","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":293,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor","fileName":"ThreadPoolExecutor.java","lineNumber":1149,"methodName":"runWorker","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor$Worker","fileName":"ThreadPoolExecutor.java","lineNumber":624,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:07:50,056 [MQTT Call: fleet_M1740556469462] DEBUG scheduler.listener.WorkStateEventListenerCallBackUrl 58- agvCode:[M1740556469462],event:[作业完成接口回调],回调相关数据：[{"param":{"STATION":"1","agvCode":"M1740556469462","errorMessage":"Error selecting key or setting result to parameter object. Cause: java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist","on_key_u_turn":false,"missionWorkId":"db79c574-88f7-424b-80ee-b7b1510b10e1","markerCode":"44","linePatrol":true,"UNLOADONLY":false,"agvMapId":"FAB_10","releaseMode":4,"seq":1.0,"status":"FAULT"},"missionWork":{"agvCode":"M1740556469462","agvName":"l2","createTime":1740557270000,"endTime":1740557270046,"id":"db79c574-88f7-424b-80ee-b7b1510b10e1","message":"Error selecting key or setting result to parameter object. Cause: java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist","missionId":"107b4794-d177-11ef-9bf4-8c8caa44c2f8","name":"m26","runtimeParam":"{\"markerCode\":\"44\",\"STATION\":\"1\",\"linePatrol\":true,\"UNLOADONLY\":false,\"on_key_u_turn\":false,\"agvMapId\":\"FAB_10\",\"releaseMode\":4,\"seq\":1.0}","sequence":2,"status":"FAULT","updateTime":1740557270048}}]
2025-02-26T16:09:20,229 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":24.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":1,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.0,"speed_vy":0.0,"speed_w":0.0},"time":1740557360227,"useElevator":false,"workStatus":1}]
2025-02-26T16:11:20,292 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":24.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":1,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.0,"speed_vy":0.0,"speed_w":0.0},"time":1740557480289,"useElevator":false,"workStatus":1}]
2025-02-26T16:11:41,429 [qtp1660228600-32] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:11:41,429 [qtp1660228600-32] DEBUG service.impl.CheckAndSendPathServiceImpl 543- agvCode:[M1740556469462],event:[clear],当前点位:[99]
2025-02-26T16:11:41,433 [qtp1660228600-32] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.service.impl.MissionWorkCommandServiceImpl","fileName":"MissionWorkCommandServiceImpl.java","lineNumber":106,"methodName":"updateMissionWork","nativeMethod":false},{"className":"com.youibot.agv.scheduler.controller.v3.MissionWorkController","fileName":"MissionWorkController.java","lineNumber":291,"methodName":"stop","nativeMethod":false},{"className":"com.youibot.agv.scheduler.controller.v3.MissionWorkController$$FastClassBySpringCGLIB$$765656eb","fileName":"<generated>","lineNumber":-1,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.cglib.proxy.MethodProxy","fileName":"MethodProxy.java","lineNumber":218,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation","fileName":"CglibAopProxy.java","lineNumber":749,"methodName":"invokeJoinpoint","nativeMethod":false},{"className":"org.springframework.aop.framework.ReflectiveMethodInvocation","fileName":"ReflectiveMethodInvocation.java","lineNumber":163,"methodName":"proceed","nativeMethod":false},{"className":"org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint","fileName":"MethodInvocationProceedingJoinPoint.java","lineNumber":88,"methodName":"proceed","nativeMethod":false},{"className":"com.youibot.agv.scheduler.config.LogOperationAspect","fileName":"LogOperationAspect.java","lineNumber":52,"methodName":"around","nativeMethod":false},{"className":"sun.reflect.NativeMethodAccessorImpl","fileName":"NativeMethodAccessorImpl.java","lineNumber":-2,"methodName":"invoke0","nativeMethod":true},{"className":"sun.reflect.NativeMethodAccessorImpl","fileName":"NativeMethodAccessorImpl.java","lineNumber":62,"methodName":"invoke","nativeMethod":false},{"className":"sun.reflect.DelegatingMethodAccessorImpl","fileName":"DelegatingMethodAccessorImpl.java","lineNumber":43,"methodName":"invoke","nativeMethod":false},{"className":"java.lang.reflect.Method","fileName":"Method.java","lineNumber":498,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.aop.aspectj.AbstractAspectJAdvice","fileName":"AbstractAspectJAdvice.java","lineNumber":644,"methodName":"invokeAdviceMethodWithGivenArgs","nativeMethod":false},{"className":"org.springframework.aop.aspectj.AbstractAspectJAdvice","fileName":"AbstractAspectJAdvice.java","lineNumber":633,"methodName":"invokeAdviceMethod","nativeMethod":false},{"className":"org.springframework.aop.aspectj.AspectJAroundAdvice","fileName":"AspectJAroundAdvice.java","lineNumber":70,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.aop.framework.ReflectiveMethodInvocation","fileName":"ReflectiveMethodInvocation.java","lineNumber":175,"methodName":"proceed","nativeMethod":false},{"className":"org.springframework.aop.interceptor.ExposeInvocationInterceptor","fileName":"ExposeInvocationInterceptor.java","lineNumber":93,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.aop.framework.ReflectiveMethodInvocation","fileName":"ReflectiveMethodInvocation.java","lineNumber":186,"methodName":"proceed","nativeMethod":false},{"className":"org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor","fileName":"CglibAopProxy.java","lineNumber":688,"methodName":"intercept","nativeMethod":false},{"className":"com.youibot.agv.scheduler.controller.v3.MissionWorkController$$EnhancerBySpringCGLIB$$55325d35","fileName":"<generated>","lineNumber":-1,"methodName":"stop","nativeMethod":false},{"className":"sun.reflect.NativeMethodAccessorImpl","fileName":"NativeMethodAccessorImpl.java","lineNumber":-2,"methodName":"invoke0","nativeMethod":true},{"className":"sun.reflect.NativeMethodAccessorImpl","fileName":"NativeMethodAccessorImpl.java","lineNumber":62,"methodName":"invoke","nativeMethod":false},{"className":"sun.reflect.DelegatingMethodAccessorImpl","fileName":"DelegatingMethodAccessorImpl.java","lineNumber":43,"methodName":"invoke","nativeMethod":false},{"className":"java.lang.reflect.Method","fileName":"Method.java","lineNumber":498,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.web.method.support.InvocableHandlerMethod","fileName":"InvocableHandlerMethod.java","lineNumber":189,"methodName":"doInvoke","nativeMethod":false},{"className":"org.springframework.web.method.support.InvocableHandlerMethod","fileName":"InvocableHandlerMethod.java","lineNumber":138,"methodName":"invokeForRequest","nativeMethod":false},{"className":"org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod","fileName":"ServletInvocableHandlerMethod.java","lineNumber":102,"methodName":"invokeAndHandle","nativeMethod":false},{"className":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","fileName":"RequestMappingHandlerAdapter.java","lineNumber":895,"methodName":"invokeHandlerMethod","nativeMethod":false},{"className":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","fileName":"RequestMappingHandlerAdapter.java","lineNumber":800,"methodName":"handleInternal","nativeMethod":false},{"className":"org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter","fileName":"AbstractHandlerMethodAdapter.java","lineNumber":87,"methodName":"handle","nativeMethod":false},{"className":"org.springframework.web.servlet.DispatcherServlet","fileName":"DispatcherServlet.java","lineNumber":1038,"methodName":"doDispatch","nativeMethod":false},{"className":"org.springframework.web.servlet.DispatcherServlet","fileName":"DispatcherServlet.java","lineNumber":942,"methodName":"doService","nativeMethod":false},{"className":"org.springframework.web.servlet.FrameworkServlet","fileName":"FrameworkServlet.java","lineNumber":1005,"methodName":"processRequest","nativeMethod":false},{"className":"org.springframework.web.servlet.FrameworkServlet","fileName":"FrameworkServlet.java","lineNumber":908,"methodName":"doPost","nativeMethod":false},{"className":"javax.servlet.http.HttpServlet","fileName":"HttpServlet.java","lineNumber":665,"methodName":"service","nativeMethod":false},{"className":"org.springframework.web.servlet.FrameworkServlet","fileName":"FrameworkServlet.java","lineNumber":882,"methodName":"service","nativeMethod":false},{"className":"javax.servlet.http.HttpServlet","fileName":"HttpServlet.java","lineNumber":750,"methodName":"service","nativeMethod":false},{"className":"org.eclipse.jetty.servlet.ServletHolder","fileName":"ServletHolder.java","lineNumber":867,"methodName":"handle","nativeMethod":false},{"className":"org.eclipse.jetty.servlet.ServletHandler$CachedChain","fileName":"ServletHandler.java","lineNumber":1623,"methodName":"doFilter","nativeMethod":false},{"className":"org.eclipse.jetty.websocket.server.WebSocketUpgradeFilter","fileName":"WebSocketUpgradeFilter.java","lineNumber":214,"methodName":"doFilter","nativeMethod":false},{"className":"org.eclipse.jetty.servlet.ServletHandler$CachedChain","fileName":"ServletHandler.java","lineNumber":1610,"methodName":"doFilter","nativeMethod":false},{"className":"org.springframework.web.filter.CorsFilter","fileName":"CorsFilter.java","lineNumber":96,"methodName":"doFilterInternal","nativeMethod":false},{"className":"org.springframework.web.filter.OncePerRequestFilter","fileName":"OncePerRequestFilter.java","lineNumber":107,"methodName":"doFilter","nativeMethod":false},{"className":"org.eclipse.jetty.servlet.ServletHandler$CachedChain","fileName":"ServletHandler.java","lineNumber":1610,"methodName":"doFilter","nativeMethod":false},{"className":"org.springframework.web.filter.CharacterEncodingFilter","fileName":"CharacterEncodingFilter.java","lineNumber":200,"methodName":"doFilterInternal","nativeMethod":false},{"className":"org.springframework.web.filter.OncePerRequestFilter","fileName":"OncePerRequestFilter.java","lineNumber":107,"methodName":"doFilter","nativeMethod":false},{"className":"org.eclipse.jetty.servlet.ServletHandler$CachedChain","fileName":"ServletHandler.java","lineNumber":1610,"methodName":"doFilter","nativeMethod":false},{"className":"org.eclipse.jetty.servlet.ServletHandler","fileName":"ServletHandler.java","lineNumber":540,"methodName":"doHandle","nativeMethod":false},{"className":"org.eclipse.jetty.server.handler.ScopedHandler","fileName":"ScopedHandler.java","lineNumber":146,"methodName":"handle","nativeMethod":false},{"className":"org.eclipse.jetty.security.SecurityHandler","fileName":"SecurityHandler.java","lineNumber":548,"methodName":"handle","nativeMethod":false},{"className":"org.eclipse.jetty.server.handler.HandlerWrapper","fileName":"HandlerWrapper.java","lineNumber":132,"methodName":"handle","nativeMethod":false},{"className":"org.eclipse.jetty.server.handler.ScopedHandler","fileName":"ScopedHandler.java","lineNumber":257,"methodName":"nextHandle","nativeMethod":false},{"className":"org.eclipse.jetty.server.session.SessionHandler","fileName":"SessionHandler.java","lineNumber":1588,"methodName":"doHandle","nativeMethod":false},{"className":"org.eclipse.jetty.server.handler.ScopedHandler","fileName":"ScopedHandler.java","lineNumber":255,"methodName":"nextHandle","nativeMethod":false},{"className":"org.eclipse.jetty.server.handler.ContextHandler","fileName":"ContextHandler.java","lineNumber":1345,"methodName":"doHandle","nativeMethod":false},{"className":"org.eclipse.jetty.server.handler.ScopedHandler","fileName":"ScopedHandler.java","lineNumber":203,"methodName":"nextScope","nativeMethod":false},{"className":"org.eclipse.jetty.servlet.ServletHandler","fileName":"ServletHandler.java","lineNumber":480,"methodName":"doScope","nativeMethod":false},{"className":"org.eclipse.jetty.server.session.SessionHandler","fileName":"SessionHandler.java","lineNumber":1557,"methodName":"doScope","nativeMethod":false},{"className":"org.eclipse.jetty.server.handler.ScopedHandler","fileName":"ScopedHandler.java","lineNumber":201,"methodName":"nextScope","nativeMethod":false},{"className":"org.eclipse.jetty.server.handler.ContextHandler","fileName":"ContextHandler.java","lineNumber":1247,"methodName":"doScope","nativeMethod":false},{"className":"org.eclipse.jetty.server.handler.ScopedHandler","fileName":"ScopedHandler.java","lineNumber":144,"methodName":"handle","nativeMethod":false},{"className":"org.eclipse.jetty.server.handler.HandlerWrapper","fileName":"HandlerWrapper.java","lineNumber":132,"methodName":"handle","nativeMethod":false},{"className":"org.eclipse.jetty.server.Server","fileName":"Server.java","lineNumber":502,"methodName":"handle","nativeMethod":false},{"className":"org.eclipse.jetty.server.HttpChannel","fileName":"HttpChannel.java","lineNumber":364,"methodName":"handle","nativeMethod":false},{"className":"org.eclipse.jetty.server.HttpConnection","fileName":"HttpConnection.java","lineNumber":260,"methodName":"onFillable","nativeMethod":false},{"className":"org.eclipse.jetty.io.AbstractConnection$ReadCallback","fileName":"AbstractConnection.java","lineNumber":305,"methodName":"succeeded","nativeMethod":false},{"className":"org.eclipse.jetty.io.FillInterest","fileName":"FillInterest.java","lineNumber":103,"methodName":"fillable","nativeMethod":false},{"className":"org.eclipse.jetty.io.ChannelEndPoint$2","fileName":"ChannelEndPoint.java","lineNumber":118,"methodName":"run","nativeMethod":false},{"className":"org.eclipse.jetty.util.thread.strategy.EatWhatYouKill","fileName":"EatWhatYouKill.java","lineNumber":333,"methodName":"runTask","nativeMethod":false},{"className":"org.eclipse.jetty.util.thread.strategy.EatWhatYouKill","fileName":"EatWhatYouKill.java","lineNumber":310,"methodName":"doProduce","nativeMethod":false},{"className":"org.eclipse.jetty.util.thread.strategy.EatWhatYouKill","fileName":"EatWhatYouKill.java","lineNumber":168,"methodName":"tryProduce","nativeMethod":false},{"className":"org.eclipse.jetty.util.thread.strategy.EatWhatYouKill","fileName":"EatWhatYouKill.java","lineNumber":126,"methodName":"run","nativeMethod":false},{"className":"org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread","fileName":"ReservedThreadExecutor.java","lineNumber":366,"methodName":"run","nativeMethod":false},{"className":"org.eclipse.jetty.util.thread.QueuedThreadPool","fileName":"QueuedThreadPool.java","lineNumber":765,"methodName":"runJob","nativeMethod":false},{"className":"org.eclipse.jetty.util.thread.QueuedThreadPool$2","fileName":"QueuedThreadPool.java","lineNumber":683,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:11:41,441 [qtp1660228600-32] DEBUG scheduler.listener.WorkStateEventListenerCallBackUrl 58- agvCode:[M1740556469462],event:[作业完成接口回调],回调相关数据：[{"param":{"STATION":"1","agvCode":"M1740556469462","errorMessage":"Error selecting key or setting result to parameter object. Cause: java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist","on_key_u_turn":false,"missionWorkId":"db79c574-88f7-424b-80ee-b7b1510b10e1","markerCode":"44","linePatrol":true,"UNLOADONLY":false,"agvMapId":"FAB_10","releaseMode":4,"seq":1.0,"status":"SHUTDOWN"},"missionWork":{"agvCode":"M1740556469462","agvName":"l2","createTime":1740557270000,"endTime":1740557270000,"id":"db79c574-88f7-424b-80ee-b7b1510b10e1","message":"Error selecting key or setting result to parameter object. Cause: java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist","missionId":"107b4794-d177-11ef-9bf4-8c8caa44c2f8","name":"m26","runtimeParam":"{\"markerCode\":\"44\",\"STATION\":\"1\",\"linePatrol\":true,\"UNLOADONLY\":false,\"on_key_u_turn\":false,\"agvMapId\":\"FAB_10\",\"releaseMode\":4,\"seq\":1.0}","sequence":2,"status":"SHUTDOWN","updateTime":1740557501435}}]
2025-02-26T16:11:41,442 [qtp1660228600-32] DEBUG controller.v3.MissionWorkController 292- agvCode:[M1740556469462],event:[停止作业],机器人已掉线，直接停止作业，不发送mq信息：[{"agvCode":"M1740556469462","agvName":"l2","createTime":1740557270000,"endTime":1740557501442,"id":"db79c574-88f7-424b-80ee-b7b1510b10e1","message":"Error selecting key or setting result to parameter object. Cause: java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist","missionId":"107b4794-d177-11ef-9bf4-8c8caa44c2f8","name":"m26","runtimeParam":"{\"markerCode\":\"44\",\"STATION\":\"1\",\"linePatrol\":true,\"UNLOADONLY\":false,\"on_key_u_turn\":false,\"agvMapId\":\"FAB_10\",\"releaseMode\":4,\"seq\":1.0}","sequence":2,"status":"SHUTDOWN","updateTime":1740557501442}]
2025-02-26T16:11:41,442 [qtp1660228600-32] DEBUG controller.v3.MissionWorkController 300- agvCode:[M1740556469462],event:[停止作业],当前停止的作业数据：[{"agvCode":"M1740556469462","agvName":"l2","createTime":1740557270000,"endTime":1740557501442,"id":"db79c574-88f7-424b-80ee-b7b1510b10e1","message":"Error selecting key or setting result to parameter object. Cause: java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION youisimulation.next_code does not exist","missionId":"107b4794-d177-11ef-9bf4-8c8caa44c2f8","name":"m26","runtimeParam":"{\"markerCode\":\"44\",\"STATION\":\"1\",\"linePatrol\":true,\"UNLOADONLY\":false,\"on_key_u_turn\":false,\"agvMapId\":\"FAB_10\",\"releaseMode\":4,\"seq\":1.0}","sequence":2,"status":"SHUTDOWN","updateTime":1740557501442}]
2025-02-26T16:11:41,503 [ForkJoinPool.commonPool-worker-5] INFO service.impl.VehicleCommandServiceImpl 166- agvCode:[M1740556469462],event[发送作业指令]，content:[发送下发任务mq消息成功,missionWork:{"agvCode":"M1740556469462","agvGroupId":"","agvName":"l2","agvType":"","createTime":1740557501000,"id":"ddcc3fec-de3d-46ac-aa6b-7ab66f691330","missionGroupId":"","missionId":"107b4794-d177-11ef-9bf4-8c8caa44c2f8","name":"m26","runtimeParam":"{\"markerCode\":\"44\",\"STATION\":\"1\",\"linePatrol\":true,\"UNLOADONLY\":false,\"on_key_u_turn\":false,\"agvMapId\":\"FAB_10\",\"releaseMode\":4,\"seq\":1.0}","sequence":2,"status":"RUNNING","updateTime":1740557501491}]
2025-02-26T16:11:41,588 [MQTT Call: fleet_M1740556469462] DEBUG scheduler.listener.WorkStateEventListenerCallBackUrl 58- agvCode:[M1740556469462],event:[作业完成接口回调],回调相关数据：[{"param":{"STATION":"1","agvCode":"M1740556469462","on_key_u_turn":false,"missionWorkId":"ddcc3fec-de3d-46ac-aa6b-7ab66f691330","markerCode":"44","linePatrol":true,"UNLOADONLY":false,"agvMapId":"FAB_10","releaseMode":4,"seq":1.0,"status":"RUNNING"},"missionWork":{"agvCode":"M1740556469462","agvName":"l2","createTime":1740557501000,"id":"ddcc3fec-de3d-46ac-aa6b-7ab66f691330","missionId":"107b4794-d177-11ef-9bf4-8c8caa44c2f8","name":"m26","runtimeParam":"{\"markerCode\":\"44\",\"STATION\":\"1\",\"linePatrol\":true,\"UNLOADONLY\":false,\"on_key_u_turn\":false,\"agvMapId\":\"FAB_10\",\"releaseMode\":4,\"seq\":1.0}","sequence":2,"startTime":1740557501580,"status":"RUNNING","updateTime":1740557501580}}]
2025-02-26T16:11:41,632 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 160- start process agv request path plan agvCode:[M1740556469462],aimMarkerCodes:[44]
2025-02-26T16:11:41,636 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 171- agvCode:[M1740556469462], side path长度:[34].
2025-02-26T16:11:41,636 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 172- sidePathPlanResult---------------------------start------------------------
2025-02-26T16:11:41,636 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[99]->[100],T0:[0.0]
2025-02-26T16:11:41,636 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[100]->[101],T0:[0.0]
2025-02-26T16:11:41,636 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[101]->[196],T0:[0.0]
2025-02-26T16:11:41,636 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[196]->[94],T0:[0.0]
2025-02-26T16:11:41,636 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[94]->[91],T0:[0.0]
2025-02-26T16:11:41,638 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[91]->[89],T0:[0.0]
2025-02-26T16:11:41,638 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[89]->[87],T0:[0.0]
2025-02-26T16:11:41,638 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[87]->[88],T0:[0.0]
2025-02-26T16:11:41,638 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[88]->[85],T0:[0.0]
2025-02-26T16:11:41,638 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[85]->[84],T0:[0.0]
2025-02-26T16:11:41,638 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[84]->[83],T0:[0.0]
2025-02-26T16:11:41,638 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[83]->[69],T0:[0.0]
2025-02-26T16:11:41,638 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[69]->[68],T0:[0.0]
2025-02-26T16:11:41,638 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[68]->[67],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[67]->[66],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[66]->[137],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[137]->[63],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[63]->[61],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[61]->[170],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[170]->[167],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[167]->[59],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[59]->[57],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[57]->[55],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[55]->[53],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[53]->[190],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[190]->[131],T0:[0.0]
2025-02-26T16:11:41,639 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[131]->[51],T0:[0.0]
2025-02-26T16:11:41,640 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[51]->[224],T0:[0.0]
2025-02-26T16:11:41,640 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[224]->[49],T0:[0.0]
2025-02-26T16:11:41,640 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[49]->[47],T0:[0.0]
2025-02-26T16:11:41,640 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[47]->[45],T0:[0.0]
2025-02-26T16:11:41,640 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[45]->[211],T0:[0.0]
2025-02-26T16:11:41,640 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[211]->[231],T0:[0.0]
2025-02-26T16:11:41,640 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 189- agvCode:[M1740556469462],event:[路径规划],路径规划结果sidePathPlanResult--------------sidePath:[231]->[44],T0:[0.0]
2025-02-26T16:11:41,640 [SimpleAsyncTaskExecutor-2] DEBUG pathplan.trafficcontrollayer.PathPlanManger 198- agvCode:[M1740556469462],event:[路径规划],content:[路径规划完成，耗时8ms]
2025-02-26T16:11:41,641 [SimpleAsyncTaskExecutor-2] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:11:41,641 [SimpleAsyncTaskExecutor-2] DEBUG service.impl.CheckAndSendPathServiceImpl 543- agvCode:[M1740556469462],event:[clear],当前点位:[99]
2025-02-26T16:11:41,644 [SimpleAsyncTaskExecutor-2] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":173,"methodName":"addSidePathPlanResult","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger","fileName":"PathPlanManger.java","lineNumber":247,"methodName":"start","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.trafficcontrollayer.PathPlanManger$$FastClassBySpringCGLIB$$5fc88548","fileName":"<generated>","lineNumber":-1,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.cglib.proxy.MethodProxy","fileName":"MethodProxy.java","lineNumber":218,"methodName":"invoke","nativeMethod":false},{"className":"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation","fileName":"CglibAopProxy.java","lineNumber":749,"methodName":"invokeJoinpoint","nativeMethod":false},{"className":"org.springframework.aop.framework.ReflectiveMethodInvocation","fileName":"ReflectiveMethodInvocation.java","lineNumber":163,"methodName":"proceed","nativeMethod":false},{"className":"org.springframework.aop.interceptor.AsyncExecutionInterceptor","fileName":"AsyncExecutionInterceptor.java","lineNumber":115,"methodName":"lambda$invoke$0","nativeMethod":false},{"className":"org.springframework.aop.interceptor.AsyncExecutionInterceptor$$Lambda$501/1222059477","lineNumber":-1,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:11:41,645 [SimpleAsyncTaskExecutor-2] DEBUG mqtt.util.MqttUtils 204- event:[mq推送消息], topic:[/fleet/M1740556469462/pathPlan], message:[{"aimMarkerId":"252ad464-92c9-4cb5-8bd4-9fa05c49e47e","missionWorkActionId":"7a7a4087-82f9-48a6-bf12-e5651ac6f49c","missionWorkId":"ddcc3fec-de3d-46ac-aa6b-7ab66f691330","status":1,"uniqueFlag":"fc979a92-1c59-463b-8b04-c801df75e4ec"}]
2025-02-26T16:11:41,688 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[101], occupyAgvCode:[M1740556469462]]
2025-02-26T16:11:41,688 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[196], occupyAgvCode:[M1740556469462]]
2025-02-26T16:11:41,688 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[100], occupyAgvCode:[M1740556469462]]
2025-02-26T16:11:41,689 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [99]->[100], t0:[0], t1:[1], id:[0abb0ff7-3189-499d-ac68-cc6b4ce8d37d]
side path: [100]->[101], t0:[0], t1:[1], id:[4535985a-690e-480b-8e78-dcd9058cc27c]
side path: [101]->[196], t0:[0], t1:[1], id:[960cc146-2034-47bd-932a-518f2bbb9cfb]
]
2025-02-26T16:11:41,693 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [99]->[100], t0:[0], t1:[1], id:[0abb0ff7-3189-499d-ac68-cc6b4ce8d37d]
side path: [100]->[101], t0:[0], t1:[1], id:[4535985a-690e-480b-8e78-dcd9058cc27c]
side path: [101]->[196], t0:[0], t1:[1], id:[960cc146-2034-47bd-932a-518f2bbb9cfb]
]]
2025-02-26T16:11:44,336 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[89], occupyAgvCode:[M1740556469462]]
2025-02-26T16:11:44,337 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[91], occupyAgvCode:[M1740556469462]]
2025-02-26T16:11:44,337 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[94], occupyAgvCode:[M1740556469462]]
2025-02-26T16:11:44,338 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [196]->[94], t0:[0], t1:[1], id:[8a055442-e23a-4c83-b87f-a6560f00fc13]
side path: [94]->[91], t0:[0], t1:[1], id:[9674c1a3-4f45-490f-89ce-ab0137b34df0]
side path: [91]->[89], t0:[0], t1:[1], id:[5ba923c1-74e0-4982-8dd5-07f079bf328f]
]
2025-02-26T16:11:44,342 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [196]->[94], t0:[0], t1:[1], id:[8a055442-e23a-4c83-b87f-a6560f00fc13]
side path: [94]->[91], t0:[0], t1:[1], id:[9674c1a3-4f45-490f-89ce-ab0137b34df0]
side path: [91]->[89], t0:[0], t1:[1], id:[5ba923c1-74e0-4982-8dd5-07f079bf328f]
]]
2025-02-26T16:11:59,621 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[100], occupyAgvCode:[null]]
2025-02-26T16:12:11,832 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[101], occupyAgvCode:[null]]
2025-02-26T16:12:17,372 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[196], occupyAgvCode:[null]]
2025-02-26T16:12:30,483 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[87], occupyAgvCode:[M1740556469462]]
2025-02-26T16:12:30,484 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [89]->[87], t0:[0], t1:[1], id:[b1e294d1-4313-4f0f-82f6-8cbbaccf74db]
]
2025-02-26T16:12:30,487 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [89]->[87], t0:[0], t1:[1], id:[b1e294d1-4313-4f0f-82f6-8cbbaccf74db]
]]
2025-02-26T16:12:32,000 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[94], occupyAgvCode:[null]]
2025-02-26T16:12:38,152 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[88], occupyAgvCode:[M1740556469462]]
2025-02-26T16:12:38,154 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [87]->[88], t0:[0], t1:[1], id:[55d465c3-e8a1-4824-a8a2-6d488481c3cb]
]
2025-02-26T16:12:38,157 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [87]->[88], t0:[0], t1:[1], id:[55d465c3-e8a1-4824-a8a2-6d488481c3cb]
]]
2025-02-26T16:12:46,939 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[91], occupyAgvCode:[null]]
2025-02-26T16:12:53,094 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[85], occupyAgvCode:[M1740556469462]]
2025-02-26T16:12:53,096 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [88]->[85], t0:[0], t1:[1], id:[a1fcb5bc-7626-4a55-9ab4-b0b52b257735]
]
2025-02-26T16:12:53,099 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [88]->[85], t0:[0], t1:[1], id:[a1fcb5bc-7626-4a55-9ab4-b0b52b257735]
]]
2025-02-26T16:12:54,308 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[89], occupyAgvCode:[null]]
2025-02-26T16:13:09,641 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[87], occupyAgvCode:[null]]
2025-02-26T16:13:12,058 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[84], occupyAgvCode:[M1740556469462]]
2025-02-26T16:13:12,059 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [85]->[84], t0:[0], t1:[1], id:[0d7fa1c9-8bca-4718-90f0-e944bb9b94af]
]
2025-02-26T16:13:12,062 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [85]->[84], t0:[0], t1:[1], id:[0d7fa1c9-8bca-4718-90f0-e944bb9b94af]
]]
2025-02-26T16:13:20,382 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":23.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.7993369635977202,"speed_vy":0.0,"speed_w":-2.55276702554319E-11},"time":1740557600379,"useElevator":false,"workStatus":2}]
2025-02-26T16:13:26,486 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[83], occupyAgvCode:[M1740556469462]]
2025-02-26T16:13:26,486 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [84]->[83], t0:[0], t1:[1], id:[d8238e3f-5a65-45c8-983a-c475fd82548a]
]
2025-02-26T16:13:26,490 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [84]->[83], t0:[0], t1:[1], id:[d8238e3f-5a65-45c8-983a-c475fd82548a]
]]
2025-02-26T16:13:27,300 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[88], occupyAgvCode:[null]]
2025-02-26T16:13:40,515 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[69], occupyAgvCode:[M1740556469462]]
2025-02-26T16:13:40,516 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [83]->[69], t0:[0], t1:[1], id:[be4cccff-d7c5-453c-b1f6-28377cbf4aeb]
]
2025-02-26T16:13:40,519 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [83]->[69], t0:[0], t1:[1], id:[be4cccff-d7c5-453c-b1f6-28377cbf4aeb]
]]
2025-02-26T16:13:42,939 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[85], occupyAgvCode:[null]]
2025-02-26T16:13:54,945 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[68], occupyAgvCode:[M1740556469462]]
2025-02-26T16:13:54,946 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [69]->[68], t0:[0], t1:[1], id:[547fa121-9b6a-419e-b762-ee328c4ef109]
]
2025-02-26T16:13:54,949 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [69]->[68], t0:[0], t1:[1], id:[547fa121-9b6a-419e-b762-ee328c4ef109]
]]
2025-02-26T16:13:56,661 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[84], occupyAgvCode:[null]]
2025-02-26T16:14:06,254 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[67], occupyAgvCode:[M1740556469462]]
2025-02-26T16:14:06,255 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [68]->[67], t0:[0], t1:[1], id:[0e953b4a-51dc-4db6-9515-72578fed6a1e]
]
2025-02-26T16:14:06,258 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [68]->[67], t0:[0], t1:[1], id:[0e953b4a-51dc-4db6-9515-72578fed6a1e]
]]
2025-02-26T16:14:11,107 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[83], occupyAgvCode:[null]]
2025-02-26T16:14:16,957 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[66], occupyAgvCode:[M1740556469462]]
2025-02-26T16:14:16,958 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [67]->[66], t0:[0], t1:[1], id:[1297fe06-1032-4889-ad8a-90d578f21d18]
]
2025-02-26T16:14:16,961 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [67]->[66], t0:[0], t1:[1], id:[1297fe06-1032-4889-ad8a-90d578f21d18]
]]
2025-02-26T16:14:22,730 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[69], occupyAgvCode:[null]]
2025-02-26T16:14:34,839 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[68], occupyAgvCode:[null]]
2025-02-26T16:14:36,350 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[137], occupyAgvCode:[M1740556469462]]
2025-02-26T16:14:36,351 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [66]->[137], t0:[0], t1:[1], id:[da2fcb4e-cc28-41ff-9b04-6be1dd071d74]
]
2025-02-26T16:14:36,354 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [66]->[137], t0:[0], t1:[1], id:[da2fcb4e-cc28-41ff-9b04-6be1dd071d74]
]]
2025-02-26T16:14:47,658 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[63], occupyAgvCode:[M1740556469462]]
2025-02-26T16:14:47,659 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [137]->[63], t0:[0], t1:[1], id:[89d12be4-62e3-4a43-b4d4-7c8a8134a265]
]
2025-02-26T16:14:47,662 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [137]->[63], t0:[0], t1:[1], id:[89d12be4-62e3-4a43-b4d4-7c8a8134a265]
]]
2025-02-26T16:14:51,902 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[67], occupyAgvCode:[null]]
2025-02-26T16:15:00,777 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[61], occupyAgvCode:[M1740556469462]]
2025-02-26T16:15:00,778 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [63]->[61], t0:[0], t1:[1], id:[97799e61-4f5a-492c-82f5-0cef71618300]
]
2025-02-26T16:15:00,780 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [63]->[61], t0:[0], t1:[1], id:[97799e61-4f5a-492c-82f5-0cef71618300]
]]
2025-02-26T16:15:04,108 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[66], occupyAgvCode:[null]]
2025-02-26T16:15:11,580 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[170], occupyAgvCode:[M1740556469462]]
2025-02-26T16:15:11,581 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [61]->[170], t0:[0], t1:[1], id:[87bdfba7-bd4c-4bbe-9908-a628f47d1625]
]
2025-02-26T16:15:11,584 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [61]->[170], t0:[0], t1:[1], id:[87bdfba7-bd4c-4bbe-9908-a628f47d1625]
]]
2025-02-26T16:15:14,609 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[137], occupyAgvCode:[null]]
2025-02-26T16:15:18,236 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[167], occupyAgvCode:[M1740556469462]]
2025-02-26T16:15:18,237 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [170]->[167], t0:[0], t1:[1], id:[3dda8c31-39ec-4bce-add3-b44aacae89eb]
]
2025-02-26T16:15:18,239 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [170]->[167], t0:[0], t1:[1], id:[3dda8c31-39ec-4bce-add3-b44aacae89eb]
]]
2025-02-26T16:15:20,543 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":23.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.782791844444742,"speed_vy":0.0,"speed_w":-2.2048057823909062E-10},"time":1740557720541,"useElevator":false,"workStatus":2}]
2025-02-26T16:15:27,692 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[63], occupyAgvCode:[null]]
2025-02-26T16:15:32,624 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[59], occupyAgvCode:[M1740556469462]]
2025-02-26T16:15:32,625 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [167]->[59], t0:[0], t1:[1], id:[760c57fc-6c56-443a-b5ce-4b8bc1d4f6ea]
]
2025-02-26T16:15:32,627 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [167]->[59], t0:[0], t1:[1], id:[760c57fc-6c56-443a-b5ce-4b8bc1d4f6ea]
]]
2025-02-26T16:15:34,436 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[61], occupyAgvCode:[null]]
2025-02-26T16:15:42,079 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[57], occupyAgvCode:[M1740556469462]]
2025-02-26T16:15:42,080 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [59]->[57], t0:[0], t1:[1], id:[020e2b20-f77c-4e0b-ba6e-ff186414f18b]
]
2025-02-26T16:15:42,083 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [59]->[57], t0:[0], t1:[1], id:[020e2b20-f77c-4e0b-ba6e-ff186414f18b]
]]
2025-02-26T16:15:49,118 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[170], occupyAgvCode:[null]]
2025-02-26T16:15:50,425 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[55], occupyAgvCode:[M1740556469462]]
2025-02-26T16:15:50,426 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [57]->[55], t0:[0], t1:[1], id:[8121b910-d4ff-46cf-a70e-e6c5e5112ded]
]
2025-02-26T16:15:50,429 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [57]->[55], t0:[0], t1:[1], id:[8121b910-d4ff-46cf-a70e-e6c5e5112ded]
]]
2025-02-26T16:15:58,572 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[167], occupyAgvCode:[null]]
2025-02-26T16:16:05,111 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[53], occupyAgvCode:[M1740556469462]]
2025-02-26T16:16:05,112 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [55]->[53], t0:[0], t1:[1], id:[04aad00d-6da2-490d-888a-912edbb1d59b]
]
2025-02-26T16:16:05,115 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [55]->[53], t0:[0], t1:[1], id:[04aad00d-6da2-490d-888a-912edbb1d59b]
]]
2025-02-26T16:16:07,530 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[59], occupyAgvCode:[null]]
2025-02-26T16:16:21,218 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[57], occupyAgvCode:[null]]
2025-02-26T16:16:26,448 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[190], occupyAgvCode:[M1740556469462]]
2025-02-26T16:16:26,449 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [53]->[190], t0:[0], t1:[1], id:[7576a8f5-2c7e-4a45-a991-f02f94961a28]
]
2025-02-26T16:16:26,452 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [53]->[190], t0:[0], t1:[1], id:[7576a8f5-2c7e-4a45-a991-f02f94961a28]
]]
2025-02-26T16:16:30,474 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[131], occupyAgvCode:[M1740556469462]]
2025-02-26T16:16:30,475 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [190]->[131], t0:[0], t1:[1], id:[d63eecb4-4405-421d-9cac-dc27a4dbde83]
]
2025-02-26T16:16:30,478 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [190]->[131], t0:[0], t1:[1], id:[d63eecb4-4405-421d-9cac-dc27a4dbde83]
]]
2025-02-26T16:16:35,300 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[51], occupyAgvCode:[M1740556469462]]
2025-02-26T16:16:35,301 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [131]->[51], t0:[0], t1:[1], id:[6d76d0ef-1b7a-48f1-b5b1-612ac8bfddb1]
]
2025-02-26T16:16:35,304 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [131]->[51], t0:[0], t1:[1], id:[6d76d0ef-1b7a-48f1-b5b1-612ac8bfddb1]
]]
2025-02-26T16:16:41,440 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[55], occupyAgvCode:[null]]
2025-02-26T16:16:44,262 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[224], occupyAgvCode:[M1740556469462]]
2025-02-26T16:16:44,263 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [51]->[224], t0:[0], t1:[1], id:[bdc169fb-1d21-487e-9867-852eb1ebf3cb]
]
2025-02-26T16:16:44,266 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [51]->[224], t0:[0], t1:[1], id:[bdc169fb-1d21-487e-9867-852eb1ebf3cb]
]]
2025-02-26T16:16:46,681 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[53], occupyAgvCode:[null]]
2025-02-26T16:16:51,513 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[190], occupyAgvCode:[null]]
2025-02-26T16:16:54,329 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[49], occupyAgvCode:[M1740556469462]]
2025-02-26T16:16:54,334 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [224]->[49], t0:[0], t1:[1], id:[4e19f739-abd6-4a99-b517-8eda4fa5dd95]
]
2025-02-26T16:16:54,337 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [224]->[49], t0:[0], t1:[1], id:[4e19f739-abd6-4a99-b517-8eda4fa5dd95]
]]
2025-02-26T16:17:00,672 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[131], occupyAgvCode:[null]]
2025-02-26T16:17:07,108 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[47], occupyAgvCode:[M1740556469462]]
2025-02-26T16:17:07,109 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [49]->[47], t0:[0], t1:[1], id:[ae241446-891c-4dcd-8137-775bb8693ad1]
]
2025-02-26T16:17:07,112 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [49]->[47], t0:[0], t1:[1], id:[ae241446-891c-4dcd-8137-775bb8693ad1]
]]
2025-02-26T16:17:10,832 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[51], occupyAgvCode:[null]]
2025-02-26T16:17:20,663 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":22.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.7985910428735382,"speed_vy":0.0,"speed_w":6.158996415902077E-4},"time":1740557840660,"useElevator":false,"workStatus":2}]
2025-02-26T16:17:23,310 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[224], occupyAgvCode:[null]]
2025-02-26T16:17:23,613 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[45], occupyAgvCode:[M1740556469462]]
2025-02-26T16:17:23,614 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [47]->[45], t0:[0], t1:[1], id:[e1be029f-cd5d-4032-bf50-77585161ff5f]
]
2025-02-26T16:17:23,617 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [47]->[45], t0:[0], t1:[1], id:[e1be029f-cd5d-4032-bf50-77585161ff5f]
]]
2025-02-26T16:17:37,702 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[211], occupyAgvCode:[M1740556469462]]
2025-02-26T16:17:37,703 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [45]->[211], t0:[0], t1:[1], id:[3590854d-8e6d-42cf-907a-5ba3e16567d6]
]
2025-02-26T16:17:37,707 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [45]->[211], t0:[0], t1:[1], id:[3590854d-8e6d-42cf-907a-5ba3e16567d6]
]]
2025-02-26T16:17:38,311 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[49], occupyAgvCode:[null]]
2025-02-26T16:17:45,051 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[231], occupyAgvCode:[M1740556469462]]
2025-02-26T16:17:45,052 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [211]->[231], t0:[0], t1:[1], id:[27e150b7-dfc1-4a6b-a217-815164111ca2]
]
2025-02-26T16:17:45,055 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [211]->[231], t0:[0], t1:[1], id:[27e150b7-dfc1-4a6b-a217-815164111ca2]
]]
2025-02-26T16:17:49,276 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[47], occupyAgvCode:[null]]
2025-02-26T16:17:54,204 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 211- agvCode:[M1740556469462],event:[路径(marker点)申请],content:[50.apply success, marker:[44], occupyAgvCode:[M1740556469462]]
2025-02-26T16:17:54,205 [PositionMessage-CheckAndSendPath-Thread] DEBUG service.impl.CheckAndSendPathServiceImpl 466- start_agvCode:[M1740556469462] send side path to vehicle: [side path: [231]->[44], t0:[0], t1:[1], id:[9131d31e-bada-4286-9a12-079df9cfb972]
]
2025-02-26T16:17:54,208 [PositionMessage-CheckAndSendPath-Thread] WARN service.impl.CheckAndSendPathServiceImpl 480- agvCode:[M1740556469462],event:[路径下发],content:[下发路径完成sidePaths:[side path: [231]->[44], t0:[0], t1:[1], id:[9131d31e-bada-4286-9a12-079df9cfb972]
]]
2025-02-26T16:18:00,039 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[211], occupyAgvCode:[null]]
2025-02-26T16:18:00,040 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[45], occupyAgvCode:[null]]
2025-02-26T16:18:17,924 [PositionMessage-CheckAndSendPath-Thread] DEBUG trafficcontrollayer.ResourcePool.PathResourcePool 220- agvCode:[M1740556469462],event:[路径(marker点)释放],content:[40.release success, marker:[231], occupyAgvCode:[null]]
2025-02-26T16:18:18,089 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 541- agvCode:[M1740556469462],event:[clear],计算定位数据耗时[0]ms
2025-02-26T16:18:18,090 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 543- agvCode:[M1740556469462],event:[clear],当前点位:[44]
2025-02-26T16:18:18,095 [MQTT Call: fleet_M1740556469462] INFO service.impl.CheckAndSendPathServiceImpl 601- agvCode:[M1740556469462], event:[clear],打印方法调用栈信息:[[{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":-2,"methodName":"dumpThreads","nativeMethod":true},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":1610,"methodName":"getAllStackTraces","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":836,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.util.CommonUtils","fileName":"CommonUtils.java","lineNumber":830,"methodName":"getStackTrace","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":601,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.engine.pathplan.service.impl.CheckAndSendPathServiceImpl","fileName":"CheckAndSendPathServiceImpl.java","lineNumber":545,"methodName":"clear","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.messageHandle.SidePathMessageHandle","fileName":"SidePathMessageHandle.java","lineNumber":42,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.DefaultSubscribe","fileName":"DefaultSubscribe.java","lineNumber":64,"methodName":"handle","nativeMethod":false},{"className":"com.youibot.agv.scheduler.mqtt.subscribe.VehicleSubscribe","fileName":"VehicleSubscribe.java","lineNumber":100,"methodName":"messageArrived","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":499,"methodName":"deliverMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":402,"methodName":"handleMessage","nativeMethod":false},{"className":"org.eclipse.paho.client.mqttv3.internal.CommsCallback","fileName":"CommsCallback.java","lineNumber":206,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.Executors$RunnableAdapter","fileName":"Executors.java","lineNumber":511,"methodName":"call","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":266,"methodName":"run$$$capture","nativeMethod":false},{"className":"java.util.concurrent.FutureTask","fileName":"FutureTask.java","lineNumber":-1,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":180,"methodName":"access$201","nativeMethod":false},{"className":"java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask","fileName":"ScheduledThreadPoolExecutor.java","lineNumber":293,"methodName":"run","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor","fileName":"ThreadPoolExecutor.java","lineNumber":1149,"methodName":"runWorker","nativeMethod":false},{"className":"java.util.concurrent.ThreadPoolExecutor$Worker","fileName":"ThreadPoolExecutor.java","lineNumber":624,"methodName":"run","nativeMethod":false},{"className":"java.lang.Thread","fileName":"Thread.java","lineNumber":748,"methodName":"run","nativeMethod":false}]]
2025-02-26T16:18:18,095 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 611- agvCode:[M1740556469462], event:[clear],清理小车的路径资源-开始
2025-02-26T16:18:18,096 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.CheckAndSendPathServiceImpl 651- agvCode:[M1740556469462], event:[clear],清理小车的路径资源-结束
2025-02-26T16:18:18,151 [MQTT Call: fleet_M1740556469462] DEBUG scheduler.listener.WorkStateEventListenerCallBackUrl 58- agvCode:[M1740556469462],event:[作业完成接口回调],回调相关数据：[{"param":{"STATION":"1","agvCode":"M1740556469462","on_key_u_turn":false,"missionWorkId":"ddcc3fec-de3d-46ac-aa6b-7ab66f691330","markerCode":"44","linePatrol":true,"UNLOADONLY":false,"agvMapId":"FAB_10","releaseMode":4,"seq":1.0,"status":"WAIT"},"missionWork":{"agvCode":"M1740556469462","agvName":"l2","createTime":1740557501000,"id":"ddcc3fec-de3d-46ac-aa6b-7ab66f691330","missionId":"107b4794-d177-11ef-9bf4-8c8caa44c2f8","name":"m26","runtimeParam":"{\"markerCode\":\"44\",\"STATION\":\"1\",\"linePatrol\":true,\"UNLOADONLY\":false,\"on_key_u_turn\":false,\"agvMapId\":\"FAB_10\",\"releaseMode\":4,\"seq\":1.0}","sequence":2,"startTime":1740557502000,"status":"WAIT","updateTime":1740557898146}}]
2025-02-26T16:21:21,159 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":21.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.0,"speed_vy":0.0,"speed_w":0.0},"time":1740558081157,"useElevator":false,"workStatus":2}]
2025-02-26T16:23:22,104 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":21.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.0,"speed_vy":0.0,"speed_w":0.0},"time":1740558202102,"useElevator":false,"workStatus":2}]
2025-02-26T16:25:22,127 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":20.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.0,"speed_vy":0.0,"speed_w":0.0},"time":1740558322124,"useElevator":false,"workStatus":2}]
2025-02-26T16:27:22,656 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":20.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.0,"speed_vy":0.0,"speed_w":0.0},"time":1740558442653,"useElevator":false,"workStatus":2}]
2025-02-26T16:29:22,759 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.StatusMessageHandle 67- agvCode:[M1740556469462],event:[状态上报],当前小车的状态数据:[{"abnormalStatus":1,"agvCode":"M1740556469462","agvMapId":"FAB_10","appointStatus":1,"batteryStatus":{"battery_charge":0.0,"battery_charge_num":0.0,"battery_discharge":1.2,"battery_value":19.0,"battery_voltage":52.308},"controlMode":1,"emecStatus":{"emc_status":0},"isExecutable":0,"mapStatus":1,"screenAlive":true,"shelfAlive":true,"shelfData":{},"simulation":true,"speedStatus":{"speed_r_vx":0.0,"speed_r_vy":0.0,"speed_r_w":0.0,"speed_vx":0.0,"speed_vy":0.0,"speed_w":0.0},"time":1740558562754,"useElevator":false,"workStatus":2}]
2025-02-26T16:29:41,232 [MQTT Call: fleet_M1740556469462] DEBUG subscribe.messageHandle.WillMessageHandle 25- agvCode:[M1740556469462],event:[遗言],topic：[/will]
2025-02-26T16:29:41,243 [MQTT Call: fleet_M1740556469462] DEBUG service.impl.LoginCommandCommandServiceImpl 611- agvCode:[M1740556469462],event:[遗言], 设置小车状态为离线
