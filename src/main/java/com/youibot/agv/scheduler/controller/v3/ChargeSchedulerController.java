package com.youibot.agv.scheduler.controller.v3;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.youibot.agv.scheduler.annotation.LogOperation;
import com.youibot.agv.scheduler.constant.VehicleConstant;
import com.youibot.agv.scheduler.engine.pathplan.service.CheckAndSendPathService;
import com.youibot.agv.scheduler.entity.ChargeScheduler;
import com.youibot.agv.scheduler.exception.ExecuteException;
import com.youibot.agv.scheduler.param.SchedulerStopParam;
import com.youibot.agv.scheduler.param.SchedulerStopResult;
import com.youibot.agv.scheduler.service.ChargeSchedulerService;
import com.youibot.agv.scheduler.util.MessageUtils;
import com.youibot.agv.scheduler.vehicle.Vehicle;
import com.youibot.agv.scheduler.vehicle.pool.VehiclePoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

@RestController("chargeSchedulerControllerV3")
@RequestMapping(value = "/api/v3/chargeSchedulers", produces = "application/json")
@Api(value = "充电调度", tags = "充电调度", description = "充电调度管理接口")
public class ChargeSchedulerController extends BaseController {

    @Autowired
    private ChargeSchedulerService chargeSchedulerService;

    @Autowired
    private VehiclePoolService vehiclePoolService;

    @Autowired
    private CheckAndSendPathService checkAndSendPathService;

    private static final Logger logger = LoggerFactory.getLogger(ChargeSchedulerController.class);

    @ApiOperation(value = "分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "数量", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sort", value = "排序", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/page")
    @ResponseStatus(value = HttpStatus.OK)
    public PageInfo<ChargeScheduler> get(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return chargeSchedulerService.findPage(searchMap);
    }

    @ApiOperation(value = "列表")
    @GetMapping
    @ResponseStatus(value = HttpStatus.OK)
    public List<ChargeScheduler> getAll(@RequestParam(required = false) @ApiIgnore Map<String, String> searchMap) {
        return chargeSchedulerService.searchAll(searchMap);
    }

    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "删除充电调度记录", paramType = "path", required = true, dataType = "String")
    @DeleteMapping(value = "/{id}")
    @LogOperation("删除充电调度")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void delete(@PathVariable("id") String id) {
        ChargeScheduler chargeScheduler = chargeSchedulerService.selectById(id);
        if (ChargeScheduler.STATUS_CANCEL.equals(chargeScheduler.getStatus()) || ChargeScheduler.STATUS_SUCCESS.equals(chargeScheduler.getStatus())) {
            this.chargeSchedulerService.deleteById(id);
        } else {
            throw new ExecuteException(MessageUtils.getMessage("service.the_status_is_do_not_delete"));
        }
    }

    @ApiOperation(value = "取消")
    @ApiImplicitParam(name = "id", value = "取消充电调度", paramType = "path", required = true, dataType = "String")
    @PostMapping(value = "/{id}/cancel")
    @ResponseStatus(value = HttpStatus.OK)
    @LogOperation("取消充电调度")
    public SchedulerStopResult cancel(@PathVariable("id") String id, @RequestBody SchedulerStopParam param) {
        ChargeScheduler chargeScheduler = chargeSchedulerService.selectById(id);
        if (chargeScheduler == null) {
            throw new ExecuteException(MessageUtils.getMessage("service.the_scheduler_is_empty"));
        }
        if (ChargeScheduler.STATUS_CANCEL.equals(chargeScheduler.getStatus()) || ChargeScheduler.STATUS_SUCCESS.equals(chargeScheduler.getStatus())) {
            throw new ExecuteException(MessageUtils.getMessage("service.the_status_do_not_cancel"));
        }

        Vehicle vehicle = vehiclePoolService.selectById(chargeScheduler.getVehicleId());
        if (!param.isForcedStop() && (vehicle == null || !VehicleConstant.ONLINE.equals(vehicle.getOnlineStatus()))) {
            return new SchedulerStopResult("ERROR", 1001);//非强制取消且机器人不在线
        }

        if (vehicle != null) {
            ThreadContext.put("ROUTINGKEY", vehicle.getId());
        }

        if (vehicle == null || VehicleConstant.OFFLINE.equals(vehicle.getOnlineStatus())) {
            chargeSchedulerService.updateCancel(chargeScheduler.getId());
        } else if (VehicleConstant.OUTLINE.equals(vehicle.getOnlineStatus())) {
            //如果机器人为离线状态, 不发送取消指令到mqtt, 直接清理相关资源, 目前该做法主要针对机器人关机掉线后, 无法快速清理资源的问题
            chargeSchedulerService.updateCancel(chargeScheduler.getId());
            if (!CollectionUtils.isEmpty(vehicle.getPathPlanMessages())) {
                vehicle.getPathPlanMessages().clear();
            }
            checkAndSendPathService.clear(vehicle.getId(), "SMART_CHARGE");
            logger.debug("agvCode:[{}],event:[充电调度],小车不存在或离线，取消充电，并且清理资源：[{}]", vehicle.getId(), JSONObject.toJSONString(chargeScheduler));
        } else {
            vehicle.cancelCharge();
            logger.debug("agvCode:[{}],event:[充电调度],当前取消的充电调度数据：[{}]", vehicle.getId(), JSONObject.toJSONString(chargeScheduler));
        }
        if (vehicle != null) {
            ThreadContext.remove("ROUTINGKEY");
        }
        return new SchedulerStopResult("SUCCESS", null);
    }

}
